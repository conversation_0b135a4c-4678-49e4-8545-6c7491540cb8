---
description: 
globs: 
alwaysApply: false
---
# Feature Modules and Implementation

## Key Modules
1. **Employee Management**
   - Employee profiles and details
   - Department structure
   - Skills tracking

2. **Attendance Management**
   - Daily attendance tracking
   - Work hours calculation
   - Absence reporting
   - Check documentation in `docs/attendance/`

3. **Leave Management**
   - Leave requests and approvals
   - Balance tracking
   - Multiple leave types
   - Check documentation in `docs/leaves/`

## Controller Best Practices
- Mark utility methods with `[NonAction]` attribute
- Use explicit routing with specific route templates
- Follow consistent routing patterns
- For controllers with complex actions, reference documentation in `docs/`

## Services and Utilities
- Email Service for notifications
- Settings Service for application settings
- Notification systems (Email and Desktop)
- Check system utility documentation in `docs/system/`

