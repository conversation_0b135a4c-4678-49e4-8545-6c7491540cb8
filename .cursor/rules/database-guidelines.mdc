---
description: 
globs: 
alwaysApply: false
---
# Database Guidelines

## Database Connections
- System uses multiple database contexts:
  - `hrmsContext`: Primary context for HR operations
  - `bcContext`: Secondary context for business configuration
- Always inject the context into controllers or services
- Do not create new migrations for the database
- Always check for existing models before creating new ones

## Model Access
- If a model doesn't exist, connect to the database directly to explore the table structure
- Entity Framework Core is used for database access
- Use Include() for eager loading related entities
- Use transactions for operations that modify multiple tables

## Connection Examples
```csharp
// Controller example with context injection
public class AttendenceController : BaseController
{
    private readonly hrmsContext _db;
    
    public AttendenceController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, 
        AppHelper helper)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
    }
}
```

