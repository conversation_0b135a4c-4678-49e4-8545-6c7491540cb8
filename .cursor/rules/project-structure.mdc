---
description: 
globs: 
alwaysApply: false
---
# Project Structure and Documentation
The Human Resource Management System follows a structured MVC architecture with detailed documentation requirements.

## Documentation First
- Always check `docs/dev-guide.md` before implementing any feature
- Update relevant documentation in the `docs/` directory after implementation
- Documentation structure includes architecture, database, API, frontend, leaves, attendance, and system folders

## Key Documentation Files
- System Overview: `docs/architecture/system-overview.md`
- Database Connections: `docs/database/connection-spec.md`
- Form Submission: `docs/frontend/form-submission-spec.md`

## Architecture
- The system follows MVC architecture with presentation, business logic, and data access layers
- Multiple controllers organized by features: Attendance, Employees, Leaves, Account
- Each controller has corresponding ViewModels

