---
description: 
globs: 
alwaysApply: false
---
# Development Process

## Documentation Requirements
- Always maintain and reference technical documentation
- Check `docs/dev-guide.md` at the start of each task
- Update relevant documentation files when implementing changes
- Create missing documentation when implementing new features
- Reference documentation in your implementation
- Structure changes based on existing documentation patterns

## Implementation Steps
For each new feature or change:

1. First understand the models and classes related to the task
2. Check existing controllers and services for similar functionality
3. For each step, understand the models and classes related
4. If you need a model that doesn't exist:
   - Do not create any migration for database
   - Make a connection to the database and search for the table and columns
5. After implementation, ensure documentation is updated, clear and easy to read

## Cross-Cutting Concerns
- **Security**: Use custom permission-based access control
- **Localization**: Support for Arabic and English, RTL layout
- **Logging**: Implement application-level logging

