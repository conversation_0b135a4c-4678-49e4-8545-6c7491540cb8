---
description: 
globs: 
alwaysApply: false
---
# Frontend Guidelines

## Form Design
- Use Bootstrap 4 for the UI framework
- Forms should use cards with shadow-sm class
- Maintain consistent visual hierarchy with card-header and card-body
- Use proper spacing with p-4 for card bodies
- Responsive design with Bootstrap grid system

## UI Components
- Use contextual classes for status indicators:
  - `.bg-warning` for pending items
  - `.bg-success` for approved items
  - `.bg-danger` for rejected items
- Button hierarchy:
  - Primary actions: `.btn-primary`
  - Approval actions: `.btn-success`
  - Rejection/Cancel actions: `.btn-danger`
  - Secondary actions: `.btn-secondary`

## Form Submission
- Implement client-side validation
- Use AJAX for form submissions
- Confirmation dialogs for important actions
- Include proper loading indicators

## Accessibility
- All form controls must have associated labels
- Use ARIA attributes where necessary
- Ensure keyboard navigation works correctly

