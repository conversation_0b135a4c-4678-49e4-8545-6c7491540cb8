# Transport Module Documentation

<!-- META: Human Resource Management System Transport Module Documentation -->
<!-- KEYWORDS: transport, vehicles, cars, requests, approval, workflow, fleet management -->

## Overview

The Transport module is a comprehensive fleet management system within the Human Resource Management System that handles vehicle allocation, maintenance tracking, fuel management, and violation records. The module supports a multi-level approval workflow for vehicle requests and integrates with the Purchases module for maintenance approvals.

### Key Features

- **Vehicle Fleet Management**: Complete vehicle inventory with detailed tracking
- **Request Management**: Multi-level approval workflow for vehicle requests
- **Maintenance Tracking**: Service history and scheduled maintenance management
- **Fuel Management**: Gas consumption tracking and cost management
- **Violation Management**: Traffic violation tracking and staff accountability
- **Integration**: Seamless integration with Purchases module for maintenance requests

## Architecture

### Component Overview

The Transport module follows the MVC architecture pattern with the following key components:

```
Transport Module
├── Controllers/
│   ├── TransportsController.cs (2,278 lines)
│   └── MyController.cs (263 lines)
├── Models/
│   ├── DTOs/
│   ├── Entities/
│   │   ├── Cars.cs
│   │   ├── CarsRequests.cs
│   │   ├── CarsServices.cs
│   │   ├── CarsGasDtl.cs
│   │   └── CarsViolations.cs
│   └── Enums/
├── Views/
│   ├── Index.cshtml
│   ├── AllRequests.cshtml
│   ├── ManagerApproval.cshtml
│   ├── DgApproval.cshtml
│   ├── Gas.cshtml
│   ├── Service.cshtml
│   ├── Violations.cshtml
│   ├── ViewCar.cshtml
│   ├── ViewRequest.cshtml
│   ├── ViewGasRecord.cshtml
│   ├── ViewServiceRecord.cshtml
│   ├── ViewViolationRecord.cshtml
│   └── My/
│       ├── Index.cshtml
│       └── View.cshtml
├── ViewModels/
│   └── TransportsViewModel.cs
├── Providers/
│   └── TransportNavigationProvider.cs
├── Configuration/
│   └── TransportConfiguration.cs
├── Services/
└── Forms/
```

### Data Flow Architecture

```mermaid
graph TD
    A[Employee Request] --> B[Department Manager Approval]
    B --> C{Request Type}
    C -->|Type 0: Regular| D[Transport Department]
    C -->|Type 1: Extended| E[Director General Approval]
    E --> D
    D --> F[Vehicle Assignment]
    F --> G[Request Completion]
    
    H[Maintenance Request] --> I[Service Creation]
    I --> J[DG Approval]
    J --> K[Purchases Integration]
    K --> L[Purchase Request Created]
```

## Data Models

### Cars Entity

The primary vehicle entity containing all vehicle information.

**Properties:**
- `CarId` (int, Primary Key): Unique vehicle identifier
- `Name` (string, Required): Vehicle name/model
- `Plate` (string, Required): License plate number
- `Vin` (string, Required): Vehicle Identification Number
- `CurrentOdo` (int, Required): Current odometer reading
- `NextOdoService` (int): Next scheduled service odometer reading
- `YearModel` (int, Required): Vehicle model year
- `GasCardNo` (string): Fuel card number
- `Expiry` (DateTime): Registration expiry date
- `Status` (string): Vehicle status (Available/Unavailable)
- `StaffId` (int?): Currently assigned staff member
- `Code` (string): Internal vehicle code
- `Description` (string): Additional vehicle description
- `Deleted01` (int): Soft delete flag
- `CreatedAt` (DateTime): Creation timestamp

**Relationships:**
- One-to-Many with `CarsRequests`
- One-to-Many with `CarsServices`
- One-to-Many with `CarsGasDtl`
- One-to-Many with `CarsViolations`

**Computed Properties:**
- `TotalServiceCost` (float, NotMapped): Sum of all service costs
- `TotalGasCost` (float, NotMapped): Sum of all fuel costs

### CarsRequests Entity

Manages vehicle allocation requests with multi-level approval workflow.

**Properties:**
- `Id` (int, Primary Key): Request identifier
- `CarId` (int?): Assigned vehicle ID
- `StaffId` (int?): Requesting staff member
- `DepManagerId` (int, Required): Department manager for approval
- `FromDate` (DateTime, Required): Request start date
- `ToDate` (DateTime, Required): Request end date
- `Reason` (string, Required): Request justification
- `Note` (string): Additional notes
- `Status` (string): Current request status (legacy field)
- `REQ_STAT` (RequestStatus enum): New workflow status using enum
- `Type` (string): Request type (0=Regular, 1=Extended)
- `CurrentOdo` (string): Odometer reading at assignment
- `CompleteOdo` (string): Odometer reading at completion
- `ManagerApprov` (int): Manager approval status (0=Pending, 1=Approved, 2=Declined)
- `DGeneralApprov` (int): Director General approval status
- `ApprovedBy` (int?): Final approver ID
- `ApprovedAt` (DateTime?): Approval timestamp
- `DeclineNote` (string): Decline reason
- `CreatedBy` (int?): Request creator
- `CreatedAt` (DateTime?): Creation timestamp
- `UpdatedBy` (int?): Last updater
- `UpdatedAt` (DateTime?): Last update timestamp
- `Deleted01` (int): Soft delete flag

**Status Values (Legacy):**
- "New": Initial request status
- "موافقة المسؤول المباشر": Manager approved
- "موافقة المدير العام": Director General approved
- "موافق عليه": Final approval
- "مرفوض": Declined
- "Completed": Request completed
- "Canceled": Request canceled

**REQ_STAT Enum Values:**
- `RequestStatus.New` (0): Initial state when employee creates request
- `RequestStatus.ManagerApproved` (1): Approved by department manager
- `RequestStatus.ManagerDeclined` (2): Declined by department manager
- `RequestStatus.DGeneralApproved` (3): Approved by Director General (for Type 1 requests)
- `RequestStatus.DGeneralDeclined` (4): Declined by Director General
- `RequestStatus.TransportManagerApproved` (5): Approved by Transport Manager (alternative to DG for Type 0 requests)
- `RequestStatus.Approved` (6): Final approval by Transport Department with car assignment
- `RequestStatus.TransportDeclined` (7): Request declined by Transport Department
- `RequestStatus.Completed` (8): Request completed and car returned
- `RequestStatus.Canceled` (9): Request canceled by user or admin
- `RequestStatus.ApprovalCanceled` (10): Approval canceled after being approved

**Request Types:**
- Type "0": Regular requests (up to 14:30 same day)
- Type "1": Extended requests (beyond 14:30 or multi-day)

### CarsServices Entity

Tracks vehicle maintenance and service history.

**Properties:**
- `Id` (int, Primary Key): Service record identifier
- `CarId` (int, Required): Associated vehicle
- `Type` (string): Service type description
- `Date` (DateTime): Service date
- `CurrentOdo` (int): Odometer reading at service
- `NextOdoService` (int): Next scheduled service odometer
- `Cost` (float): Service cost
- `Description` (string): Service description
- `LongDescription` (string): Detailed service notes
- `Status` (string): Service status
- `ApprovedBy` (int): Service approver
- `ApprovedAt` (DateTime?): Approval timestamp
- `CreatedBy` (int): Record creator
- `CreatedAt` (DateTime): Creation timestamp
- `Deleted01` (int): Soft delete flag

**Service Integration:**
When a service is approved by Director General or Transport Admin, it automatically creates a purchase request in the Purchases module.

### CarsGasDtl Entity

Manages fuel consumption and cost tracking.

**Properties:**
- `Id` (int, Primary Key): Gas record identifier
- `CarId` (int, Required): Associated vehicle
- `Reference` (string): Fuel receipt reference
- `Date` (DateTime, Required): Fuel date
- `CurrentOdo` (int, Required): Odometer reading
- `Cost` (float): Fuel cost
- `Tax` (float): Tax amount
- `Description` (string): Additional notes
- `CreatedBy` (int): Record creator
- `CreatedAt` (DateTime): Creation timestamp
- `Deleted01` (int): Soft delete flag

### CarsViolations Entity

Tracks traffic violations and staff accountability.

**Properties:**
- `Id` (int, Primary Key): Violation record identifier
- `CarId` (int): Associated vehicle
- `StaffId` (int): Responsible staff member
- `Reference` (string): Violation reference number
- `Location` (string): Violation location
- `Date` (DateTime): Violation date
- `CurrentOdo` (int): Odometer reading
- `Cost` (float): Violation fine amount
- `ReceiptNo` (string): Payment receipt number
- `PaidAt` (DateTime): Payment date
- `Description` (string): Violation description
- `CreatedBy` (int): Record creator
- `CreatedAt` (DateTime): Creation timestamp
- `Deleted01` (int): Soft delete flag

## Controllers Overview

### TransportsController

The main controller handling all transport-related operations.

**Base Route:** `/Transports`
**Namespace:** `HumanResource.Modules.Transports.Controllers`
**Inherits:** `BaseController`

**Key Methods:**

1. **Index()** - Main dashboard showing all vehicles
2. **ManagerApproval()** - Department manager approval interface
3. **DgApproval()** - Director General approval interface
4. **Gas()** - Fuel management dashboard
5. **Service()** - Maintenance management dashboard
6. **Violations()** - Traffic violations dashboard
7. **AllRequests()** - Comprehensive request management
8. **ViewCar(id)** - Detailed vehicle view with history
9. **ViewRequest(id)** - Detailed request view
10. **CreateRequest()** - New request creation
11. **UpdateRequest()** - Request modification
12. **ApproveRequest()** - Final approval with vehicle assignment
13. **RequestCompleted()** - Mark request as completed
14. **DeleteRequest(id)** - Soft delete request

### MyController

Handles personal vehicle requests for employees.

**Base Route:** `/Transports/My`
**Namespace:** `HumanResource.Modules.Transports.Controllers`
**Inherits:** `TransportsController`

**Key Methods:**

1. **Index()** - Personal requests dashboard
2. **View(id)** - View personal request details
3. **CreateRequest()** - Submit personal vehicle request
4. **DeleteRequest(id)** - Cancel personal request

**Key Differences from TransportsController:**
- Filtered to show only user's own requests
- Limited permissions for viewing/editing
- Simplified approval workflow for DG users
- Arabic language messages for user interface

## API Endpoints

### Vehicle Management Endpoints

#### GET /Transports/Index
**Description:** Main vehicles dashboard
**Permissions:** `transports-admin|transports-department|transports-dgeneral`
**Response:** HTML view with vehicle list and maintenance alerts

#### GET /Transports/ViewCar/{id}
**Description:** Detailed vehicle information with history
**Permissions:** `transports-admin|transports-department`
**Parameters:**
- `id` (int): Vehicle ID
**Response:** Vehicle details with service history, fuel records, and violations

#### POST /Transports/CreateCar
**Description:** Create new vehicle record
**Permissions:** `transports-department|transports-admin`
**Request Body:**
```json
{
  "Name": "Toyota Camry",
  "Plate": "ABC-123",
  "Vin": "1HGBH41JXMN109186",
  "CurrentOdo": 50000,
  "NextOdoService": 55000,
  "YearModel": 2020,
  "GasCardNo": "GC001",
  "Expiry": "2025-12-31",
  "Description": "Executive vehicle"
}
```
**Response:**
```json
{
  "success": true,
  "message": ["تم إنشاء السيارة بنجاح."],
  "action": "reload"
}
```

### Request Management Endpoints

#### GET /Transports/My
**Description:** Employee's personal vehicle requests dashboard
**Permissions:** Authenticated users
**Controller:** MyController
**Query Parameters:**
- `from` (date): Filter start date
- `to` (date): Filter end date
**Response:** HTML view with user's requests

#### GET /Transports/My/View/{id}
**Description:** View personal request details
**Permissions:** Authenticated users (own requests only)
**Controller:** MyController
**Parameters:**
- `id` (int): Request ID
**Response:** Detailed request view

#### GET /Transports/AllRequests
**Description:** All vehicle requests (role-based filtering)
**Permissions:** `transports-admin|transports-department`
**Response:** HTML view with DataTable for request management

#### POST /Transports/AllDatatable
**Description:** DataTable endpoint for request pagination
**Permissions:** `transports-admin|transports-department`
**Request Body:** DataTable parameters
**Response:** Paginated request data in DataTable format

#### POST /Transports/CreateRequest
**Description:** Create new vehicle request
**Permissions:** Authenticated users
**Controller:** TransportsController
**Request Body:**
```json
{
  "StaffId": 1001,
  "DepManagerId": 1002,
  "FromDate": "2024-01-15T08:00:00",
  "ToDate": "2024-01-15T17:00:00",
  "Reason": "Business meeting",
  "Note": "Client visit downtown"
}
```
**Response:**
```json
{
  "success": true,
  "message": ["Request created successfully."],
  "action": "reload"
}
```

#### POST /Transports/My
**Description:** Create personal vehicle request
**Permissions:** Authenticated users
**Controller:** MyController
**Request Body:** Same as CreateRequest
**Response:**
```json
{
  "success": true,
  "message": ["تم إنشاء الطلب بنجاح."],
  "action": "reload"
}
```

### Approval Workflow Endpoints

#### GET /Transports/Manager
**Description:** Department manager approval dashboard
**Permissions:** `transports-admin|department-manager`
**Response:** Requests pending manager approval

#### GET /Transports/Manager/View/{cId}
**Description:** Manager request review
**Permissions:** `transports-admin|department-manager`
**Parameters:**
- `cId` (string): Encrypted request ID
**Response:** Request details for manager review

#### GET /Transports/DepManagerApprove/{id}
**Description:** Approve request as department manager
**Permissions:** `department-manager|transports-admin`
**Parameters:**
- `id` (int): Request ID
**Response:** Redirect to requests list

#### POST /Transports/DepManagerDecline
**Description:** Decline request as department manager
**Permissions:** `department-manager|transports-admin`
**Request Body:**
```json
{
  "Id": 123,
  "DeclineNote": "Insufficient justification"
}
```

#### GET /Transports/Dg
**Description:** Director General approval dashboard
**Permissions:** `transports-admin|transports-dgeneral`
**Response:** Extended requests pending DG approval

#### POST /Transports/DGeneralApprove
**Description:** Director General approval
**Permissions:** `transports-admin|transports-dgeneral`
**Request Body:**
```json
{
  "Id": 123,
  "CarId": 5,
  "Note": "Approved for official business"
}
```

#### POST /Transports/ApproveRequest
**Description:** Final request approval with vehicle assignment
**Permissions:** `transports-admin|transports-department`
**Request Body:**
```json
{
  "Id": 123,
  "CarId": 5,
  "CurrentOdo": "75000"
}
```

### Maintenance Management Endpoints

#### GET /Transports/Service
**Description:** Vehicle maintenance dashboard
**Permissions:** `transports-admin|transports-department`
**Response:** Service records and maintenance schedule

#### POST /Transports/CreateService
**Description:** Create maintenance record
**Permissions:** `transports-department|transports-admin`
**Request Body:**
```json
{
  "CarId": 5,
  "Type": "Oil Change",
  "Date": "2024-01-15",
  "CurrentOdo": 75000,
  "NextOdoService": 80000,
  "Description": "Regular maintenance",
  "LongDescription": "Full service including oil, filters, and inspection"
}
```

#### GET /Transports/ApproveService/{cId}
**Description:** Approve service and create purchase request
**Permissions:** `transports-dgeneral|transports-admin`
**Parameters:**
- `cId` (string): Encrypted service ID
**Integration:** Creates purchase request in Purchases module

### Fuel Management Endpoints

#### GET /Transports/Gas
**Description:** Fuel consumption dashboard
**Permissions:** `transports-admin|transports-department`
**Response:** Fuel records and cost analysis

#### POST /Transports/CreateGasDtl
**Description:** Record fuel consumption
**Permissions:** `transports-department|transports-admin`
**Request Body:**
```json
{
  "CarId": 5,
  "Reference": "GAS-001",
  "Date": "2024-01-15",
  "CurrentOdo": 75500,
  "Cost": 150.00,
  "Tax": 15.00,
  "Description": "Regular fuel"
}
```

### Violation Management Endpoints

#### GET /Transports/Violations
**Description:** Traffic violations dashboard
**Permissions:** `transports-admin|transports-department`
**Response:** Violation records and staff accountability

#### POST /Transports/CreateViolation
**Description:** Record traffic violation
**Permissions:** `transports-department|transports-admin`
**Request Body:**
```json
{
  "CarId": 5,
  "StaffId": 1001,
  "Reference": "VIO-001",
  "Location": "Main Street",
  "Date": "2024-01-15",
  "CurrentOdo": 75200,
  "Cost": 200.00,
  "Description": "Speeding violation"
}
```

## Business Rules and Validation Logic

### Request Creation Rules

1. **Time-based Request Types:**
   - Requests ending after 14:30 are classified as Type "1" (Extended)
   - Type "1" requests require Director General approval
   - Type "0" requests only need department manager approval

2. **Date Validation:**
   - `FromDate` cannot be in the past (except for admin/department users)
   - `ToDate` must be after `FromDate`
   - System validates date ranges for conflicts

3. **Staff Assignment:**
   - Admin and department users can create requests for other staff
   - Regular users can only create requests for themselves
   - Department manager is automatically determined from organizational hierarchy

### Approval Workflow Rules

1. **Department Manager Approval:**
   - Required for all requests
   - Manager must be the requesting employee's direct supervisor
   - Sets `ManagerApprov = 1` and status to "موافقة المسؤول المباشر"

2. **Director General Approval:**
   - Required only for Type "1" (Extended) requests
   - Can assign specific vehicle during approval
   - Sets `DGeneralApprov = 1` and status to "موافقة المدير العام"

3. **Final Approval:**
   - Performed by Transport Department
   - Assigns vehicle and updates odometer
   - Sets vehicle status to "Unavailable"
   - Links vehicle to staff member

### Vehicle Assignment Rules

1. **Availability Check:**
   - Vehicle must have status "Available"
   - Cannot assign vehicle already in use
   - System prevents double-booking

2. **Odometer Validation:**
   - Current odometer must be provided at assignment
   - Odometer reading must be logical (not decreasing)
   - Updates vehicle's current odometer

### Maintenance Rules

1. **Service Scheduling:**
   - Tracks next service based on odometer reading
   - Alerts when service is due (within 300km)
   - Updates vehicle's next service odometer

2. **Approval Integration:**
   - Service requests require DG approval for purchases
   - Approved services automatically create purchase requests
   - Links to Purchases module with proper references

### Cost Tracking Rules

1. **Fuel Management:**
   - Records actual costs and tax amounts
   - Updates vehicle odometer with each fuel entry
   - Tracks fuel efficiency and consumption patterns

2. **Violation Accountability:**
   - Links violations to responsible staff member
   - Tracks payment status and receipt numbers
   - Maintains violation history per vehicle and staff

## Integration Points

### Purchases Module Integration

The Transport module integrates seamlessly with the Purchases module for maintenance approvals:

**Integration Flow:**
1. Service request created in Transport module
2. Director General approves service via `/Transports/ApproveService/{cId}`
3. System automatically creates `PurchasesRequest` with:
   - `RelId`: Service ID
   - `RelType`: "CarsServices"
   - `Reference`: "Transports service #[ServiceId]"
   - `Origin`: "Transports Department"

4. Creates corresponding `RequestItem` with:
   - Service type and vehicle details as item name
   - Service description as notes
   - Links back to original service record

**Code Example:**
```csharp
var NewRequest = new PurchasesRequest
{
    RelId = Service.Id,
    RelType = "CarsServices",
    Reference = "Transports service #" + Service.Id,
    Date = DateTime.Now,
    Origin = "Transports Department",
    LastActionDate = DateTime.Now,
};

var NewItem = new RequestItem
{
    Name = Service.Type + "[" + Car.Plate + "-" + Car.Name + "]",
    Quantity = 1,
    Note = Service.Description,
    RequestId = NewRequest.Id,
    RelId = Service.Id,
    RelType = "CarsServices",
    CreatedBy = _v.Profile.EmpNo.Value,
};
```

### Employee Management Integration

- Uses `VempDtls` entity for staff information
- Integrates with organizational hierarchy for manager determination
- Leverages department structure for filtering and permissions

### Notification System Integration

While not explicitly implemented in the current codebase, the module is designed to integrate with the notification system for:
- Request status updates
- Approval notifications
- Maintenance reminders
- Violation alerts

## Security Considerations

### Role-Based Access Control

The Transport module implements comprehensive role-based security:

#### Permission Levels

1. **transports-admin**: Full system access
   - All CRUD operations
   - Override approvals
   - System configuration

2. **transports-department**: Department operations
   - Vehicle management
   - Request processing
   - Maintenance tracking
   - Fuel and violation management

3. **transports-dgeneral**: Executive approval
   - Director General approval authority
   - Service approval for purchases
   - High-level request oversight

4. **department-manager**: Team management
   - Approve team member requests
   - Department-level oversight
   - Automatically assigned based on hierarchy level (≤6)

#### Permission Matrix

| Action | Admin | Department | DGeneral | Dept Manager | Regular User |
|--------|-------|------------|----------|--------------|--------------|
| View Vehicles | ✓ | ✓ | ✓ | ✗ | ✗ |
| Create Vehicle | ✓ | ✓ | ✗ | ✗ | ✗ |
| Create Request | ✓ | ✓ | ✓ | ✓ | ✓ |
| Manager Approval | ✓ | ✗ | ✗ | ✓ | ✗ |
| DG Approval | ✓ | ✗ | ✓ | ✗ | ✗ |
| Final Approval | ✓ | ✓ | ✗ | ✗ | ✗ |
| Service Management | ✓ | ✓ | ✗ | ✗ | ✗ |
| Service Approval | ✓ | ✗ | ✓ | ✗ | ✗ |

### Data Security

1. **Soft Deletes**: Uses `Deleted01` flag instead of hard deletes
2. **Audit Trail**: Tracks creation and modification timestamps
3. **User Attribution**: Records who created/modified records
4. **Input Validation**: Model validation and business rule enforcement
5. **Authorization Checks**: Every endpoint validates user permissions

### Session Management

- User rights cached in session for performance
- Cache key format: `UserRights_{EmpNo}`
- Automatic cache invalidation on role updates
- Session timeout: 30 minutes

## Usage Examples and Common Workflows

### Employee Request Workflow

**Scenario:** Employee needs vehicle for business meeting

1. **Create Request:**
```csharp
// Employee submits request via MyController
POST /Transports/My
{
    "FromDate": "2024-01-15T08:00:00",
    "ToDate": "2024-01-15T17:00:00",
    "Reason": "Client meeting downtown",
    "DepManagerId": 1002
}
```

2. **Manager Approval:**
```csharp
// Department manager approves
GET /Transports/DepManagerApprove/123
// Status changes to "موافقة المسؤول المباشر"
```

3. **Transport Department Processing:**
```csharp
// Transport assigns vehicle
POST /Transports/ApproveRequest
{
    "Id": 123,
    "CarId": 5,
    "CurrentOdo": "75000"
}
// Vehicle status becomes "Unavailable"
// Staff assigned to vehicle
```

4. **Request Completion:**
```csharp
// Transport marks complete
POST /Transports/RequestCompleted
{
    "Id": 123,
    "CurrentOdo": "75150",
    "ToDate": "2024-01-15T16:30:00",
    "Note": "Returned in good condition"
}
// Vehicle status returns to "Available"
```

### Extended Request Workflow

**Scenario:** Multi-day business trip requiring DG approval

1. **Create Extended Request:**
```csharp
POST /Transports/My  // Via MyController for personal requests
{
    "FromDate": "2024-01-15T08:00:00",
    "ToDate": "2024-01-17T18:00:00",  // Multi-day = Type "1"
    "Reason": "Regional conference attendance",
    "DepManagerId": 1002
}
```

2. **Manager Approval:** (Same as above)

3. **Director General Approval:**
```csharp
POST /Transports/DGeneralApprove
{
    "Id": 123,
    "CarId": 3,
    "Note": "Approved for official conference"
}
// Status: "موافقة المدير العام"
```

4. **Final Processing:** (Same as regular workflow)

### Maintenance Workflow

**Scenario:** Vehicle requires scheduled maintenance

1. **Create Service Record:**
```csharp
POST /Transports/CreateService
{
    "CarId": 5,
    "Type": "Scheduled Maintenance",
    "Date": "2024-01-20",
    "CurrentOdo": 80000,
    "NextOdoService": 85000,
    "Description": "10,000km service"
}
```

2. **Director General Approval:**
```csharp
GET /Transports/ApproveService/abc123
// Creates purchase request automatically
// Status: "Sent to Purchases"
```

3. **Purchase Integration:**
```csharp
// Automatically created in Purchases module:
PurchasesRequest {
    RelId: ServiceId,
    RelType: "CarsServices",
    Reference: "Transports service #123",
    Origin: "Transports Department"
}
```

### Fuel Tracking Workflow

**Scenario:** Recording fuel consumption

```csharp
POST /Transports/CreateGasDtl
{
    "CarId": 5,
    "Reference": "RECEIPT-001",
    "Date": "2024-01-15",
    "CurrentOdo": 75500,
    "Cost": 180.00,
    "Tax": 18.00,
    "Description": "Full tank - Premium"
}
// Updates vehicle odometer
// Tracks fuel efficiency
```

### Violation Management Workflow

**Scenario:** Recording traffic violation

```csharp
POST /Transports/CreateViolation
{
    "CarId": 5,
    "StaffId": 1001,
    "Reference": "TICKET-001",
    "Location": "Highway 101",
    "Date": "2024-01-15",
    "CurrentOdo": 75200,
    "Cost": 300.00,
    "Description": "Speeding - 20km over limit"
}
// Links violation to responsible staff
// Tracks payment status
```

## Performance Considerations

### Database Optimization

1. **Indexing Strategy:**
   - Primary keys on all entities
   - Foreign key indexes for relationships
   - Composite indexes on frequently queried columns

2. **Query Optimization:**
   - Uses Entity Framework Include() for eager loading
   - Implements pagination for large datasets
   - Filters deleted records consistently

3. **Caching Strategy:**
   - User permissions cached in session
   - Static data cached where appropriate
   - Cache invalidation on updates

### DataTable Integration

The module uses server-side DataTable processing for large datasets:

```csharp
[Route("Transports/AllDatatable")]
[HttpPost]
public IActionResult AllRequestsDatatable([FromForm] DataTableHelper datatable)
{
    // Server-side pagination
    var query = _db.CarsRequests.Include(car => car.Car)
        .Where(/* filters */);

    // Search functionality
    if (!string.IsNullOrEmpty(datatable.Search.Value))
    {
        query = query.Where(/* search conditions */);
    }

    // Sorting
    switch (datatable.Order[0].Column)
    {
        // Column-based sorting
    }

    // Pagination
    var data = query.Skip(datatable.Start).Take(datatable.Length).ToList();

    return Json(new {
        datatable.Draw,
        recordsTotal = total,
        recordsFiltered = filteredTotal,
        data = formattedData
    });
}
```

## Error Handling and Logging

### Exception Management

The module implements comprehensive error handling:

```csharp
try
{
    _context.CarsRequests.Add(NewRequest);
    _context.SaveChanges();

    return Json(new {
        success = true,
        message = new List<string> { "تم إنشاء الطلب بنجاح." },
        action = "reload"
    });
}
catch (Exception ex)
{
    return Json(new {
        success = false,
        message = new List<string> { $"Error: {ex.Message}" },
        action = ""
    });
}
```

### Validation Handling

Model validation is consistently applied:

```csharp
if (!IsValid(ModelState))
{
    return Json(new {
        success = false,
        message = ValidateErrors(ModelState),
        action = ""
    });
}
```

## Future Enhancements

### Recommended Improvements

1. **API Modernization:**
   - RESTful API endpoints
   - OpenAPI/Swagger documentation
   - JSON-based responses

2. **Real-time Features:**
   - SignalR integration for live updates
   - Push notifications for approvals
   - Real-time vehicle tracking

3. **Advanced Analytics:**
   - Fuel efficiency reporting
   - Cost analysis dashboards
   - Predictive maintenance

4. **Mobile Integration:**
   - Mobile-friendly interfaces
   - GPS integration
   - Photo uploads for violations

5. **Workflow Automation:**
   - Automated approval routing
   - Email notifications
   - Calendar integration

## Troubleshooting

### Common Issues

1. **Permission Denied (403):**
   - Verify user has required role
   - Check session validity
   - Confirm role assignments in database

2. **Request Creation Fails:**
   - Validate date ranges
   - Check department manager assignment
   - Verify staff exists in VempDtls

3. **Vehicle Assignment Issues:**
   - Confirm vehicle availability
   - Check for existing assignments
   - Validate odometer readings

4. **Integration Problems:**
   - Verify Purchases module connectivity
   - Check RelId and RelType mappings
   - Confirm purchase request creation

### Debugging Tips

1. **Enable Detailed Logging:**
   - Add logging to controller actions
   - Monitor database queries
   - Track approval workflow states

2. **Database Queries:**
   - Check soft delete filters (`Deleted01 != 1`)
   - Verify foreign key relationships
   - Monitor query performance

3. **Session Issues:**
   - Clear user rights cache
   - Check session timeout settings
   - Verify authentication state

---

**Documentation Reference:** [doc-ref:transport-module]

**Related Documentation:**
- [System Architecture Overview](architecture/system-overview.md)
- [Database Models](database/models.md)
- [API Endpoints Reference](api/endpoints.md)
- [User Rights Caching System](features/user-rights-caching.md)
