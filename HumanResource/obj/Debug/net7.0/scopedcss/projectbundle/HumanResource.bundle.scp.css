/* _content/HumanResource/Modules/Shared/Views/Shared/_HomeLayout.cshtml.rz.scp.css */
/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-yuzpgh4ayf] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-yuzpgh4ayf] {
  color: #0077cc;
}

.btn-primary[b-yuzpgh4ayf] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-yuzpgh4ayf], .nav-pills .show > .nav-link[b-yuzpgh4ayf] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-yuzpgh4ayf] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-yuzpgh4ayf] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-yuzpgh4ayf] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-yuzpgh4ayf] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-yuzpgh4ayf] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
/* _content/HumanResource/Modules/Shared/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-8oei08wwhy] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-8oei08wwhy] {
  color: #0077cc;
}

.btn-primary[b-8oei08wwhy] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-8oei08wwhy], .nav-pills .show > .nav-link[b-8oei08wwhy] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-8oei08wwhy] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-8oei08wwhy] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-8oei08wwhy] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-8oei08wwhy] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-8oei08wwhy] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
