﻿using System.Text.RegularExpressions;
using Newtonsoft.Json;
namespace HumanResource.Language;

public class Translator
{
	public string lang { get; set; }
	public bool USE_COOKIES { get; set; }
	private string lang_file = "C:\\language\\AR.json";
	public Dictionary<string, string> dictionary;
	private string languages_dir = Path.Combine(Directory.GetCurrentDirectory(), "C:\\language\\");
	private string DEFAULT_LANGUAGE = "AR";
#pragma warning disable CS0169 // The field 'Translator.untranslated_logging' is never used
	private bool untranslated_logging;
#pragma warning restore CS0169 // The field 'Translator.untranslated_logging' is never used
	public bool last_translated = false;
#pragma warning disable CS0169 // The field 'Translator.httpContextAccessor' is never used
	private readonly IHttpContextAccessor httpContextAccessor;
#pragma warning restore CS0169 // The field 'Translator.httpContextAccessor' is never used
#pragma warning disable CS0169 // The field 'Translator._http' is never used
	private readonly IHttpContextAccessor _http;
#pragma warning restore CS0169 // The field 'Translator._http' is never used
#pragma warning disable CS0169 // The field 'Translator._env' is never used
	private readonly IWebHostEnvironment _env;
#pragma warning restore CS0169 // The field 'Translator._env' is never used

	public Translator()
	{
		USE_COOKIES = true;
	
		var httpContextAccessor = new HttpContextAccessor();
		
		lang = httpContextAccessor.HttpContext.Request.Cookies["lang"] ?? string.Empty;


		if (File.Exists(lang_file))
		{
			var json = File.ReadAllText(lang_file);
            dictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
		}


		if (string.IsNullOrEmpty(lang))
		{
			setLanguage(DEFAULT_LANGUAGE);
		}
	}


#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
	public string Tr(string? word)
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
	{
		return word;

        if (word == null)
			return "";

    

        var lookup_word = Regex.Replace(word.ToLower(), "{{.*}}", "");
		var translatedis = dictionary.ContainsKey(word);
		if (dictionary != null && dictionary.ContainsKey(word))
		{
			var trWord = dictionary[word];

			var arr = Regex.Matches(trWord, "{{([0-9]+)}}").Select(m => m.Groups[1].Value).ToList();
			var arr2 = Regex.Matches(word, "{{(.*?)}}").Select(m => m.Groups[1].Value).ToList();

			foreach (var value in arr)
			{
				var val = int.Parse(value) - 1;
				if (val < arr2.Count)
				{
					trWord = trWord.Replace($"{{{{{value}}}}}", arr2[val]);
				}
			}
			last_translated = true;
			return trWord;
		}
		else
		{
			last_translated = false;
			return word;
		}
	}

	public void setLanguage(string language_code, int duration = 604800)
	{
		if (language_code.Length > 2)
		{
			language_code = DEFAULT_LANGUAGE;
		}
		
		lang = language_code.ToUpper();
		
		var httpContextAccessor = new HttpContextAccessor();
		httpContextAccessor.HttpContext.Response.Cookies.Append("lang", lang);
		
		
		lang_file = Path.Combine(languages_dir, $"{lang}.json");
		if (File.Exists(lang_file))
		{
			var json = File.ReadAllText(lang_file);
			dictionary = JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
		}
	}

	public bool translated()
	{
		return last_translated;
	}

	public string language()
	{
		return lang;
	}

}

