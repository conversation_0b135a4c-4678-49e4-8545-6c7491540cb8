﻿using HumanResource.Modules.Assets.Models.Entities;
using HumanResource.Modules.Inventory.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Inventory.ViewModels;

public class InventoryViewModel : BaseViewModel
{

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
    public InventoryViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _db = context;


    }


    public List<InventoryCategory> InventoryCategories { get; set; }
    public InventoryCategory InventoryCategory { get; set; }

 
    public List<InventoryItem> InventoryItems { get; set; }
    public InventoryItem InventoryItem { get; set; }
    public List<InventoryLog> InventoryLogs { get; set; }
    public List<InventoryRequest> InventoryRequests { get; set; }
    public InventoryRequest InventoryRequest { get; set; }

	public List<RequestItem> RequestItems { get; set; }
	public RequestItem RequestItem { get; set; }
    public TDeptCode TDeptCode { get; set; }
    public List<TDeptCode> TDeptCodes { get; set; }
    public List<string> requestStatuses { get; set; }
    



    public InventoryItem GetItem(int Id)
    {
        return _db.InventoryItem.Find(Id);
    }



}

