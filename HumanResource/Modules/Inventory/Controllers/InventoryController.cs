﻿
using Microsoft.AspNetCore.Mvc;

using Microsoft.EntityFrameworkCore;
using Microsoft.CodeAnalysis;

using Newtonsoft.Json;

using HumanResource.Modules.Inventory.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Inventory.Forms;
using HumanResource.Modules.Inventory.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Core.UI.Models;



namespace HumanResource.Modules.Inventory.Controllers;


//new Role {Label="Inventory admin", Name="inventory-admin"},
//new Role { Label = "Inventory department", Name = "inventory-department" },
//new Role { Label = "Inventory D General", Name = "inventory-dgeneral" },

[Area("Inventory")]
[Route("Inventory")]

public class InventoryController : BaseController
{

    public InventoryViewModel _v;
  

    public List<string> _statuses;

    public InventoryController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _v = new InventoryViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "inventory";

       

        _v.requestStatuses = new List<string> { "جديد", "معلق", "في انتظار توفر الكمية", "جاري العمل عليه", "مكتمل" };

    }

    [Route("Index")]
    [Can("inventory-department")]
    public IActionResult Requests(DateTime? from, DateTime? to)
    {

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Inventory", Url=$"/Inventory/Index"},
            new Breadcrumb {Label="Requests", Url=$"/Inventory/Index"},
        };


        _v.Page.Reload = true;


        _v.Page.Title = "Inventory request";


        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);


        _v.InventoryRequests = _db.InventoryRequest
        .Where(r =>
            r.ManagerApproveBy != 0
            && r.CancelFlag == 0
            ).ToList();

        return View(_v);


    }

    [HttpGet("Update/{Id}")]
    [Can("inventory-department")]
    public IActionResult RequestUpdate(int Id)
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="طلبات المخازن", Url=$"/Inventory/Index"},
            new Breadcrumb {Label="View", Url=$"/Inventory/Requests"},
        };

        _v.Page.Back = $"/Inventory/Index";
        _v.Page.Title = "طلب مخزني";

        _v.InventoryRequest = _db.InventoryRequest.Where(r => r.Id == Id).FirstOrDefault();

        if (_v.InventoryRequest == null)
            return StatusCode(404);

        _v.RequestItems = _db.RequestItem.Where(r => r.RelType == "InventoryRequest" && r.RelId == Id).ToList();
        _v.TDeptCodes = _db.TDeptCode.ToList();

        ViewBag.List = JsonConvert.SerializeObject(_v.RequestItems);



        return View(_v);

    }


    [HttpPost("Update/Note/{Id}/{ItemId}")]
    [Can("inventory-department")]
    public async Task<IActionResult> RequestUpdateNote(int Id, int ItemId, [FromForm] IFormCollection post)
    {

        var request = _db.InventoryRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        var requestItem = _db.RequestItem.Where(r => r.Id == ItemId && r.RelId == Id && r.RelType == "InventoryRequest").FirstOrDefault();

        if (requestItem == null)
            return StatusCode(404);



        if (!post.ContainsKey("InventoryNote"))
            return Json(new
            {
                success = false,
                message = new List<string> { "يجب ادخال الملاحظة" },
                action = "",
            });


        requestItem.InventoryNote = post["InventoryNote"];

        _db.Update(requestItem);
        _db.SaveChanges();


        return Json(new
        {
            success = true,
            message = new List<string> { "تم تحديث الطلب" },
            action = "",
        });

    }

    [HttpPost("Update/{Id}/{ItemId}")]
    [Can("inventory-department")]
    public async Task<IActionResult> RequestUpdate(int Id, int ItemId, [FromForm] IFormCollection post)
    {

        var request = _db.InventoryRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        var requestItem = _db.RequestItem.Where(r => r.Id == ItemId && r.RelId == Id && r.RelType == "InventoryRequest").FirstOrDefault();

        if (requestItem == null)
            return StatusCode(404);



        if (!post.ContainsKey("Action"))
            return Json(new
            {
                success = false,
                message = new List<string> { "يجب اختيار اجراء" },
                action = "",
            });

        if (post["Action"] == "request_technical_note" && !post.ContainsKey("DeptCode"))
            return Json(new
            {
                success = false,
                message = new List<string> { "يجب اختيار القسم المعني" },
                action = "",
            });

        if (post["Action"] == "deliver" && !post.ContainsKey("DeliveredQty"))
            return Json(new
            {
                success = false,
                message = new List<string> { "يجب ادخال الكمية المسلمة" },
                action = "",
            });


        if (post["Action"] == "deliver")
        {

            if (requestItem.DeliveryStat == 1)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "تم تسليم الطلب بالفعل" },
                    action = "",
                });
            }

            if (requestItem.Quantity < int.Parse(post["DeliveredQty"]))
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "الكمية المسلمة اكبر من الطلب" },
                    action = "",
                });
            }

            requestItem.DeliveredQty = int.Parse(post["DeliveredQty"]);
            requestItem.DeliveryStat = 1;

            _db.Update(requestItem);
            _db.SaveChanges();

            if (requestItem.ItemId != 0)
            {
                var item = _db.InventoryItem.Find(requestItem.ItemId);

                if (item != null)
                {

                    var change = -requestItem.DeliveredQty;

                    item.Quantity = item.Quantity - requestItem.DeliveredQty;

                    _db.Update(item);
                    _db.SaveChanges();

                    var Log = new InventoryLog
                    {
                        ItemId = item.Id,
                        Quantity = change,
                        Description = "تسليم طلب مخزني رقم:" + request.Id,
                        CreatedBy = _v.Profile.EmpNo.Value
                    };

                    _db.InventoryLog.Add(Log);
                    _db.SaveChanges();
                }
            }
        }

        if (post["Action"] == "cancel_deliver")
        {

            if (requestItem.DeliveryStat == 0)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "لم يتم تسليم هذا الطلب" },
                    action = "",
                });
            }

            if (requestItem.ItemId != 0)
            {
                var item = _db.InventoryItem.Find(requestItem.ItemId);

                if (item != null)
                {
                    item.Quantity = item.Quantity + requestItem.DeliveredQty;

                    _db.Update(item);
                    _db.SaveChanges();

                    var Log = new InventoryLog
                    {
                        ItemId = item.Id,
                        Quantity = requestItem.DeliveredQty,
                        Description = "ارجاع طلب مخزني رقم:" + request.Id,
                        CreatedBy = _v.Profile.EmpNo.Value
                    };
                }
            }

            requestItem.DeliveredQty = 0;
            requestItem.DeliveryStat = 0;

            _db.Update(requestItem);
            _db.SaveChanges();
        }

        if (post["Action"] == "request_technical_note")
        {
            if (requestItem.TechnicalStat == 1 || requestItem.TechnicalRequired == 1)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "تم طلب ملاحظة فنية بالفعل" },
                    action = "",
                });
            }

            requestItem.TechnicalRequired = 1;
            requestItem.TechnicalDepartmentCode = int.Parse(post["DeptCode"]);

            _db.Update(requestItem);
            _db.SaveChanges();
        }

        if (post["Action"] == "cancel_technical_note")
        {
            if (requestItem.TechnicalStat == 1)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "تم طلب ملاحظة فنية بالفعل" },
                    action = "",
                });
            }

            requestItem.TechnicalRequired = 0;
            requestItem.TechnicalDepartmentCode = 0;

            _db.Update(requestItem);
            _db.SaveChanges();
        }


        if (post["Action"] == "request_dg_approval")
        {
            if (requestItem.DgApprovalStat == 1 || requestItem.DgApprovalRequired == 1)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "تم طلب اعتماد المدير العام بالفعل" },
                    action = "",
                });
            }

            requestItem.DgApprovalRequired = 1;

            _db.Update(requestItem);
            _db.SaveChanges();
        }

        if (post["Action"] == "cancel_dg_approval")
        {
            if (requestItem.DgApprovalStat == 1)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "تم طلب اعتماد المدير العام بالفعل" },
                    action = "",
                });
            }

            requestItem.DgApprovalRequired = 0;

            _db.Update(requestItem);
            _db.SaveChanges();
        }

        if (post["Action"] == "request_purchases")
        {
            if (requestItem.PurchasesStat == 1)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "تم طلب مشتريات بالفعل" },
                    action = "",
                });
            }

            requestItem.PurchasesStat = 1;

            _db.Update(requestItem);
            _db.SaveChanges();
        }

        if (post["Action"] == "cancel_request_purchases")
        {
            if (requestItem.FinanceManagerStat == 1)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "تم قبول الطلب بالفعل" },
                    action = "",
                });
            }

            requestItem.PurchasesStat = 0;

            _db.Update(requestItem);
            _db.SaveChanges();
        }



        return Json(new
        {
            success = true,
            message = new List<string> { "تم تحديث الطلب" },
            action = "reload",
        });
    }



    [Route("Technical")]
    [Can("inventory-technicalnote")]
    public IActionResult TechnicalNote()
    {

        _v.Page.Reload = true;


        _v.Page.Title = "Technical note";

        _v.Page.Active = "";


        if (Can("inventory-admin"))
        {
            _v.RequestItems = _db.RequestItem.Where(r => r.RelType == "InventoryRequest" && r.TechnicalRequired == 1).ToList();
        }
        else
        {
            _v.RequestItems = _db.RequestItem
                .Where(r =>
                r.RelType == "InventoryRequest"
                && r.TechnicalRequired == 1
                && r.TechnicalDepartmentCode == _v.Profile.DeptCode
                ).ToList();
        }

        return View(_v);

    }

    [HttpGet("Technical/Update/{Id}")]
    [Can("inventory-technicalnote")]
    public IActionResult TechnicalUpdate(int Id)
    {

        _v.Page.Reload = true;

        _v.Page.Title = "ملاحظة فنية";
        //_v.Page.Layout = "";

        if (Can("inventory-admin"))
        {
            _v.RequestItem = _db.RequestItem
            .Where(r =>
                r.RelType == "InventoryRequest"
                && r.Id == Id
                && (r.TechnicalRequired == 1 || r.TechnicalStat == 1)
                )
            .FirstOrDefault();
        }
        else
        {
            _v.RequestItem = _db.RequestItem
            .Where(r =>
                r.RelType == "InventoryRequest"
                && r.Id == Id
                && (r.TechnicalRequired == 1 || r.TechnicalStat == 1)
                && r.TechnicalDepartmentCode == _v.Profile.DeptCode
                )
            .FirstOrDefault();
        }

        if (_v.RequestItem == null)
            return StatusCode(404);


        _v.InventoryRequest = _db.InventoryRequest
            .Where(r => r.CancelFlag == 0 && r.Id == _v.RequestItem.RelId)
            .FirstOrDefault();

        if (_v.InventoryRequest == null)
            return StatusCode(404);


        return View(_v);
    }

    [HttpPost("Technical/Update/{Id}")]
    [Can("inventory-technicalnote")]
    public IActionResult TechnicalUpdate(int Id, [FromForm] IFormCollection post)
    {

        if (Can("inventory-admin"))
        {
            _v.RequestItem = _db.RequestItem
            .Where(r =>
                r.RelType == "InventoryRequest"
                && r.Id == Id
                && r.TechnicalRequired == 1
                )
            .FirstOrDefault();
        }
        else
        {
            _v.RequestItem = _db.RequestItem
            .Where(r =>
                r.RelType == "InventoryRequest"
                && r.Id == Id
                && r.TechnicalRequired == 1
                && r.TechnicalDepartmentCode == _v.Profile.DeptCode
                )
            .FirstOrDefault();
        }

        var item = _v.RequestItem;

        if (item == null)
            return StatusCode(404);


        item.TechnicalStat = 1;
        item.TechnicalNoteDate = DateTime.Now;
        item.TechnicalNoteBy = _v.Profile.EmpNo.Value;
        item.TechnicalNote = post["Note"];
        item.TechnicalRequired = 0;


        _db.Update(item);
        _db.SaveChanges();


        return Json(new
        {
            success = true,
            action = "reload",
            message = new List<string> { }
        });


    }


    [HttpGet("Dg")]
    [Can("inventory-dgeneral")]
    public IActionResult DgRequests()
    {

        _v.Page.Reload = true;

        _v.Page.Title = "Inventory request";


        _v.RequestItems = _db.RequestItem
                .Where(r =>

                (r.DgApprovalRequired == 1 || r.DgApprovalStat == 2 || r.DgApprovalStat == 1) && r.RelType == "InventoryRequest"
                ).ToList();

        return View(_v);

    }

    [HttpGet("Dg/Update/{Id}")]
    [Can("inventory-dgeneral")]
    public IActionResult DgUpdate(int Id)
    {


        _v.Page.Reload = true;
        _v.Page.Back = "/Inventory/Dg";

        _v.Page.Title = "Inventory request";
        //_v.Page.Layout = "_Popup";

        _v.RequestItem = _db.RequestItem
        .Where(r =>
            r.RelType == "InventoryRequest"
            && r.Id == Id
            && r.DgApprovalRequired == 1
            )
        .FirstOrDefault();

        if (_v.RequestItem == null)
            return StatusCode(404);

        _v.InventoryRequest = _db.InventoryRequest
            .Where(r => r.CancelFlag == 0 && r.Id == _v.RequestItem.RelId)
            .FirstOrDefault();

        if (_v.InventoryRequest == null)
            return StatusCode(404);

        return View(_v);

    }

    [HttpGet("Dg/Approve/{Id}")]
    [Can("inventory-dgeneral")]
    public IActionResult DgApprove(int Id)
    {

        var ritem = _db.RequestItem
            .Where(r => r.DgApprovalRequired == 1 && r.Id == Id && r.RelType == "InventoryRequest")
            .FirstOrDefault();

        if (ritem == null)
            return StatusCode(404);

        ritem.DgApprovalStat = 1;
        ritem.DgApprovalRequired = 0;
        ritem.DgNo = _v.Profile.EmpNo.Value;
        ritem.DgActionAt = DateTime.Now;

        _db.Update(ritem);
        _db.SaveChanges();

        return Redirect("/Inventory/Dg");
    }

    [HttpPost("Dg/Decline/{Id}")]
    [Can("inventory-dgeneral")]
    public IActionResult DgDecline(int Id, [FromForm] IFormCollection post)
    {

        var item = _db.RequestItem
            .Where(r => r.DgApprovalRequired == 1 && r.Id == Id && r.RelType == "InventoryRequest")
            .FirstOrDefault();

        if (item == null)
            return StatusCode(404);

        item.DgApprovalStat = 2;
        item.DgApprovalRequired = 0;
        item.DgNo = _v.Profile.EmpNo.Value;
        item.DgActionAt = DateTime.Now;
        item.DgNote = post["Note"];

        _db.Update(item);
        _db.SaveChanges();

        return Json(new
        {
            success = false,
            message = new List<string> { _("Request declined successfully") },
            action = "location.href='/Inventory/Dg/?success=Action completed'",
        });
    }

    [HttpPost("Update2/{Id}")]
    [Can("inventory-department")]
    public async Task<IActionResult> RequestUpdate2(int Id, InventoryRequestForm post)
    {

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var request = _db.InventoryRequest
            .Where(r => r.CancelFlag == 0 && r.Id == Id)
            .FirstOrDefault();

        if (request == null)
            return StatusCode(404);


        request.InventoryNote = post.InventoryRequest.InventoryNote;


        _db.Update(request);
        await _db.SaveChangesAsync();


        foreach (var item in post.RequestItems)
        {

            var ritem = _db.RequestItem.Where(i => i.Id == item.Id && i.RelId == request.Id && i.RelType == "InventoryRequest").FirstOrDefault();

            if (ritem != null)
            {

                if (ritem.DeliveryStat == 0)
                {

                    if (ritem.Quantity < item.DeliveredQty)
                        item.DeliveredQty = ritem.Quantity;

                    ritem.DeliveredQty = item.DeliveredQty;
                    ritem.InventoryNote = item.InventoryNote;

                    _db.Update(ritem);
                    await _db.SaveChangesAsync();

                }
            }
        }

        return Json(new
        {
            success = true,
            message = new List<string> { "Request updated successfully." },
            action = "",
        });

    }


    [Route("Categories")]
    public IActionResult Categories()
    {

        if (!Can("inventory-department"))
            return StatusCode(403);

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Inventory", Url=$"/Inventory/Index"},
            new Breadcrumb {Label="Categories", Url=$"/Inventory/Index"},
        };

        _v.Page.Title = "Categories";

        _v.InventoryCategories = _db.InventoryCategory.ToList();

        return View(_v);
    }

    [Route("CreateCategory")]
    [HttpPost]
    public IActionResult CreateCategory(InventoryCategory post)
    {

        if (!Can("inventory-department|inventory-admin"))
            return StatusCode(403);


        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var NewCategory = new InventoryCategory
        {
            Name = post.Name,
            Description = post.Description,
            Rights = post.Rights,
            IsAsset= post.IsAsset
        };

        _db.InventoryCategory.Add(NewCategory);
        _db.SaveChanges();

        return Json(new
        {
            success = true,
            message = new List<string> { _("Category created successfully") },
            action = "reload",
        });

    }

    [Route("UpdateCategory/{cId}")]
    [HttpPost]
    public IActionResult UpdateCategory(string cId, [FromForm] InventoryCategory post)
    {

        int Id = _v.DcInt(cId);

        if (!Can("inventory-department|inventory-admin"))
            return StatusCode(403);


        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };


            return Json(response);
        }

        var CategoryToUpdate = _db.InventoryCategory.Find(Id);

        if (CategoryToUpdate == null)
            return StatusCode(404);

        CategoryToUpdate.Name = post.Name;
        CategoryToUpdate.Rights = post.Rights;
        CategoryToUpdate.IsAsset = post.IsAsset;

        _db.Update(CategoryToUpdate);
        _db.SaveChanges();

        return Json(new
        {
            success = true,
            message = new List<string> { _("Category updated successfully") },
            action = "reload",
        });
    }

    

    [Route("DeleteCategory/{cId}")]
    public IActionResult DeleteCategory(string cId)
    {

        return View(_v);
    }

    
   

    [Route("Items")]
    public IActionResult Items()
    {

        if (!Can("inventory-department"))
            return StatusCode(403);

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Inventory", Url=$"/Inventory/Index"},
            new Breadcrumb {Label="Items", Url=$"/Inventory/Index"},
        };

        _v.Page.Title = "Items";

        _v.InventoryItems = _db.InventoryItem.Include(i => i.Category).Where(c => c.Deleted01 == 0).ToList();
        _v.InventoryCategories = _db.InventoryCategory.ToList();

        return View(_v);
    }

    [Route("Item/{Id}")]
    public IActionResult Item(int Id)
    {

        if (!Can("inventory-department"))
            return StatusCode(403);

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Inventory", Url=$"/Inventory/Index"},
            new Breadcrumb {Label="Items", Url=$"/Inventory/items"},
            new Breadcrumb {Label="View", Url=$"/Inventory/Index"},
        };

        _v.Page.Title = "Items";
        _v.Page.Back = $"/Inventory/items";

        _v.InventoryItem = _db.InventoryItem.Find(Id);

        if (_v.InventoryItem == null)
            return StatusCode(403);

        _v.InventoryCategories = _db.InventoryCategory.ToList();
        _v.InventoryLogs = _db.InventoryLog.Where(c => c.ItemId == Id).ToList();

        return View(_v);

    }

    [Route("UpdateItem/{Id}")]
    public async Task<IActionResult> UpdateItem(int Id, [FromForm] InventoryItem post)
    {

        if (!Can("inventory-department|inventory-admin"))
            return StatusCode(403);

        List<string> errors = new List<string>();


        if (!IsValid(ModelState))
        {
            errors = ValidateErrors(ModelState);
        }


        var VCategory = _db.InventoryCategory.Find(post.CategoryId);

        if (VCategory == null)
            errors.Add(_("Category not exists"));


        var VItem = _db.InventoryItem.Where(i => i.Code == post.Code && i.Id != Id).FirstOrDefaultAsync();

        if (VItem == null)
            errors.Add(_("Item code already exists"));


        if (errors.Count() > 0)
        {
            return Json(new
            {
                success = false,
                message = errors,
                action = "",
            });
        }

        var ItemToUpdate = _db.InventoryItem.Find(Id);

        string FileGuid = ItemToUpdate.FileGuid;

        if (post.File != null && post.File.Length > 0)
        {
            FileGuid = await _h.UploadAsync(post.File);
        }


        if (ItemToUpdate == null)
            return StatusCode(404);

        ItemToUpdate.Name = post.Name;
        ItemToUpdate.Code = post.Code;
        ItemToUpdate.Description = post.Description;
        ItemToUpdate.CategoryId = post.CategoryId;
        ItemToUpdate.QuantityAlert = post.QuantityAlert;
        ItemToUpdate.Location = post.Location;
        ItemToUpdate.Unit = post.Unit;
        ItemToUpdate.FileGuid = FileGuid;
        ItemToUpdate.UpdatedBy = _v.Profile.EmpNo.Value;
        ItemToUpdate.UpdatedAt = DateTime.Now;

        _db.Update(ItemToUpdate);
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
        _db.SaveChangesAsync();
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed

        return Json(new
        {
            success = true,
            message = new List<string> { _("Item updated successfully") },
            action = "reload",
        });

    }

    [Route("CreateItem")]
    [HttpPost]
    public async Task<IActionResult> CreateItem(InventoryItem post)
    {

        if (!Can("inventory-department|inventory-admin"))
            return StatusCode(403);

        List<string> errors = new List<string>();


        if (!IsValid(ModelState))
        {
            errors = ValidateErrors(ModelState);
        }


        var VCategory = _db.InventoryCategory.Find(post.CategoryId);

        if (VCategory == null)
            errors.Add(_("Category not exists"));


        var VItem = _db.InventoryItem.Where(i => i.Code == post.Code).FirstOrDefaultAsync();

        if (VItem == null)
            errors.Add(_("Item code already exists"));


        string FileGuid = null;

        if (post.File != null && post.File.Length > 0)
        {
            FileGuid = await _h.UploadAsync(post.File);
        }


        if (errors.Count() > 0)
        {
            return Json(new
            {
                success = false,
                message = errors,
                action = "",
            });
        }

        var NewItem = new InventoryItem
        {
            Name = post.Name,
            Code = post.Code,
            Description = post.Description,
            CategoryId = post.CategoryId,
            Quantity = post.Quantity,
            QuantityAlert = post.QuantityAlert,
            Location = post.Location,
            Unit = post.Unit,
            FileGuid = FileGuid,
            CreatedBy = _v.Profile.EmpNo.Value
        };

        _db.InventoryItem.Add(NewItem);
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
        _db.SaveChangesAsync();
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed


        return Json(new
        {
            success = true,
            message = new List<string> { _("Item created successfully") },
            action = "reload",
        });
    }



    [Route("DeleteCategory/{cId}")]
    public IActionResult DeleteItem(string cId)
    {



        return View(_v);
    }



    [Route("Stocking/{Id}")]
    public IActionResult Stocking(int Id, [FromForm] IFormCollection post)
    {

        var Item = _db.InventoryItem.Find(Id);

        if (Item.Quantity != int.Parse(post["Quantity"]))
        {

            double change = double.Parse(post["Quantity"]) - Item.Quantity;

            Item.Quantity = double.Parse(post["Quantity"]);


            _db.Update(Item);
            _db.SaveChanges();

            var Log = new InventoryLog
            {
                ItemId = Item.Id,
                Quantity = change,
                Description = _("Stocking"),
                CreatedBy = _v.Profile.EmpNo.Value
            };

            _db.InventoryLog.Add(Log);
            _db.SaveChanges();

        }

        return Json(new
        {
            success = true,
            message = new List<string> { _("Quantity updated successfully") },
            action = "reload",
        });



    }







    [Route("Manager")]
    public IActionResult ManagerRequests()
    {

        if (!Can("department-manager"))
            return StatusCode(403);


        _v.Page.Reload = true;


        _v.Page.Title = "Inventory request";



        if (Can("inventory-admin"))
        {
            _v.InventoryRequests = _db.InventoryRequest.Where(r => r.CancelFlag == 0).ToList();
        }
        else
        {
            _v.InventoryRequests = _db.InventoryRequest
            .Where(r =>
                r.ManagerNo == _v.Profile.EmpNo
                && r.CancelFlag == 0
             ).ToList();
        }

        return View(_v);

    }


    [Route("Manager/Update/{Id}")]
    [Can("department-manager")]
    public IActionResult ManagerUpdateRequest(int Id)
    {
        if (!Can("department-manager"))
            return StatusCode(403);

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Inventory requests", Url=$"/Inventory/Manager"},
            new Breadcrumb {Label="View", Url=$"/Inventory/Manager"},
        };

        _v.Page.Back = $"/Inventory/Manager";
        _v.Page.Title = "Inventory request";
        _v.Page.Layout = "_Popup";

        _v.InventoryRequest = _db.InventoryRequest.Where(r => r.ManagerNo == _v.Profile.EmpNo && r.CancelFlag == 0 && r.Id == Id).FirstOrDefault();

        if (Can("inventory-admin"))
        {
            _v.InventoryRequest = _db.InventoryRequest.Where(r => r.CancelFlag == 0 && r.Id == Id).FirstOrDefault();
        }

        if (_v.InventoryRequest == null)
            return StatusCode(404);

        _v.RequestItems = _db.RequestItem.Where(r => r.RelType == "InventoryRequest" && r.RelId == Id).ToList();

        return View(_v);

    }

    [Route("Manager/Approve/{Id}")]
    [Can("department-manager")]
    public IActionResult ManagerApprove(int Id)
    {

        var request = _db.InventoryRequest
            .Where(r => r.ManagerNo == _v.Profile.EmpNo && r.CancelFlag == 0 && r.Id == Id && r.ReqStat == 1)
            .FirstOrDefault();

        if (Can("inventory-admin"))
        {
            request = _db.InventoryRequest
            .Where(r => r.CancelFlag == 0 && r.Id == Id && r.ReqStat == 1)
            .FirstOrDefault();
        }

        if (request == null)
            return StatusCode(404);


        request.ReqStat = 20;
        request.ManagerApproveBy = _v.Profile.EmpNo.Value;

        _db.Update(request);
        _db.SaveChanges();

        return Content("<script>window.opener.location.href='/Inventory/Manager/?success=Action completed';window.close()</script>", "text/html");

    }

    [HttpPost("Manager/Decline/{Id}")]
    [Can("department-manager")]
    public IActionResult ManagerDecline(int Id, [FromForm] IFormCollection post)
    {


        var request = _db.InventoryRequest
            .Where(r => r.ManagerNo == _v.Profile.EmpNo && r.CancelFlag == 0 && r.Id == Id && r.ReqStat == 1)
            .FirstOrDefault();

        if (Can("inventory-admin"))
        {
            request = _db.InventoryRequest
            .Where(r => r.CancelFlag == 0 && r.Id == Id && r.ReqStat == 1)
            .FirstOrDefault();
        }

        if (request == null)
            return StatusCode(404);


        request.ReqStat = 21;
        request.DeclineNote = post["Note"];

        _db.Update(request);
        _db.SaveChanges();

        return Json(new
        {
            success = false,
            message = new List<string> { _("Request updated successfully") },
            action = "reload",
        });

    }


    [Route("My")]
    public IActionResult MyRequests()
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Inventory request", Url=$"/Inventory/My"},
        };

        _v.Page.Title = "Inventory request";


        _v.InventoryRequests = _db.InventoryRequest.Where(r => r.EmpNo == _v.Profile.EmpNo && r.CancelFlag == 0).ToList();

        return View(_v);

    }

    [HttpGet("My/Create")]
    public IActionResult MyCreateRequest()
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="طلب مخزني", Url=$"/Inventory/My"},
            new Breadcrumb {Label="طلب مخزني جديد", Url=$"/Inventory/My"},
        };

        _v.Page.Back = $"/Inventory/My";

        _v.Page.Title = "طلب مخزني جديد";

        _v.InventoryRequests = _db.InventoryRequest.Where(r => r.EmpNo == _v.Profile.EmpNo && r.CancelFlag == 0).ToList();

        var List = _db.InventoryItem
        .Include(i => i.Category)
        .ToList()
        .OrderBy(i => i.CategoryId)
        .Select(item => new InventoryItem
        {
            Id = item.Id,
            Name = item.Name,
            Code = item.Code,
            Quantity = item.Quantity,
            FileGuid = item.FileGuid,
            CategoryName = item.Category.Name,
            Description = item.Description,
            Unit = item.Unit,
            Image = "",
        }).ToList();

        foreach (var item in List)
        {
            if (item.FileGuid != null)
            {
                item.Image = _h.GetFile(item.FileGuid);
            }
        }

        var categories = _db.InventoryCategory.Include(i => i.Items).ToList()
            .OrderBy(i => i.Id)
            .Select(item => new InventoryCategory
            {
                Id = item.Id,
                Name = item.Name,
                _Items = new List<InventoryItem> { },
            }).ToList();

        foreach (var category in categories)
        {
            category._Items = _db.InventoryItem
                .Where(r => r.CategoryId == category.Id)
                .ToList()
                .Select(item => new InventoryItem
                {
                    Id = item.Id,
                    Name = item.Name,
                    Code = item.Code,
                    Quantity = item.Quantity,
                    FileGuid = item.FileGuid,
                    CategoryName = item.Category.Name,
                    Description = item.Description,
                    Unit = item.Unit,
                    Image = "",
                }).ToList();

            foreach (var item in category._Items)
            {
                if (item.FileGuid != null)
                {
                    item.Image = _h.GetFile(item.FileGuid);
                }
            }
        }

        _v.InventoryCategories = categories;

        ViewBag.ItemList = List;

        return View(_v);

    }

    [HttpPost("My/Create")]
    public async Task<IActionResult> MyCreateRequest(InventoryRequestForm post)
    {

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var Managers = _h.Managers(_v.Profile.EmpNo);

        bool ValidManager = Managers.Any(item => item.EmpNo == post.InventoryRequest.ManagerNo);

        if (!ValidManager)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Invalid department manager") },
                action = "",
            });
        }


        var NewRequest = new InventoryRequest
        {
            Note = post.InventoryRequest.Note,
            ManagerNo = post.InventoryRequest.ManagerNo,
            EmpNo = _v.Profile.EmpNo.Value,
            ReqStat = 1,
            CreatedBy = _v.Profile.EmpNo.Value
        };

        _db.InventoryRequest.Add(NewRequest);
        await _db.SaveChangesAsync();


        foreach (var item in post.RequestItems)
        {

            if (item.ItemId == 0)
            {
                var NewItem = new RequestItem
                {
                    Name = item.Name,
                    Quantity = item.Quantity,
                    Note = item.Note,
                    RelId = NewRequest.Id,
                    RelType = "InventoryRequest",
                    CreatedBy = Auth().EmpNo.Value,
                };

                _db.RequestItem.Add(NewItem);
                await _db.SaveChangesAsync();
            }
            else
            {
                var itemData = _db.InventoryItem.Where(i => i.Id == item.ItemId && i.Deleted01 == 0).FirstOrDefault();

                if (itemData != null)
                {
                    var NewItem = new RequestItem
                    {
                        ItemId = itemData.Id,
                        Name = itemData.Name,
                        Quantity = item.Quantity,
                        Note = itemData.Description,
                        RelId = NewRequest.Id,
                        RelType = "InventoryRequest",
                        CreatedBy = Auth().EmpNo.Value,
                    };

                    _db.RequestItem.Add(NewItem);
                    await _db.SaveChangesAsync();
                }
            }
        }

        return Json(new
        {
            success = true,
            message = new List<string> { "تم انشاء طلب مخزني" },
            action = $"location.replace('/Inventory/My/Update/{NewRequest.Id}')",
        });

    }

    [HttpGet("My/Update/{Id}")]
    public IActionResult MyUpdateRequest(int Id)
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Inventory request", Url=$"/Inventory/My"},
            new Breadcrumb {Label="View", Url=$"/Inventory/My"},
        };

        _v.Page.Back = $"/Inventory/My";
        _v.Page.Title = "Inventory request";

        _v.InventoryRequest = _db.InventoryRequest.Where(r => r.EmpNo == _v.Profile.EmpNo && r.CancelFlag == 0 && r.Id == Id).FirstOrDefault();

        if (_v.InventoryRequest == null)
            return StatusCode(404);

        _v.RequestItems = _db.RequestItem.Where(r => r.RelType == "InventoryRequest" && r.RelId == Id).ToList();

        ViewBag.List = JsonConvert.SerializeObject(_v.RequestItems);

        return View(_v);

    }

    [HttpPost("My/Update/{Id}")]
    public async Task<IActionResult> MyUpdateRequest(int Id, InventoryRequestForm post)
    {

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var request = _db.InventoryRequest
            .Where(r => r.EmpNo == _v.Profile.EmpNo && r.CancelFlag == 0 && r.Id == post.InventoryRequest.Id && r.ReqStat == 1)
            .FirstOrDefault();

        if (request == null)
            return StatusCode(404);

        var Managers = _h.Managers(_v.Profile.EmpNo);

        bool ValidManager = Managers.Any(item => item.EmpNo == post.InventoryRequest.ManagerNo);

        if (!ValidManager)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Invalid department manager") },
                action = "",
            });
        }

        request.Note = post.InventoryRequest.Note;
        request.ManagerNo = post.InventoryRequest.ManagerNo;



        _db.Update(request);
        await _db.SaveChangesAsync();

        var items = _db.RequestItem.Where(r => r.RelType == "InventoryRequest" && r.RelId == request.Id).ToList();


        _db.RequestItem.RemoveRange(items);
        await _db.SaveChangesAsync();


        foreach (var item in post.RequestItems)
        {

            if (item.ItemId == 0)
            {
                var NewItem = new RequestItem
                {
                    Name = item.Name,
                    Quantity = item.Quantity,
                    Note = item.Note,
                    RelId = request.Id,
                    RelType = "InventoryRequest",
                    CreatedBy = Auth().EmpNo.Value,
                };

                _db.RequestItem.Add(NewItem);
                await _db.SaveChangesAsync();
            }
            else
            {
                var itemDate = _db.InventoryItem.Where(i => i.Id == item.ItemId && i.Deleted01 == 0).FirstOrDefault();

                if (itemDate != null)
                {

                    var NewItem = new RequestItem
                    {
                        ItemId = itemDate.Id,
                        Name = itemDate.Name,
                        Quantity = item.Quantity,
                        Note = itemDate.Description,
                        RelId = request.Id,
                        RelType = "InventoryRequest",
                        CreatedBy = Auth().EmpNo.Value,
                    };

                    _db.RequestItem.Add(NewItem);
                    await _db.SaveChangesAsync();
                }
            }
        }

        return Json(new
        {
            success = true,
            message = new List<string> { "Request updated successfully." },
            action = "",
        });

    }


    [Route("My/Delete/{Id}")]
    public IActionResult DeleteMyRequest(int Id)
    {
        var request = _db.InventoryRequest
            .Where(r => r.EmpNo == _v.Profile.EmpNo && r.CancelFlag == 0 && r.Id == Id && r.ReqStat == 1)
            .FirstOrDefault();

        if (request == null)
            return StatusCode(404);

        request.CancelFlag = 1;

        _db.Update(request);
        _db.SaveChanges();

        return Redirect($"~/Inventory/My/?success=Action completed");
    }


    [Route("Status/{Id}")]
    [HttpPost]
    public IActionResult RequestStatus(int Id, [FromForm] IFormCollection post)
    {
        var request = _db.InventoryRequest
            .Where(r => r.CancelFlag == 0 && r.Id == Id)
            .FirstOrDefault();

        if (request == null)
            return StatusCode(404);

        request.Status = post["Status"];


        _db.Update(request);
        _db.SaveChanges();

        return Json(new
        {
            success = true,
            message = new List<string> { _v._("Status updated") },
            action = ""
        });
    }

    [Route("Completed/{Id}")]
    [HttpPost]
    public IActionResult RequestCompleted(int Id)
    {
        var request = _db.InventoryRequest
            .Where(r => r.CancelFlag == 0 && r.Id == Id)
            .FirstOrDefault();

        if (request == null)
            return StatusCode(404);

        request.Status = "Completed";

        _db.Update(request);
        _db.SaveChanges();

        return Json(new
        {
            success = true,
            action = "reload"
        });
    }


    [Route("Supply")]
    public IActionResult InventorySupplies()
    {

        if (!Can("department-manager"))
            return StatusCode(403);


        _v.Page.Reload = true;


        _v.Page.Title = "Inventory request";


        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }
        }

        if (!string.IsNullOrEmpty(toQueryString))
        {
            if (DateTime.TryParse(toQueryString, out DateTime to))
            {
                to = to.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }
        }



        return Json(new
        {
            success = true,
            action = "reload"
        });
    }



    [Route("ItemsDropdown/{search}")]
    public IActionResult ItemsDropdown(string search)
    {

        var List = _db.InventoryItem
            .Include(i => i.Category)
            .Where(i => i.Name.Contains(search) || i.Code.Contains(search) || i.Description.Contains(search) || i.Category.Name.Contains(search))
            .ToList()
            .Select(item => new InventoryItem
            {
                Id = item.Id,
                Name = item.Name,
                Code = item.Code,
                Quantity = item.Quantity,
                FileGuid = item.FileGuid,
                CategoryName = item.Category.Name,
                Description = item.Description,
                Unit = item.Unit,
                Image = "",
            }).ToList();

        foreach (var item in List)
        {
            if (item.FileGuid != null)
            {
                item.Image = _h.GetFile(item.FileGuid);
            }
        }

        return Json(new
        {
            data = List
        });

    }

    [Route("GetNextCode")]
    public string GetNextCode()
    {

        var Max = _db.InventoryItem.OrderByDescending(i => i.Id).FirstOrDefault();

        if (Max == null)
            return "00001";

        return (Max.Id + 1).ToString("D5");

    }
}
