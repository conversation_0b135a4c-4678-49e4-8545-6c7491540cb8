using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Modules.Shared.Models.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Inventory.Providers
{
    /// <summary>
    /// Task provider for the Inventory module
    /// </summary>
    public class InventoryTaskProvider : ITaskProvider
    {
        private readonly hrmsContext _context;
        private readonly AppHelper _appHelper;

        public string ModuleName => "Inventory";

        public InventoryTaskProvider(hrmsContext context, AppHelper appHelper)
        {
            _context = context;
            _appHelper = appHelper;
        }

        public async Task<List<UserTask>> GetTasksAsync()
        {
            var tasks = new List<UserTask>();

            try
            {
                // Get current user info
                var currentUser = _appHelper.Auth();
                if (currentUser?.EmpNo == null)
                {
                    return tasks;
                }

                // Technical notes tasks
                if (_appHelper.Can("inventory-admin|inventory-technicalnote"))
                {
                    var techNotes = await _context.RequestItem
                        .Where(r => r.RelType == "InventoryRequest"
                                 && r.TechnicalRequired == 1
                                 && r.TechnicalDepartmentCode == currentUser.DeptCode)
                        .ToListAsync();

                    if (_appHelper.Can("inventory-admin"))
                    {
                        techNotes = await _context.RequestItem
                            .Where(r => r.RelType == "InventoryRequest"
                                     && r.TechnicalRequired == 1)
                            .ToListAsync();
                    }

                    foreach (var item in techNotes)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "قسم المخازن",
                            Text = $"طلب ملاحظة فنية ({item.Name})",
                            Url = $"/Inventory/Technical/Update/{item.Id}",
                        });
                    }
                }

                // Director General approval tasks
                if (_appHelper.Can("inventory-admin|inventory-dgeneral"))
                {
                    var dgApprovalItems = await _context.RequestItem
                        .Where(r => r.RelType == "InventoryRequest"
                                 && r.DgApprovalRequired == 1)
                        .ToListAsync();

                    foreach (var item in dgApprovalItems)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "قسم المخازن",
                            Text = $"طلب اعتماد ({item.Name})",
                            Url = $"/Inventory/Dg/Update/{item.Id}",
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break task loading
                Console.WriteLine($"Error loading inventory tasks: {ex.Message}");
            }

            return tasks;
        }
    }
} 