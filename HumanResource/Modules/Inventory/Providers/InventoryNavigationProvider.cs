using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Inventory.Providers
{
    /// <summary>
    /// Navigation provider for the Inventory module
    /// </summary>
    public class InventoryNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Inventory";
        public int Priority => 40;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Inventory Group navigation items
            var inventoryNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "Inventory",
                    Label = "المخازن",
                    Active = "inventory",
                    Icon = "<i class=\"fas fa-warehouse\"></i>",
                    Rights = new List<Right> { Right.InventoryAdmin, Right.InventoryDepartment },
                    Priority = 10,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "Requests",
                            Label = "طلبات المخازن",
                            Rights = new List<Right> { Right.InventoryAdmin, Right.InventoryDepartment },
                            Url = "/Inventory/Index"
                        },
                        new NavLink
                        {
                            Name = "Items",
                            Label = "المواد",
                            Rights = new List<Right> { Right.InventoryAdmin },
                            Url = "/Inventory/Items"
                        },
                        new NavLink
                        {
                            Name = "Categories",
                            Label = "التصنيفات",
                            Rights = new List<Right> { Right.InventoryAdmin },
                            Url = "/Inventory/Categories"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Inventory] = inventoryNavItems;

            return navigation;
        }
    }
} 