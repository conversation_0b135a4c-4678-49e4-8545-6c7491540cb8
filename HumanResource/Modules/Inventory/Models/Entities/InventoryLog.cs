﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Inventory.Models.Entities;

[Table("INVENTORY_LOG")]
public class InventoryLog
{
    [Key]
    public int Id { get; set; }

    public int ItemId { get; set; } = 0;
    public string Description { get; set; } = "";

    public double Quantity { get; set; } = 0;

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public int CreatedBy { get; set; } = 0;
}

