﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Inventory.Models.Entities;

[Table("INVENTORY_REQUEST")]
public class InventoryRequest
{

    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Required]
    [Column("MANAGER_NO")]
    public int ManagerNo { get; set; } = 0;

    [Required]
    [Column("EMP_NO")]
    public int EmpNo { get; set; } = 0;

    [Column("MANAGER_APPROVE_BY")]
    public int ManagerApproveBy { get; set; } = 0;


    [Column("STATUS")]
    public string Status { get; set; } = "New";

    [Column("NOTE")]
    public string Note { get; set; } = "";

    [Column("INVENTORY_NOTE")]
    public string InventoryNote { get; set; } = "";

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [Column("CREATED_BY")]
    public int CreatedBy { get; set; } = 0;

    [Column("CANCEL_FLAG")]
    public int CancelFlag { get; set; } = 0;

    [Column("REQ_STAT")]
    public int ReqStat { get; set; }

    [Column("DECLINE_NOTE")]
    public string DeclineNote { get; set; } = "";




}

