﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Inventory.Models.Entities
{
    [Table("INVENTORY_ITEM")]
    public class InventoryItem
    {

        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("NAME")]
        public string Name { get; set; } = "";

        [Required]
        [Column("CODE")]
        public string Code { get; set; } = "";

        [Column("DESCRIPTION")]
        public string Description { get; set; } = "";

        [Column("QUANTITY")]
        public double Quantity { get; set; } = 0;

        [Column("QUANTITY_ALERT")]
        public double QuantityAlert { get; set; } = 0;

        [Column("LOCATION")]
        public string Location { get; set; } = "";

        [Column("UNIT")]
        public string Unit { get; set; } = "";

        [Column("CATEGORY_ID")]
        public int CategoryId { get; set; } = 0;

        [Column("FILE_GUID")]
        public string FileGuid { get; set; } = null;

        [Column("CREATED_AT")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Column("CREATED_BY")]
        public int CreatedBy { get; set; } = 0;

        [Column("UPDATED_AT")]
        public DateTime? UpdatedAt { get; set; } = null;

        [Column("UPDATED_BY")]
        public int UpdatedBy { get; set; } = 0;

        [Column("IS_DELETED")]
        public int Deleted01 { get; set; } = 0;


        [NotMapped]
        public IFormFile File { get; set; }

        [NotMapped]
        public string Image { get; set; } = "";

        [NotMapped]
        public string CategoryName { get; set; } = "";

        public InventoryCategory Category { get; set; }


    }
}
