﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HumanResource.Modules.Assets.Models.Entities;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Inventory.Models.Entities
{
    [Table("INVENTORY_CATEGORY")]
    public class InventoryCategory
    {   

        [Key]
        [Column("ID")]
        public int Id { get; set; }



        [Required]
        [Column("NAME")]
        public string Name { get; set; } = "";

        [Column("DESCRIPTION")]
        public string Description { get; set; } = "";

        [Column("CATEGORY_ID")]
        public int CategoryId { get; set; } = 0;

        [Column("FILE_ID")]
        public int FileId { get; set; } = 0;

        [Column("ISASSET")]
        public int IsAsset { get; set; } = 0;

        [Column("RIGHTS")]
        public string Rights { get; set; } = "";


        public virtual ICollection<InventoryItem> Items { get; set; }

        [NotMapped]
        public List<InventoryItem> _Items { get; set; }

        public virtual ICollection<AssetItem> AssetItem { get; set; }

    }
}
