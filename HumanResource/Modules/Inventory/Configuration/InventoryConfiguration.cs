using HumanResource.Modules.Inventory.Providers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Inventory.Configuration
{
    /// <summary>
    /// Configuration for the Inventory module
    /// </summary>
    public static class InventoryConfiguration
    {
        /// <summary>
        /// Registers all Inventory module services
        /// </summary>
        public static IServiceCollection AddInventoryServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, InventoryNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, InventoryBadgeProvider>();

            // Register task providers
            services.AddScoped<ITaskProvider, InventoryTaskProvider>();


            // Register module services
            // services.AddScoped<InventoryService>();

            return services;
        }
    }
} 