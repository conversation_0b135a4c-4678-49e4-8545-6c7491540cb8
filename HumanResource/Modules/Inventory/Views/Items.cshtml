@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model._("Item")</h3>

    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
            <i class="fas fa-plus"></i>  @Model._("New item")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead class="bg-primary">
            <tr>

                <th>@Model._("Code")</th>
                <th>@Model._("Item")</th>
                <th>@Model._("Category")</th>
                <th>@Model._("Quantity")</th>

            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.InventoryItems)
            {
                <tr>
                    <td><a href="/Inventory/Item/@item.Id">@item.Code</a></td>
                    <td>
                        <a href="/Inventory/Item/@item.Id" class="d-flex">

                            @if (item.FileGuid == null)
                            {
                                <img src="~/assets/images/noimg.png" style="height: 60px;">
                            }
                            else
                            {
                                <img class="rounded mr-2" src="@Model._h.GetFile(item.FileGuid)" style="height: 60px;">
                            }
                            <div>
                                @item.Name
                                <br>
                                <span class="text-muted">@item.Description</span>
                            </div>


                        </a>


                    </td>
                    <td>@item.Category.Name</td>
                    <td>@item.Quantity</td>

                </tr>
            }
        </tbody>
    </table>
</div>

<form action="~/Inventory/CreateItem" method="post" class="ajax">
    <div class="modal fade" id="create-modal" tabindex="-1" role="dialog" aria-labelledby="create-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("New item")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="">@Model._("Name")</label>
                            <input type="text" required class="form-control" name="Name"> <br>



                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._("Code")</label>
                            <div class="input-group">

                                <input type="text" id="new-item-code" required class="form-control" name="Code">

                                <div class="input-group-perpen mr-1">
                                    <button type="button" onclick="get_code()" class="btn btn-light"> <i class="fa fa-arrow-down"></i></button>

                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <div class="row">

                        <div class="col-lg-12">
                            <label for="">@Model._("Category")</label>
                            <div>
                                <select name="CategoryId" class="select2">
                                    @foreach (var cat in Model.InventoryCategories)
                                    {
                                        <option value="@cat.Id">@cat.Name</option>
                                    }
                                </select>
                            </div><br>

                        </div>
                    </div>

                    <div class="row">

                        <div class="col-lg-6">
                            <label for="">@Model._("Unit")</label>
                            <input type="text" class="form-control" name="Unit"> <br>
                        </div>

                        <div class="col-lg-6">
                            <label for="">@Model._("Location")</label>
                            <input type="text" class="form-control" name="Location"> <br>
                        </div>

                    </div>

                    <div class="row">

                        <div class="col-lg-6">
                            <label for="">@Model._("Current Quantity")</label>
                            <input type="number" class="form-control" step="0.1" min="0" name="Quantity" value="0"> <br>

                        </div>

                        <div class="col-lg-6">
                            <label for="">@Model._("Quantity Alert")</label>
                            <input type="number" class="form-control" name="QuantityAlert" value="0"> <br>
                        </div>

                    </div>

                    <div class="row">

                        <div class="col-lg-6">
                            <label for="">@Model._("Description")</label>
                            <textarea name="Description" class="form-control"></textarea>
                            <br>

                        </div>

                        <div class="col-lg-6">
                            <label for="">@Model._("Image")</label>
                            <input type="file" class="form-control" name="File">
                            <br>

                        </div>


                    </div>




                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>





<script>
    function get_code() {
        $.get("/Inventory/GetNextCode", function (data) {
            console.log(data)

            $('#new-item-code').val(data);
        });
    }
</script>