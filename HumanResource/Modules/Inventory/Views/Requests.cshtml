@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel



<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>

                <th>@Model._("No")</th>
                <th>@Model._("Staff")</th>

                <th>@Model._("Created At")</th>
                <th>@Model._("Status")</th>
           

            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.InventoryRequests)
            {
                var s  = Model._h.StaffData(request.EmpNo);

                <tr>
                    <td>
                        
                        <a  href="~/Inventory/Update/@request.Id" >#@request.Id</a>

                    </td>
                    <td>@s.EmpNo - @s.EmpNameA</td>
                    <td>@Model._dt(request.CreatedAt)</td>
                    <td>@Model._(request.Status)</td>
                </tr>
            }
        </tbody>
    </table>
</div>




