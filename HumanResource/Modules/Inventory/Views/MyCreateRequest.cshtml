@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel


<div class="d-flex justify-content-between ">


    <h3>@Model._("طلب مخزني جديد")</h3>


</div>
<form action="/Inventory/My/Create" method="post" class="ajax">
    <div class="card shadow" id="app">
        <div class="card-body">

            <div class="row">
                <div class="col"></div>
            </div>

            <div class="row">
                <div class="col-lg-4">

                    <input type="search" class="form-control" placeholder="@Model._("Search")  " id="item-search">
                    <button class="btn btn-link" type="button" data-toggle="modal" data-target="#items-modal">الكل</button>
                </div>
            </div>
        </div>

        <table class="table">
            <thead class="bg-primary">
                <tr>
                    <th>#</th>
                    <th>@Model._("Item")</th>
                    <th>@Model._("Description")</th>
                    <th>@Model._("Quantity")</th>


                    <th></th>
                </tr>
            </thead>

            <tbody>
                <tr v-for="(item,index) in list">

                    <!-- ITEM FROM LIST -->
                    <template v-if="item.ItemId!=0">
                        <td>{{index+1}}</td>
                        <td>
                            {{item.Name}}
                            <input type="text"
                                   class="form-control"
                                   :name="'RequestItems['+index+'].Name'"
                                   v-model="item.Name"
                                   v-show="0">


                        </td>
                        <td>
                            {{item.Description}}
                            <textarea class="form-control"
                                      rows="3"
                                      v-model="item.Description"
                                      :name="'RequestItems['+index+'].Note'"
                                      @@change="item.Note = item.Description"
                                      v-show="0"></textarea>
                        </td>
                        <td>

                            <input type="number"
                                   min="0"
                                   step="0.25"
                                   class="form-control"
                                   :name="'RequestItems['+index+'].Quantity'"
                                   v-model="item.Quantity">
                        </td>

                        <td class="text-end">
                            <button type="button"
                                    class="btn btn-link text-danger"
                                    @@click="list.splice(index,1)">
                                <i class="fas fa-trash-alt"></i>
                            </button>

                            <input type="hidden"
                                   :name="'RequestItems['+index+'].ItemId'"
                                   v-model="item.ItemId">
                        </td>

                    </template>

                    <!-- CUSTOM ITEM  -->
                    <template v-if="item.ItemId==0">
                        <td>{{index+1}}</td>
                        <td>
                            <input type="text"
                                   class="form-control"
                                   :name="'RequestItems['+index+'].Name'"
                                   v-model="item.Name">
                        </td>
                        <td>
                            <textarea class="form-control"
                                      rows="3"
                                      v-model="item.Description"
                                      :name="'RequestItems['+index+'].Note'"
                                      @@change="item.Note = item.Description"></textarea>
                        </td>
                        <td>
                            <input type="number"
                                   min="0"
                                   step="0.25"
                                   class="form-control"
                                   :name="'RequestItems['+index+'].Quantity'"
                                   v-model="item.Quantity">
                        </td>

                        <td class="text-end">
                            <button type="button"
                                    class="btn btn-link text-danger"
                                    @@click="list.splice(index,1)">
                                <i class="fas fa-trash-alt"></i>
                            </button>

                            <input type="hidden"
                                   :name="'RequestItems['+index+'].ItemId'"
                                   v-model="item.ItemId">
                        </td>

                    </template>


                </tr>
            </tbody>

            <tfoot>
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>

                    <td colspan="2" class="text-end">
                        <button @@click="add_custom()" type="button" class="btn btn-secondary btn-sm"><i class="fa fa-plus"></i> @Model._("طلب خاص")</button>
                    </td>
                </tr>
            </tfoot>


        </table>


        <div class="card-body">
            <div class="row">
                <div class="col-lg-6">

                    <label for="">@Model._("ملاحظة")</label>
                    <textarea name="InventoryRequest.Note" class="form-control" rows="3"></textarea>
                </div>

                <div class="col-lg-6">
                    <label for="">@Model._("Manager")</label>
                    <select name="InventoryRequest.ManagerNo" class="select2">
                        <option hidden selected disabled>@Model._("Select Manager")</option>
                        @foreach (var man in Model._h.Managers(Model.Profile.EmpNo))
                        {
                            <option value="@man.EmpNo">#@man.EmpNo - @man.EmpNameA</option>
                        }
                    </select>
                </div>
            </div>
        </div>

        <div class="card-footer">
            <button class="btn btn-primary "><i class="fa fa-save"></i> @Model._("Submit")</button>
        </div>




        <div class="modal fade" id="items-modal" tabindex="-1" role="dialog" aria-labelledby="new-vehicle-modalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="new-vehicle-modalLabel">قائمة المواد</h3>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">

                            <div class="col-3">
                                <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                    @foreach (var category in Model.InventoryCategories)
                                    {
                                        <a class="nav-link" id="<EMAIL>-tab" data-toggle="pill" href="#<EMAIL>" role="tab" aria-controls="<EMAIL>" aria-selected="true">
                                            @category.Name
                                        </a>
                                    }

                                </div>
                            </div>

                            <div class="col-9">
                                <div class="tab-content" id="v-pills-tabContent">
                                    @foreach (var category in Model.InventoryCategories)
                                    {
                                        <div class="tab-pane fade " id="<EMAIL>" role="tabpanel" aria-labelledby="<EMAIL>-tab">
                                            <div class="row">
                                                @foreach (var item in category._Items)
                                                {
                                                    <div class="col-md-3">
                                                        <div class="card shadow">
                                                            <div class="card-body">
                                                                <div class=" text-center">
                                                                    <img src="@item.Image" alt="" style="max-width: 100px;">
                                                                </div>
                                                                <div class="text-center">
                                                                    <h5>@item.Name</h5>
                                                                    <p>@item.Code - @item.Description</p>
                                                                </div>
                                                            </div>
                                                            <div class="card-footer">
                                                                <button type="button"
                                                                        class="btn btn-block rounded-pill btn-primary"
                                                                        @@click="list.push({
                                                                            ItemId:'@item.Id',
                                                                            Name:'@item.Name',
                                                                            Description:'',
                                                                            Quantity:1,
                                                                        });">
                                                                    اضافة
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>

                                        </div>
                                    }

                                </div>
                            </div>


                        </div>


                    </div>
                    <div class="modal-footer d-felx">
                        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>

                    </div>
                </div>
            </div>
        </div>


    </div>


</form>




<script>



    let app = new Vue({
        el: "#app",
        data: {
            list: []
        },
        methods: {
            add_custom() {
                let job = this;

                job.list.push({
                    ItemId: 0,
                    Name: "",
                    Description: "",
                    Note: "",
                    Quantity: 1,

                });
            }
        }
    });

    $(document).ready(function () {

        $("#item-search").autocomplete({
            minLenght: 2,
            delay: 1000,
            source: function (request, response) {
                console.log(request);
                var term = request.term;

                $.get('/Inventory/ItemsDropdown/' + term, function (data) {
                    response(data.data)
                }, 'json');
            },
            select: function (event, ui, item) {
                // add item to list
                console.log(ui.item);
                app.list.push({
                    ItemId: ui.item.id,
                    Name: ui.item.name,
                    Description: ui.item.description,
                    Quantity: 1,
                });

            },
            close: function (event, ui) {

            },
            search: function () {

            },
            open: function () {

            }
        }).focus(function () {
            $(this).autocomplete('search')
        })
            .autocomplete('instance')._renderItem = function (ul, item) {
                return $('<li>').append(
                    '<div class="d-flex "> ' +

                    '<div style="height:40px;width:40px">    <img src=" ' + item.image + '" style="height:40px"> </div>' +
                    '<p class="text-start ml-3">  ' + item.name + ' <br> <i class="text-muted"> (' + item.code + ') ' + item.categoryName + '</i> </p>' +

                    '<p class="text-danger ml-auto"  style="width:40px"> ' + item.quantity + '</p>' +

                    '</div>'
                ).appendTo(ul)
            }
    })
</script>
