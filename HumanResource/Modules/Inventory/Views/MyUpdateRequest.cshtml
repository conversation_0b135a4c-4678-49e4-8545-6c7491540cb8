﻿@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel


<div class="d-flex justify-content-between">


    <h3>@Model._("Inventory request") #@Model.InventoryRequest.Id</h3>




</div>

<div class="card shadow">


    <table class="table">
        <thead class="bg-primary">
            <tr>

                <th>نوع الطلب</th>
                <th>@Model._("Item")</th>
                <th>@Model._("Description")</th>
                <th>@Model._("Quantity")</th>
                <th>ملاحظة قسم المخازن</th>


                <th></th>
            </tr>
        </thead>

        <tbody>
            @foreach (var item in Model.RequestItems)
            {
                <tr>
                    <td>
                        @if (item.ItemId == 0)
                        {
                            <span>طلب خاص</span>
                        }
                        else
                        {
                            <span>طلب مخزني</span>
                        }
                    </td>
                    <td>@item.Name</td>
                    <td>@item.Note</td>
                    <td>@item.Quantity</td>
                    <td>@item.InventoryNote</td>

                    <td>

                        @if (item.TechnicalRequired == 1 || item.TechnicalStat == 1)
                        {
                            if (item.TechnicalStat == 1)
                            {
                                <span class="badge badge-success"><i class="fa fa-check"></i> ملاحظة فنية</span>

                            }
                            if (item.TechnicalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i> ملاحظة فنية</span>
                            }
                        }

                        @if (item.DgApprovalRequired == 1 || item.DgApprovalStat != 0)
                        {
                            if (item.DgApprovalStat == 1)
                            {
                                <span class="badge badge-success"><i class="fa fa-check"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 2)
                            {
                                <span class="badge badge-danger"><i class="fa fa-times"></i>  اعتماد المدير العام</span>
                            }
                        }

                        @if (item.PurchasesStat == 1)
                        {
                            if (item.FinanceManagerStat == 1)
                            {
                                <span class="badge badge-success"><i class="fa fa-check"></i>  قبول قسم المشتريات  </span>
                            }
                            if (item.FinanceManagerStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i>   قسم المشتريات </span>
                            }
                        }

                    </td>
                </tr>
            }




        </tbody>




    </table>


    <div class="card-body">
        <div class="row">
            <div class="col-lg-6">

                <label for="">@Model._("Note")</label>
                <textarea disabled class="form-control" rows="3">@Model.InventoryRequest.Note</textarea>
            </div>

            <div class="col-lg-6">
                <label for="">@Model._("Manager")</label>
                <input type="text" class="form-control" disabled value="@Model._h.StaffData(Model.InventoryRequest.ManagerNo).EmpNameA">
            </div>
        </div>
    </div>


</div>





