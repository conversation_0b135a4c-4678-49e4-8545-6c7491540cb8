@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel

@Html.Partial("_MangerAppTabs")

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead class="bg-primary">
            <tr>

                <th>@Model._("No")</th>
                <th>@Model._("Staff")</th>

                <th>@Model._("Created At")</th>
                <th>@Model._("Status")</th>


            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.InventoryRequests)
            {
                var s = Model._h.StaffData(request.EmpNo);

                <tr>
                    <td>

                        <a href="~/Inventory/Manager/Update/@request.Id" class="popup" data-size="1000x720">#@request.Id</a>

                    </td>
                    <td>@s.EmpNo - @s.EmpNameA</td>
                    <td>@Model._dt(request.CreatedAt)</td>
                    <td>@Model._h.renderSatus(request.ReqStat)</td>
                </tr>
            }
        </tbody>
    </table>
</div>




