@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model.InventoryItem.Name</h3>

    <div>
        <a href="#" class="btn btn-info rounded-pill shadow btn-sm " data-toggle="modal" data-target="#stocking-modal">
            <i class="far fa-boxes"></i> @Model._("Stocking")
        </a>

        <a href="#" class="btn btn-danger rounded-pill shadow btn-sm after-confirm  ">
            <i class="fas fa-trash"></i> @Model._("Delete")
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-7">
        <div class="card shadow">
            <div class="card-body">
                <form action="/Inventory/UpdateItem/@Model.InventoryItem.Id" method="post" class="ajax">
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="">@Model._("Name")</label>
                            <input type="text" required class="form-control" name="Name" value="@Model.InventoryItem.Name"> <br>

                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._("Code")</label>
                            <div class="input-group">

                                <input type="text" id="new-item-code" required class="form-control" name="Code" value="@Model.InventoryItem.Code">


                            </div>
                            <br>
                        </div>
                    </div>

                    <div class="row">

                        <div class="col-lg-6">
                            <label for="">@Model._("Category")</label>
                            <div>
                                <select name="CategoryId" class="select2">
                                    @foreach (var cat in Model.InventoryCategories)
                                    {
                                        @if (Model.InventoryItem.CategoryId == cat.Id)
                                        {
                                            <option value="@cat.Id" selected>@cat.Name</option>
                                        }
                                        else
                                        {
                                            <option value="@cat.Id">@cat.Name</option>
                                        }

                                    }
                                </select>
                            </div><br>

                        </div>
                    </div>

                    <div class="row">

                        <div class="col-lg-6">
                            <label for="">@Model._("Unit")</label>
                            <input type="text" class="form-control" name="Unit" value="@Model.InventoryItem.Unit"> <br>
                        </div>

                        <div class="col-lg-6">
                            <label for="">@Model._("Location")</label>
                            <input type="text" class="form-control" name="Location" value="@Model.InventoryItem.Location"> <br>
                        </div>

                    </div>

                    <div class="row">

                        <div class="col-lg-6">
                            <label for="">@Model._("Current Quantity")</label>
                            <input type="number" class="form-control" step="0.1" min="0" disabled value="@Model.InventoryItem.Quantity"> <br>

                        </div>

                        <div class="col-lg-6">
                            <label for="">@Model._("Quantity Alert")</label>
                            <input type="number" class="form-control" name="QuantityAlert" value="0" value="@Model.InventoryItem.QuantityAlert"> <br>
                        </div>

                    </div>

                    <div class="row">

                        <div class="col-lg-6">
                            <label for="">@Model._("Description")</label>
                            <textarea name="Description" class="form-control">@Model.InventoryItem.Description</textarea>
                            <br>

                        </div>

                        <div class="col-lg-6 ">
                            <label for="">@Model._("Image")</label>
                            <input type="file" class="form-control" name="File">
                            <div class="text-center">
                                @if (Model.InventoryItem.FileGuid == null)
                                {
                                    <img src="~/assets/images/noimg.png" style="height: 80px;">
                                }
                                else
                                {
                                    <img src="@Model._h.GetFile(Model.InventoryItem.FileGuid)" style="height: 80px;">
                                }
                            </div>

                        </div>


                    </div>

                    <button class="btn btn-primary rounded-pill     px-3"><i class="fa fa-save"></i> @Model._("Save")</button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-5">
        <div class="card shadow">
            <div class="card-body">
                <h3>@Model._("Available"): @Model.InventoryItem.Quantity (@Model.InventoryItem.Unit)</h3>
            </div>

            <table class="table table-sm datatable">
                <thead class="bg-primary">
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </thead>
                @foreach (var log in Model.InventoryLogs)
                {
                    <tr>
                        <td data-order="@log.Id">@log.CreatedAt</td>
                        <td>@log.CreatedBy</td>
                        <td>@log.Quantity</td>
                        <td>@log.Description</td>
                    </tr>
                }
            </table>
        </div>
    </div>
</div>


<form action="/Inventory/Stocking/@Model.InventoryItem.Id" method="post" class="ajax">
    <div class="modal fade" id="stocking-modal" tabindex="-1" role="dialog" aria-labelledby="stocking-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="stocking-modalLabel">@Model._l("Stocking")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">@Model._("New Quantity")</label>
                    <input type="text" required name="Quantity" class="form-control" value="@Model.InventoryItem.Quantity"><br>

                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                </div>
            </div>
        </div>
    </div>
</form>