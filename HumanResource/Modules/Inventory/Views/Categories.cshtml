@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model._("Categories")</h3>

    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
            <i class="fas fa-plus"></i>  @Model._("New Category")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead class="bg-primary">
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Category")</th>
                <th>@Model._("Desciption")</th>
                <th>@Model._("Items")</th>

            </tr>
        </thead>
        <tbody>
            @foreach (var cat in Model.InventoryCategories)
            {
                <tr>
                    <td><a href="#" data-toggle="modal" data-target="#<EMAIL>-modal">@cat.Id</a></td>
                    <td><a href="#" data-toggle="modal" data-target="#<EMAIL>-modal">@cat.Name</a></td>
                    <td>@cat.Description</td>
                    <td>1</td>
                </tr>
            }
        </tbody>
    </table>
</div>

<form action="~/Inventory/CreateCategory" method="post" class="ajax">
    <div class="modal fade" id="create-modal" tabindex="-1" role="dialog" aria-labelledby="create-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("New category")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">@Model._("Category name")</label>
                    <input type="text" required name="Name" class="form-control"><br>

                    <label for="">@Model._("Rights")</label>
                    <input type="text" name="Rights" class="form-control"><br>

                    <label for="">@Model._("IsAsset")</label><br>
                    
                    <input type="radio" name="IsAsset" id="Asset" value="1" /><label for="">@Model._("YES")</label> <br>
                    
                    <input type="radio" name="IsAsset" id="Asset" value="0" /><label for="">@Model._("NO")</label>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>



@foreach (var cat in Model.InventoryCategories)
{
    <form action="~/Inventory/UpdateCategory/@Model.Ec(cat.Id)" method="post" class="ajax">
        <div class="modal fade" id="<EMAIL>-modal" tabindex="-1" role="dialog" aria-labelledby="update-modalLabel"
             aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="update-modalLabel">@Model._l("New category")</h3>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">

                        <label for="">@Model._("Category name")</label>
                        <input type="text" required name="Name" class="form-control" value="@cat.Name"><br>

                        <label for="">@Model._("Rights")</label>
                        <input type="text" name="Rights" class="form-control" value="@cat.Rights"><br>
                        <label for="">@Model._("IsAsset")</label><br>

                        <input type="radio" name="IsAsset" id="Asset" value="1" @(cat.IsAsset==1?"checked":"") /><label for="">@Model._("YES")</label> <br>

                        <input type="radio" name="IsAsset" id="Asset" value="0" @(cat.IsAsset == 0 ? "checked" : "") /><label for="">@Model._("NO")</label>

                    </div>
                    <div class="modal-footer d-flex">
                        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                    </div>
                </div>
            </div>
        </div>

    </form>

}
