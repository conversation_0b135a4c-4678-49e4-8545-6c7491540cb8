﻿@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel

@{
    Layout = Model.Page.Layout;
}

<br>
 

<div class="card shadow">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                @{
                    var s1 = Model._h.<PERSON>ata(Model.InventoryRequest.EmpNo);
                }
                <p>@Model._("Request") #@Model.InventoryRequest.Id <br> @Model._dt(Model.InventoryRequest.CreatedAt) </p>

                @Model._("Created By") : @Model.InventoryRequest.EmpNo - @s1.EmpNameA  <br>


            </div>

            <div class="col-md-6">

                <p> @Model.InventoryRequest.InventoryNote </p>


            </div>


        </div>
    </div>
</div>

<div class="card shadow">
    <table class="table">
        <thead class="bg-primary">
            <tr>
                <th>
                    الطلب
                </th>

                <th>
                    وصف
                </th>
                <th>
                    عدد
                </th>
                <th>
                    ملاحظة قسم المخازن
                </th>
                <th>

                </th>
            </tr>


        </thead>
        <tbody>
            <tr>

                <td>@Model.RequestItem.Name</td>
                <td>@Model.RequestItem.Note</td>
                <td>@Model.RequestItem.Quantity</td>
                <td>@Model.RequestItem.InventoryNote</td>

                <td>

                    @if (Model.RequestItem.DgApprovalStat == 1)
                    {
                    <span class="badge badge-success"><i class="fa fa-check"></i>  اعتماد المدير العام</span>
                    }

                    @if (Model.RequestItem.DgApprovalStat == 0 && Model.RequestItem.DgApprovalRequired==1)
                    {
                    <span class="badge badge-warning"><i class="fa fa-clock"></i>  اعتماد المدير العام</span>
                    }

                    @if (Model.RequestItem.DgApprovalStat == 2)
                    {
                    <span class="badge badge-danger"><i class="fa fa-times"></i>  اعتماد المدير العام</span>
                    }
                    
                </td>

            </tr>
        </tbody>
    </table>
</div>


<form action="/Inventory/Technical/Update/@Model.RequestItem.Id" method="post" class="ajax">

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body">
                    <label for="">@Model._("الملاحظة الفنية")</label>
                    <textarea name="Note" required class="form-control" @(Model.RequestItem.TechnicalStat==0?"":"disabled")>@Model.RequestItem.TechnicalNote</textarea>

                    <br>
                    @if (@Model.RequestItem.TechnicalStat == 0)
                    {
                        <button type="submit" class="btn btn-primary ">@Model._("Submit")</button>
                    }
                    

                </div>
            </div>
        </div>
    </div>

</form>