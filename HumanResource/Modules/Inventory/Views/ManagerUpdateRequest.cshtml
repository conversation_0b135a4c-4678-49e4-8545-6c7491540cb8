@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel

@{
    Layout = Model.Page.Layout;
}

@{
    var s1 = Model._h.StaffData(Model.InventoryRequest.EmpNo);
}
<br>

<div class="row">
    <div class="col"></div>
    <div class="col"></div>
</div>
<div class="card shadow">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p>@Model._("Request")    </p>
                <p># @Model.InventoryRequest.Id <br> @Model._dt(Model.InventoryRequest.CreatedAt) </p>
                <p> @Model._(Model.InventoryRequest.Status) </p>
            </div>

            <div class="col-md-6">
                <p>@Model._("Staff")    </p>
                <p>@s1.EmpNo <br> @s1.EmpNameA</p>
            </div>
        </div>
    </div>
</div>

<div class="card shadow">
    <table class="table">
        @foreach (var item in Model.RequestItems)
        {
            <tr>
                <td>@item.Name</td>
                <td>@item.Note</td>
                <td>@item.Quantity</td>

            </tr>
        }
    </table>
</div>

@if (Model.InventoryRequest.ReqStat == 1 && Model.InventoryRequest.CancelFlag == 0)
{
    <div class="d-flex">
        <a href="/inventory/Manager/Approve/@Model.InventoryRequest.Id" class="btn btn-success btn-block rounded-pill after-confirm mx-1">@Model._("Approve")</a>
        <a href="#" data-toggle="modal" data-target="#department-manager-decline-modal" class="btn btn-danger btn-block rounded-pill mx-1">@Model._("Decline")</a>
    </div>


    <form action="~/inventory/Manager/Decline/@Model.InventoryRequest.Id" class="ajax" method="post">
        <div class="modal fade" id="department-manager-decline-modal" role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-danger">
                        <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("Declined by department manager")</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <label for="">@Model._l("Decline Note")</label>
                        <textarea name="Note" class="form-control"></textarea>
                    </div>
                    <div class="modal-footer d-flex">
                        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Cancel")</button>
                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                    </div>
                </div>
            </div>
        </div>


    </form>
}

