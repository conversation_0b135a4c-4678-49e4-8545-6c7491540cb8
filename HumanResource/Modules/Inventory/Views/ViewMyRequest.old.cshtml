@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model._("Inventory request")</h3>

    @if (Model.InventoryRequest.Status == "New" || Model.InventoryRequest.CancelFlag == 1)
    {
    <div>
            <a href="/Inventory/My/Delete/@Model.InventoryRequest.Id" class="btn btn-danger px-3 rounded-pill btn-sm after-confirm"> <i class="fas fa-trash-alt"></i> @Model._("Delete")</a>
    </div>
    }

    
</div>
<form action="/Inventory/My/Update" method="post" class="ajax">
<div class="card shadow" id="app">
    <div class="card-body">

        <div class="row">
            <div class="col"></div>
        </div>

            @if (Model.InventoryRequest.Status == "New" || Model.InventoryRequest.CancelFlag == 1)
            {
            <div class="row">
                <div class="col-lg-4">
                    <input type="search" class="form-control" placeholder="@Model._("Search")  " id="item-search">
                </div>
            </div>
        }
        
    </div>

    <table class="table">
        <thead class="bg-primary">
            <tr>
                <th>#</th>
                <th>@Model._("Item")</th>
                <th>@Model._("Description")</th>
                <th>@Model._("Quantity")</th>

      
                <th></th>
            </tr>
        </thead>

        <tbody>
            <tr v-for="(item,index) in list" >

                <!-- ITEM FROM LIST -->
                <template v-if="item.ItemId!=0">
                    <td>{{index+1}}</td>
                    <td>
                        {{item.Name}}
                        <input 
                        type="text" 
                        class="form-control" 
                        :name="'RequestItems['+index+'].Name'"
                        v-model="item.Name"
                        v-show="0" >

                        
                    </td>
                    <td>
                        {{item.Note}}
                        <textarea 
                        class="form-control" 
                        rows="3"
                        v-model="item.Note" 
                        :name="'RequestItems['+index+'].Note'"
                        @@change="item.Description = item.Note"
                        :value="item.Note"
                        v-show="0" ></textarea>
                    </td>
                    <td>
                       
                         <input 
                        type="number" 
                        min="0" 
                        step="0.25" 
                        class="form-control" 
                        :name="'RequestItems['+index+'].Quantity'"
                        v-model="item.Quantity">
                    </td>

                    <td class="text-end" >
                        <button type="button" 
                        class="btn btn-link text-danger" 
                        @@click="list.splice(index,1)"><i class="fas fa-trash-alt"></i></button>

                        <input 
                        type="hidden" 
                        :name="'RequestItems['+index+'].ItemId'"
                        v-model="item.ItemId">
                    </td>

                </template>

                <!-- CUSTOM ITEM  -->
                <template v-if="item.ItemId==0">
                    <td>{{index+1}}</td>
                    <td>
                        <input 
                        type="text" 
                        class="form-control" 
                        
                        :name="'RequestItems['+index+'].Name'"
                        v-model="item.Name" >
                    </td>
                    <td>
                        <textarea 
                        class="form-control" 
                        rows="3"
                        v-model="item.Note" 
                        :name="'RequestItems['+index+'].Note'"
                        @@change="item.Description = item.Note"  ></textarea>
                    </td>
                    <td>
                        <input 
                        type="number" 
                        min="0" 
                        step="0.25" 
                        class="form-control" 
                        :name="'RequestItems['+index+'].Quantity'"
                        v-model="item.Quantity">
                    </td>

                    <td class="text-end">
                        <button 
                        type="button" 
                        class="btn btn-link text-danger" 
                        @@click="list.splice(index,1)"><i class="fas fa-trash-alt"></i></button>

                        <input 
                        type="hidden" 
                        :name="'RequestItems['+index+'].ItemId'"
                        v-model="item.ItemId">
                    </td>

                </template>
   
   
            </tr>
        </tbody>

        <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td></td>
              
                <td colspan="2" class="text-end">
                    <button @@click="add_custom()" type="button" class="btn btn-primary rounded-pill px-3">@Model._("Custom Request")</button>
                </td>
            </tr>
        </tfoot>


    </table>


    <div class="card-body">
        <div class="row">
            <div class="col-lg-6">

                <label for="">@Model._("Note")</label>
                    <textarea name="InventoryRequest.Note" class="form-control" rows="3">@Model.InventoryRequest.Note</textarea>
            </div>

            <div class="col-lg-6">
                <label for="">@Model._("Manager")</label>
                <select name="InventoryRequest.ManagerId" class="select2">
                    <option hidden selected disabled>@Model._("Select Manager")</option>
                    @foreach(var man in Model._h.Managers(Model.Profile.EmpNo)){
                            @if (Model.InventoryRequest.ManagerNo == man.EmpNo)
                            {
                            <option value="@man.EmpNo" selected>#@man.EmpNo - @man.EmpNameA</option>
                        }else{
                            <option value="@man.EmpNo">#@man.EmpNo - @man.EmpNameA</option>
                        }
                        
                    }
                </select>
            </div>
        </div>
    </div>

    <div class="card-footer">
        <button class="btn btn-primary rounded-pill px-3" > <i class="fas fa-save"></i> @Model._("Save")</button>
    </div>
</div>


    <input type="hidden" name="InventoryRequest.Id" value="@Model.InventoryRequest.Id">

</form>


<script>



    let app = new Vue({
        el:"#app",
        data:{
            list:@Html.Raw(ViewBag.List),
        },
        methods:{
            add_custom(){
                let job = this;

                job.list.push({
                    ItemId:0,
                    Name:"",
                    Description:"",
                    Note:"",
                    Quantity:1,
      
                });
            }
        }
    });

    $(document).ready(function(){

        $("#item-search").autocomplete({
            minLenght:2,
            delay:1000,
            source: function(request, response){
                console.log(request);
                var term = request.term;

                $.get('/Inventory/ItemsDropdown/'+term, function(data){
                    response(data.data)
                },'json');
            },
            select:function(event,ui,item){
                // add item to list
                console.log(ui.item);
                app.list.push({
                    ItemId:ui.item.id,
                    Name:ui.item.name,
                    Description:ui.item.description,
                    Note:ui.item.description,
                    Quantity:1,
                });

            },
            close:function(event,ui){

            },
            search:function(){

            },
            open:function(){

            }
        }).focus(function(){
            $(this).autocomplete('search')
        })
        .autocomplete('instance')._renderItem = function(ul, item){
            return $('<li>').append(
                '<div class="d-flex justify-content-between"> ' + 

                    '<img src=" ' + item.image + '" style="height:40px">'  + 
                    '<p class=""> ' + item.name + '</p>'  + 
                    '<p class="text-danger"> ' + item.quantity + '</p>'  + 

                '</div>'
                ).appendTo(ul)
        }
    })
</script>
