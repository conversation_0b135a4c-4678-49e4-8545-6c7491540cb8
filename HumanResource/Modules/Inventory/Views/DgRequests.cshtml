﻿@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel

@Html.Partial("_DgAppTabs")

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead class="bg-primary">
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Created At")</th>
                <th>@Model._("Item")</th>
                <th>@Model._("Note")</th>
                <th>@Model._("Quantity")</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.RequestItems)
            {
      

                <tr>
                    <td>
                        
                        <a  href="~/Inventory/Dg/Update/@item.Id">#@item.RelId</a>

                    </td>

                    <td>@Model._dt(item.CreatedAt)</td>
                    <td>@item.Name</td>
                    <td>@item.Note</td>
                    <td>@item.Quantity</td>
                    <td>
                        @if(item.DgApprovalStat == 1)
                        {
                            <span>مقبول</span>
                        }
                        else if (item.DgApprovalStat == 2)
                        {
                            <span>مرفوض</span>
                        }
                        else
                        {
                            <span class="badge badge-danger rounded-pill px-2">جديد</span>
                        }
                    </td>

                </tr>
            }
        </tbody>
    </table>
</div>




