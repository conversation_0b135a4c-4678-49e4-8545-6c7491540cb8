﻿@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel

@{
    Layout = Model.Page.Layout;
}

<div class="row">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        @{
                            var s1 = Model._h.StaffData(Model.InventoryRequest.EmpNo);
                        }
                        <p>@Model._("Request") #@Model.InventoryRequest.Id <br> @Model._dt(Model.InventoryRequest.CreatedAt) </p>

                        @Model._("Created By") : @Model.InventoryRequest.EmpNo - @s1.EmpNameA  <br>


                    </div>

                    <div class="col-md-6">

                        <p> @Model.InventoryRequest.InventoryNote </p>


                    </div>


                </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow">
    <table class="table">
        <thead class="bg-primary">
            <tr>
                <th>
                    الطلب
                </th>

                <th>
                    وصف
                </th>
                <th>
                    عدد
                </th>
                <th>
                    ملاحظة قسم المخازن
                </th>
                <th>

                </th>
            </tr>


        </thead>
        <tbody>
            <tr>

                <td>@Model.RequestItem.Name</td>
                <td>@Model.RequestItem.Note</td>
                <td>@Model.RequestItem.Quantity</td>
                <td>@Model.RequestItem.InventoryNote</td>

                <td>
                    
                        @if (Model.RequestItem.TechnicalStat == 1)
                        {
                            <span class="badge badge-success"><i class="fa fa-check"></i> ملاحظة فنية</span>
                        }
                        @if (Model.RequestItem.TechnicalStat == 0 && Model.RequestItem.TechnicalRequired == 1)
                        {
                            <span class="badge badge-warning"><i class="fa fa-clock"></i> ملاحظة فنية</span>
                        }
                    
                </td>

            </tr>
        </tbody>
    </table>
</div>



<div class="row">
    <div class="col-md-6">
        <div class="d-flex">
            <a href="/inventory/dg/Approve/@Model.Ec(Model.RequestItem.Id)" class="btn btn-success btn-block after-confirm mx-1"><i class="fa fa-check"></i> @Model._("Approve")</a>
            <a href="#" data-toggle="modal" data-target="#department-dg-decline-modal" class="btn btn-danger btn-block "><i class="fa fa-times"></i> @Model._("Decline")</a>
        </div>


        <form action="~/inventory/Dg/Decline/@Model.Ec(Model.RequestItem.Id)" class="ajax" method="post">
            <div class="modal fade" id="department-dg-decline-modal" role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-danger">
                            <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("Declined by director general")</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <label for="">@Model._l("Decline Note")</label>
                            <textarea name="Note" class="form-control"></textarea>
                        </div>
                        <div class="modal-footer d-flex">
                            <button type="button" class="btn btn-secondary btn-block " data-dismiss="modal">@Model._l("Cancel")</button>
                            <button type="submit" class="btn btn-primary btn-block">@Model._l("Submit")</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>



