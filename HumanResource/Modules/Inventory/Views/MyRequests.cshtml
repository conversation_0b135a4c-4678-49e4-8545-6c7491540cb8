﻿@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel


<div class="d-flex justify-content-between ">


    <h3>@Model._("Inventory requests")</h3>

    <div>
        <a href="/Inventory/My/Create" class="btn btn-primary  btn-sm ">
            <i class="fas fa-plus"></i>  @Model._("New Request")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead class="bg-primary">
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Created At")</th>
                <th>@Model._("Status")</th>

            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.InventoryRequests)
            {
                <tr>
                    <td><a href="~/Inventory/My/Update/@request.Id">@request.Id</a></td>
                    <td>@Model._dt(request.CreatedAt)</td>
                    <td>
                        @Model.renderSatus(request.ReqStat)
                        @if (request.ReqStat == 20)
                        {
                            <br>
                            <span>قسم المخازن(@request.Status)</span>
                        }
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>




