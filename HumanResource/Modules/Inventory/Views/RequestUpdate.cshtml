﻿@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel

<div class="d-flex justify-content-between mb-1">


    <h3>@Model._("Inventory Request") #@Model.InventoryRequest.Id </h3>

    <div class="d-flex">

        <div class="mx-2">
            <form action="/Inventory/Status/@Model.InventoryRequest.Id" method="post" class="ajax" id="status-form">
                <select name="Status" class="form-control form-control-sm " onchange="$('#status-form').submit()">
                    @foreach (var status in Model.requestStatuses)
                    {
                        if (Model.InventoryRequest.Status == status)
                        {
                            <option selected value="@status">@status</option>
                        }
                        else
                        {
                            <option value="@status">@status</option>
                        }
                    }


                </select>
            </form>
        </div>


        <div>
            @* <a href="/Inventory/My/Delete/@Model.Request.Id" class="btn btn-danger px-3 rounded-pill btn-sm after-confirm mx-1"> <i class="fas fa-trash-alt"></i> @Model._("Delete")</a> *@
        </div>



    </div>


</div>
<div>


    <div class="card shadow">
        <div class="card-body">
            <div class="row">
                <div class="col">
                    @{
                        var s1 = Model._h.StaffData(Model.InventoryRequest.EmpNo);
                    }

                    @Model._("Created By") : @Model.InventoryRequest.EmpNo - @s1.EmpNameA  <br>
                    @Model._("Created At"): @Model._dt(Model.InventoryRequest.CreatedAt) <br>


                </div>

                <div class="col">

                    @{
                        var s2 = Model._h.StaffData(Model.InventoryRequest.ManagerNo);
                        var s3 = Model._h.StaffData(Model.InventoryRequest.ManagerApproveBy);
                    }

                    <p>
                        @Model._("Manager"): @s2.EmpNo - @s2.EmpNameA
                    </p>

                    <p>
                        @Model._("Approved By"): @s3.EmpNo - @s3.EmpNameA
                    </p>

                </div>
            </div>
        </div>
    </div>


    <div class="card shadow">


        <table class="table table-striped">
            <thead class="bg-primary">
                <tr>
                    <th>رقم الطلب</th>
                    <th>النوع</th>
                    <th>الاسم</th>
                    <th>وصف</th>
                    <th>العدد المطلوب</th>
                    <th>ملاحظة قسم المخازن</th>
                    <th>حالة التسليم</th>
                    <th>اجراء</th>
                    <th>تقدم الطلب</th>



                </tr>
            </thead>

            <tbody>

                @foreach (var item in Model.RequestItems)
                {
                    <tr>
                        <td>
                            @item.Id
                        </td>

                        <td>
                            @if (item.ItemId == 0)
                            {
                                <span>طلب خاص</span>
                            }
                            else
                            {
                                <span>طلب مخزني</span>
                            }
                        </td>

                        <td>
                            @if (item.ItemId == 0)
                            {
                                <span>@item.Name</span>
                            }
                            else
                            {
                                var _item = Model.GetItem(item.ItemId);
                                <a href="#">@item.Name</a> <small class="text-@(_item.Quantity>0?"success":"danger")">(متوفر @_item.Quantity)</small>
                            }

                        </td>

                        <td>
                            @item.Note
                        </td>

                        <td>
                            @item.Quantity
                        </td>

                        <td>
                            <form action="/Inventory/Update/Note/@item.RelId/@item.Id" method="post" class="ajax">
                                <textarea class="form-control" name="InventoryNote">@item.InventoryNote</textarea>
                                <button class="btn btn-primary btn-sm mt-1"><i class="fa fa-save"></i> حفظ</button>
                            </form>
                        </td>

                        <td>
                            @if (item.DeliveryStat == 0)
                            {
                                <span class="text-danger">لم يتم</span>
                            }
                            else
                            {
                                <span class="text-success"><i class="fa fa-check"></i> مكتمل</span>
                            }
                        </td>

                        <td>
                            @if (item.DeliveryStat == 0)
                            {
                                <button type="button"
                                        onclick="
                                        app.requestId = @item.Id;
                                        app.itemQuantity = @item.Quantity;
                                        app.type = @(item.ItemId==0?"1":"0");
                                        " data-toggle="modal" data-target="#item-action-modal" class="btn btn-sm btn-secondary">
                                    <i class="fas fa-external-link"></i> عمل اجراء
                                </button>
                            }
                        </td>

                        <td>

                            @if (item.TechnicalRequired == 1 || item.TechnicalStat == 1)
                            {
                                if (item.TechnicalStat == 1)
                                {
                                    <a href="#" data-toggle="modal" data-target="#<EMAIL>-mdoal"><span class="badge badge-success"><i class="fa fa-check"></i> ملاحظة فنية</span></a>

                                    <div class="modal fade" id="<EMAIL>-mdoal" tabindex="-1" role="dialog" aria-labelledby="item-action-modal"
                                         aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h3 class="modal-title" id="new-vehicle-modalLabel">ملاحظة فنية</h3>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    @item.TechnicalNote

                                                </div>
                                                <div class="modal-footer ">
                                                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("Close")</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                }
                                if (item.TechnicalStat == 0)
                                {
                                    <span class="badge badge-warning"><i class="fa fa-clock"></i> ملاحظة فنية</span>
                                }
                            }

                            @if (item.DgApprovalRequired == 1 || item.DgApprovalStat != 0)
                            {
                                if (item.DgApprovalStat == 1)
                                {
                                    <span class="badge badge-success"><i class="fa fa-check"></i>  اعتماد المدير العام</span>
                                }

                                if (item.DgApprovalStat == 0)
                                {
                                    <span class="badge badge-warning"><i class="fa fa-clock"></i>  اعتماد المدير العام</span>
                                }

                                if (item.DgApprovalStat == 2)
                                {
                                    <span class="badge badge-danger"><i class="fa fa-times"></i>  اعتماد المدير العام</span>
                                }
                            }

                            @if (item.PurchasesStat == 1)
                            {
                                if (item.FinanceManagerStat == 1)
                                {
                                    <span class="badge badge-success"><i class="fa fa-check"></i>  قبول قسم المشتريات  </span>
                                }
                                if (item.FinanceManagerStat == 0)
                                {
                                    <span class="badge badge-warning"><i class="fa fa-clock"></i>   قسم المشتريات </span>
                                }
                            }


                        </td>

                    </tr>


                }



            </tbody>

            <tfoot>
                <tr>

                    <td colspan="3">
                        <div class="d-flex">

                            <div>
                            </div>
                            <div>
                            </div>

                        </div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>

                </tr>
            </tfoot>



        </table>


        <div class="card-body">
            <div class="row">
                <div class="col-lg-6">

                    <label for="">@Model._("Note")</label>
                    <textarea disabled class="form-control" rows="3">@Model.InventoryRequest.Note</textarea>
                </div>



            </div>
        </div>


    </div>





</div>

<div id="app">
    <form :action="'/Inventory/Update/@Model.InventoryRequest.Id/' + requestId" method="post" class="ajax">
        <div class="modal fade" id="item-action-modal" tabindex="-1" role="dialog" aria-labelledby="item-action-modal"
             aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="new-vehicle-modalLabel">اجراء</h3>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div>
                            <label>النوع</label>
                            <select class="form-control" v-model="action" name="Action">
                                <option selected disabled value="">اختر اجراء</option>
                                <option value="deliver">تسليم </option>
                                <option value="cancel_deliver">الغئ تسليم</option>

                                <option v-show="type==1" value="request_purchases">طلب من المشتريات</option>

                                <option v-show="type==1" value="request_technical_note">طلب ملاحظة فنية</option>
                                <option v-show="type==1" value="cancel_technical_note">الغاء طلب الملاحظة الفنية</option>

                                <option value="request_dg_approval">تطلب اعتماد من المدير العام</option>
                                <option value="cancel_dg_approval">الغاء طلب اعتماد من المدير العام</option>

                            </select>
                            <br>
                        </div>

                        <div v-show="action=='request_technical_note'">
                            <select class="form-control" name="DeptCode">
                                <option value="">اختر قسم</option>

                                @foreach (var dep in Model.TDeptCodes)
                                {
                                    <option value="@dep.DeptCode">@dep.Name</option>
                                }

                            </select>
                        </div>

                        <div v-show="action=='deliver'">

                            <label>الكمية المسلمة</label>
                            <input class="form-control" name="DeliveredQty" type="number" min="0" :max="itemQuantity" />

                        </div>


                    </div>
                    <div class="modal-footer d-felx">
                        <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("Close")</button>
                        <button type="submit" class="btn btn-primary ">@Model._l("Submit")</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>



    let app = new Vue({
        el: "#app",
        data: {
            requestId: 0,
            action: "",
            itemQuantity: 0,
            type: 0,
        },
        methods: {


        }
    });




</script>
