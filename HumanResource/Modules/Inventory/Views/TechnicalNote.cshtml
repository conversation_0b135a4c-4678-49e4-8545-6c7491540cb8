@using HumanResource.Modules.Inventory.ViewModels
@model InventoryViewModel

@Html.Partial("_MangerAppTabs")

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead class="bg-primary">
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Created At")</th>
                <th>@Model._("Item")</th>
                <th>@Model._("Note")</th>
                <th>@Model._("Quantity")</th>
                <th>@Model._("Inventory Note")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.RequestItems)
            {
      

                <tr>
                    <td>
                        
                        <a  href="~/Inventory/Technical/View/@Model.Ec(item.Id)" class="popup" data-size="1000x720">#@item.RelId</a>

                    </td>
 
                    <td>@Model._dt(item.CreatedAt)</td>
                    <td>@item.Name</td>
                    <td>@item.Note</td>
                    <td>@item.Quantity</td>
                    <td>@item.InventoryNote</td>
                </tr>
            }
        </tbody>
    </table>
</div>




