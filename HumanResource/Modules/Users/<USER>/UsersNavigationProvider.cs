using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;
namespace HumanResource.Modules.Users.Providers
{
    /// <summary>
    /// Navigation provider for the Users module
    /// </summary>
    public class UsersNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Users";
        public int Priority => 50;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Administration Group navigation items
            var administrationNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "users",
                    Label = "المستخدمون",
                    Active = "users",
                    Icon = "<i class=\"far fa-user-lock\"></i>",
                    Rights = new List<Right> { Right.Admin },
                    Priority = 10,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "Users",
                            Label = "المستخدمون",
                            Rights = new List<Right> { Right.Admin },
                            Url = "/Users/<USER>"
                        },
                        new NavLink
                        {
                            Name = "Roles",
                            Label = "الأدوار",
                            Rights = new List<Right> { Right.Admin },
                            Url = "/Users/<USER>"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Administration] = administrationNavItems;

            return navigation;
        }
    }
} 