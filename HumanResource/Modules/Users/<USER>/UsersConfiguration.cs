using HumanResource.Modules.Users.Providers;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Users.Configuration
{
    /// <summary>
    /// Configuration for the Users module
    /// </summary>
    public static class UsersConfiguration
    {
        /// <summary>
        /// Registers all Users module services
        /// </summary>
        public static IServiceCollection AddUsersServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, UsersNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, UsersBadgeProvider>();

            // Register module services
            // services.AddScoped<UserService>();

            return services;
        }
    }
} 