﻿@using HumanResource.Modules.Users.ViewModels
@model UsersViewModel



<h3>@Model.Role.Name<h3>
<div class="row">
    <div class="col-md-6">

        <div class="card shadow ">
            <div class="card-body">
                <form action="~/users/Roles/Update/@Model.Role.Code" method="post" class="ajax">

                    <label for="" required="">@Model._("Role name")</label>
					<input type="text" class="form-control" name="Role.Name" value="@Model.Role.Name"><br>


                    <label for="" required="">@Model._("Role description")</label>
					<input type="text" class="form-control" name="Role.Rem" value="@Model.Role.Rem"><br>



                    <table class="table table-sm table-bordered">
                        @foreach (var group in @Model.Rights.List)
                        {
                            <tr class="bg-primary">
                                <th colspan="3">@group.Name</th>
                            </tr>

                            @foreach(var right in group.Rights){
                                <tr>
                                    <td>
                                        <input type="checkbox" name="rights" value="@right.Name" id="@right.Name" @(Model.Role._Rights.Contains(right.Name) ? "checked" : "")>
                                    </td>
                                    <td>
                                        @right.Label
                                    </td>
                                    <td>
                                        <span class="text-muted text-monospace ">@right.Name</span>
                                    </td>
                                </tr>
                            }
                        }
                    </table>
          


                    <br>



                    <button class="btn btn-primary rounded-pill px-3">@Model._("Save")</button>
                </form>
            </div>
        </div>
    </div>
</div>


