﻿using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Users.ViewModels;

public class UsersViewModel : BaseViewModel
{
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly hrmsContext _context;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly IHttpContextAccessor _http;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    private AppHelper _h;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword

    public UsersViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _context = context;
        _db = context;
        _http = httpContextAccessor;
        _h = helper;
    }

    public List<string> UserRights { get; set; }
    public List<Role> Roles { get; set; }
    public Role Role { get; set; }


	public List<VempDtl> Emps { get; set; }
	public VempDtl Emp { get; set; }

    public  List<RoleEmp> RoleEmps { get; set; }



	public float? getAmt(int empNo)
    {
        var EmpEarn = _db.EmpEarnings
                .Where(ro => ro.EmpNo == empNo && ro.EarnCode == 1 &&  ro.ToDate == null)
                .FirstOrDefault();

        if (EmpEarn == null)
        {
            return 0;

        }

        float limitAmt = (float)(EmpEarn.EarnAmt / 2);
        
        return limitAmt;
    }


}

