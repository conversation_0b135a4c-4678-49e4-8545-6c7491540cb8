﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Users.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Core.UI.Models;

namespace HumanResource.Modules.Users.Controllers
{

    [Area("Users")]
    [Route("Users")]
    [Can("admin")]
    public class UsersController : BaseController
    {


        public UsersViewModel _v;

        public UsersController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
        {

            _v = new UsersViewModel(context, httpContextAccessor, helper);

            _v.Page.Active = "users";
            _v.Helper = helper;
            _v.Auth = Auth();

        }



        [Route("My")]
        public ActionResult My()
        {

            return View(_v);
        }


        [Route("Index")]
        public ActionResult Index()
        {

            _v.Page.Reload = true;

            _v.Page.Breadcrumb = new List<Breadcrumb> {
                 new Breadcrumb {Label="Users", Url=$"/Users/<USER>"},
            };

            _v.Emps = _db.VempDtls
                .Include(r=>r.RoleEmps)
                .ThenInclude(er=>er.Role)
                .ToList();

            return View(_v);
        }


        [HttpPost("Datatable")]
        public IActionResult Datatable([FromForm] DataTableHelper datatable)
        {

            _v.Emps = _db.VempDtls
                .Include(r => r.RoleEmps)
                .ThenInclude(er => er.Role)
                .ToList();

            var query = _db.VempDtls
                .Include(r => r.RoleEmps)
                .ThenInclude(er => er.Role)
                .Select(user => new
                {
                    user.EmpNo,
                    user.EmpNameA,
                    user.DgDespA,
                    user.DeptDespA,
                    Roles = string.Join(" . ", user.RoleEmps.Select(roleEmp => roleEmp.Role.Name)),
                });



            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                query = query.Where(f =>
                f.EmpNo.ToString().Contains(datatable.Search.Value)
                || f.EmpNameA.ToString().Contains(datatable.Search.Value)
                || f.DgDespA.ToString().Contains(datatable.Search.Value)
                || f.DeptDespA.ToString().Contains(datatable.Search.Value)

                );


            }

            var total = query.Count();

            var emps = query.ToList();

            var data = emps.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();

            switch (datatable.Order[0].Column)
            {
                case 1:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = emps.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = emps.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 2:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = emps.OrderByDescending(o => o.EmpNameA).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = emps.OrderBy(o => o.EmpNameA).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 3:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = emps.OrderByDescending(o => o.DeptDespA).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = emps.OrderBy(o => o.DeptDespA).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 4:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = emps.OrderByDescending(o => o.Roles).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = emps.OrderBy(o => o.Roles).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;


                default:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = emps.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = emps.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }

                    break;
            }

            var table = data.Select(ro => new
            {
                empNo = "<a href='/Users/<USER>/" + ro.EmpNo + "'> " + ro.EmpNo + " </a>",
                empNameA = ro.EmpNameA,
                department = ro.DeptDespA + "<br>"+ ro.DgDespA,
                role = ro.Roles != null ? ro.Roles : "--",
            }).ToList();


            var output = new
            {
                datatable.Draw,
                recordsTotal = total,
                recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
                data = table
            };


            return Json(output);
        }


        [HttpGet("View/{empNo}")]
        // GET: UserController/Details/5
        public ActionResult View(int empNo)
        {
            _v.Page.Reload = true;
            _v.Page.Back = $"/Users/<USER>";

            _v.Page.Breadcrumb = new List<Breadcrumb> {
                 new Breadcrumb {Label="Users", Url=$"/Users/"},
                 new Breadcrumb {Label="View", Url=$"/Users/<USER>"},
            };

            var emp = _db.VempDtls.FirstOrDefault(s => s.EmpNo == empNo);

            if (emp == null)
                return StatusCode(404);

            _v.Emp = _db.VempDtls.FirstOrDefault(s=>s.EmpNo== empNo);

            var roleEmp = _db.RoleEmps.Where(r => r.EmpNo == emp.EmpNo).ToList();

            _v.Role = null;

            _v.RoleEmps = roleEmp;

            ViewBag.UserRoleCodes = roleEmp.Select(r=>r.RoleCode).ToList();

			_v.Roles = _db.Roles.ToList();
            //_v.UserRights = _db.UserRoles.Where(s => s.StaffId == empNo).Select(s=>s.Role).ToList();

            return View(_v);
        }



        [HttpPost("Update/{EmpNo}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Update(int EmpNo, [FromForm] IFormCollection post)
        {

            var emp = _db.VempDtls.FirstOrDefault(e => e.EmpNo == EmpNo);

            if (emp == null)
                return StatusCode(404);

            if (!post.ContainsKey("RoleCode[]"))
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("الصلاحية مطلوب") },
                });
            }

            string postCode = post["RoleCode[]"];

            var selectedRoles = postCode.Split(',').Select(x => int.Parse(x.Trim())).ToList();

			foreach (var code in selectedRoles)
			{
				var role = _db.Roles.Find(code);

				if (role == null)
                {
                    selectedRoles.Remove(role.Code);

				}
			}

            try
            {
                var roleEmp = _db.RoleEmps.Where(e => e.EmpNo == EmpNo).ToList();

                _db.RoleEmps.RemoveRange(roleEmp);
				await _db.SaveChangesAsync();

                foreach (var roleCode in selectedRoles)
                {
					var newRole = new RoleEmp
					{
						EmpNo = EmpNo,
						RoleCode = roleCode,
					};

					_db.RoleEmps.Add(newRole);

					await _db.SaveChangesAsync();
				}

                // Clear the user rights cache for this employee
                string cacheKey = $"UserRights_{EmpNo}";
                HttpContext.Session.Remove(cacheKey);

                var response = new
                {
                    success = true,
                    message = new List<string> { "User updated successfully." },
                };

                return Json(response);
            }
            catch (Exception ex)
            {
                var response = new
                {
                    success = false,
                    message = new List<string> { expError(ex) },
                    action = "",
                };

                return Json(response);
            }
        }

        [Route("Roles")]
        public ActionResult Roles()
        {


            _v.Page.Reload = true;

            _v.Page.Breadcrumb = new List<Breadcrumb> {
                 new Breadcrumb {Label="Roles", Url=$"/Users/<USER>"},
            };

            _v.Roles = _db.Roles.ToList();

            return View(_v);
        }

		

		[Route("Roles/Create")]
		public ActionResult RoleCreate()
		{
			_v.Page.Reload = true;


			_v.Page.Breadcrumb = new List<Breadcrumb> {
				 new Breadcrumb {Label="Roles", Url=$"/Users/<USER>"},
				 new Breadcrumb {Label="Create Role", Url=$"/Users/<USER>"},
			};

			_v.Page.Back = $"/Users/<USER>";


			return View(_v);
		}

		[HttpPost("Roles/Create")]
		public async Task<IActionResult> RoleCreate(Role role, [FromForm] IFormCollection post)
		{

			if (!IsValid(ModelState))
			{
				var response = new
				{
					success = false,
					message = ValidateErrors(ModelState),
					action = "",
				};

				return Json(response);
			}

            var rights = "";

			if (post.ContainsKey("rights"))
			{
				foreach (var right in post["rights"])
				{
                    rights += "|" + right;
				}
			}

            try
            {
                var Role = new Role
                {
                    Name = role.Name,
                    Rights = rights,
                };

                _db.Roles.Add(Role);

                _db.SaveChanges();
            }
            catch (Exception ex)
            {

                return Json(new
                {
                    success = false,
                    message = new List<string> { expError(ex) },
                });
            }

			return Json(new
			{
				success = true,
				message = new List<string> { _("تم انشاء صالحية جديدة") },
				action = "reload",
			});
		}


		[Route("Roles/Update/{Code}")]
		public ActionResult RoleUpdate(int Code)
		{
			

			var role = _db.Roles.FirstOrDefault(s => s.Code == Code);

			if (role == null)
				return StatusCode(404);

			_v.Page.Reload = true;

			_v.Page.Breadcrumb = new List<Breadcrumb> {
				 new Breadcrumb {Label="Roles", Url=$"/Users/<USER>"},
				 new Breadcrumb {Label=role.Name, Url=$"/Users/<USER>"},
			};

			_v.Page.Back = $"/Users/<USER>";


			_v.Role = role;

			return View(_v);
		}

		[HttpPost("Roles/Update/{Code}")]
		public async Task<IActionResult> RoleUpdate(int Code, Role role, [FromForm] IFormCollection post)
		{
			if (!IsValid(ModelState))
			{
				var response = new
				{
					success = false,
					message = ValidateErrors(ModelState),
					action = "",
				};

				return Json(response);
			}

			var rights = "";

			if (post.ContainsKey("rights"))
			{
				foreach (var right in post["rights"])
				{
					rights += "|" + right;
				}
			}

			var _role = _db.Roles.First(s => s.Code == Code);

			if (_role == null)
				return StatusCode(404);

            _role.Name = role.Name;
            _role.Rem = role.Rem;
            _role.Rights = rights;

            try
            {
                _db.Update(_role);

                _db.SaveChanges();

                // Clear cache for all users who have this role
                var usersWithRole = _db.RoleEmps.Where(r => r.RoleCode == Code).Select(r => r.EmpNo).ToList();
                foreach (var empNo in usersWithRole)
                {
                    string cacheKey = $"UserRights_{empNo}";
                    HttpContext.Session.Remove(cacheKey);
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { expError(ex) },
                });
            }

            return Json(new
			{
				success = true,
				message = new List<string> { _("تم تحديث الصلاحية") },
				action = "reload",
			});
		}
	}
}
