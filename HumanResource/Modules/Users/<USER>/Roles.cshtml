﻿@using HumanResource.Modules.Users.ViewModels
@model UsersViewModel


<div class="d-flex justify-content-between my-1">
    <div>
        <h4>@Model._l("Roles")</h4>
    </div>
    <div>
        <a href="~/Users/<USER>/Create" class="btn btn-primary "><i class="fa fa-plus"></i> @Model._("Create role")</a>
    </div>
</div>
<div class="card shadow">
    

    <table class="table table-sm datatable">
        <thead>
            <tr>
                <td>@Model._l("الرقم")</td>
                <td>@Model._l("Role")</td>
                <td>@Model._l("Description")</td>
                <td>@Model._l("العدد")</td>

            </tr>
        </thead>
        <tbody>
            @foreach (var role in Model.Roles)
            {
                <tr>
                    <td>
                        <a href="~/Users/<USER>/Update/@role.Code">#@role.Code</a>
                    </td>

                    <td>@role.Name </td>

                    <td>@role.Rem</td>

                    <td>--</td>
                </tr>
            }
        </tbody>
    </table>
</div>

