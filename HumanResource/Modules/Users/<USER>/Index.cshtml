﻿@using HumanResource.Modules.Users.ViewModels
@model UsersViewModel
<div class="d-flex justify-content-between">
        <div>
            <h3>@Model._l("Users")</h3>
        </div>
        <div>
            
        </div>
    </div>
<div class="card shadow">
    
    <table class="table table-sm  table-sm" id="datatable">
        <thead>
            <tr>
                <td>@Model._l("ID")</td>
                <td>@Model._l("Staff")</td>
                <td>@Model._l("Department")</td>
                <td>@Model._l("Role")</td>
        
            </tr>
        </thead>
        <tbody>
  
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function(){
        createDatatable('#datatable',true, {
            ajax: "/Users/<USER>",
            columns:[
                {"data":"empNo","title":"#"},
                {"data":"empNameA","title":"@Model._("Staff")"},
                {"data":"department","title":"@Model._("Department")"},
                {"data":"role","title":"@Model._("Role")"},

            ]
        });
    })
</script>
