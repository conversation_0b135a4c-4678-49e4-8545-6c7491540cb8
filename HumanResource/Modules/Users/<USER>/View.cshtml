﻿@using HumanResource.Modules.Users.ViewModels
@model UsersViewModel


<h3>
    @Model.Emp.EmpNameA<h3>
        <div class="row">
            <div class="col-md-6">

                <div class="card shadow ">
                    <div class="card-body">
                        <form action="~/Users/<USER>/@Model.Emp.EmpNo" method="post" class="ajax">

                            <label for="" required="">@Model._("No")</label>
                            <input type="text" disabled class="form-control" value="@Model.Emp.EmpNo"><br>

                            <label for="" required="">@Model._("Name")</label>
                            <input type="text" disabled class="form-control" value="@Model.Emp.EmpNameA"><br>

                            <label for="" required="">@Model._("Position")</label>
                            <input type="text" disabled class="form-control" value="@Model.Emp.DesgCode"><br>

                            <label for="" required="">@Model._("Department")</label>
                            <input type="text" disabled class="form-control" value="@Model.Emp.DeptDespA"><br>

                            <label for="" required="">@Model._("DG")</label>
                            <input type="text" disabled class="form-control" value="@Model.Emp.DgDespA"><br>

                            @foreach (var role in Model.Roles)
                            {
                                if (ViewBag.UserRoleCodes.Contains(role.Code))
                                {
                                    @role.Name
                                }
                                else
                                {
                                    
                                }
                            }

                            <div>
                                <label for="">@Model._("Roles")</label>
                                <select name="RoleCode[]" class="select2" multiple>
                                    @foreach (var role in Model.Roles)
                                    {
                                        if (ViewBag.UserRoleCodes.Contains(role.Code))
                                        {
                                            <option selected value="@role.Code">@role.Name</option>
                                        }
                                        else
                                        {
                                            <option value="@role.Code">@role.Name</option>
                                        }
                                    }
                                </select>


                            </div>

                            <br>



                            <button class="btn btn-primary"> <i class="fa fa-save"></i> @Model._("Save")</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>




