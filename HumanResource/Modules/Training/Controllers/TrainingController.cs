﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.CodeAnalysis;
using HumanResource.Modules.Training.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Training.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Training.Froms;
using HumanResource.Core.UI.Models;

namespace HumanResource.Modules.Training.Controllers
{
    public class TrainingController : BaseController
    {
        public TrainingViewModel _v;
        private readonly IWebHostEnvironment _webHost;

        public TrainingController(
          hrmsContext context,
          IHttpContextAccessor httpContextAccessor, AppHelper helper, IWebHostEnvironment webHost)
          : base(context, httpContextAccessor, helper)
        {
            _v = new TrainingViewModel(context, httpContextAccessor, helper);
            _v.Page.Active = "Training";
            _v.Auth = Auth();
            _webHost = webHost;


        }

        public class WeekDay
        {
            public int Id { get; set; }
            public string Name { get; set; }
        }

        public IActionResult AcademicDegree()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Academic Degree";
            _v.TqualCodes = _context.TqualCodes.ToList();

            return View(_v);

        }
        [HttpPost]
        public IActionResult CreateAcademicDegree(int id, TqualCode tqual)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    if (!Can("Training-admin|Training-department"))
                        return StatusCode(403);

                    //Insert
                    if (id == 0 || id == null)
                    {

                        if (_context.TqualCodes.Count() == 0)
                        {
                            tqual.QualCode = 1001;
                        }
                        else
                        {
                            tqual.QualCode = _context.TqualCodes.Max(m => m.QualCode) + 1;
                        }


                        var NewTqualCode = new TqualCode
                        {
                            QualCode = tqual.QualCode,
                            QualDespA = tqual.QualDespA,
                            QualDespE = tqual.QualDespE,
                            QualHierLevel = tqual.QualHierLevel
                        };

                        _context.TqualCodes.Add(NewTqualCode);
                        _context.SaveChanges();


                    }
                    //Update
                    else
                    {
                        _context.TqualCodes.Update(tqual);
                        _context.SaveChanges();

                    }
                    return Json(new
                    {
                        success = true,
                        message = new List<string> { _("Academic Degree Created Successfully") },
                        action = "reload",
                    });
                }

                var response = new
                {
                    success = false,
                    message = ValidateErrors(ModelState),
                    action = "",
                };

                return Json(response);

            }
            catch (Exception ex)
            {

                return RedirectToAction("Errorpage404", "Account");
            }
        }

        public IActionResult ViweAcademicDegreeRecord(int id)
        {
            if (id == null)
            {
                return NotFound();
            }

            _v.TqualCode = _context.TqualCodes
                .Where(g => g.QualCode == id)
                .FirstOrDefault();

            if (_v.TqualCode == null)
            {
                return NotFound();
            }



            return View(_v);

        }

        public IActionResult University()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "University";
            _v.TunivInstCodes = _context.TunivInstCodes.ToList();

            return View(_v);

        }

        public IActionResult ViweUniversityRecord(int id)
        {
            if (id == null)
            {
                return NotFound();
            }

            _v.TunivInstCode = _context.TunivInstCodes
                .Where(g => g.UnivInstCode == id)
                .FirstOrDefault();

            if (_v.TunivInstCode == null)
            {
                return NotFound();
            }



            return View(_v);

        }

        [HttpPost]
        public IActionResult CreateUniversity(int id, TunivInstCode tunivCode)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    if (!Can("Training-admin|Training-department"))
                        return StatusCode(403);

                    //Insert
                    if (id == 0 || id == null)
                    {

                        if (_context.TunivInstCodes.Count() == 0)
                        {
                            tunivCode.UnivInstCode = 1001;
                        }
                        else
                        {
                            tunivCode.UnivInstCode = _context.TunivInstCodes.Max(m => m.UnivInstCode) + 1;
                        }


                        var NewTunivInstCode = new TunivInstCode
                        {
                            UnivInstCode = tunivCode.UnivInstCode,
                            UnivInstDespA = tunivCode.UnivInstDespA,
                            UnivInstDespE = tunivCode.UnivInstDespE,
                            UnivInstPlace = tunivCode.UnivInstPlace,
                            CountryCode = tunivCode.CountryCode,
                        };

                        _context.TunivInstCodes.Add(NewTunivInstCode);
                        _context.SaveChanges();


                    }
                    //Update
                    else
                    {
                        _context.TunivInstCodes.Update(tunivCode);
                        _context.SaveChanges();

                    }
                    return Json(new
                    {
                        success = true,
                        message = new List<string> { _("University Created Successfully") },
                        action = "reload",
                    });
                }

                var response = new
                {
                    success = false,
                    message = ValidateErrors(ModelState),
                    action = "",
                };

                return Json(response);

            }
            catch (Exception ex)
            {

                return RedirectToAction("Errorpage404", "Account");
            }
        }

        public IActionResult EmpRequest()
        {
            _v.Page.Active = "";
            _v.Page.Reload = true;
            _v.Page.Title = "Training Request";

            _v.TqualReqs = _context.TqualReqs
               .Include(a => a.TqualCode).Include(x => x.TstudyType).Include(x => x.TSubjectCode).Include(x => x.TcountryCode)
               .Where(x => x.EmpNo == _v.Profile.EmpNo && x.CancelFlag == 0).ToList();


            var EmpCurrQual = _context.EmpMaxOualDets.FirstOrDefault(x => x.EmpNo == _v.Profile.EmpNo);
            ViewBag.CurrentQual = null;
            if (EmpCurrQual != null)
            {
                ViewBag.CurrentQual = EmpCurrQual.QualDespA;
            }



            ViewBag.StudyType = new SelectList(_context.TstudyTypes.OrderBy(x => x.StudyTypeDespA).ToList(), "StudyType", "StudyTypeDespA");
            ViewBag.FieldofStudy = new SelectList(_context.TSubjectCodes.OrderBy(x => x.SubjDespA).ToList(), "SubjCode", "SubjDespA");
            ViewBag.QualType = new SelectList(_context.TqualCodes.Where(a => a.QualHierLevel < EmpCurrQual.QualHierLevel).OrderBy(x => x.QualDespA).ToList(), "QualCode", "QualDespA");
            ViewBag.countryCode = new SelectList(_context.TcountryCode.OrderBy(x => x.CountryDespA).ToList(), "CountryCode", "CountryDespA");
            ViewBag.univInstCode = new SelectList(_context.TunivInstCodes.OrderBy(x => x.UnivInstDespA).ToList(), "UnivInstCode", "UnivInstDespA");


            return View(_v);
        }

        [HttpPost]
        public IActionResult EmpRequest(int id, TqualReq tqual)
        {
            try
            {
                if (!IsValid(ModelState))
                {
                    var response = new
                    {
                        success = false,
                        message = ValidateErrors(ModelState),
                        action = "",
                    };
                    return Json(response);
                }


                //If Request Is Exit 
                var chaeck = _context.TqualReqs.FirstOrDefault(x => x.EmpNo == _v.Profile.EmpNo && x.CancelFlag == 0);

                if (chaeck != null)
                {
                    return Json(new
                    {
                        success = false,
                        message = new List<string> { "Request with same Request already exists!" },
                        action = "",
                    });
                }

                //Save Data into Dataabse
                var InmailNo = DateTime.Now.Month.ToString() + DateTime.Now.Day.ToString();
                //Save To Database (Tincoming_Mail)
                TincomingMail tincoming = new TincomingMail();
                tincoming.InYear = Convert.ToInt32(DateTime.Now.ToString("yy"));
                tincoming.InDeptInd = 1;
                tincoming.InMailNo = Convert.ToInt32(InmailNo);
                tincoming.LastDocSlNo = DoclNo(tincoming.InYear, tincoming.InMailNo);
                tincoming.LetterDate = DateTime.Now;
                tincoming.LetterRefNo = DateTime.Now.Year;
                tincoming.RecdDate = DateTime.Now;
                tincoming.RecdTime = DateTime.Now;
                tincoming.DocType = 2;
                tincoming.LetterSubj = "طلب تأهيل";
                _context.TincomingMails.Add(tincoming);


                tqual.InYear = tincoming.InYear;
                tqual.InDeptInd = tincoming.InDeptInd;
                tqual.InMailNo = tincoming.InMailNo;
                tqual.InDocSlNo = tincoming.LastDocSlNo;
                tqual.UnitCode = 1;
                tqual.EmpNo = _v.Profile.EmpNo;
                tqual.TransType = 151;
                tqual.TxnDate = DateTime.Now;
                tqual.UserId = _v.Profile.EmpNo.ToString();
                tqual.TimeStamp = DateTime.Now;
                tqual.ReqStat = "Pending";
                _context.TqualReqs.Add(tqual);
                _context.SaveChanges();

                return Json(new
                {
                    success = true,
                    message = new List<string> { "Request created successfully." },
                    action = "reload",
                });

            }
            catch (Exception ex)
            {

                return RedirectToAction("Errorpage404", "Account");
            }



        }

        public int DoclNo(int InYear, int InMailNo)
        {
            var req = _context.TincomingMails.Where(x => x.InYear == InYear && x.InMailNo == InMailNo).Max(m => m.LastDocSlNo);
            if (req == null || req == 0)
            {
                return 1;
            }
            else
            {
                return (int)req + 1;
            }

        }
        public IActionResult GetUinversity(int value)
        {

            try
            {

                // ------- Getting Data from Database Using EntityFrameworkCore -------
                List<SelectListItem> Items = _context.TunivInstCodes
                                  .Where(c => c.CountryCode == value)
                                     .Select(c => new SelectListItem
                                     {
                                         Value = c.UnivInstCode.ToString(),
                                         Text = c.UnivInstDespA
                                     }).OrderBy(x => x.Text).ToList();


                Items.Insert(0, new SelectListItem()
                {
                    Value = "",
                    Text = "--- select Items ---"
                });
                return Json(Items);


            }
            catch (Exception)
            {

                return null;
            }

        }

        public IActionResult ManagerApproval()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            try
            {
                _v.Page.Reload = true;

                _v.Page.Title = "Training request";
                _v.Page.Active = "ManagerApproval";

                _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

                var fromQueryString = HttpContext.Request.Query["from"].ToString();
                var toQueryString = HttpContext.Request.Query["to"].ToString();


                if (!string.IsNullOrEmpty(fromQueryString))
                {
                    if (DateTime.TryParse(fromQueryString, out DateTime from))
                    {
                        _v.Page.Filter.DateFrom = from;
                    }
                }

                if (!string.IsNullOrEmpty(toQueryString))
                {
                    if (DateTime.TryParse(toQueryString, out DateTime to))
                    {
                        to = to.Date.Add(new TimeSpan(23, 59, 59));
                        _v.Page.Filter.DateTo = to;
                    }
                }

                var ReqList = _context.TqualReqs
                    .Include(a => a.TqualCode).Include(x => x.TstudyType).Include(x => x.TSubjectCode).Include(x => x.TcountryCode)
                    .Where(m => m.MangAppId == _v.Profile.EmpNo && m.TimeStamp >= _v.Page.Filter.DateFrom && m.TimeStamp <= _v.Page.Filter.DateTo && m.CancelFlag == 0)
                    .ToList();

                _v.TqualReqs = ReqList;

                return View(_v);
            }
            catch (Exception ex)
            {

                return RedirectToAction("Errorpage404", "Account");
            }


        }

        public IActionResult MangerDecline(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, string DeclineNote, int EmpNo)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            var request = _context.TqualReqs.FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.EmpNo == EmpNo && a.CancelFlag == 0);
            if (request != null)
            {

                request.ApprovalDate = DateTime.Now;
                request.ApprovalRem = DeclineNote;
                request.MandAprrStat = 2;
                request.ReqStat = "Decline by Manager";
                _context.TqualReqs.Update(request);
                _context.SaveChanges();

                //_notyf.Success("Your request has been Decline successfully", 10);

            }

            return Json(new
            {
                success = true,
                message = new List<string> { "Your request has been Rejected successfully" },
                action = "reload",
            });

        }

        public IActionResult MangerApprove(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, int EmpNo)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);


            var requestToUpdate = _context.TqualReqs.FirstOrDefault(a => a.MangAppId == _v.Profile.EmpNo && a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.CancelFlag == 0);
            if (requestToUpdate != null)
            {

                if (requestToUpdate == null)
                    return StatusCode(404);

                requestToUpdate.ApprovalDate = DateTime.Now;
                requestToUpdate.MandAprrStat = 1;
                requestToUpdate.ReqStat = "Sign by Manager";

                _context.TqualReqs.Update(requestToUpdate);
                _context.SaveChanges();


            }
            return RedirectToAction("ManagerApproval");
            //return Json(new
            //{
            //    success = true,
            //    message = new List<string> { "Your request has been Approve successfully" },
            //    action = "reload",
            //});
        }


        public IActionResult DGManagerApproval()
        {

            if (!Can("Training-admin|Training-DG"))
                return StatusCode(403);

            try
            {
                _v.Page.Reload = true;

                _v.Page.Title = "Training request";
                _v.Page.Active = "DGManagerApproval";

                _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

                var fromQueryString = HttpContext.Request.Query["from"].ToString();
                var toQueryString = HttpContext.Request.Query["to"].ToString();


                if (!string.IsNullOrEmpty(fromQueryString))
                {
                    if (DateTime.TryParse(fromQueryString, out DateTime from))
                    {
                        _v.Page.Filter.DateFrom = from;
                    }
                }

                if (!string.IsNullOrEmpty(toQueryString))
                {
                    if (DateTime.TryParse(toQueryString, out DateTime to))
                    {
                        to = to.Date.Add(new TimeSpan(23, 59, 59));
                        _v.Page.Filter.DateTo = to;
                    }
                }

                var ReqList = _context.TqualReqs
                    .Include(a => a.TqualCode).Include(x => x.TstudyType).Include(x => x.TSubjectCode).Include(x => x.TcountryCode)
                    .Where(m => m.SectionStat == 1 && m.TimeStamp >= _v.Page.Filter.DateFrom && m.TimeStamp <= _v.Page.Filter.DateTo && m.CancelFlag == 0)
                    .ToList();

                _v.TqualReqs = ReqList;

                return View(_v);
            }
            catch (Exception ex)
            {

                return RedirectToAction("Errorpage404", "Account");
            }


        }

        public IActionResult DGMangerDecline(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, string DeclineNote, int EmpNo)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            var request = _context.TqualReqs.FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.EmpNo == EmpNo && a.CancelFlag == 0);
            if (request != null)
            {

                request.DGMangAprrDate = DateTime.Now;
                request.DGMangAprrRem = DeclineNote;
                request.DGMangStat = 2;
                request.ReqStat = "Decline by DG Manager";
                request.DGMangAprrId = _v.Profile.EmpNo;
                _context.TqualReqs.Update(request);
                _context.SaveChanges();

                //_notyf.Success("Your request has been Decline successfully", 10);

            }

            return Json(new
            {
                success = true,
                message = new List<string> { "Your request has been Rejected successfully" },
                action = "reload",
            });

        }

        public IActionResult DGMangerApprove(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, int EmpNo)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);


            var requestToUpdate = _context.TqualReqs.FirstOrDefault(a => a.SectionStat == 1 && a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.CancelFlag == 0);
            if (requestToUpdate != null)
            {

                if (requestToUpdate == null)
                    return StatusCode(404);

                requestToUpdate.DGMangAprrDate = DateTime.Now;
                requestToUpdate.DGMangStat = 1;
                requestToUpdate.DGMangAprrId = _v.Profile.EmpNo;
                requestToUpdate.ReqStat = "Sign by DG Manager";
                _context.TqualReqs.Update(requestToUpdate);
                _context.SaveChanges();


            }
            return RedirectToAction("DGManagerApproval");

        }

        public IActionResult StudyApproval()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            try
            {
                _v.Page.Reload = true;

                _v.Page.Title = "Study Approval";
                _v.Page.Active = "StudyApproval";

                _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

                var fromQueryString = HttpContext.Request.Query["from"].ToString();
                var toQueryString = HttpContext.Request.Query["to"].ToString();


                if (!string.IsNullOrEmpty(fromQueryString))
                {
                    if (DateTime.TryParse(fromQueryString, out DateTime from))
                    {
                        _v.Page.Filter.DateFrom = from;
                    }
                }

                if (!string.IsNullOrEmpty(toQueryString))
                {
                    if (DateTime.TryParse(toQueryString, out DateTime to))
                    {
                        to = to.Date.Add(new TimeSpan(23, 59, 59));
                        _v.Page.Filter.DateTo = to;
                    }
                }

                var ReqList = _context.TqualReqs
                    .Include(a => a.TqualCode).Include(x => x.TstudyType).Include(x => x.TSubjectCode).Include(x => x.TcountryCode)
                    .Where(m => m.TimeStamp >= _v.Page.Filter.DateFrom && m.TimeStamp <= _v.Page.Filter.DateTo && m.CancelFlag == 0 && m.MandAprrStat != 0)
                    .ToList();

                _v.TqualReqs = ReqList;

                return View(_v);
            }
            catch (Exception ex)
            {

                return RedirectToAction("Errorpage404", "Account");
            }


        }

        public IActionResult SectionApproval(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, int EmpNo)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);


            var requestToUpdate = _context.TqualReqs
                .FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.CancelFlag == 0 && a.MandAprrStat == 1);


            if (requestToUpdate != null)
            {

                if (requestToUpdate == null)
                    return StatusCode(404);

                requestToUpdate.SectionAprrDate = DateTime.Now;
                requestToUpdate.SectionAprrId = _v.Profile.EmpNo;
                requestToUpdate.SectionStat = 1;
                requestToUpdate.ReqStat = "Sign by Section";

                _context.TqualReqs.Update(requestToUpdate);
                _context.SaveChanges();
            }

            return RedirectToAction("StudyApproval");



        }

        public IActionResult SectionDecline(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, string DeclineNote, int EmpNo)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            var request = _context.TqualReqs.FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.EmpNo == EmpNo && a.CancelFlag == 0);
            if (request != null)
            {

                request.SectionAprrDate = DateTime.Now;
                request.SectionAprrRem = DeclineNote;
                request.SectionStat = 2;
                request.SectionAprrId = _v.Profile.EmpNo;
                request.ReqStat = "Decline by Section";

                _context.TqualReqs.Update(request);
                _context.SaveChanges();

                //_notyf.Success("Your request has been Decline successfully", 10);

            }

            return Json(new
            {
                success = true,
                message = new List<string> { "Your request has been Rejected successfully" },
                action = "reload",
            });

        }


        public IActionResult AffairsCommitteeApproval()
        {

            if (!Can("Training-admin|Affairs-Committee"))
                return StatusCode(403);

            try
            {
                _v.Page.Reload = true;

                _v.Page.Title = "Training request";
                _v.Page.Active = "DGManagerApproval";

                _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

                var fromQueryString = HttpContext.Request.Query["from"].ToString();
                var toQueryString = HttpContext.Request.Query["to"].ToString();


                if (!string.IsNullOrEmpty(fromQueryString))
                {
                    if (DateTime.TryParse(fromQueryString, out DateTime from))
                    {
                        _v.Page.Filter.DateFrom = from;
                    }
                }

                if (!string.IsNullOrEmpty(toQueryString))
                {
                    if (DateTime.TryParse(toQueryString, out DateTime to))
                    {
                        to = to.Date.Add(new TimeSpan(23, 59, 59));
                        _v.Page.Filter.DateTo = to;
                    }
                }

                var ReqList = _context.TqualReqs
                    .Include(a => a.TqualCode).Include(x => x.TstudyType).Include(x => x.TSubjectCode).Include(x => x.TcountryCode)
                    .Where(m => m.DGMangStat == 1 && m.TimeStamp >= _v.Page.Filter.DateFrom && m.TimeStamp <= _v.Page.Filter.DateTo && m.CancelFlag == 0)
                    .ToList();

                _v.TqualReqs = ReqList;

                return View(_v);
            }
            catch (Exception ex)
            {

                return RedirectToAction("Errorpage404", "Account");
            }


        }
        public IActionResult AffairsCommitteeDecline(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, string DeclineNote, int EmpNo)
        {
            if (!Can("Training-admin|Affairs-Committee"))
                return StatusCode(403);

            var request = _context.TqualReqs.FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.EmpNo == EmpNo && a.CancelFlag == 0);
            if (request != null)
            {

                request.AffairsCommitteeAprrDate = DateTime.Now;
                request.AffairsCommitteeAprrRem = DeclineNote;
                request.AffairsCommitteeStat = 2;
                request.AffairsCommitteeId = _v.Profile.EmpNo;
                request.ReqStat = "Decline by Affairs Committee Manager";

                _context.TqualReqs.Update(request);
                _context.SaveChanges();

                //_notyf.Success("Your request has been Decline successfully", 10);

            }

            return Json(new
            {
                success = true,
                message = new List<string> { "Your request has been Rejected successfully" },
                action = "reload",
            });

        }

        public IActionResult AffairsCommitteeApprove(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, int EmpNo)
        {
            if (!Can("Training-admin|Affairs-Committee"))
                return StatusCode(403);


            var requestToUpdate = _context.TqualReqs.FirstOrDefault(a => a.SectionStat == 1 && a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.CancelFlag == 0);
            if (requestToUpdate != null)
            {

                if (requestToUpdate == null)
                    return StatusCode(404);

                requestToUpdate.AffairsCommitteeAprrDate = DateTime.Now;
                requestToUpdate.AffairsCommitteeStat = 1;
                requestToUpdate.AffairsCommitteeId = _v.Profile.EmpNo;
                requestToUpdate.ReqStat = "Sign by Affairs Committee Manager";
                _context.TqualReqs.Update(requestToUpdate);
                _context.SaveChanges();


            }
            return RedirectToAction("AffairsCommitteeApproval");

        }

        public IActionResult StudentDeatils(int InYear, int InMailNo, int InDeptInd, int InDocSlNo)
        {
            // if (!Can("Training-Student"))
            //   return StatusCode(403);

            _v.Page.Class = " sidebar-xs ";
            _v.Page.Reload = true;
            _v.Page.Back = $"/Training/EmpRequest";

            _v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Training", Url=$"/Training/EmpRequest"},
             new Breadcrumb {Label="View", Url=""},
        };

            if (InYear == null || InYear == 0 || InMailNo == null || InMailNo == 0 || InDeptInd == null || InDeptInd == 0 || InDocSlNo == null || InDocSlNo == 0)
            {
                return NotFound();
            }

            var TqualReqs = _context.TqualReqs
                .Include(a => a.TqualCode).Include(x => x.TstudyType).Include(x => x.TSubjectCode).Include(x => x.TcountryCode).Include(x => x.TunivInstCode)
                .Where(x => x.InYear == InYear && x.InMailNo == InMailNo && x.InDocSlNo == InDocSlNo && x.InDeptInd == InDeptInd).FirstOrDefault();

            var TqualEmpAttendances = _context.TqualEmpAttendances.Where(x => x.EmpNo == TqualReqs.EmpNo).ToList();
            var TqualStudyReport = _context.TqualStudyReports.Where(x => x.EmpNo == TqualReqs.EmpNo).ToList();


            List<WeekDay> weekDays = new List<WeekDay>
            {
                new WeekDay {Id=1, Name="Sunday" },
                new WeekDay {Id=2, Name="Monday" },
                new WeekDay {Id=3, Name="Tuesday" },
                new WeekDay {Id=4, Name="Wednesday" },
                new WeekDay {Id=5, Name="Thursday" },
                new WeekDay {Id=6, Name="Friday" },
                new WeekDay {Id=7, Name="Saturday" },

            };


            List<WeekDay> Year = new List<WeekDay>
            {
                new WeekDay {Id=DateTime.Now.Year, Name=DateTime.Now.Year.ToString() },
                new WeekDay {Id=DateTime.Now.Year-1, Name=(DateTime.Now.Year-1).ToString() },
            };


            if (TqualReqs == null)
            {
                return NotFound();
            }

            _v.TqualReq = TqualReqs;
            _v.tqualStudyReports = TqualStudyReport;
            _v.TqualEmpAttendances = TqualEmpAttendances;
            ViewBag.Day = new SelectList(weekDays, "Id", "Name");
            ViewBag.Year = new SelectList(Year, "Id", "Name");


            return View(_v);

        }

        public IActionResult UpdateStudyPlan(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, TqualReq reqs)
        {

            try
            {

                var qualReqs = _context.TqualReqs
                    .Where(x => x.InYear == InYear && x.InMailNo == InMailNo && x.InDocSlNo == InDocSlNo && x.InDeptInd == InDeptInd).FirstOrDefault();


                if (qualReqs != null)
                {
                    qualReqs.UnivInstCode = reqs.UnitCode;


                }



                _context.SaveChanges();


                var response = new
                {
                    success = true,
                    message = new List<string> { "Request updated successfully." },
                    action = "reload",
                };


                return Json(response);
            }
            catch (Exception ex)
            {

                var response = new
                {
                    success = false,
                    message = new List<string> { $"Error: {ex.Message}" },
                    action = "",
                };

                return Json(response);
            }



        }

        [HttpPost]
        public IActionResult UpdateEmpAttend(int id, TqualEmpAttendance empAttendance)
        {
            try
            {
                if (!IsValid(ModelState))
                {
                    var response = new
                    {
                        success = false,
                        message = ValidateErrors(ModelState),
                        action = "",
                    };

                    return Json(response);
                }


                if (!Can("Training-admin|Training-department"))
                    return StatusCode(403);
                //Insert
                if (id == 0 || id == null)
                {
                    //If Emp Attend Exit 
                    var checkcontr = _context.TqualEmpAttendances.Where(z => z.EmpNo == empAttendance.EmpNo && (empAttendance.FromDate >= z.FromDate && empAttendance.FromDate <= z.ToDate || empAttendance.ToDate >= z.FromDate && empAttendance.ToDate <= z.ToDate) && z.Day == empAttendance.Day);
                    if (checkcontr.Count() >= 1)
                    {
                        return Json(new
                        {
                            success = false,
                            message = new List<string> { "Request with same day range already exists!" },
                            action = "",
                        });
                    }

                    if (_context.TqualEmpAttendances.Count() == 0)
                    {
                        empAttendance.Id = 1;
                    }
                    else
                    {
                        empAttendance.Id = _context.TqualEmpAttendances.Max(m => m.Id) + 1;
                    }


                    var NewempAttendance = new TqualEmpAttendance
                    {
                        Id = empAttendance.Id,
                        EmpNo = empAttendance.EmpNo,
                        FromDate = empAttendance.FromDate,
                        ToDate = empAttendance.ToDate,
                        Day = empAttendance.Day,
                        TimeFrom = empAttendance.TimeFrom,
                        TimeTo = empAttendance.TimeTo,
                    };

                    _context.TqualEmpAttendances.Add(NewempAttendance);
                    _context.SaveChanges();
                    return Json(new
                    {
                        success = true,
                        message = new List<string> { "Request created successfully." },
                        action = "reload",
                    });

                }
                else
                {
                    //To Update Recored
                    var checkcontr = _context.TqualEmpAttendances.Where(z => (empAttendance.FromDate >= z.FromDate && empAttendance.FromDate <= z.ToDate || empAttendance.ToDate >= z.FromDate && empAttendance.ToDate <= z.ToDate) && z.Day == empAttendance.Day && z.Id != id);
                    if (checkcontr.Count() >= 1)
                    {
                        return Json(new
                        {
                            success = false,
                            message = new List<string> { "Request with same day range already exists!" },
                            action = "",
                        });
                    }

                    var AttenedUpdate = _context.TqualEmpAttendances.FirstOrDefault(x => x.Id == id);

                    AttenedUpdate.FromDate = empAttendance.FromDate;
                    AttenedUpdate.ToDate = empAttendance.ToDate;
                    AttenedUpdate.TimeFrom = empAttendance.TimeFrom;
                    AttenedUpdate.TimeTo = empAttendance.TimeTo;
                    AttenedUpdate.Day = empAttendance.Day;



                    // Save the changes
                    _context.Entry(AttenedUpdate).State = EntityState.Modified;
                    _context.SaveChanges();
                    return Json(new
                    {
                        success = true,
                        message = new List<string> { "Request created successfully." },
                        action = "reload",
                    });

                }
            }
            catch (Exception ex)
            {

                var response = new
                {
                    success = false,
                    message = new List<string> { $"Error: {ex.Message}" },
                    action = "",
                };

                return Json(response);
            }

        }

        [HttpPost]
        public IActionResult UpdateStudyReport(int id, TqualStudyReport studyReport)
        {
            try
            {
                if (!IsValid(ModelState))
                {
                    var response = new
                    {
                        success = false,
                        message = ValidateErrors(ModelState),
                        action = "",
                    };

                    return Json(response);
                }

                if (!Can("Training-admin|Training-department"))
                    return StatusCode(403);

                //If Emp Study Report Exit 
                var checkcontr = _context.TqualStudyReports.Where(z => z.EmpNo == studyReport.EmpNo && z.Year == studyReport.Year && z.Semester == studyReport.Semester);
                if (checkcontr.Count() >= 1)
                {
                    return Json(new
                    {
                        success = false,
                        message = new List<string> { "Request with same day range already exists!" },
                        action = "",
                    });
                }

                //Insert Into Database if not exit
                //studyReport.EmpNo = _v.Profile.EmpNo;

                _context.TqualStudyReports.Add(studyReport);
                _context.SaveChanges();

                return Json(new
                {
                    success = true,
                    message = new List<string> { "Request created successfully." },
                    action = "reload",
                });



            }
            catch (Exception ex)
            {

                var response = new
                {
                    success = false,
                    message = new List<string> { $"Error: {ex.Message}" },
                    action = "",
                };

                return Json(response);
            }
        }

        public IActionResult DocumentDecline(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, string DeclineNote, int EmpNo, int InitialAcceptanceAppr)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            var request = _context.TqualReqs.FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.EmpNo == EmpNo && a.CancelFlag == 0);
            if (request != null)
            {

                if (InitialAcceptanceAppr != null && InitialAcceptanceAppr != 0)
                {
                    request.InitialAcceptanceAppr = 2;
                    request.InitialAcceptanceDate = DateTime.Now;
                    request.InitialAcceptanceRem = DeclineNote;
                }

                _context.TqualReqs.Update(request);
                _context.SaveChanges();


            }

            return Json(new
            {
                success = true,
                message = new List<string> { "Your request has been Rejected successfully" },
                action = "reload",
            });

        }

        public IActionResult DocumentApprove(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, int EmpNo, int InitialAcceptanceAppr)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);


            var requestToUpdate = _context.TqualReqs.FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.CancelFlag == 0);
            if (requestToUpdate != null)
            {

                if (requestToUpdate == null)
                    return StatusCode(404);


                if (InitialAcceptanceAppr != null && InitialAcceptanceAppr != 0)
                {
                    requestToUpdate.InitialAcceptanceAppr = 1;
                    requestToUpdate.InitialAcceptanceDate = DateTime.Now;

                }

                _context.TqualReqs.Update(requestToUpdate);
                _context.SaveChanges();


            }
            return RedirectToAction("StudentDeatils", new { requestToUpdate.InYear, requestToUpdate.InMailNo, requestToUpdate.InDeptInd, requestToUpdate.InDocSlNo });

        }

        public IActionResult DecitionData(int InYear, int InMailNo, int InDeptInd, int InDocSlNo, string DecisionNo, DateTime DecisionDate, string DecisionNote)
        {
            try
            {


                if (!Can("Training-admin|Training-department"))
                    return StatusCode(403);


                var requestToUpdate = _context.TqualReqs.FirstOrDefault(a => a.InYear == InYear && a.InDeptInd == InDeptInd && a.InMailNo == InMailNo && a.InDocSlNo == InDocSlNo && a.CancelFlag == 0);
                if (requestToUpdate != null)
                {

                    if (requestToUpdate == null)
                        return StatusCode(404);



                    requestToUpdate.DecisionNo = DecisionNo;
                    requestToUpdate.DecisionDate = DecisionDate;
                    requestToUpdate.DecisionNote = DecisionNote;
                    requestToUpdate.DecisionEmpNo = _v.Profile.EmpNo;


                    _context.TqualReqs.Update(requestToUpdate);
                    _context.SaveChanges();


                }
                var response = new
                {
                    success = true,
                    message = new List<string> { "Request updated successfully." },
                    action = "reload",
                };


                return Json(response);
            }
            catch (Exception ex)
            {

                var response = new
                {
                    success = false,
                    message = new List<string> { $"Error: {ex.Message}" },
                    action = "",
                };

                return Json(response);
            }
        }

        public IActionResult TrainingAndQualificationLaw()
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Training And Qualification Law";


            return View(_v);


        }

        public IActionResult ViewAttendanceRecord(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            _v.TqualEmpAttendance = _context.TqualEmpAttendances
                .Where(g => g.Id == id)
                .FirstOrDefault();

            if (_v.TqualEmpAttendance == null)
            {
                return NotFound();
            }
            List<WeekDay> weekDays = new List<WeekDay>
            {
                new WeekDay {Id=1, Name="Sunday" },
                new WeekDay {Id=2, Name="Monday" },
                new WeekDay {Id=3, Name="Tuesday" },
                new WeekDay {Id=4, Name="Wednesday" },
                new WeekDay {Id=5, Name="Thursday" },
                new WeekDay {Id=6, Name="Friday" },
                new WeekDay {Id=7, Name="Saturday" },

            };
            ViewBag.Day = new SelectList(weekDays, "Id", "Name", _v.TqualEmpAttendance.Day);


            return View(_v);
        }

        public IActionResult CourseSupplier()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Course Supplier";
            _v.TcourseSuppliers = _context.TcourseSuppliers.ToList();

            return View(_v);
        }

        [HttpPost]
        public IActionResult CreateCourseSupplier(int id, TcourseSupplier tsupplier)
        {

            if (ModelState.IsValid)
            {
                if (!Can("Training-admin|Training-department"))
                    return StatusCode(403);


                if (id == 0 || id == null)
                {

                    if (_context.TcourseSuppliers.Count() == 0)
                    {
                        tsupplier.CourseSupplerCode = 1001;
                    }
                    else
                    {

                        tsupplier.CourseSupplerCode = _context.TcourseSuppliers.Max(m => m.CourseSupplerCode) + 1;
                    }


                    var Newtsupplier = new TcourseSupplier
                    {
                        CourseSupplerCode = tsupplier.CourseSupplerCode,
                        CourseSupplierName_E = tsupplier.CourseSupplierName_E,
                        CourseSupplierName_A = tsupplier.CourseSupplierName_A,
                        SupplierAddress = tsupplier.SupplierAddress,
                        ContactPersonName_A = tsupplier.ContactPersonName_A,
                        PhoneNO = tsupplier.PhoneNO,
                        UserId = _v.Profile.EmailId,
                    };

                    _context.TcourseSuppliers.Add(Newtsupplier);
                    _context.SaveChanges();


                }
                //Update
                else
                {
                    _context.TcourseSuppliers.Update(tsupplier);
                    _context.SaveChanges();

                }
                return Json(new
                {
                    success = true,
                    message = new List<string> { _("Academic Degree Created Successfully") },
                    action = "reload",
                });
            }

            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);


        }

        public IActionResult CourseCatalogue()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Course Catalogue";

            var course = _context.TcourseCatalogue.Include(d => d.TcourseCatCode).Include(a => a.TcourseType).Include(F => F.TtrgPlaceCode).ToList();
            _v.TcourseCatalogue = course;
            _v.TcourseType = _db.TcourseType.ToList();
            _v.TcourseCatCode = _db.TcourseCatCode.ToList();
            _v.TtrgPlaceCode = _db.TtrgPlaceCode.ToList();
            _v.TcourseCatalogue = _db.TcourseCatalogue.ToList();


            return View(_v);
        }

        [HttpPost]
        public IActionResult CreateCourseCatalogue(int id, TcourseCatalogue catalogue)
        {
            var course = _context.TcourseCatalogue.Include(d => d.TcourseCatCode).Include(a => a.TcourseType).Include(F => F.TtrgPlaceCode).ToList();

            if (ModelState.IsValid)
            {
                if (!Can("Training-admin|Training-department"))
                    return StatusCode(403);


                if (id == 0 || id == null)
                {

                    if (_context.TcourseCatalogue.Count() == 0)
                    {
                        catalogue.CourseCode = 1001;
                    }
                    else
                    {

                        catalogue.CourseCode = _context.TcourseCatalogue.Max(m => m.CourseCode) + 1;
                    }


                    var NewtCatalogue = new TcourseCatalogue
                    {
                        CourseCode = catalogue.CourseCode,
                        SubjCode = 0,
                        CourseNameA = catalogue.CourseNameA,
                        CourseNameE = catalogue.CourseNameE,
                        TimeStamp = catalogue.TimeStamp,
                        UserId = _v.Profile.EmailId,
                        CourseCatCode = catalogue.CourseCatCode,
                        CourseType = catalogue.CourseType,
                        TrgPlaceCode = catalogue.TrgPlaceCode,
                        Fees = catalogue.Fees,
                        ClassOnline = catalogue.ClassOnline,
                    };

                    _context.TcourseCatalogue.Add(NewtCatalogue);
                    _context.SaveChanges();


                }
                //Update
                else
                {
                    _context.TcourseCatalogue.Update(catalogue);
                    _context.SaveChanges();

                }
                return Json(new
                {
                    success = true,
                    message = new List<string> { _("Course Created Successfully") },
                    action = "reload",
                });
            }

            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);


        }

        public IActionResult CourseReq()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Course Request";


            _v.VempDtl = _db.VempDtl.ToList();





            return View(_v);
        }

        [HttpPost]

        public async Task<IActionResult> CourseReq(int id, CourseReqForm post)
        {


            if (id == 0 || id == null)
            {

                if (_context.TempTrgReq.Count() == 0)
                {
                    post.TempTrgReq.ReqNo = 1001;
                }
                else
                {

                    post.TempTrgReq.ReqNo = _context.TempTrgReq.Max(m => m.ReqNo) + 1;
                }



            }
            var CourseReq = new TempTrgReq
            {
                ReqNo = post.TempTrgReq.ReqNo,
                CourseEndDate = post.TempTrgReq.CourseEndDate,
                CourseName = post.TempTrgReq.CourseName,
                CourseStartDate = post.TempTrgReq.CourseStartDate,
                Fee = post.TempTrgReq.Fee,
                Year = DateTime.Now.Year,
                ReqDate = DateTime.Now,
                BossNo = _v.Profile.EmpNameA,
                Place = post.TempTrgReq.Place,
                Approval = post.TempTrgReq.Approval,
                ApprovalDate = DateTime.Now,
            };



            _context.TempTrgReq.Add(CourseReq);
            await _context.SaveChangesAsync();

            if (post.TempTrgReq.File != null && post.TempTrgReq.File.Length > 0)
            {
                //var requpdate = _context.TempTrgReq.Find(CourseReq.ReqNo)
                var FileGuid = await _h.UploadAsync(post.TempTrgReq.File);
                CourseReq.FileGuid = FileGuid;
                _context.Update(CourseReq);
                await _context.SaveChangesAsync();

            }


            foreach (var item in post.TempReqDtls)
            {
                var nid = _context.TempReqDtls.Max(m => m.Id) + 1;
                var CourseReqDtl = new TempReqDtls
                {
                    Id = nid,
                    EmpNo = item.EmpNo,
                    ReqNo = CourseReq.ReqNo,
                    StartDate = CourseReq.CourseStartDate,
                    EndDate = CourseReq.CourseEndDate,

                };

                _context.TempReqDtls.Add(CourseReqDtl);
                await _context.SaveChangesAsync();


            }



            return Json(new
            {
                success = true,
                message = new List<string> { _("Course Created Successfully") },
                action = "reload",
            });




        }
        public IActionResult CourseApproval()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Course Approval";



            _v.TempTrgReq = _db.TempTrgReq.ToList();
            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();




            return View(_v);
        }

        public IActionResult CourseApprovalEmp(int id)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Course Approval";



            _v.TempReqDtls = _db.TempReqDtls.Where(C => C.ReqNo == id).ToList();
            _v.VempDtls = _db.VempDtls.ToList();
            _v.TempTrgReq = _db.TempTrgReq.Where(C => C.ReqNo == id).ToList();


            return View(_v);
        }

        public IActionResult AddCourse()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Add Course";

            _v.TcourseCatalogue = _db.TcourseCatalogue.ToList();
            _v.TcourseSuppliers = _db.TcourseSuppliers.ToList();
            _v.TcountryCodes = _db.TcountryCode.ToList();
            _v.VempDtl = _db.VempDtl.ToList();


            return View(_v);
        }

        [HttpPost]
        public IActionResult AddCourse(int id, AddCourseForm post)
        {
            // var course = _context.TempTrgHist.Include(a => a.TcourseType).Include(F => F.TtrgPlaceCode).ToList();

            if (ModelState.IsValid)
            {
                if (!Can("Training-admin|Training-department"))
                    return StatusCode(403);


                if (id == 0 || id == null)
                {

                    if (_context.TempTrgMas.Count() == 0)
                    {
                        post.TempTrgMas.SalNo = 1001;
                    }
                    else
                    {


                        post.TempTrgMas.SalNo = _context.TempTrgMas.Max(m => m.SalNo) + 1;
                    }

                    var AddNewCourse1 = new TempTrgMas
                    {
                        SalNo = post.TempTrgMas.SalNo,
                        CourseCode = post.TempTrgMas.CourseCode,
                        Fees = post.TempTrgMas.Fees,
                        NoofPeople = post.TempTrgMas.NoofPeople,
                        CountryCode = post.TempTrgMas.CountryCode,
                        UserId = _v.Profile.EmailId,
                        CourseSupplierCode = post.TempTrgMas.CourseSupplierCode,
                    };

                    _context.TempTrgMas.Add(AddNewCourse1);
                    _context.SaveChanges();


                    foreach (var item in post.TempTrgHists)
                    {
                        var nid = (int)_context.TempTrgHist.Max(m => m.Id) + 1;

                        var AddNewCourse = new TempTrgHist
                        {
                            Id = nid,
                            SalNo = post.TempTrgMas.SalNo,
                            CourseCode = post.TempTrgMas.CourseCode,
                            EmpNo = item.EmpNo,
                            CourseSupplierCode = post.TempTrgMas.CourseSupplierCode,
                            UserId = _v.Profile.EmailId,
                        };

                        _context.TempTrgHist.Add(AddNewCourse);
                        _context.SaveChanges();

                    }
                }


                //Update
                else
                {
                    //_context.TempTrgHist.Update(trg);
                    //_context.SaveChanges();

                }
                return Json(new
                {
                    success = true,
                    message = new List<string> { _("Course Created Successfully") },
                    action = "reload",
                });
            }

            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);


        }

        [HttpPost]
        public async Task<IActionResult> PlanFileUploadAsync(IFormFile InitialAcceptanceFile, int InYear, int InMailNo, int InDeptInd, int InDocSlNo)
        {

            try
            {

                if (InitialAcceptanceFile == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = new List<string> { "No file selected for upload ...." },
                        action = "",
                    });
                }

                var qualReqs = _context.TqualReqs
                    .Where(x => x.InYear == InYear && x.InMailNo == InMailNo && x.InDocSlNo == InDocSlNo && x.InDeptInd == InDeptInd).FirstOrDefault();

                if (qualReqs == null)
                    return StatusCode(404);

                string FileGuid = null;


                if (qualReqs != null && InitialAcceptanceFile != null && (qualReqs.InitialAcceptanceAppr == 2 || qualReqs.InitialAcceptanceAppr == 0 || qualReqs.InitialAcceptanceAppr == null))
                {
                    FileGuid = await _h.UploadAsync(InitialAcceptanceFile);
                    qualReqs.InitialAcceptanceFileGuid = FileGuid;
                    if (qualReqs.InitialAcceptanceAppr == 2)
                    {
                        qualReqs.InitialAcceptanceAppr = 0;
                        qualReqs.InitialAcceptanceDate = DateTime.Now;
                    }
                }


                _context.TqualReqs.Update(qualReqs);
                _context.SaveChanges();

                var response = new
                {
                    success = true,
                    message = new List<string> { "Request updated successfully." },
                    action = "reload",
                };


                return Json(response);
            }
            catch (Exception ex)
            {

                var response = new
                {
                    success = false,
                    message = new List<string> { $"Error: {ex.Message}" },
                    action = "",
                };

                return Json(response);
            }
        }

        public IActionResult ManagerViewCourse()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager View Course";
            _v.TempTrgMas = _db.TempTrgMas.Include(c => c.TcourseCatalogue).Where(C => C.Approval == 0).ToList();
            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();
            return View(_v);
        }

        public IActionResult ManagerCourseApproval(int Id)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.TempTrgHist = _db.TempTrgHist
                .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
                .Where(c => c.SalNo == Id).FirstOrDefault();


            _v.TempTrgHists = _db.TempTrgHist
               .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
               .Where(c => c.SalNo == Id).ToList();


            _v.TempTrgMa = _db.TempTrgMas
                .Include(a => a.TcourseSupplier).Include(a => a.TcountryCode)
                .Include(c => c.TcourseCatalogue).Include(d => d.TempTrgHist)
                .Where(C => C.Approval == 0 && C.SalNo == Id).FirstOrDefault();

            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();


            return View(_v);
        }

        [HttpPost]
        public IActionResult ManagerCourseApproval(TempTrgHist tempTrgHist)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();

            var nid = (int)_context.TempTrgHist.Max(m => m.Id) + 1;

            var AddNewCourse = new TempTrgHist
            {
                Id = nid,
                SalNo = tempTrgHist.SalNo,
                CourseCode = tempTrgHist.CourseCode,
                EmpNo = tempTrgHist.EmpNo,
                CourseSupplierCode = tempTrgHist.CourseSupplierCode,
                UserId = _v.Profile.EmailId,
                TimeStamp = tempTrgHist.TimeStamp,




            };
            _context.TempTrgHist.Add(AddNewCourse);
            _context.SaveChanges();

            return Json(new
            {
                success = true,
                message = new List<string> { _("Course has been Approved Successfully") },
                action = "reload",
            });
        }

        public IActionResult Delete(int? Id)
        {
            if (Id == null)
            {
                return NotFound();
            }
            var EmpName = _db.TempTrgHist.Find(Id);
            _db.TempTrgHist.Remove(EmpName);
            _db.SaveChanges();

            if (EmpName == null)
            {
                return NotFound();

            }

            return RedirectToAction("ManagerCourseApproval", "Training");
        }


        //[HttpPost]
        public IActionResult Submit(int id, int stat)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            var updateApproval = _db.TempTrgMas.Find(id);

            if (stat == 1)


                updateApproval.Approval = 1;

            else if (stat == 2)


                updateApproval.Approval = 2;


            else if (stat == 3)

                updateApproval.Approval = 3;

            _context.TempTrgMas.Update(updateApproval);
            _context.SaveChanges();

            return RedirectToAction("ManagerViewCourse");
        }


        public IActionResult ViewCourses()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "ViewCourse ";



            _v.TempTrgMas = _db.TempTrgMas
            .Include(a => a.TcourseSupplier).Include(a => a.TcountryCode)
            .Include(c => c.TcourseCatalogue).Include(d => d.TempTrgHist)
           .ToList();


            //   _v.TempTrgHist = _db.TempTrgHist

            //.Include(c => c.TcourseCatalogue).Include(d => d.TempTrgMas).FirstOrDefault();
            //viewBag details=_v.TcourseCatalogue.Include(sub=>sub.)
            return View(_v);
        }

        //public IActionResult DeptManager(int sal, AddCourseForm AF)
        //{
        //    if (!Can("Training-admin|Training-department"))
        //        return StatusCode(403);

        //    _v.Page.Reload = true;
        //
        //    _v.Page.Title = "Manager View Course";
        //    _v.TempTrgHist=_db.TempTrgHist.Include(a=>a.VempDtl).Where(c=>c.SalNo == sal).FirstOrDefault();
        //    _v.TempTrgMa = _db.TempTrgMas.Include(t=>t.TcourseCatalogue).Include(a=>a.TcourseSupplier).Include(v=>v.TcountryCode).Where(c => c.SalNo == sal).FirstOrDefault();

        //    return View(_v);

        //}


    //    public async Task<IActionResult> DeptManager(int sal, AddCourseForm AF)
    //    {
    //        if (!Can("Training-admin|Training-department"))
    //            return StatusCode(403);

    //        _v.Page.Reload = true;
    //    
    //        _v.Page.Title = "Manager View Course";
    //        _v.TempTrgHist = _db.TempTrgHist.Include(a => a.VempDtl).Where(c => c.SalNo == sal).FirstOrDefault();
    //        _v.TempTrgMa = _db.TempTrgMas.Include(t => t.TcourseCatalogue)
    //                                       .Include(a => a.TcourseSupplier)
    //                                       .Include(v => v.TcountryCode)
    //                                      .Where(c => c.SalNo == sal).FirstOrDefault();

    //        var dept = _v.TempTrgHist?.VempDtl?.DeptCode;
    //        var dg = _v.TempTrgHist?.VempDtl?.DgCode;
    //        var desg = _v.TempTrgHist?.VempDtl?.DesgnHierLevel;

    //        // Send email when course is recommended
    //        if (desg == 99) {
    //            var managerEmail = await GetmanagerEmail(dept, dg, 6);

    //        }
    //        if (!string.IsNullOrEmpty(managerEmail))
    //        {
    //            var emaillist = managerEmail.Split(';')
    //                .Select(email => email.Trim())
    //                .ToList();
    //            var bossemail = _db.VempDtls
    //              .Where(c => c.DeptCode == dept.Value && c.DgCode == dg.Value
    //               && desg.Value < c.DesgnHierLevel);

    //            string subject = "Training Program Recommendation";
    //            string body = $@"
    //        <p>عزيزي المسؤول،</p>
    //        <p>نود إعلامك بإن الفاضل/ـة <strong>{_v.TempTrgHist.VempDtl.EmpNameA}</strong> قد تم ترشيحه للبرنامج التدريبي <strong>{_v.TempTrgMa.TcourseCatalogue.CourseNameA}</strong>.</p>
    //        <p>تاريخ البدء: {_v.TempTrgMa.CourseStartDate.ToString("yyyy-MM-dd")} <br />
    //        تاريخ الانتهاء: {_v.TempTrgMa.CourseEndDate.ToString("yyyy-MM-dd")} <br />
    //        المكان: {_v.TempTrgMa.TcountryCode.CountryDespA}</p>";




    //            await SendEmailAsync(managerEmail, subject, body);

    //            if (desg.HasValue && desg.Value <= 6)
    //            {
    //                var abovemanagerEmail = await GetmanagerEmail(dept, 5);//help manager or above

    //                if (!string.IsNullOrEmpty(abovemanagerEmail))
    //                {
    //                    await SendEmailAsync(abovemanagerEmail, subject, body);//sed email to above manager
    //                }
    //            }
            
    //    }
    //      return View(_v);

    //}
    //    private async Task GetmanagerEmail(int dept,int desg)
    //    {
    //       var managerEmail=await _db.VempDtl.Where(v=>v.DeptCode==dept.Value && v.DesgnHierLevel==desg.Value)
    //            .order


    //    }

    //        private async Task SendEmailAsync(string to, string subject, string body)
    //        {
    //            // Hardcoded SMTP settings
    //            string smtpServer = "smtp.yourmailserver.com";
    //            int smtpPort = 587;
    //            string smtpUsername = "<EMAIL>";
    //            string smtpPassword = "your-email-password";
    //            string fromAddress = "<EMAIL>";

    //            var smtpClient = new SmtpClient(smtpServer)
    //            {
    //                Port = smtpPort,
    //                Credentials = new NetworkCredential(smtpUsername, smtpPassword),
    //                EnableSsl = true
    //            };

    //            var mailMessage = new MailMessage
    //            {
    //                From = new MailAddress(fromAddress),
    //                Subject = subject,
    //                Body = body,
    //                IsBodyHtml = true,
    //            };
    //            mailMessage.To.Add(to);

    //            await smtpClient.SendMailAsync(mailMessage);
            
    //    }


        public IActionResult Submit3(int id, int stat)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            var updateApproval = _db.TempTrgReq.Find(id);
            _v.VempDtls = _db.VempDtls.ToList();


            if (stat == 1)

            {
                updateApproval.Approval = 1;
                updateApproval.ApprovalDate = DateTime.Now;
                updateApproval.ApprovedBy = _v.Profile.EmailId;
            }

            else if (stat == 2)

            {
                updateApproval.Approval = 2;
                updateApproval.ApprovalDate = DateTime.Now;
                updateApproval.ApprovedBy = _v.Profile.EmailId;
            }


            _context.TempTrgReq.Update(updateApproval);
            _context.SaveChanges();

            return RedirectToAction("CourseApproval");
        }

        public IActionResult ReportCourseCost(int id)
        {
            id = 2024;
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Cost Report";
            _v.TempTrgMas = _db.TempTrgMas.Include(c => c.TempTrgHist).Include(a => a.TcourseCatalogue).Where(C => C.Approval == 1).ToList();


            return View(_v);
        }


        public IActionResult ReportCourses(int id)
        {
            //id = 2024;
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Cost Report";
            _v.TempTrgHists = _db.TempTrgHist.Include(d => d.TempTrgMas).Include(a => a.TcourseCatalogue).Include(c => c.TempTrgMas.TcourseSupplier).Include(m => m.TempTrgMas.TcountryCode).Where(C => C.TempTrgMas.Approval == 1 && C.TempTrgMas.Year == id).ToList();



            return View(_v);
        }

        public IActionResult CourseCatalougeDtls(int id)
        {
            //year = 2024;
            id = 11;
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Cost Report";
            _v.TcourseCatalogue = _db.TcourseCatalogue.Include(c => c.TcourseType)
                .Include(c => c.TcourseCatCode).Include(c => c.TSubjectCode)
                .Include(c => c.TtrgPlaceCode).ToList();
            _v.VempDtls = _db.VempDtl.Where(c => c.DeptCode == id).ToList();

            return View(_v);
        }

        public IActionResult AboveManagerApproval(int Id)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.TempTrgHist = _db.TempTrgHist
                .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
                .Where(c => c.SalNo == Id).FirstOrDefault();


            _v.TempTrgHists = _db.TempTrgHist
               .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
               .Where(c => c.SalNo == Id).ToList();


            _v.TempTrgMa = _db.TempTrgMas
                .Include(a => a.TcourseSupplier).Include(a => a.TcountryCode)
                .Include(c => c.TcourseCatalogue).Include(d => d.TempTrgHist)
                .Where(C => C.Approval == 3 && C.SalNo == Id).FirstOrDefault();

            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();


            return View(_v);


        }

        public IActionResult AboveManagerView()
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager View Course";
            _v.TempTrgMas = _db.TempTrgMas.Include(c => c.TcourseCatalogue).Where(C => C.Approval == 3).ToList();
            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();

            return View(_v);
        }




        public IActionResult TrainingTrack(int Id)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";

            _v.TempTrgHist = _db.TempTrgHist
                .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
                .Where(c => c.SalNo == Id).FirstOrDefault();


            _v.TempTrgHists = _db.TempTrgHist
               .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
               .Where(c => c.SalNo == Id).ToList();


            _v.TempTrgMa = _db.TempTrgMas
                .Include(a => a.TcourseSupplier).Include(a => a.TcountryCode)
                .Include(c => c.TcourseCatalogue).Include(d => d.TempTrgHist)
                .Where(C => C.SalNo == Id).FirstOrDefault();

            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();


            return View(_v);



        }

        [HttpPost]
        public IActionResult TrainingTrack(TempTrgHist tempTrgHist)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();

            var nid = (int)_context.TempTrgHist.Max(m => m.Id) + 1;

            var AddNewCourse = new TempTrgHist
            {
                Id = nid,
                SalNo = tempTrgHist.SalNo,
                CourseCode = tempTrgHist.CourseCode,
                EmpNo = tempTrgHist.EmpNo,
                CourseSupplierCode = tempTrgHist.CourseSupplierCode,
                UserId = _v.Profile.EmailId,
                TimeStamp = tempTrgHist.TimeStamp,




            };
            _context.TempTrgHist.Add(AddNewCourse);
            _context.SaveChanges();

            return Json(new
            {
                success = true,
                message = new List<string> { _("Employee has been Added Successfully") },
                action = "reload",
            });

        }

        public IActionResult YearRep() {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";


            _v.Years = _db.Years.ToList();
            return View(_v);

        }




        public IActionResult AddSkills()
        {
            ViewBag.GetAllSkills = _context.EmpSkills.ToList();
            return View(_v);

        }


        [HttpPost]
        public IActionResult AddSkills(EmpSkill empSkill)
        {
            var id = _context.EmpSkills.Max(m => m.Id) + 1;
            ViewBag.GetAllSkills = new SelectList(_context.EmpSkills, "Id", "Name");
            var AddNewSkill = new EmpSkill
            {
                Id = id,
                Name = empSkill.Name,
            };
            _context.EmpSkills.Add(AddNewSkill);
            _context.SaveChanges();

            return Json(new
            {
                success = true,
                message = new List<string> { _("Skill has been Added Successfully") },
                action = "reload",
            });


        }

        public IActionResult SpecialiedSkill()
        {
            ViewBag.GetAllSkills = new SelectList(_context.EmpSkills, "Id", "Name");

            ViewBag.GetALLImpacts = new SelectList(_context.EmpImpacts, "Id", "Name");
          
            return View(_v);

        }

        [HttpPost]
        public IActionResult SpecialiedSkill(SpecialiedSkill specialiedSkill)
        {

            var nid=0;
            if (_context.SpecialiedSkills.Count() == 0)
            {
                nid = 1001;
            }
            else
            {
                nid = _context.SpecialiedSkills.Max(m => m.Id) + 1;

            }

           
            var AddspecialiedSkill = new SpecialiedSkill
            {
                Id = nid,
                EmpId = _v.Profile.EmpNo,
                SkillId = specialiedSkill.SkillId,
                ImpactId = specialiedSkill.ImpactId,
                ApprovedBy = specialiedSkill.ApprovedBy,
                Time = DateTime.Now,
                DgId= _v.Profile.DgCode,
            };
            _context.SpecialiedSkills.Add(AddspecialiedSkill);
            _context.SaveChanges();

            return Json(new
            {
                success = true,
                message = new List<string> { _("Specialied Skill has been Added Successfully") },
                action = "reload",
            });


        }


        public IActionResult DirectManager()
        {

            _v.Page.Reload = true;
            _v.Page.Title = "Direct Manager";

            _v.specialiedSkills = _db.SpecialiedSkills.Include(s => s.EmpImpact)
                .Include(a => a.EmpSkill).Where(b => b.Approve == 0 && b.ApprovedBy == _v.Profile.EmailId).ToList();
            ViewBag.EmpSkills = _db.EmpSkills.ToList();
            ViewBag.GetAlldg = _db.VempDtl.ToList();




            return View(_v);

        }

        public IActionResult DirectManagerApproval(int id, int stat)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            var updateApproval = _db.SpecialiedSkills.Find(id);

            if (stat == 1)
            {

                updateApproval.Approve = 1;

            }
            else if (stat == 2)
            {
                updateApproval.Approve = 2;
            }

            _context.SpecialiedSkills.Update(updateApproval);
            _context.SaveChanges();

            return RedirectToAction("DirectManager");
        }
        [HttpGet]
        public IActionResult SkillsReport(int?dgid, int?skillid)
        {
            _v.Page.Reload = true;
            _v.Page.Title = "Skills Report";
          


            var query = _db.SpecialiedSkills.Include(s => s.EmpImpact)
               .Include(a => a.EmpSkill).Where(a => a.Approve == 1);



            if (skillid.HasValue){
                query=query.Where(x=>x.SkillId == skillid.Value);
            }
            if (dgid.HasValue)
            {
                query = query.Where(x => x.DgId== dgid.Value);
            }
            _v.specialiedSkills = query.ToList();
            _v.VempDtls = _db.VempDtls.ToList();
            _v.EmpSkills = _db.EmpSkills.ToList();

            return View(_v);
        }

       

        public IActionResult AddTrainingField()
        {
            ViewBag.GetAllField = _db.TrainingFields.ToList();

            return View(_v);
        }
            [HttpPost]
        public IActionResult AddTrainingField(TrainingField trainingField)
        {

            var nid = 0;
            if (_context.TrainingFields.Count() == 0)
            {
                nid = 1001;
            }
            else
            {
                nid = _context.TrainingFields.Max(m => m.Id) + 1;

            }
            ViewBag.GetAllFied = new SelectList(_context.TrainingFields, "Id", "Name");
            var addtrainingfield = new TrainingField
            {
                Id = nid,
                Name = trainingField.Name,
            };
            _context.TrainingFields.Add(addtrainingfield);
            _context.SaveChanges();

            return Json(new
            {
                success = true,
                message = new List<string> { _("Skill has been Added Successfully") },
                action = "reload",
            });

        }
      

        public IActionResult TrainingPlan(int? dgid, int? skillid)
        {
            _v.Page.Reload = true;
            _v.Page.Title = "Skills Report";


            ViewBag.cities = _context.TcityCodes.Where(a => a.CountryCode == "968").ToList();

            ViewBag.trainingfield = _context.TrainingFields.ToList();
           var query = _db.SpecialiedSkills.Include(s => s.EmpImpact).Include(a => a.EmpSkill).
                Where(c => c.EmpImpact.Id == c.ImpactId && c.EmpSkill.Id == c.SkillId && c.Approve==1).ToList();

            if (skillid.HasValue)
            {
                query =query.Where(c=>c.SkillId == skillid.Value).ToList();
            }
            if (dgid.HasValue)
            {
                query = query.Where(x => x.DgId == dgid.Value).ToList();
            }
            _v.specialiedSkills = query.ToList();
            _v.VempDtls = _db.VempDtls.ToList();
            _v.EmpSkills = _db.EmpSkills.ToList();


            return View(_v);
        }

        [HttpPost]
        public IActionResult TrainingPlan(TrainigPlan trainigPlan)
        {
            _v.Page.Reload = true;
            _v.Page.Title = "Skills Report";
            var nid = 0;
            if (_context.TrainigPlans.Count() == 0)
            {
                nid = 1001;
            }
            else
            {
                nid = _context.TrainigPlans.Max(m => m.Id) + 1;

            }

            // foreach (var item in trainigPlan)
            //{
            //   if (!string.IsNullOrEmpty(trainigPlan.Trg))
            //    {


            var AddTrainigPlan = new TrainigPlan

            {
                Id = nid,
                EmpId = trainigPlan.EmpId,
                Trg = trainigPlan.Trg,
                SkillId = trainigPlan.SkillId,
                time = trainigPlan.time,
                PlaceId = trainigPlan.PlaceId,
                Fees = trainigPlan.Fees,
                CreatedBy = _v.Profile.EmailId,
                spId = trainigPlan.spId,

            };

            _context.TrainigPlans.Add(AddTrainigPlan);
            _context.SaveChanges();


            return Json(new
            {
                success = true,
                message = new List<string> { _("Training Cost has been Added Successfully") },
                action = "reload",
            });


            //   }
            //}
        }



            public IActionResult TrainingPlanReport()
        {
            _v.Page.Reload = true;
            _v.Page.Title = "Skills Report";


            _v.trainigPlans = _db.TrainigPlans.Include(s => s.SpecialiedSkill).ToList();


            return View(_v);
        }





        

        public IActionResult TrainingCostView()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager View Course";
            _v.TempTrgMas = _db.TempTrgMas.Include(c => c.TcourseCatalogue).Where(C => C.IsSend == 0 && C.Approval == 1 || C.Approval == 3).ToList();
            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();

            return View(_v);
        }

        public IActionResult TrainingCost(int Id)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.TempTrgHist = _db.TempTrgHist
                .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
                .Where(c => c.SalNo == Id).FirstOrDefault();


            _v.TempTrgHists = _db.TempTrgHist
               .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
               .Where(c => c.SalNo == Id).ToList();


            _v.TempTrgMa = _db.TempTrgMas
                .Include(a => a.TcourseSupplier).Include(a => a.TcountryCode)
                .Include(c => c.TcourseCatalogue).Include(d => d.TempTrgHist)
                .Where(C => C.Approval == 1 && C.SalNo == Id).FirstOrDefault();

            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();
            return View(_v);
        }
        [HttpPost]

        public async Task<IActionResult> TrainingCost(TrainingComm trainingComm, IFormFile file)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.VempDtl = _db.VempDtl.ToList();
            var nid = 0;
            if (_context.TrainingComms.Count() == 0)
            {
                nid = 1001;
            }
            else
            {
                nid = _context.TrainingComms.Max(m => m.Id) + 1;

            }

            string FileGuid = null;

            if (file != null && file.Length > 0)
            {
                FileGuid =await _h.UploadAsync(file);
            }


            var AddtrainingCost = new TrainingComm
                {
                    Id = nid,
                    CourseId = trainingComm.CourseId,
                    SupplierId = trainingComm.SupplierId,
                    TcreatedBy = _v.Profile.EmailId,
                    PlaceId = trainingComm.PlaceId,
                    Dfrom = trainingComm.Dfrom,
                    Dto = trainingComm.Dto,
                    Cost = trainingComm.Cost,
                    Total = trainingComm.Total,
                    SalNo = trainingComm.SalNo,
                    IsSend = 1,
                    FileGuid = FileGuid,
                };



                var salno = trainingComm.SalNo;
                var updatesendstat = _db.TempTrgMas.Find(salno);
                { updatesendstat.IsSend = 1; }

                _context.TrainingComms.Add(AddtrainingCost);
                _context.SaveChanges();


                return Json(new
                {
                    success = true,
                    message = new List<string> { _("Training Cost has been Added Successfully") },
                    action = "reload",
                });


            
          
        } 
    

        public IActionResult FinanceManagerView()
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager View Course";
            _v.TempTrgMas = _db.TempTrgMas.Include(c => c.TcourseCatalogue).Where(C => C.IsSend==1).ToList();
            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();
            

            return View(_v);
        }

        public IActionResult FinanceManagerApproval(int Id)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.TempTrgHist = _db.TempTrgHist
                .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
                .Where(c => c.SalNo == Id).FirstOrDefault();


            _v.TempTrgHists = _db.TempTrgHist
               .Include(c => c.TcourseCatalogue).Include(c => c.TempTrgMas)
               .Where(c => c.SalNo == Id).ToList();


            _v.TempTrgMa = _db.TempTrgMas
                .Include(a => a.TcourseSupplier).Include(a => a.TcountryCode)
                .Include(c => c.TcourseCatalogue).Include(d => d.TempTrgHist)
                .Where(C => C.Approval == 1 && C.SalNo == Id).FirstOrDefault();

            _v.VempDtl = _db.VempDtl.ToList();
            _v.TempReqDtls = _db.TempReqDtls.ToList();
            _v.TrainingComm = _db.TrainingComms.Where(c => c.SalNo == Id).FirstOrDefault();
            ViewBag.GetVendor = _db.VendorTraining.ToList();
           
           
            return View(_v);
        }

        [HttpPost]
        public IActionResult FinanceManagerApproval(TrainingComm trainingComm)
        {

            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            _v.Page.Reload = true;
            _v.Page.Title = "Manager Course Approval";
            _v.VempDtl = _db.VempDtl.ToList();
           
            var id = trainingComm.Id;
           
            var updateCost  = _db.TrainingComms.Find(id);
            if(updateCost == null)
            {
                return Json(new
                {
                    success = true,
                    message = new List<string> { _("No record") },
                    action = "reload",
                });

            }

            updateCost.Total =trainingComm.Total;
            updateCost.Cost = trainingComm.Cost;
            updateCost.FcreatedBy=_v.Profile.EmailId;
            updateCost.AccountNo=trainingComm.AccountNo;
            updateCost.VendorNo=trainingComm.VendorNo;

         
                _db.TrainingComms.Update(updateCost);
                _db.SaveChanges();

                return Json(new
                {
                    success = true,
                    message = new List<string> { _("Training Cost has been Added Successfully") },
                    action = "reload",
                });

            

        }


        [HttpPost]
        public IActionResult updateCostTotal([FromBody] dynamic payload)
        {
            if (!Can("Training-admin|Training-department"))
                return StatusCode(403);

            try
            {
                int salNo =Convert.ToInt32( payload.salNo);
                string field = Convert.ToString(payload.field);
                int value = Convert.ToInt32(payload.value);

                var record = _db.TrainingComms.FirstOrDefault(c => c.SalNo == salNo);
                if(record==null)
                {
                    return Json(new { success = false, message = "Record not found." });
                }
                if (field == "Cost")
                {
                    record.Cost = value;
                }
                else if (field == "Total")
                {
                    record.Total = value;
                }
                else
                {
                    return Json(new { success = false, message = "Invalid field" });
                }
                record.FcreatedBy = _v.Profile.EmailId;
                _db.SaveChanges();
                return Json(new { success = true, message = "updated successfully" });
            }
            catch(Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
            
        }
    
    
    
    }
}