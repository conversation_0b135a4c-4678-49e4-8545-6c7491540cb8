using HumanResource.Modules.Training.Providers;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Training.Configuration
{
    /// <summary>
    /// Configuration for the Training module
    /// </summary>
    public static class TrainingConfiguration
    {
        /// <summary>
        /// Registers all Training module services
        /// </summary>
        public static IServiceCollection AddTrainingServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, TrainingNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, TrainingBadgeProvider>();

            // Register module services
            // services.AddScoped<TrainingService>();

            return services;
        }
    }
} 