using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;
namespace HumanResource.Modules.Training.Providers
{
    /// <summary>
    /// Navigation provider for the Training module
    /// </summary>
    public class TrainingNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Training";
        public int Priority => 25;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // HR Group navigation items
            var hrNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "Training",
                    Label = "التدريب",
                    Active = "Training",
                    Icon = "<i class=\"far fa-graduation-cap\"></i>",
                    Rights = new List<Right> { Right.TrainingDepartment },
                    Priority = 30,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "AcademicDegree",
                            Label = "الشهادات الأكاديمية",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/AcademicDegree"
                        },
                        new NavLink
                        {
                            Name = "University",
                            Label = "الجامعات",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/University"
                        },
                        new NavLink
                        {
                            Name = "CourseCatalogue",
                            Label = "كتالوج الدورات",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/CourseCatalogue"
                        },
                        new NavLink
                        {
                            Name = "CourseSupplier",
                            Label = "مزودي الدورات",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/CourseSupplier"
                        },
                        new NavLink
                        {
                            Name = "AddCourse",
                            Label = "إضافة دورة",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/AddCourse"
                        },
                        new NavLink
                        {
                            Name = "StudyApproval",
                            Label = "طلبات الدراسة والتدريب",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/StudyApproval"
                        },
                        new NavLink
                        {
                            Name = "AddSkills",
                            Label = "إضافة مهارات",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/AddSkills"
                        },
                        new NavLink
                        {
                            Name = "SpecialiedSkill",
                            Label = "المهارات المتخصصة",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/SpecialiedSkill"
                        },
                        new NavLink
                        {
                            Name = "AddTrainingField",
                            Label = "إضافة مجال التدريب",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/AddTrainingField"
                        },
                        new NavLink
                        {
                            Name = "TrainingPlan",
                            Label = "خطة التدريب",
                            Rights = new List<Right> { Right.TrainingDepartment },
                            Url = "/Training/TrainingPlan"
                        }
                    }
                }
            };

            // Approvals Group navigation items
            var approvalNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "AffairsCommittee",
                    Label = "لجنة الشؤون",
                    Active = "AffairsCommittee",
                    Icon = "<i class=\"far fa-users\"></i>",
                    Rights = new List<Right> { Right.AffairsCommittee, Right.TrainingAdmin },
                    Priority = 40,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "AffairsCommittee",
                            Label = "موافقة لجنة الشؤون",
                            Rights = new List<Right> { Right.AffairsCommittee },
                            Url = "/Training/AffairsCommitteeApproval"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.HR] = hrNavItems;
            navigation[NavigationGroup.Approvals] = approvalNavItems;

            return navigation;
        }
    }
} 