﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel
<br />
<div class="d-flex justify-content-between py-2">

    <h3>@Model._l("Study Requests")</h3>

    <div>
        <button data-toggle="modal" data-target="#new-request-modal" class="btn btn-primary rounded-pill"> 
            <i class="fa fa-plus"></i> @Model._l("New request")</button>
    </div>
</div>
<div class="card sahdow">
    <table class="table table-sm datatable">
        <thead>
            <tr>
                <th>@Model._l("No")</th>
                <th>@Model._l("Request date")</th>
                <th>@Model._l("Current Qualification")</th>
                <th>@Model._l("Required Qualification")</th>
                <th>@Model._l("Field of Study")</th>
                <th>@Model._l("University")</th>
                <th>@Model._l("Country code")</th>
                <th>@Model._l("Reason")</th>
                <th>@Model._l("Status")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.TqualReqs)
            {
                <tr>
                    <td>
                        @if (request.AffairsCommitteeStat == 1)
                        {
                        //Add Button to move to student page
                        <a href="/Training/StudentDeatils?InYear=@request.InYear&InDeptInd=@request.InDeptInd&InMailNo=@request.InMailNo&InDocSlNo=@request.InDocSlNo">
                            @request.InYear / @request.InDeptInd /@request.InMailNo/@request.InDocSlNo
                            </a>
                        }
                        else
                        {
                            <p>@request.InYear/ @request.InDeptInd /@request.InMailNo/@request.InDocSlNo </p>
                        }
                       
                    </td>
                    <td>@Model._d(@request.TxnDate)</td>
                    <td>@ViewBag.CurrentQual</td>
                    <td>@request.TSubjectCode.SubjDespA</td>
                    <td>@request.TqualCode.QualDespA</td>
                    <td>@request.TunivInstCode.UnivInstDespA</td>
                    <td>@request.TcountryCode.CountryDespA</td>
                    <td>@request.Reson</td>
                    <td>@request.ReqStat </td>
                </tr>
            }
        </tbody>
    </table>
</div>



<form action="/Training/EmpRequest" method="post" class="ajax">
    <div class="modal fade" id="new-request-modal" tabindex="-1" role="dialog" aria-labelledby="new-leave-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._l("New request")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="app">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="">@Model._l("Current Qualification")</label>
                                <input type="text" class="form-control" value="@ViewBag.CurrentQual" disabled /><br>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="">@Model._l("Required Qualification")</label>
                                @Html.DropDownListFor(m=> m.TqualReqs.FirstOrDefault().QualCode, (SelectList)@ViewBag.QualType, "---Select Option---", new {@class="form-control select2" , required="required"})
                            </div>
                        </div>
                    </div>

                     <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="">@Model._l("Field of Study")</label>
                                @Html.DropDownListFor(m=> m.TqualReqs.FirstOrDefault().SubjCode, (SelectList)@ViewBag.FieldofStudy, "---Select Option---", new {@class="form-control select2" , required="required"})
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("Country code")</label>
                            @Html.DropDownListFor(m=> m.TqualReqs.FirstOrDefault().CountryCode, (SelectList)@ViewBag.countryCode, "---Select Option---", new {@class="form-control select2" , id="Country" , required="required"})
                        </div>
                    </div>
                        <div class="row">                    
                        <div class="col-lg-6">
                            <label for="">@Model._l("University")</label>
                            @Html.DropDownListFor(m=> m.TqualReqs.FirstOrDefault().UnivInstCode, (SelectList)@ViewBag.univInstCode, "---Select Option---", new {@class="form-control select2", id="Uinversity" , required="required"})
                            </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="">@Model._l("Reason")</label>
                                <textarea name="Reson" class="form-control" required></textarea>
                            </div>
                        </div>
                        </div>
                      <div class="row">
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label for="">@Model._l("Department Manager")</label>
                                <select name="MangAppId" class="form-control" placeholder="Select Manager" required>
                                    <option value="" selected disabled hidden>@Model._l("Select Staff")</option>
                                    @foreach (var staff in Model._h.Managers(Model.Auth.EmpNo))
                                    {
                                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                    }

                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>

<script>
    $(document).ready(function () {
        $("#Country").change(function () {
            var selectItem = $(this).val();

            $.ajax({

                url: '/Training/GetUinversity',
                type: 'GET',
                dataType: 'json',
                data: { value: selectItem },
                success: function (data) {
                    $('#Uinversity').empty();

                    $.each(data, function (index, item) {
                        console.log(item);
                        $('#Uinversity').append($('<option>').text(item.text).attr('value', item.value));
                    });

                },
                error: function () {
                    alert('Error occurred while fetching data.');
                }
            });
        });
    });
</script>