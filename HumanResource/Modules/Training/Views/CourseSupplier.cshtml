﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model._("Course Supplier")</h3>

    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
            <i class="fas fa-plus"></i>  @Model._("Course Supplier")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("COURSE SUPPLIER NAME ARABIC ")</th>
                <th>@Model._("COURSE SUPPLIER NAME ENGLISH")</th>
                <th>@Model._("SUPPLIER ADDRESS")</th>
                @* <th></th>*@
            </tr>
        </thead>
        <tbody>
            @foreach (var code in Model.TcourseSuppliers)
            {
                <tr>
                    <td><a href="#" data-toggle="modal" data-target="#<EMAIL>-modal">@code.CourseSupplerCode</a></td>
                    <td>@code.CourseSupplierName_A</td>
                    <td>@code.CourseSupplierName_A</td>
                    <td>@code.SupplierAddress</td>
                 
                </tr>
            }
        </tbody>
    </table>
</div>







<form action="~/Training/CreateCourseSupplier" method="post" class="ajax">
    <div class="modal fade" id="create-modal" tabindex="-1" role="dialog" aria-labelledby="create-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("New Course Supplier")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">@Model._("COURSE SUPPLIER NAME ARABIC")</label>
                    <input type="text" required name="CourseSupplierName_A" class="form-control"><br>

                    <label for="">@Model._("COURSE SUPPLIER NAME ENGLISH")</label>
                    <input type="text" name="CourseSupplierName_E" class="form-control"><br>

                    <label for="">@Model._("SUPPLIER ADDRESS")</label>
                    <input type="text" name="SupplierAddress" class="form-control"><br>


                    <label for="">@Model._("CONTACT PERSON NAME ARABIC")</label>
                    <input type="text" name="ContactPersonName_A" class="form-control"><br>


                    <label for="">@Model._("PHONE NO")</label>
                    <input type="text" name="PhoneNo" class="form-control"><br>


                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>




@section Scripts{
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}