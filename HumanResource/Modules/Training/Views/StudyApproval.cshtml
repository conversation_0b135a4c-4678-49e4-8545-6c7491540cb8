﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

@Html.Partial("_TrainingAppTabs")
<div class="card shadow">

    <table class="table table-sm datatable table-striped table-hover">
        <thead>

            <tr>
                <th>@Model._l("No")</th>
                <th>@Model._l("Staff")</th>
                <th>@Model._l("Request Date")</th>
                <th>@Model._l("Required Qualification")</th>
                <th>@Model._l("Field of Study")</th>
                <th>@Model._l("Reason")</th>
                <th>@Model._l("Manager")</th>
                <th>@Model._l("Managr Approval Date")</th>
                <th>@Model._l("Section Approval Date")</th>
                <th>@Model._l("Section Remark")</th>
                <th>@Model._l("Remarks")</th>
                <th>@Model._l("Status")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.TqualReqs)
            {
                <tr>
                    <td>
                        @if (request.AffairsCommitteeStat == 1)
                        {
                            //Add <PERSON><PERSON> to move to student page
                            <a href="/Training/StudentDeatils?InYear=@request.InYear&InDeptInd=@request.InDeptInd&InMailNo=@request.InMailNo&InDocSlNo=@request.InDocSlNo">
                                @request.InYear / @request.InDeptInd /@request.InMailNo/@request.InDocSlNo
                            </a>
                        }
                        else
                        {
                            <p>@request.InYear/ @request.InDeptInd /@request.InMailNo/@request.InDocSlNo </p>
                        }
                        </td>
                    <td>@Model._h.StaffData(request.EmpNo).EmpNo  <br> @Model._h.StaffData(request.EmpNo).EmpNameA</td>
                   <td>@Model._d(@request.TxnDate)</td>
                    <td>@request.TqualCode.QualDespA</td>
                    <td>@request.TSubjectCode.SubjDespA</td>
                    <td>@request.Reson</td>

                    <td>@Model._h.StaffData(request.MangAppId).EmpNo  <br> @Model._h.StaffData(request.MangAppId).EmpNameA </td>
                    <td>
                        @if (@request.ApprovalDate != null)
                        { @Model._d(@request.ApprovalDate) }
                    </td>
                    
                    <td>
                        @if (@request.SectionAprrDate != null)
                        {
                            @Model._d( @request.SectionAprrDate)
                        }
                       
                    </td>
                    <td>@request.SectionAprrRem </td>
                    <td>
                        @{

                            var AppointDate = DateTime.Now.Year - (Model._h.StaffData(request.EmpNo).GscAppointDate.Value.Year);
                            var age = DateTime.Now.Year - (Model._h.StaffData(request.EmpNo).BirthDate.Value.Year);


                            if (AppointDate >= 2 && age < 35 && (request.QualCode == 53 || request.QualCode == 60))
                            {
                                <span class="badge badge-outline-danger shadow rounded-pill px-3 border-danger border-2 ">@Model._l("Meets the Age and Qualification Requirements")</span>
                            }
                            else if (AppointDate >= 2 && age < 45 && (request.QualCode == 90 || request.QualCode == 80))
                            {
                                <span class="badge badge-outline-danger shadow rounded-pill px-3 border-danger border-2 ">@Model._l("Meets the Age and Qualification Requirements")</span>
                            }
                            else
                            {
                                if (AppointDate < 2)
                                {
                                    <span class="badge badge-danger shadow rounded-pill px-3">@Model._l("Does not Meet Experience Requirements")</span>
                                }
                                if (age > 35 && (request.QualCode == 53 || request.QualCode == 60))
                                {
                                    <span class="badge badge-danger shadow rounded-pill px-3">@Model._l("Does not Meet the Age")</span>

                                }
                                if (age > 45 && (request.QualCode == 90 || request.QualCode == 80))
                                {
                                    <span class="badge badge-danger shadow rounded-pill px-3">@Model._l("Does not Meet the Age")</span>

                                }
                            }


                        }

                    </td>

                    <td>
                       @if (request.ApprovalDate != null )
                            {
                                if (request.MandAprrStat == 1 && (request.SectionStat == 0 || request.SectionStat == null) )
                                {
                                <form action="~/Training/SectionDecline" class="ajax" method="post">
                                    <div class="modal fade" id="department-manager-decline-modal" role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-danger">
                                                    <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("Declined by department manager")</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    <label for="">@Model._l("Decline Note")</label>
                                                    <textarea maxlength="225" name="DeclineNote" class="form-control"></textarea>
                                                </div>
                                                <div class="modal-footer d-flex">
                                                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Cancel")</button>
                                                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <input type="hidden" name="InYear" value="@request.InYear">
                                    <input type="hidden" name="InDeptInd" value="@request.InDeptInd">
                                    <input type="hidden" name="InDocSlNo" value="@request.InDocSlNo">
                                    <input type="hidden" name="InMailNo" value="@request.InMailNo">
                                    <input type="hidden" name="EmpNo" value="@request.EmpNo">

                                </form>

                                <div class="d-flex justify-content-center">
                                    <a href="~/Training/SectionApproval?InYear=@request.InYear&InDeptInd=@request.InDeptInd&InDocSlNo=@request.InDocSlNo&InMailNo=@request.InMailNo&EmpNo=@request.EmpNo"
                                       class="btn btn-larg  mx-1 btn-success after-confirm rounded-pill btn-block">@Model._l("Approve")</a>

                                    <a href="#" class="btn btn-larg btn-danger  mx-1  rounded-pill btn-block" data-toggle="modal" data-target="#department-manager-decline-modal">@Model._l("Decline")</a>
                                </div>
                   
                                }
                            else
                            {
                              @request.ReqStat
                            }
                         }
                    

                    </td>

                </tr>
            }
        </tbody>
    </table>

</div>
