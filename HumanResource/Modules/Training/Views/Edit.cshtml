﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<div class="d-flex justify-content-between py-2">





    <form action="Edit" method="post" class="ajax">

  

                    <table class="table">
                        <thead>
                            <tr>
                             
                                <th colspan="3">@Model._("إسم الموظف")</th>

                            </tr>
                        </thead>





                        <tbody>
                            <tr>
                                

                                <td>

                                    <div style="max-width:500px">
                                        <selec>
                                            <option value="" disabled selected>@Model._("Select option")</option>

                                            @foreach (var item in Model.VempDtl)
                                            {
                                                <option value="@item.EmpNo" data-name="@item.EmpNameA">@item.EmpNo - @item.EmpNameA</option>
                                            }
                                        </select>
                                    </div>


                                </td>


                    </table>
                    <div>
                        <input type="hidden" name="Id" value="@Model                                                                                                                                                                                                                                                                                                                                                                                                                                           " />
                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("save")</button>



                    </div>
                
           
       
    </form>



</div>