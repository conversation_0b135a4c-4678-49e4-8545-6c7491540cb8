﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

@Html.Partial("_DgAppTabs")
<table class="table table-sm datatable table-striped table-hover">
    <thead>

        <tr>
            <th>@Model._l("No")</th>
            <td>@Model._l("البرنامج")</td>
            <td>@Model._l(":من تاريخ ")</td>
            <th>@Model._l("الى تاريخ:")</th>
            <th>@Model._l("تاريخ الإنشاء")</th>
           

        </tr>
    </thead>
    <tbody>


        <div class="form-group col-6">
         


            @foreach (var course in Model.TempTrgMas)
            {
            <tr>
                <td>
                    <a href="/training/ManagerCourseApproval/@course.SalNo"> #@course.SalNo</a>

                </td>
                <td>
                        @course.CourseCode (@course.TcourseCatalogue.CourseNameA)

                </td>
                <td>
                        @course.CourseStartDate

                </td>

                <td>
                        @course.CourseEndDate

                </td>

                <td>
                        @course.TimeStamp

                </td>
            </tr>
            }

    </tbody>
</table>

