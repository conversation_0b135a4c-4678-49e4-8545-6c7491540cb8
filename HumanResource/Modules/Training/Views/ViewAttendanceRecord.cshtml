﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

@{
    Layout = null;
}

<form action="~/Training/UpdateEmpAttend" id="@Model.TqualEmpAttendance.Id" method="POST" class="ajax">
 
   <div class="row">
        <input type="hidden" name="Id" value="@Model.TqualEmpAttendance.Id">
              <div class="col-lg-6">
                <label for="">@Model._l("Date From")</label>
            <input type="date" name="FromDate" value="@Model.TqualEmpAttendance.FromDate.ToString("yyyy-MM-dd")"  v-model="DateFromTextBoxValue" :max="DateToTextBoxValue" class="form-control" required /><br>

                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("Date To")</label>
            <input type="date" :min='DateFromTextBoxValue' name="ToDate" v-model="DateToTextBoxValue" class="form-control" required value="@Model.TqualEmpAttendance.ToDate.ToString("yyyy-MM-dd")" /><br>
                        </div>

                       <div class="col-lg-6">
                            <label for="">@Model._l("Day")</label>
                             @Html.DropDownList("Day", (SelectList)@ViewBag.Day, "---Select Option---", new {@class="form-control", required="required"})

                        </div>
                        <div class="col-lg-6">
                            <label for="">
                                @Model._l("From Time")
                            </label>
            <input type="time" name="TimeFrom" min="07:00" max="14:30" v-model="FromTextBoxValue" class="form-control" required value="@Model.TqualEmpAttendance.TimeFrom.ToString("hh:mm")" /><br>

                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("To Time")</label>
            <input type="time" min="07:00" max="14:30" name="TimeTo" v-model="ToTextBoxValue" class="form-control" required value="@Model.TqualEmpAttendance.TimeTo.ToString("hh:mm")" /><br>
                        </div>
  </div>



              
                <div class="modal-footer d-flex">

                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                </div>
 

</form>
