﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<h3>@Model._("Above Manager Course Apporoval")</h3>


<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header  d-flex justify-content-between">
            </div>

            <div class="card-body">
                <div class="row">
                    <table class="table">
                        @*<tr>

                        <th>@Model._l("Request date")</th>
                        <td>@Model._d(@Model.TempTrgMa.TimeStamp)</td>

                        </tr>*@
                        <tr>
                            <th>@Model._l("COURSE Name")</th>
                            <td>@Model.TempTrgMa.TcourseCatalogue.CourseNameA</td>
                        </tr>
                        <tr>
                            <th>@Model._l("Course start Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseStartDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("Course End Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseEndDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("مكان التنفيذ")</th>
                            <td>@Model.TempTrgMa.TcountryCode.CountryDespA</td>
                        </tr>


                        <tr>
                            <th>@Model._l("المؤوسسة التدريبية")</th>
                            <td>@Model.TempTrgMa.TcourseSupplier.CourseSupplierName_A</td>
                        </tr>

                        <tr>
                            <th>@Model._l("التكلفة")</th>
                            <td>@Model.TempTrgMa.Fees</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

    </div>
</div>



<div class="d-flex justify-content-center">
    <a href="~/Training/Submit?id=@Model.TempTrgMa.SalNo&stat=1" class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block">@Model._l("Approve")</a>
    <a href="~/Training/Submit?id=@Model.TempTrgMa.SalNo&stat=2" class="btn btn-larg btn-danger mx-1 after-confirm rounded-pill btn-block">@Model._l("Decline")</a>

</div>
</br><br />
<div class="row">
    <div class="col-lg-12">
        <div class="card">

            <div class="card-header  d-flex justify-content-between">
            


                <div class="card-body">
                    <div class="row">

                        <table class="table table-sm  table-striped table-hover">
                            <thead>

                                <tr>
                                    <th>@Model._l("No")</th>
                                    <th>@Model._l("Course Employee")</th>
                                    <th></th>


                                </tr>
                            </thead>
                            <tbody>


                                @foreach (var course in Model.TempTrgHists)
                                {
                                    <tr>

                                        <td>
                                            @course.EmpNo


                                        </td>
                                        <td>
                                            @Model._h.StaffData(course.EmpNo).EmpNameA


                                        </td>

                                     
                                    </tr>
                                }


                            </tbody>


                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


