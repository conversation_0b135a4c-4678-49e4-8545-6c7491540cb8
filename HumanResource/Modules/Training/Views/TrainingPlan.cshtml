﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<br>
<br>
<br>
<div class="card shadow">
    <form action="TrainingPlan" method="post">
    <table class="table table-sm datatable table-striped table-hover">

            <div class="row">
                <div class="col-6">
                    <lable for="skillid">@Model._l("select skill")</lable>
                    <select id="skillid" name="skillid" class="form-control" onchange="filterResult()">

                        <option value="">--select skill--</option>
                        @foreach (var skill in Model.EmpSkills)
                        {
                            string selected = (skill.Id.ToString() == Context.Request.Query["dgid"]) ? "selected" : "";
                            <option value="@skill.Id">

                                @skill.Name
                            </option>
                        }

                    </select>
                </div>
                <div class="col-6">
                    <lable for="dgid">@Model._l("select DG")</lable>
                    <select id="dgid" name="dgid" class="form-control" onchange="filterResult()">

                        <option value="">--select DG--</option>
                        @foreach (var d in Model.VempDtls)
                        {
                            string selected = (d.DgCode.ToString() == Context.Request.Query["dgid"]) ? "selected" : "";
                            <option value="@d.DgCode">
                                @selected

                                @d.DgDespA
                            </option>
                        }

                    </select>
                </div>
            </div>
        <thead>

            <tr>
                <th>@Model._l("إسم الموظف")</th>
                <th>@Model._l("المسمى الوظيفي")</th>
                <td>@Model._l("مجال البرنامج التعاقدي")</td>
                <th>@Model._l("مجال البرنامج الفردي")</th>
                <th>@Model._l("مدة البرنامج الفردي")</th>
                    <th>@Model._l("رسوم البرنامج الفردي ")</th>
                <th>@Model._l("مكان البرنامج الفردي  ")</th>
               



            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.specialiedSkills)
            {
                <tr>

                        <td>
                            <input type="hidden" name="spId" value="@item.Id" hidden>
                            <input type="hidden" name="SkillId" value="@item.EmpSkill.Id" hidden>
                        <input type="hidden" name="EmpId" value="@item.EmpId" hidden>
                            @Model._h.StaffData(@item.EmpId).EmpNameA
                        </td>
                   
                    <td>
                        @Model._h.StaffData(@item.EmpId).DesgCode
                    </td>

                    <td>
                            <select name="Trg">
                                <option>select Training Field</option>
                                @foreach (var d in ViewBag.trainingfield)
                                {
                                    <option value="@d.Id">@d.Name</option>
                                }
                            </select>

                    </td>

                    <td>
                         
                        @item.EmpSkill.Name

                    </td>

                    <td>
                            

                    </td>
               
                 
                    <td>
                       <input type="text" name="Fees">
                    </td>


                    <td>
                            <select name="PlaceId">
                            <option >select City</option>
                                @foreach (var d in ViewBag.cities)
                        {
                          
                                <option value="@d.Id">@d.CityDespa</option>
                          
                        }
                        </select>

                    </td>
                </tr>
            }
  
        </tbody>
        <tr>
           
        </tr>
    </table>
    <div class="row"><div class="col-6">
            <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
            </div>
            <div class="col-6">
            <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>

        </div></div>
    </form>
</div>




