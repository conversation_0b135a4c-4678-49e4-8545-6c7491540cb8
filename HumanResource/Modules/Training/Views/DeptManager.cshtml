﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<table class="table table-sm datatable table-striped table-hover">




<div class="row">
    <div class="col-lg-12">
        <div class="card">
   
            <div class="card-body">
      
            



                     <p>
                       
                        : @Model._h.StaffData(@Model.TempTrgHist.EmpNo).EmpNameA :الفاضل:
                         عزيزي المسؤول نودإعلامك بإن الفاضل/ـة


                          :   قد تم ترشيحة للبرنامج التدريبي      
              
                            (@Model.TempTrgMa.TcourseCatalogue.CourseNameA)
                            <br />
                        بتاريخ :

   
                              من:  @Model._d(@Model.TempTrgMa.CourseStartDate)


                               إالى:  @Model._d(@Model.TempTrgMa.CourseEndDate)

                               <br />
                               

                               المكان: @Model.TempTrgMa.TcountryCode.CountryDespA
                               <br />

                     @*          الجهة المنفذة: @Model.TempTrgMa.TcourseSupplier.CourseSupplierName_A*@
                                  
                        
                        
                                 @*   <td>(@Model.TempTrgMa.TcourseCatalogue.TcourseType.CourseTypeDesp)</td>*@
    </div>
    <br />
    <br />

        <div class="d-flex justify-content-center">
            <a href="~/Training/Submit2?id=@Model.TempTrgMa.SalNo&stat=1&id=@Model.TempTrgHist.EmpNo" class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block">@Model._l("Approve")</a>
                    <a href="~/Training/Submit2?id=@Model.TempTrgMa.SalNo&stat=2 @Model.TempTrgHist.EmpNo" class="btn btn-larg btn-danger mx-1 after-confirm rounded-pill btn-block">@Model._l("Decline")</a>
        </div>
</div></div></div></table>