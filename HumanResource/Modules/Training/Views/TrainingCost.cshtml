﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<h3>@Model._(" Manager Course Apporoval")</h3>


<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header  d-flex justify-content-between">
            </div>

            <div class="card-body">
                <div class="row">
                    <table class="table">

                        <tr>
                            <th>@Model._l("Course Name")</th>
                            <td>@Model.TempTrgMa.TcourseCatalogue.CourseNameA</td>
                        </tr>
                        <tr>
                            <th>@Model._l("Course start Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseStartDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("Course End Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseEndDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("البـلـد")</th>
                            <td>@Model.TempTrgMa.TcountryCode.CountryDespA</td>
                        </tr>


                        <tr>
                            <th>@Model._l("المؤوسسة التدريبية")</th>
                            <td>@Model.TempTrgMa.TcourseSupplier.CourseSupplierName_A</td>
                        </tr>

                    </table>
                </div>
            </div>
        </div>

    </div>
</div>


<div class="row">
    <div class="col-lg-12">
        <div class="card">

            <div class="card-header  d-flex justify-content-between">


                <div class="card-body">
                    <div class="row">

                        <table class="table table-sm  table-striped table-hover">
                            <thead>

                                <tr>
                                    <th>@Model._l("No")</th>
                                    <th>@Model._l("Course Employee")</th>
                                    <th></th>


                                </tr>
                            </thead>
                            <tbody>


                                @foreach (var course in Model.TempTrgHists)
                                {
                                    <tr>

                                        <td>
                                            @course.EmpNo


                                        </td>
                                        <td>
                                            @Model._h.StaffData(course.EmpNo).EmpNameA


                                        </td>


                                    </tr>
                                }


                            </tbody>


                        </table>

                    </div>


                </div>
            </div>
        </div>
    </div>
</div>


<form action="~/Training/TrainingCost" method="post" class="ajax">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-header  d-flex justify-content-between">


                    <div class="card-body">
                        <div class="row">
                            <div class="col-4"><input type="text" name="Total"/></div>
                            <div class="col-4"><input type="text" name="Cost"/></div>
                            <div class="col-4"><input type="file" name="file" /></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="create-modal" role="dialog" aria-labelledby="edit-create-modal" aria-hidden="true">

        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("Add Employee")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>

                    <input type="hidden" name="Dfrom" value="@Model.TempTrgMa.CourseStartDate" />
                    <input type="hidden" name="Dto" value="@Model.TempTrgMa.CourseEndDate" />
                    <input type="hidden" name="CourseId" value="@Model.TempTrgMa.CourseCode" />
                    <input type="hidden" name="SalNo" value="@Model.TempTrgMa.SalNo" />
                    <input type="hidden" name="SupplierId" value="@Model.TempTrgMa.CourseSupplierCode" />
                    <input type="hidden" name="PlaceId" value="@Model.TempTrgMa.CountryCode" />
                    <input type="hidden" name="Cost" value="TrainingComms.Cost" />
                    <input type="hidden" name="Total" value="TrainingComms.Total" />

                </div>


            </div>
        </div>
    </div>

    <div class="d-flex justify-content-center">

        <input type="submit" name="btn" class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block" />@Model._l("SEND")"

       
    </div>
</form>

