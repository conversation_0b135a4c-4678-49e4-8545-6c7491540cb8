﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

@Html.Partial("_DgAppTabs")

<div class="card shadow">

    <table class="table table-sm datatable table-striped table-hover">
        <thead>

            <tr>
                <th>@Model._l("No")</th>
                <th>@Model._l("Staff")</th>
                <th>@Model._l("Request date")</th>
                <th>@Model._l("Required Qualification")</th>
                <th>@Model._l("Field of Study")</th>
                <th>@Model._l("Reason")</th>
                <th>@Model._l("Approval Date")</th>
                
                <th>@Model._l("DG Manager Approval Date")</th>
                <th>@Model._l("DG Manager Remark")</th>
                <th>@Model._l("Status")</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.TqualReqs)
            {
                <tr>
                    <td> @request.InYear / @request.InDeptInd / @request.InMailNo / @request.InDocSlNo </td>
                    <td>@Model._h.StaffData(request.EmpNo).EmpNo  <br> @Model._h.StaffData(request.EmpNo).EmpNameA</td>
                    <td>@Model._d(@request.TxnDate)</td>
                    <td>@request.TqualCode.QualDespA</td>
                    <td>@request.TSubjectCode.SubjDespA</td>                  
                    <td>@request.Reson</td>
                    <td> @if(@request.ApprovalDate != null) { @Model._d(@request.ApprovalDate) } </td>
                    <td>
                        @if (@request.DGMangAprrDate != null)
                        {
                         @Model._d(@request.DGMangAprrDate)
                        }
                        
                       
                        
                    
                    </td>
                    <td>@request.DGMangAprrRem</td>
              
                    <td>
                        @if (request.SectionStat == 1 && (request.DGMangStat == 0 || request.DGMangStat== null))
                        {
                            <form action="~/Training/DGMangerDecline" class="ajax" method="post">
                                <div class="modal fade" id="department-manager-decline-modal" role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header bg-danger">
                                                <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("Declined by department manager")</h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <label for="">@Model._l("Decline Note")</label>
                                                <textarea maxlength="225" name="DeclineNote" class="form-control"></textarea>
                                            </div>
                                            <div class="modal-footer d-flex">
                                                <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Cancel")</button>
                                                <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="InYear" value="@request.InYear">
                                <input type="hidden" name="InDeptInd" value="@request.InDeptInd">
                                <input type="hidden" name="InDocSlNo" value="@request.InDocSlNo">
                                <input type="hidden" name="InMailNo" value="@request.InMailNo">
                                <input type="hidden" name="EmpNo" value="@request.EmpNo">
                            
                            </form>

                            <div class="d-flex justify-content-center">
                                <a href="~/Training/DGMangerApprove?InYear=@request.InYear&InDeptInd=@request.InDeptInd&InDocSlNo=@request.InDocSlNo&InMailNo=@request.InMailNo&EmpNo=@request.EmpNo"
                                   class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block">@Model._l("Approve")</a>

                                <a href="#" class="btn btn-larg btn-danger mx-1  rounded-pill btn-block" data-toggle="modal" data-target="#department-manager-decline-modal">@Model._l("Decline")</a>
                            </div>
                        }
                        else
                        {
                            @request.ReqStat 
                           

                        }

                    </td>

                </tr>
            }
        </tbody>
    </table>

</div>
