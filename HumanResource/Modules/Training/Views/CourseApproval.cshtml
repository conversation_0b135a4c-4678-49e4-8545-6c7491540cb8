﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<br>
<br>
<br>
<div class="card shadow">

    <table class="table table-sm datatable table-striped table-hover">
        <thead>

            <tr>
                <th>@Model._l("No")</th>
            <th>@Model._l("الملف")</th>
            <th>@Model._l("مقدم الطلب ")</th>
                <th>@Model._l("تاريخ الطلب")</th>
                <td>@Model._l("البرنامج")</td>
              
                <td>@Model._l(":من تاريخ ")</td>
                <th>@Model._l("الى تاريخ:")</th>
                
                <th>@Model._l("مكان البرنامج")</th>
            <th>@Model._l("الحالة")</th>
            
            </tr>
        </thead>
        <tbody>
                @foreach (var course in Model.TempTrgReq)
                    {
            <tr>
                     <td>
                        <a href="/training/CourseApprovalEmp/@course.ReqNo"> #@course.ReqNo</a>
                   
                     </td>
                     <td>
                        @if (course.FileGuid != null)
                        {
                            <a href="@Model._h.Storage().Get(course.FileGuid)"><i class="fa fa-file"></i></a>
                        }
                    </td>
                      <td>
                            @course.BossNo
  
                      </td>
                       <td>
                        @course.ReqDate

                       </td>
                       <td>
                        @course.CourseName

                       </td>
                       <td>
                        @course.CourseStartDate

                      </td>

                      <td>
                        @course.CourseEndDate

                     </td>
                     <td></td>
                     <td>
                        @if(course.Approval==0){
                            <span class="badge badge-primary">@Model._("Pending")</span>
                        }
                        @if (course.Approval==1)
                        {
                        <span class="badge badge-success">@Model._("Approvied")</span>
                        }

                        @if (course.Approval==2)
                        {
                        <span class="badge badge-danger">@Model._("Rejected")</span>
                        }
                    </td>
                      </tr>
                    }
         </tbody>
           
            </table>

</div>
         