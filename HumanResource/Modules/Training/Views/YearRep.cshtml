﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel



<form action="~/Training/Years method="post" class="ajax">
    <div class="row">
        <div class="col-lg-3"></div>
        <div class="col-lg-3">
            <label for="">@Model._("Year")</label>
            <select name="Year" class="select2" required>
                <option selected disabled hidden>@Model._("Select a Year")</option>
                @foreach (var m in Model.Years)
                {
                    <option value="@m.Year">
                        @m.Year

                    </option>
                }

            </select>
        </div>
     
        <div class="col-lg-3"></div>
        </div>
        <br />
    <div class="row">
        <div class="col-lg-3"></div>
        

        <div class="col-lg-3">
        <div class="d-flex justify-content-between">

            <div>
                    <a href="~/Training/ReportCourses?id=#" class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block">@Model._l("Approve")</a>

                </div>
        </div>
    </div>
    <div class="col-lg-3"></div>
    </div>
</form>
