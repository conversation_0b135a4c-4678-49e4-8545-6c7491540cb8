﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<br>
<br>
<br>
<div class="card shadow">

    <table class="table table-sm datatable table-striped table-hover">
        <thead>

            <tr>
                <th>@Model._l("إسم البرنامج التدريبي")</th>
                <th>@Model._l("التكلفة المالية")</th>
                <th>@Model._l("عدد المشاركين في البرناممج ")</th>
                <td>@Model._l(":من تاريخ ")</td>
                <th>@Model._l("الى تاريخ:")</th>

           

            </tr>
        </thead>
        <tbody>
            @foreach (var course in Model.TempTrgMas)
            {
                <tr>
              
                    <td>
                        @course.TcourseCatalogue.CourseNameA

                    </td>
                    <td>
                        @course.Fees

                    </td>
                    <td>
                        @course.NoofPeople

                    </td>
                    <td>
                        @course.CourseStartDate

                    </td>

                    <td>
                        @course.CourseEndDate

                    </td>
                   </tr>
            }
        </tbody>

    </table>

</div>
