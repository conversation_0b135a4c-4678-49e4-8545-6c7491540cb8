﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<div class="card shadow">
    <h3>@Model._l("Skill Report")</h3>
    </div>
    <table class="table table-sm datatable table-striped table-hover">
        <thead>

            <tr>
                <th>@Model._l("إسم الوظف")</th>
                <th>@Model._l("الدائرة")</th>
                <td>@Model._l("المهارات")</td>
                <th>@Model._l("العائد الوظيفي المكتسب")</th>




            </tr>
        </thead>
        <tbody>
        @foreach (var item in Model.trainigPlans)
            {
                <tr>


                    @*<td>
                        @Model._h.StaffData(@item.EmpId).EmpNameA


                    </td>
                    <td>
                        @Model._h.StaffData(@item.EmpId).DeptDespA

                    </td>*@
                    <td>@item.CreatedBy</td>
                  @*  <td>
                        @item.SpecialiedSkill.EmpSkill.Name

                    </td>

                    <td>
                    @item.SpecialiedSkill.EmpImpact.Name

                    </td>*@
                </tr>
            }
        </tbody>

    </table>
