﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<h3>@Model._("متابعة قسم التدريب ")</h3>


<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header  d-flex justify-content-between">
            </div>

            <div class="card-body">
                <div class="row">
                    <table class="table">
                        <tr>

                        <th>@Model._l("Request date")</th>
                        <td>@Model._d(@Model.TempTrgMa.TimeStamp)</td>

                        </tr>
                        <tr>
                            <th>@Model._l("Course Name")</th>
                            <td>@Model.TempTrgMa.TcourseCatalogue.CourseNameA</td>
                        </tr>
                        <tr>
                            <th>@Model._l("Course start Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseStartDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("Course End Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseEndDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("التكــلفـة")</th>
                            <td>@Model.TempTrgMa.Fees</td>
                        </tr>
                        <tr>
                            <th>@Model._l("البـلـد")</th>
                            <td>@Model.TempTrgMa.TcountryCode.CountryDespA</td>
                        </tr>


                      <tr>
                            <th>@Model._l("المؤوسسة التدريبية")</th>
                            <td>@Model.TempTrgMa.TcourseSupplier.CourseSupplierName_A</td>
                        </tr>


                    </table>
                </div>
            </div>
        </div>

    </div>
</div>


<div class="row">
    <div class="col-lg-12">
        <div class="card">

            <div class="card-header  d-flex justify-content-between">
                <div>
                    <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
                        <i class="fas fa-plus"></i>  @Model._("Add Employee")
                    </a>
                </div>


                <div class="card-body">
                    <div class="row">

                        <table class="table table-sm  table-striped table-hover">
                            <thead>

                                <tr>
                                    <th>@Model._l("No")</th>
                                    <th>@Model._l("Course Employee")</th>
                                    <th></th>


                                </tr>
                            </thead>
                            <tbody>


                                @foreach (var course in Model.TempTrgHists)
                                {
                                    <tr>

                                        <td>
                                            @course.EmpNo


                                        </td>
                                        <td>
                                            @Model._h.StaffData(course.EmpNo).EmpNameA


                                        </td>

                                        <td>
                                            <a href="/Training/Delete/@course.Id">
                                                <span class="badge badge-danger">@Model._("Delete")</span>
                                            </a>

                                        </td>
                                    </tr>
                                }


                            </tbody>


                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<form action="~/Training/ManagerCourseApproval" method="post" class="ajax">
    <div class="modal fade" id="create-modal" role="dialog" aria-labelledby="edit-create-modal" aria-hidden="true">

        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("Add Employee")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                 <input type="hidden" name="CourseStartDate" value="@Model.TempTrgMa.CourseStartDate" />
                    <input type="hidden" name="CourseEndDate" value="@Model.TempTrgMa.CourseEndDate" />
                    <input type="hidden" name="CourseCode" value="@Model.TempTrgMa.TcourseCatalogue.CourseCode" />
                    <input type="hidden" name="TimeStamp" value="@Model.TempTrgMa.TimeStamp" />
                    <input type="hidden" name="SalNo" value="@Model.TempTrgMa.SalNo" />
                    <input type="hidden" name="CourseSupplierCode" value="@Model.TempTrgMa.CourseSupplierCode" />
                </div>
                <div class="modal-body">
                    <div class="row">
                        <select name="EmpNo" class="form-control select2">
                            <option value="" disabled selected>@Model._("Select option")</option>

                            @foreach (var item in Model.VempDtl)
                            {
                                <option name="EmpNo" value="@item.EmpNo" data-name="@item.EmpNameA">@item.EmpNo - @item.EmpNameA</option>

                            }



                        </select>
                    </div>
                </div>
                <div class="modal-footer d-flex">
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>

                </div>


            </div>
        </div>
    </div>

</form>

