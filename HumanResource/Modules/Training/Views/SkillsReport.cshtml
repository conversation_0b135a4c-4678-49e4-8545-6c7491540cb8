﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

@*<div class="card shadow">
    <h3>@Model._l("Skill Report")</h3>*@
    <form method="get" action="@Url.Action("SkillsReport")" id="filterForm">
    <div class="row">
        <div class="col-6">
            <lable for="skillid">@Model._l("select skill")</lable>
            <select id="skillid" name="skillid" class="form-control" onchange="filterResult()">

            <option value="">--select skill--</option>
            @foreach (var skill in Model.EmpSkills)
                {
                    string selected = (skill.Id.ToString() == Context.Request.Query["dgid"]) ? "selected" : "";
                    <option value="@skill.Id">
                   
                        @skill.Name
                    </option>
            }

        </select>
        </div>
        <div class="col-6">
            <lable for="dgid">@Model._l("select DG")</lable>
            <select id="dgid" name="dgid" class="form-control" onchange="filterResult()">

            <option value="">--select DG--</option>
                @foreach (var d in Model.VempDtls)
            {
                    string selected =(d.DgCode.ToString() == Context.Request.Query["dgid"]) ? "selected" : "";
                    <option value = "@d.DgCode">@selected
             
                        @d.DgDespA
                    </option>
            }

        </select>
        </div>
    </div>
    <table class="table table-sm datatable table-striped table-hover">
        <thead>

            <tr>
                <th>@Model._l("إسم الموظف")</th>
                <th>@Model._l("الدائرة")</th>
                <td>@Model._l("المهارات")</td>
                <th>@Model._l("العائد الوظيفي المكتسب")</th>
               



            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.specialiedSkills)
            {
                <tr>


                    <td>
                        @Model._h.StaffData(@item.EmpId).EmpNameA


                    </td>
                    <td>
                        @Model._h.StaffData(@item.EmpId).DeptDespA

                    </td>

                    <td>
                        @item.EmpSkill.Name

                    </td>

                    <td>
                        @item.EmpImpact.Name

                    </td>
                </tr>
            }
        </tbody>

    </table>
    </form>



   






@section Scripts {

    <script type="text/javascript">
        function filterResult() {
         document.getElementById('filterForm').submit();
        }
    </script>
     
 @*       //document.addEventListener("DOMContentLoaded",function(){
        //    const urlPrams=new URLSearchParams(window.location.search);
        //    const skillId=urlPrams.get("skillId");
        //    const DgId=urlPrams.get("DgId")

        //    if(skillId){
        //        document.getElementById("skillId").value=skillId;
        //    }
        //    if (DgId) {
        //        document.getElementById("DgId").value = DgId;
        //    }

        //});

        //function filterResult(){
        //    var skillId=document.getElementById("skillId").value;
        //    var DgId=document.getElementById("DgId").value;

        //    var url = '@Url.Action("skilldReport","Training")'+
        //    '?skillId='+encodeURIComponent(skillId)+
        //    '$DgId'+encodeURIComponent(DgId);

        //    window.location.href=url;
        //}*@
 
}
