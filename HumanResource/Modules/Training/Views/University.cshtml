﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel
<div class="d-flex justify-content-between py-2">
    <h3>@Model._("University")</h3>
    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
            <i class="fas fa-plus"></i>  @Model._("New University")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("University Description A")</th>
                <th>@Model._("University Description E")</th>
                <th>@Model._("University Place")</th>
                <th>@Model._("University Country")</th>
                @* <th> </th>*@
            </tr>
        </thead>
        <tbody>
            @foreach (var code in Model.TunivInstCodes)
            {
                <tr>
                    <td><a href="#" data-toggle="modal" data-target="#<EMAIL>-modal">@code.UnivInstCode</a></td>
                    <td>@code.UnivInstDespA</td>
                    <td>@code.UnivInstDespE</td>
                    <td>@code.UnivInstPlace</td>
                    <td>@code.CountryCode</td>
                    @* <td>
                <a onclick="showInPopup('@Url.Action("CreateAcademicDegree","Training",new {id=code.QualCode},Context.Request.Scheme)','@Model._l("Edit")')" class="btn btn-info text-white"><i class="fas fa-pencil-alt"></i> @Model._l("Edit")</a>
                </td>*@
                </tr>
            }
        </tbody>
    </table>
</div>

<form action="~/Training/CreateUniversity" method="post" class="ajax">
    <div class="modal fade" id="create-modal" tabindex="-1" role="dialog" aria-labelledby="create-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("New University")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">@Model._("University Description A")</label>
                    <input type="text" required name="UnivInstDespA" class="form-control"><br>

                    <label for="">
                        @Model._("University Description E")
                    </label>
                    <input type="text" name="UnivInstDespE" class="form-control"><br>

                        <label for="">@Model._("University Place")</label>
                    <input type="text" name="UnivInstPlace" class="form-control"><br>

                    <label for="">@Model._("University Country")</label>
                    <input type="text" name="CountryCode" class="form-control"><br>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>




@section Scripts{
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}