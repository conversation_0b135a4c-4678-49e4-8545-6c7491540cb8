﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<h3>@Model._(" Manager Course Apporoval")</h3>


<div class="row">
    <div class="col-lg-12">
        <div class="card">




                <div class="card-body">
                    <div class="row">

                        <table class="table table-sm  table-striped table-hover">
                            <thead>

                                <tr>

                                    <th>@Model._l("أسماء المرشحيين للبرنامج")</th>
                                    <th></th>


                                </tr>
                            </thead>
                            <tbody>


                                @foreach (var course in Model.TempReqDtls)
                                {
                                    <tr>


                                        <td>
                                            @Model._h.StaffData(course.EmpNo).EmpNameA
                                            

                                        </td>
                                        <td>@course.ReqNo</td>
                                    </tr>
                                <div class="d-flex justify-content-center">
                                    <a href="~/Training/Submit3?id=@course.ReqNo&stat=1" class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block">@Model._l("Approve")</a>
                                    <a href="~/Training/Submit3?id=@course.ReqNo&stat=2" class="btn btn-larg btn-danger mx-1 after-confirm rounded-pill btn-block">@Model._l("Decline")</a>



                                </div>
                               
                                }
             
                            </tbody>


                        </table>

            
                    </div>
                </div>
            </div>

            <br />
            <br />

