﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel
@Html.Partial("_TrainingAppTabs")

<div class="d-flex justify-content-between py-2">


    <h3>@Model._("View Courses")</h3>

   
</div>
<form action="~/Training/ViewCourses" method="post" class="ajax">
<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>
                <th>@Model._("NO")</th>
                <th>@Model._("اسم البرنامج")</th>
                <th>@Model._("الجهـة المنفـذة ")</th>
                <th>@Model._("الـبـلـد")</th>
                <th>@Model._("تاريخ بدء البرنامج")</th>
                <th>@Model._("تاريخ إنتهاء البرنامج")</th>
                <th>@Model._("الحــالـة")</th>
                <th>@Model._(" ")</th>
                @* <th></th>*@
            </tr>
        </thead>
        <tbody>
            @foreach (var code in Model.TempTrgMas)
            {
                <tr>
                           <td>
                        <a href="/training/TrainingTrack/@code.SalNo"> #@code.SalNo</a>

                </td>
                    <td>@code.TcourseCatalogue.CourseNameA</td>
                    <td>@code.TcourseSupplier.CourseSupplierName_A</td>
                    <td>@code.TcountryCode.CountryDespA</td>
                    <td>@code.CourseStartDate</td>
                    <td>@code.CourseEndDate</td>
                    
                        @foreach(var item in Model.TempTrgMas)
                        {
                            <input type="hidden" value="@item.TempTrgHist.">
                            
                        }

                       
                    
                  <td>

                     
                        @if (code.Approval == 0)
                        {
                            <span class="badge badge-danger">@Model._("Pending")</span>
                        }
                        @if (code.Approval == 1)
                        {
                            <span class="badge-success">@Model._("تمت الموافقة")</span>
                        }

                        @if (code.Approval == 2)
                        {
                            <span class="badge badge-danger">@Model._("مرفوض")</span>
                        }

                        @if (code.Approval == 3)
                        {
                            <span class="badge badge-danger">@Model._("رفع للمسؤول الأعلى")</span>
                        }
                    </td>

                    <td>
                    @if (code.Approval == 1)
                    {
                            <a href="~/Training/DeptManager?sal=@code.SalNo" class="btn btn-larg btn-primary mx-1 after-confirm rounded-pill btn-block">إرسال للمسؤول المباشر</a>
                    }
                    </td>
                   
               </tr> 
        
            }
         
        </tbody>
    </table>
</div>

</form>





