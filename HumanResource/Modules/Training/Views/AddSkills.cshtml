﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model._(" Skill Catalogue ")</h3>

    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
            <i class="fas fa-plus"></i>  @Model._("Add Skill")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Skill NAME ARABIC ")</th>
          
            </tr>
        </thead>
        <tbody>
            @foreach (var code in ViewBag.GetAllSkills)
            {
                <tr>
                    <td><a href="#" data-toggle="modal" data-target="#<EMAIL>-modal">@code.Id</a></td>
                    <td>@code.Name</td>
                   

                </tr>
            }
        </tbody>
    </table>
</div>







<form action="~/Training/AddSkills" method="post" class="ajax">
    <div class="modal fade" id="create-modal" tabindex="-1" role="dialog" aria-labelledby="create-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("New Skill")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">@Model._("Skill NAME")</label>
                    <input type="text" required name="Name" class="form-control"><br>

            

                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>




@section Scripts{
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}