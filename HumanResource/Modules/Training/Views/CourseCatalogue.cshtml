﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model._(" Course Catalogue ")</h3>

    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
            <i class="fas fa-plus"></i>  @Model._("Course Catalogue")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("COURSE  NAME ARABIC ")</th>
                <th>@Model._("COURSE  ENGLISH")</th>
              @*  <th>@Model._("COURSE SUBJECT")</th>*@
                @* <th></th>*@
            </tr>
        </thead>
        <tbody>
            @foreach (var code in Model.TcourseCatalogue)
            {
                <tr>
                    <td><a href="#" data-toggle="modal" data-target="#<EMAIL>-modal">@code.CourseCode</a></td>
                    <td>@code.CourseNameA</td>
                    <td>@code.CourseNameE</td>
                @*    <td>@code.TcourseType.CourseTypeDesp</td>
                    <td>@code.TtrgPlaceCode.TrgPlaceCode</td>*@
                    
                </tr>
            }
        </tbody>
    </table>
</div>




<form action="~/Training/CreateCourseCatalogue" method="post" class="ajax">
    <div class="modal fade" id="create-modal" tabindex="-1" role="dialog" aria-labelledby="create-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("New Course Catalogue")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">@Model._("COURSE CATALOGUE NAME ARABIC")</label>
                    <input type="text" required name="CourseNameA" class="form-control"><br>

                    <label for="">@Model._("COURSE CATALOGUE NAME ENGLISH")</label>
                    <input type="text" name="CourseNameE" class="form-control"><br>
                  
                    <div class="row">
                        <div class="form-group col-6">
                            <label for="">@Model._("COURSE PLACE")</label>
                            <select name="TrgPlaceCode" class="select2" required>
                                <option selected disabled hidden>@Model._("Select a PLACE")</option>
                                @foreach (var course in Model.TtrgPlaceCode)
                                {
                                    <option value="@course.TrgPlaceCode">
                                        @course.TrgPlaceDespA

                                    </option>
                                }
                            </select>
                        </div>
                    
                    <div class="form-group col-6">
                        <label for="">@Model._("COURSE TYPE")</label>
                            <select name="CourseType" class="select2" required>
                            <option selected disabled hidden>@Model._("Select a Course")</option>
                            @foreach (var course in Model.TcourseType)
                            {
                                <option value="@course.CourseType" >
                                    @course.CourseTypeDesp 

                                </option>
                            }

                        </select>
                    </div>
                    </div>


                    <div class="row">
                        <div class="form-group col-6">
                            <label for="">@Model._("COURSE CATAGORY")</label>
                            <select name="CourseCatCode" class="select2" required>
                            <option selected disabled hidden>@Model._("Select a CATOGORY")</option>
                            @foreach (var course in Model.TcourseCatCode)
                            {
                                <option value="@course.CourseCatCode">
                                    @course.CourseCatDesp

                                </option>
                            }
                        </select>
                        </div>
                        <div class="form-group col-3">
                        <div class="form-check" >
                                <input class="form-check-input" type="radio" name="ClassOnline" id="0" value="1" />
                        <label class="form-check-input" for="0">At class</label>
                         </div>
                         </div>



                        <div class="form-group col-3">
                            <div>
                                <input class="form-check-input" type="radio" name="ClassOnline" id="1" value="2" />
                                <label class="form-check-input" for="1">OnLine</label>
                            </div>
                        </div>


                        </div>
                    <div class="row">
                    

                        <div class="form-group col-6">
                            <div >
                                <input class="form-check-input" type="radio" name="Fees" id="yes" value="yes" />
                                <label class="form-check-input" for="yes">with fees</label>
                            </div>
                        </div>


                        <div class="form-group col-6">
                            <div>
                                <input class="form-check-input" type="radio" name="Fees" id="no" value="no" />
                                <label class="form-check-input" for="no">free</label>
                            </div>
                        </div>


                    </div>
                         
                  


                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>




@section Scripts{
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}