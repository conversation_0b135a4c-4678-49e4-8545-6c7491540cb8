﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<div class="d-flex justify-content-between py-2">


    <h3>@Model._("Academic Degree")</h3>

    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal">
            <i class="fas fa-plus"></i>  @Model._("New Academic Degree")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Qualification Description A")</th>
                <th>@Model._("Qualification Description E")</th>
                <th>@Model._("Qualification HIER LEVEL")</th>
              @* <th></th>*@
            </tr>
        </thead>
        <tbody>
            @foreach (var code in Model.TqualCodes)
            {
                <tr>
                    <td><a href="#" onclick="get_TqualCodes_data(@code.QualCode)">#@code.QualCode</a> </td>
                    <td>@code.QualDespA</td>
                    <td>@code.QualDespE</td>
                    <td>@code.QualHierLevel</td>
                   @* <td>
                        <a onclick="showInPopup('@Url.Action("CreateAcademicDegree","Training",new {id=code.QualCode},Context.Request.Scheme)','@Model._l("Edit")')" class="btn btn-info text-white"><i class="fas fa-pencil-alt"></i> @Model._l("Edit")</a>
                    </td>*@
                </tr>
            }
        </tbody>
    </table>
</div>

<form action="~/Training/CreateAcademicDegree" method="post" class="ajax">
    <div class="modal fade" id="create-modal" tabindex="-1" role="dialog" aria-labelledby="create-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("New Academic Degree")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">@Model._("Qualification Description A")</label>
                    <input type="text" required name="QualDespA" class="form-control"><br>

                    <label for="">@Model._("Qualification Description E")</label>
                    <input type="text" name="QualDespE" class="form-control"><br>

                    <label for="">@Model._("Qualification HIER LEVEL")</label>
                    <input type="text" name="QualHierLevel" class="form-control"><br>

                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>


<div class="modal fade" id="edit-TqualCodes-modal" role="dialog" aria-labelledby="edit-TqualCodes-modalLabel" aria-hidden="true">

    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="edit-TqualCodes-modalLabel">@Model._l("Edit Academic Degree record")</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="edit-modal-html">
            </div>
        </div>
    </div>
</div>
<script>

    function get_TqualCodes_data(id) {
        $.get("/Training/ViweAcademicDegreeRecord/" + id, function (data) {
            $('#edit-modal-html').html(data);
            $('#edit-TqualCodes-modal').modal().show();

        });
    }


</script>


@*@section Scripts{
    @{
          await Html.RenderPartialAsync("_ValidationScriptsPartial");

    }

}*@