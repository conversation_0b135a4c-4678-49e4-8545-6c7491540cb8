﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<div class="d-flex justify-content-between py-2">

    <form action="~/Training/SpecialiedSkill" method="post" class="ajax">

        <div class="card shadow" id="app">
            <div class="card-body">
                <h3 class="modal-title" id="create-modalLabel">@Model._l("Create SpecialiedSkill")</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">


                <div class="row">
                    <div class="form-group col-6">
                        <label for="">@Model._("Skill Name")</label>
                        <select name="SkillId" asp-items="ViewBag.GetAllSkills" class="form-control" required>
                          <option>
                              select a Skill
                                </option>
                            }
                        </select>
                    </div>
                    <div class="form-group col-6">
                        <label for="">@Model._("Impact Name")</label>
                        <select name="ImpactId" asp-items="ViewBag.GetALLImpacts" class="form-control" required>
                            <option>
                                select employee Impacts
                            </option>
                            }
                        </select>
                    </div>
                   
                </div>
                <div class="row">
                    <div class="col-md-10">
                        <div>
                            <label for="">@Model._l("Department Manager")</label>
                            <select name="ApprovedBy" class="select2" required>
                                <option value="0" selected disabled hidden>@Model._l("Select Staff")</option>
                                @foreach (var staff in Model._h.Managers(Model.Auth.EmpNo))
                                {
                                    <option value="@staff.EmailId">@staff.EmpNo - @staff.EmpNameA</option>
                                }
                            </select>
                        </div>
                        <br>
                    </div>
                    </div>

                <div class="row">

                    <div> 
                        <button type="submit" class="btn btn-primary">@Model._l("Submit")</button>

                       
                    </div>
                    </div>

            </div>
        </div>
    </form>


</div>


