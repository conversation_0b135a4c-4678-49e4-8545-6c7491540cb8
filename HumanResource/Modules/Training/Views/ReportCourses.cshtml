﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<br>
<br>
<br>
<div class="card shadow">

    <table class="table table-sm datatable table-striped table-hover">
        <thead>

            <tr>
                <th>@Model._l("إسم الوظف")</th>
                <th>@Model._l("إسم البرنامج التدريبي")</th>
                <td>@Model._l(":من تاريخ ")</td>
                <th>@Model._l("الى تاريخ:")</th>
                <th>@Model._l("المؤوسسة التدريبية")</th>
                <th>@Model._l("البلد ")</th>



            </tr>
        </thead>
        <tbody>
            @foreach (var course in Model.TempTrgHists)
            {
                <tr>

                  
                    <td>
                        @Model._h.StaffData(@course.EmpNo).EmpNameA


                    </td>
                    <td>
                            @course.TcourseCatalogue.CourseNameA

                    </td>

                    <td>
                            @course.CourseStartDate

                    </td>

                    <td>
                            @course.CourseEndDate

                    </td>

                    <td>
                            @course.TempTrgMas.TcourseSupplier.CourseSupplierName_A

                    </td>

                    <td>
                            @course.TempTrgMas.TcountryCode.CountryDespA
                            </td>
                </tr>
            }
        </tbody>

    </table>

</div>




