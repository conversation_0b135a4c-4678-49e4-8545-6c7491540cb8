﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<h3>@Model._(" Manager Course Apporoval")</h3>


<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header  d-flex justify-content-between">
            </div>

            <div class="card-body">
                <div class="row">
                    <table class="table">
          
                        <tr>
                            <th>@Model._l("Course Name")</th>
                            <td>@Model.TempTrgMa.TcourseCatalogue.CourseNameA</td>
                        </tr>
                        <tr>
                            <th>@Model._l("Course start Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseStartDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("Course End Date")</th>
                            <td>@Model._d(@Model.TempTrgMa.CourseEndDate)</td>
                        </tr>

                        <tr>
                            <th>@Model._l("البـلـد")</th>
                            <td>@Model.TempTrgMa.TcountryCode.CountryDespA</td>
                        </tr>


                        <tr>
                            <th>@Model._l("المؤوسسة التدريبية")</th>
                            <td>@Model.TempTrgMa.TcourseSupplier.CourseSupplierName_A</td>
                        </tr>
                              


                        <tr>
                            <th>@Model._l("المرفق")</th>
                            @if(Model.TrainingComm.FileNo>0){
                           
                                <td><a href="@Model._h.GetFile(Model.TrainingComm.FileGuid)">عرض الملف</a></td>
                            }
                            else
                            {
                              <td>لا يوجد ملف</td>
                            }
                        </tr>
                    </table>
                </div>
            </div>
        </div>

    </div>
</div>


<div class="row">
    <div class="col-lg-12">
        <div class="card">

            <div class="card-header  d-flex justify-content-between">
               

                <div class="card-body">
                    <div class="row">

                        <table class="table table-sm  table-striped table-hover">
                            <thead>
                           
                                <tr>
                                    <th>@Model._l("No")</th>
                                    <th>@Model._l("Course Employee")</th>
                                </tr>
                            </thead>
                            <tbody>


                                @foreach (var course in Model.TempTrgHists)
                                {
                                    <tr>

                                        <td>
                                            @course.EmpNo

                                        </td>
                                        <td>
                                            @Model._h.StaffData(course.EmpNo).EmpNameA

                                        </td>  </tr>
                                }


                            </tbody>


                        </table>

                    </div>


                </div>
            </div>
        </div>
    </div>
</div>


<form action="~/Training/FinanceManagerApproval" method="post" class="ajax">


    <div class="row">
        <div class="col-lg-12">
            <div class="card">

                <div class="card-header  d-flex justify-content-between">


                    <div class="card-body">
                        <div class="row">

                            <div class="col-3">
                           الموزانة:  <label name="comm">10201</label>
                               
                                </div>
                            <div class="col-3">
                                الحساب: <label name="comm">2-101-33-16</label>
                            </div>
                            <div class="col-3">
                                المستفيد:
                                <select name="VendorNo" class="select2" required>
                                    @foreach (var item in ViewBag.GetVendor)
                                    {
                                        var isSelected = item.Id == Model.TrainingComm.VendorNo? "selected" : "";

                                        <option value="@item.Id" selected="@isSelected"> @item.Name</option>
                                    }
                                   
                                </select>
                            </div>
                            <input type="hidden" name="SalNo" value="@Model.TempTrgMa.SalNo" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
      
            <div class="card-body">
                <div class="row">
            <div class="col-6">
              
                @Model._l("التكلفة")
                <input type="text" name="Cost" value="@Model.TrainingComm.Cost" class="form-control" />
                </div>
            

            <div class="col-6">
                @Model._l("الإجمالي")
               <input type="text" name="Total" value="@Model.TrainingComm.Total" class="form-control"/>
            </div>
        </div>
        </div>
    </div>



      <div class="modal fade" id="create-modal" role="dialog" aria-labelledby="edit-create-modal" aria-hidden="true">

        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="create-modalLabel">@Model._l("Add Employee")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                 
                    
                    <input type="hidden" name="SalNo" value="@Model.TempTrgMa.SalNo" />
                 
                    <input type="hidden" name="VendorNo" value="@Model.TrainingComm.VendorNo" />
                    <input type="hidden" name="AccountNo" value="2-101-33-16"/>
                    <input type="hidden" name="Id" value="@Model.TrainingComm.Id" />
                </div>
               

            </div>
        </div>
    </div>

    <div class="d-flex justify-content-center">

        <input type="submit" name="btn" class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block" />

  
        <a href="~/Training/FinanceManagerApproval" class="btn btn-larg btn-danger mx-1 after-confirm rounded-pill btn-block">@Model._l("Decline")</a>
    </div>
    </form>
  