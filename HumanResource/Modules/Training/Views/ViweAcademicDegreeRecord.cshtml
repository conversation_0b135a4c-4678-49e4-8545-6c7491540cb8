﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

@{
    Layout = null;
}


<form action="~/Training/CreateAcademicDegree/@Model.TqualCode.QualCode" method="post" class="ajax">
    <div class="row">
        <div class="col-lg-6">
        <label for="">@Model._("Qualification Description A")</label>
            <input type="text" required name="QualDespA" class="form-control" value="@Model.TqualCode.QualDespA"><br>
            <label for="">@Model._("Qualification HIER LEVEL")</label>
            <input  type="number" min="1" step="1" name="QualHierLevel" class="form-control" value="@Model.TqualCode.QualHierLevel"><br>
        
        </div>
        <div class="col-lg-6">
            <label for="">@Model._("Qualification Description E")</label>
            <input type="text" name="QualDespE" class="form-control" value="@Model.TqualCode.QualDespE"><br>
       
     </div>
        <div class="d-flex justify-content-between">
          
            <div>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("Close")</button>
                <button type="submit" class="btn btn-primary">@Model._l("Save")</button>
            </div>
        </div>
    </div>
        
     
     

</form>
