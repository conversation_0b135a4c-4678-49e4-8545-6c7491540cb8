﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<div class="row" >
    <div class="col-3">
    <select>
        <option>Select Skill</option>
        @foreach (var item in ViewBag.EmpSkills)
        {
         <option value="@item.Id">@item.Name</option>
        }
    </select>
    </div>
    <div class="col-3">
        <select>
            <option>Select DG</option>
            @foreach (var item in ViewBag.GetAlldg)
            {
                <option value="@item.DgCode">@item.DgDespA</option>
            }
        </select>
    </div>
</div>
<table class="table table-sm datatable table-striped table-hover">
    <thead>

        <tr>
            <th>@Model._l("No")</th>
            <td>@Model._l("الموظف")</td>
            <td>@Model._l("المهارات")</td>
            <th>@Model._l("العائد الوظيفي المكتسب ")</th>
            <th>@Model._l("تاريخ الطلب")</th>
            <th>    </th>


        </tr>
    </thead>
    <tbody>


            @foreach (var item in Model.specialiedSkills)
         {
            <tr>
                <td>
                <a href="/training/DirectManager/@item.Id"> #@item.Id</a>

                </td>
                <td>
                    @Model._h.StaffData(item.EmpId).EmpNameA

                </td>
                <td>
                        @item.EmpSkill.Name

                </td>

                <td>
                        @item.EmpImpact.Name

                </td>
               
                <td>
                    <a href="~/Training/DirectManagerApproval?id=@item.Id&stat=1" class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block">@Model._l("Approve")</a>
                   
                </td>
                <td> <a href="~/Training/DirectManagerApproval?id=@item.Id&stat=2" class="btn btn-larg btn-danger mx-1 after-confirm rounded-pill btn-block">@Model._l("Decline")</a></td>
            </tr>
           }
     
    </tbody>
</table>

