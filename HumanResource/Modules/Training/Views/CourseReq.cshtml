﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel


<div class="d-flex justify-content-between py-2">





    <form action="~/Training/CourseReq" method="post" class="ajax">

        <div class="card shadow" id="app">
            <div class="card-body">
                <h3 class="modal-title" id="create-modalLabel">@Model._l("Course Request")</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">


                <div class="row">
                    <div class="form-group col-6">
                        <label for="">@Model._("COURSE Name")</label>
                        <input type="text" name="TempTrgReq.CourseName" class="form-control">
                    </div>

           


                    <div class="form-group col-6">
                        <label for="">@Model._("File")</label>
                        <input type="file" name="TempTrgReq.File" class="form-control">
                    </div>


                    <div class="form-group col-6">

                        <label for="">@Model._("التكلفة")</label>
                        <input type="text" name="TempTrgReq.Fee" class="form-control">


            
                     

                    </div>

                    <div class="form-group col-6">

                        <label for="">@Model._("من")</label>
                        <input type="date" name="TempTrgReq.CourseStartDate" class="form-control">


                    </div>
                    <div class="form-group col-6">

                        <label for="">@Model._("إلى")</label>
                        <input type="date" name="TempTrgReq.CourseEndDate" class="form-control">


                    </div>

                    <div class="form-group col-12">

                        <label for="">@Model._("المكان")</label>
                        <input type="text" name="TempTrgReq.Place" class="form-control">


                    </div>

                    <table class="table">
                        <thead>
                            <tr>
                                <th>م</th>
                                <th >@Model._("إسم الموظف")</th>
                                <th></th>

                            </tr>
                        </thead>





                        <tbody>
                            <tr v-for="(item,index) in list">
                                <td>{{index+1}}</td>

                                <td >
                                   
                                    <div style="max-width:500px">
                                        <select class="form-control add-select" :name="'TempReqDtls['+index+'].EmpNo'">
                                            <option value="" disabled selected>@Model._("Select option")</option>

                                            @foreach (var item in Model.VempDtl)
                                            {
                                                <option value="@item.EmpNo" data-name="@item.EmpNameA">@item.EmpNo - @item.EmpNameA</option>
                                            }
                                        </select>
                                    </div>

                                  
                                </td>


                                <td class="text-end">

                                    <button type="button" @@click="list.splice(index,1)" class="btn btn-danger btn-sm rounded">@Model._("Delete")</button>
                                </td>

                            </tr>
                        </tbody>

                        <tfoot>
                            <tr>

                                <td></td>
                                <td>

                             
                                </td>

                          

                                <td colspan="2" class="text-end">
                                    <button type="button"
                                            @@click="  list.push({}); initSelect2()"
                                            class="btn btn-primary btn-sm rounded-pill">
                                        <i class="fa fa-plus"></i> @Model._("Add")
                                    </button> <br>
                                </td>
                            </tr>
                        </tfoot>


                    </table>
                    <div>
                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("إرسـال للإعتمـاد")</button>



                    </div>
                </div>
            </div>
        </div>
    </form>


    </div>


  <script>
        let app = new Vue({
            el: '#app',
            data: {
                list: []
            },

            methods:{
                initSelect2(){
                    setTimeout(function () {
                        $('.add-select').each(function(index,el){
                        
                                $(el).select2()
                        
                        });
                    }, 500)
                }
            }
        });
    </script>