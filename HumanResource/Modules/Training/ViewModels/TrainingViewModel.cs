﻿using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Shared.ViewModels;
namespace HumanResource.Modules.Training.ViewModels
{
    public class TrainingViewModel : BaseViewModel
    {

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        public readonly hrmsContext _context;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        public readonly hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        public readonly IHttpContextAccessor _http;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        private AppHelper _h;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword

        public TrainingViewModel(
            hrmsContext context,
            IHttpContextAccessor httpContextAccessor, AppHelper helper)
            : base(context, httpContextAccessor, helper)
        {

            _context = context;
            _db = context;
            _http = httpContextAccessor;
            _h = helper;
        }



        public List<TqualCode> TqualCodes { get; set; }
        public List<TunivInstCode> TunivInstCodes { get; set; }
        public List<TqualReq> TqualReqs { get; set; }
        public List<TqualEmpAttendance> TqualEmpAttendances { get; set; }
        public List<TqualStudyReport> tqualStudyReports { get; set; }


        public TqualCode TqualCode { get; set; }
        public TunivInstCode TunivInstCode { get; set; }
        public TqualPlan TqualPlan { get; set; }
        public TqualReq TqualReq { get; set; }
        public TSubjectCode SubjectCode { get; set; }
        public TstudyType TstudyType { get; set; }
        public TcountryCode TcountryCode { get; set; }
        public TqualEmpAttendance TqualEmpAttendance { get; set; }
        public TqualStudyReport TqualStudyReport { get; set; }
        public TempTrgMas TempTrgMa { get; set; }
        public TempTrgHist TempTrgHist { get; set; }

        ////////////////////////SAFA//////////

        public List<TcourseSupplier> TcourseSuppliers { get; set; }

        public List<TcourseCatalogue> TcourseCatalogue { get; set; }

        public List<TempTrgHist> TempTrgHists { get; set; }

        public List<TcourseCatCode> TcourseCatCode { get; set; }
        public List<TcourseType> TcourseType { get; set; }
        public List<TtrgPlaceCode> TtrgPlaceCode { get; set; }

        public List<TcountryCode> TcountryCodes { get; set; }
        public TSubjectCode SubjectCodes { get; set; }

        public List<VempDtl> VempDtl { get; set; }

        public List<Years> Years { get; set; }
        public Years Year { get; set; }
        public List<TempTrgReq> TempTrgReq { get; set; }
        public List<TempTrgMas> TempTrgMas { get; set; }
        public List<TempReqDtls> TempReqDtls { get; set; }

        public TempReqDtls TempReqDtl { get; set; }
        public EmpSkill EmpSkill { get; set; }
        public  List<EmpSkill> EmpSkills { get; set; }
        public  List<EmpImpact> EmpImpacts { get; set; }

        public EmpImpact EmpImpact { get; set; }
        public List<SpecialiedSkill> specialiedSkills { get; set; }
        public SpecialiedSkill SpecialiedSkill { get; set; }

        public List<TcityCode> tcityCodes { get; set; }
        public List<TrainigPlan> trainigPlans { get; set; }

        public List<TrainingComm> TrainingComms { get; set; }
        public TrainingComm TrainingComm { get; set; }
        public List<VendorTraining> vendorTraining { get; set; }
        public List<TrainingField> TrainingFields { get; set; }
    }
}
