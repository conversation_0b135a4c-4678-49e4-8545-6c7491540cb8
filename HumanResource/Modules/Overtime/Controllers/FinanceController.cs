﻿using Microsoft.AspNetCore.Mvc;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Overtime.ViewModels;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Overtime.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.Unifi;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Mapping;
using HumanResource.Core.UI.Models;

namespace HumanResource.Modules.Overtime.Controllers
{
    [Can("finance-department|finance-manager")]
    [Area("Overtime")]
    [Route("Overtime/Finance")]
    public class FinanceController : OverTimeController
    {

        public FinanceController(
            hrmsContext context,
            IHttpContextAccessor httpContextAccessor, AppHelper helper)
            : base(context, httpContextAccessor, helper)
        {

            _v = new OverTimeViewModel(context, httpContextAccessor, helper);

            _v.Page.Active = "hr_overtime";
        }
        [HttpGet("")]
        public IActionResult FinanceRequests(DateTime? from, DateTime? to, int? year, int? month, int payStatusFilter = 3)
        {
            if (!Can("finance-department|finance-manager"))
                return StatusCode(403);


            if (year == null)
            {
                year = DateTime.Now.Year;
            }
            if (month == null)
            {
                month = DateTime.Now.Month;
            }

            _v.Page.Class = "  ";
            _v.Page.Active = "hr_overtime";
            _v.Page.Reload = true;

            _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="العمل الاضافي", Url=$"/OverTime/Finance"},
        };



            ViewBag.PayStatusFilter = payStatusFilter;
            ViewBag.Year = year;
            ViewBag.Month = month;

            return View(_v);
        }

        [HttpPost("Datatable")]
        public IActionResult FinanceRequestsDatatable([FromForm] DataTableHelper datatable, int? year, int? month, int payStatusFilter = 3)
        {
            if (!Can("finance-manager|finance-department"))
                return StatusCode(403);

            if (year == null)
            {
                year = DateTime.Now.Year;
            }

            if (month == null)
            {
                month = DateTime.Now.Month;
            }

            // Build optimized query with LEFT JOIN to get payment status and RunNo in single query
            var baseQuery = from mas in _db.OvertimeReqMas
                            from firstDtl in _db.OvertimeReqDtls
                                .Where(d => d.InYear == mas.InYear &&
                                           d.InMailNo == mas.InMailNo &&
                                           d.InDocSlNo == mas.InDocSlNo &&
                                           d.OtAmount > 0)
                                .Take(1)
                                .DefaultIfEmpty()
                            where mas.EmpNo > 0
                                  && mas.ReqStatus == 60
                                  && mas.AuditStat == 1
                                  && mas.CancelFlag != 1
                                  && mas.OrderYear == (year - 2000)
                                  && mas.OrderMonth == month
                            select new
                            {
                                Request = mas,
                                PayStatus = firstDtl == null ? 0 : (firstDtl.PayStat >= 1 ? 2 : 0), // 0=unpaid, 2=paid
                                RunNo = firstDtl != null ? firstDtl.RunNo : null
                            };

            // Apply payment status filter if specified
            if (payStatusFilter != 3)
            {
                baseQuery = baseQuery.Where(x => x.PayStatus == payStatusFilter);
            }

            // Apply search filter if provided
            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                baseQuery = baseQuery.Where(x => x.Request.EmpNo.ToString().Contains(datatable.Search.Value));
            }

            // Get total count before pagination
            var total = baseQuery.Count();

            // Convert to list for sorting (since EF can't handle complex sorting on anonymous types)
            var allData = baseQuery.ToList();

            // Apply sorting in memory (simple default sorting)
            if (datatable.Order != null && datatable.Order.Count > 0)
            {
                bool isAscending = datatable.Order[0].Dir == "asc";
                allData = datatable.Order[0].Column switch
                {
                    1 => isAscending ? allData.OrderBy(x => x.Request.EmpNo).ToList() : allData.OrderByDescending(x => x.Request.EmpNo).ToList(),
                    6 => isAscending ? allData.OrderBy(x => x.PayStatus).ToList() : allData.OrderByDescending(x => x.PayStatus).ToList(),
                    _ => allData.OrderByDescending(x => x.Request.EmpNo).ToList()
                };
            }
            else
            {
                allData = allData.OrderByDescending(x => x.Request.EmpNo).ToList();
            }

            // Apply pagination
            var data = allData.Skip(datatable.Start).Take(datatable.Length).ToList();

            // Map to table rows
            var table = data.Select(item =>
            {
                var ro = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(item.Request);

                // Payment status badge
                var payStatusBadge = item.PayStatus == 0
                    ? "<span class=\"badge badge-danger rounded-pill px-2\">متوقف</span>"
                    : "<span class=\"badge badge-success rounded-pill px-2\">تم الصرف</span>";

                // RunNo information
                var runNoInfo = item.RunNo == null || item.RunNo == 0
                    ? "<span class=\" rounded-pill px-2\">غير مصروف</span>"
                    : $"<span class=\"  rounded-pill px-2\">{item.RunNo}</span>";

                return new
                {
                    id = "<a href='/OverTime/Finance/View/" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "-" + ro.Rel + "'  >" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "</a>",
                    emp = "#" + ro.EmpNo + "<br>" + _h.StaffData(ro.EmpNo).EmpNameA,
                    createdAt = ro.OrderYear.Value + "-" + ro.OrderMonth.Value,
                    extraSum = _h.FormatHour(ro.ExtraSum),
                    extraAmountSum = _v._amount(ro.ExtraAmountSum.Value),
                    estematedFlag = ro.EstimatedFlag == 1 ? "<span class='badge badge-warning rounded-pill px-2'>تقديري</span>" : "فعلي",
                    payStatus = payStatusBadge,
                    runNo = runNoInfo,
                    payStatusNum = item.PayStatus
                };
            }).ToList();

            return Json(new
            {
                datatable.Draw,
                recordsTotal = total,
                recordsFiltered = total,
                data = table
            });
        }



        [HttpGet("View/{Id}")]
        public IActionResult FinanceRequestView(string Id)
        {

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (!Can("finance-manager|finance-department"))
                return StatusCode(403);

            _v.Page.Title = "العمل الاضافي";
            _v.Page.Active = "hr_overtime";
            //_v.Page.Layout = "_Popup";
            _v.Page.Reload = true;
            _v.Page.Back = $"/OverTime/Finance";




            var requestEmp = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
            .First();

            if (requestEmp == null)
                return StatusCode(404);

            _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

            _v.OvertimeReqDtls = _db.OvertimeReqDtls
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
            .OrderBy(o => o.OtDate)
            .ToList();

            _v.UniOvertimeReqDtls = AutoMapper.MapList<OvertimeReqDtls, UnifiOvertimeReqDtls>(_v.OvertimeReqDtls);
            _v.EmpEarning = _db.EmpEarnings
                .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null))

                .FirstOrDefault();


            _v.Page.Breadcrumb = new List<Breadcrumb> {
                new Breadcrumb {Label="المالية", Url=$"/OverTime/Finance"},
                new Breadcrumb {Label="العمل الاضافي", Url=$"/OverTime/Finance"},
                new Breadcrumb {Label=_v._h.StaffData(_v.UniOvertimeReqMas.EmpNo).EmpNameA, Url=$"/OverTime/"},
            };



            var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null)).FirstOrDefault();
            ViewBag.MaxEmpEarnAmt = EmpEarn.EarnAmt / 2;

            ViewBag.ExtraAmountSum = _db.OvertimeReqMas
            .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
            .Sum(max => max.ExtraAmountSum);

            ViewBag.ExtraSum = _db.OvertimeReqMas
                .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .Sum(max => max.ExtraSum);

            ViewBag.RequestCount = _db.OvertimeReqMas
                .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.SignAuthCode > 0 && ro.CancelFlag == 0 && ro.AuditStat == 1 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .Count();

            ViewBag.newsum = _db.OvertimeReqDtls.Where(ro =>
                                                ro.InYear == InYear &&
                                                ro.InMailNo == InMailNo &&
                                                ro.InDocSlNo == InDocSlNo &&
                                                ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                                                .Sum(sum => sum.ComputedAmount);



            _v.OvertimeComments = _db.OvertimeComments
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .OrderBy(o => o.TimeStamp)
                .ToList();


            return View(_v);
        }


        [HttpPost("Manager/Approved/{Id}")]
        public IActionResult FinanceManagerRequestApprove(string Id, [FromForm] IFormCollection post)
        {

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (!Can("finance-manager"))
                return StatusCode(403);

            int EmpNo = 0;


            if (Rel == "Con")
            {

                return StatusCode(403);
            }

            if (Rel == "Emp")
            {

                var requestEmp = _db.OvertimeReqMas
               .Where(ro =>
                   ro.InYear == InYear
                   && ro.InMailNo == InMailNo
                   && ro.InDocSlNo == InDocSlNo
                   && ro.CancelFlag == 0
                   && ro.AuditStat == 0
                   && ro.ReqStatus == 60
               )
               .FirstOrDefault();

                if (requestEmp == null)
                    return StatusCode(404);

                EmpNo = requestEmp.EmpNo.Value;

                requestEmp.AuditStat = 1;
                requestEmp.AuditUserId = requestEmp.FirstAuditUserId;
                requestEmp.AuditDate = requestEmp.AuditDate;


                _db.Update(requestEmp);
                _db.SaveChanges();

            }

            if (post.ContainsKey("comment"))
            {
                var newComment = new OvertimeComment
                {
                    InYear = InYear,
                    InMailNo = InMailNo,
                    InDocSlNo = InDocSlNo,
                    Comment = post["comment"],
                    EmpNo = EmpNo,
                    CreatedBy = _v.Profile.EmpNo.Value
                };

                _db.OvertimeComments.Add(newComment);
                _db.SaveChanges();
            }

            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "اعتماد الصرف"
            );

            return Json(new
            {
                success = true,
                message = new List<string> { _("تم الموافقة") },
                action = "reload",
            });

        }


        [HttpGet("Manager")]
        [Can("finance-manager")]
        public IActionResult FinanceManagerRequests()
        {

            _v.Page.Class = "  ";
            _v.Page.Active = "hr_overtime";
            _v.Page.Reload = true;

            _v.Page.Breadcrumb = new List<Breadcrumb> {

             new Breadcrumb {Label="العمل الاضافي", Url=$"/OverTime/Finance"},
        };

            // Get trend data for dashboard
            ViewBag.TrendData = GetOvertimeTrendData();

            return View(_v);
        }

        [HttpGet("Manager/TrendData")]
        [Can("finance-manager")]
        public IActionResult GetOvertimeTrendData()
        {
            var currentDate = DateTime.Now;
            var currentYear = currentDate.Year;
            var currentMonth = currentDate.Month;
            var lastMonth = currentMonth == 1 ? 12 : currentMonth - 1;
            var lastMonthYear = currentMonth == 1 ? currentYear - 1 : currentYear;

            // Get approved requests (audit_stat = 1) that are not yet paid (pay_stat = 0)
            var currentMonthData = GetMonthlyOvertimeData(currentYear, currentMonth);
            var lastMonthData = GetMonthlyOvertimeData(lastMonthYear, lastMonth);
            var yearToDateData = GetYearToDateOvertimeData(currentYear);

            // Calculate pending payment amounts
            var currentMonthPending = GetPendingPaymentData(currentYear, currentMonth);
            var lastMonthPending = GetPendingPaymentData(lastMonthYear, lastMonth);
            var yearPending = GetYearPendingPaymentData(currentYear);

            var trendData = new
            {
                currentMonth = new
                {
                    year = currentYear,
                    month = currentMonth,
                    totalAmount = currentMonthData.TotalAmount,
                    totalHours = currentMonthData.TotalHours,
                    requestCount = currentMonthData.RequestCount,
                    pendingAmount = currentMonthPending.PendingAmount,
                    pendingHours = currentMonthPending.PendingHours,
                    pendingCount = currentMonthPending.PendingCount
                },
                lastMonth = new
                {
                    year = lastMonthYear,
                    month = lastMonth,
                    totalAmount = lastMonthData.TotalAmount,
                    totalHours = lastMonthData.TotalHours,
                    requestCount = lastMonthData.RequestCount,
                    pendingAmount = lastMonthPending.PendingAmount,
                    pendingHours = lastMonthPending.PendingHours,
                    pendingCount = lastMonthPending.PendingCount
                },
                yearToDate = new
                {
                    year = currentYear,
                    totalAmount = yearToDateData.TotalAmount,
                    totalHours = yearToDateData.TotalHours,
                    requestCount = yearToDateData.RequestCount,
                    pendingAmount = yearPending.PendingAmount,
                    pendingHours = yearPending.PendingHours,
                    pendingCount = yearPending.PendingCount
                },
                trends = new
                {
                    amountChange = currentMonthData.TotalAmount - lastMonthData.TotalAmount,
                    amountChangePercent = lastMonthData.TotalAmount > 0 ?
                        Math.Round(((currentMonthData.TotalAmount - lastMonthData.TotalAmount) / lastMonthData.TotalAmount) * 100, 2) : 0,
                    hoursChange = currentMonthData.TotalHours - lastMonthData.TotalHours,
                    hoursChangePercent = lastMonthData.TotalHours > 0 ?
                        Math.Round(((currentMonthData.TotalHours - lastMonthData.TotalHours) / lastMonthData.TotalHours) * 100, 2) : 0,
                    requestChange = currentMonthData.RequestCount - lastMonthData.RequestCount,
                    requestChangePercent = lastMonthData.RequestCount > 0 ?
                        Math.Round(((double)(currentMonthData.RequestCount - lastMonthData.RequestCount) / lastMonthData.RequestCount) * 100, 2) : 0
                }
            };

            return Json(trendData);
        }

        private (double TotalAmount, double TotalHours, int RequestCount) GetMonthlyOvertimeData(int year, int month)
        {
            // Get data from OvertimeReqDtls for approved requests
            var monthlyData = _db.OvertimeReqDtls
                .Where(d => d.PayYear == year && d.PayMonth == month && d.PayStat == 1)
                .GroupBy(d => 1)
                .Select(g => new
                {
                    TotalAmount = g.Sum(x => x.OtAmount),
                    TotalHours = g.Sum(x => x.OtDuration),
                    RequestCount = g.Count()
                })
                .FirstOrDefault();

            return monthlyData != null ?
                (monthlyData.TotalAmount, monthlyData.TotalHours, monthlyData.RequestCount) :
                (0, 0, 0);
        }

        private (double TotalAmount, double TotalHours, int RequestCount) GetYearToDateOvertimeData(int year)
        {
            var yearData = _db.OvertimeReqDtls
                .Where(d => d.PayYear == year && d.PayStat == 1)
                .GroupBy(d => 1)
                .Select(g => new
                {
                    TotalAmount = g.Sum(x => x.OtAmount),
                    TotalHours = g.Sum(x => x.OtDuration),
                    RequestCount = g.Count()
                })
                .FirstOrDefault();

            return yearData != null ?
                (yearData.TotalAmount, yearData.TotalHours, yearData.RequestCount) :
                (0, 0, 0);
        }

        private (double PendingAmount, double PendingHours, int PendingCount) GetPendingPaymentData(int year, int month)
        {
            // Get requests that are approved by audit (audit_stat = 1) but not yet paid (pay_stat = 0)
            var pendingData = (from mas in _db.OvertimeReqMas
                               join dtl in _db.OvertimeReqDtls on new { mas.InYear, mas.InMailNo, InDocSlNo = mas.InDocSlNo.Value }
                                   equals new { dtl.InYear, dtl.InMailNo, dtl.InDocSlNo }
                               where mas.AuditStat == 1 && mas.EmpNo > 1 && dtl.PayStat == 0
                                   && mas.OrderYear == year && mas.OrderMonth == month
                               group dtl by 1 into g
                               select new
                               {
                                   PendingAmount = g.Sum(x => x.ComputedAmount),
                                   PendingHours = g.Sum(x => x.OtDuration),
                                   PendingCount = g.Count()
                               }).FirstOrDefault();

            return pendingData != null ?
                (pendingData.PendingAmount, pendingData.PendingHours, pendingData.PendingCount) :
                (0, 0, 0);
        }

        private (double PendingAmount, double PendingHours, int PendingCount) GetYearPendingPaymentData(int year)
        {
            var yearPendingData = (from mas in _db.OvertimeReqMas
                                   join dtl in _db.OvertimeReqDtls on new { mas.InYear, mas.InMailNo, InDocSlNo = mas.InDocSlNo.Value }
                                       equals new { dtl.InYear, dtl.InMailNo, dtl.InDocSlNo }
                                   where mas.AuditStat == 1 && mas.EmpNo > 1 && dtl.PayStat == 0 && mas.OrderYear == year
                                   group dtl by 1 into g
                                   select new
                                   {
                                       PendingAmount = g.Sum(x => x.ComputedAmount),
                                       PendingHours = g.Sum(x => x.OtDuration),
                                       PendingCount = g.Count()
                                   }).FirstOrDefault();

            return yearPendingData != null ?
                (yearPendingData.PendingAmount, yearPendingData.PendingHours, yearPendingData.PendingCount) :
                (0, 0, 0);
        }

        [HttpPost("Manager/Return/{Id}")]
        public IActionResult FinanceRequestReturn(string Id, [FromForm] IFormCollection post)
        {
            return ProcessReturnRequest(
                Id: Id,
                post: post,
                permission: "finance-manager",
                allowedStatuses: new int[] { 60 },
                sendNotification: false,
                redirectUrl: "/OverTime/Finance/Manager/",
                logMessage: "ارجاع لقسم المراجعة الداخلية",
                responseMessage: "تم الارجاع",
                customReqStatusLogic: (requestEmp) => requestEmp.EstimatedFlag == 1 ? 50 : 40
            ).Result;
        }

        [HttpPost("Manager/Datatable")]
        [Can("finance-manager")]
        public IActionResult FinanceManagerDatatable([FromForm] DataTableHelper datatable)
        {




            var query = _db.OvertimeReqMas.Where(
                    r =>
                    r.EmpNo > 0
                     && r.CancelFlag != 1
                     && (r.ReqStatus == 60)
                     && r.AuditStat == 0


                    );



            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                query = query.Where(f =>
                f.EmpNo.ToString().Contains(datatable.Search.Value)

                );
            }

            var total = query.Count();


            var empReq = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(
              query.ToList()
                );

            List<UnifiOvertimeReqMas> allReq = empReq.ToList();

            allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).ToList();



            var data = allReq.OrderBy(o => o.ReqStatus).Skip(datatable.Start).Take(datatable.Length).ToList();

            switch (datatable.Order[0].Column)
            {

                case 1:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 2:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 3:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 4:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.ExtraAmountSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.ExtraAmountSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 5:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EstimatedFlag).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EstimatedFlag).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;


                default:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderBy(o => o.ReqStatus).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderByDescending(o => o.ReqStatus).Skip(datatable.Start).Take(datatable.Length).ToList();

                    }

                    break;
            }

            var table = data.Select(ro => new
            {


                id = "<a href='/OverTime/Finance/Manager/View/" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "-" + ro.Rel + "'  >" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "</a>",
                emp = "#" + ro.EmpNo + "<br>" + _h.StaffData(ro.EmpNo).EmpNameA,
                createdAt = ro.OrderYear.Value + "-" + ro.OrderMonth.Value,

                extraSum = _h.FormatHour(ro.ExtraSum),
                extraAmountSum = _v._amount(ro.ExtraAmountSum.Value),
                estematedFlag = ro.EstimatedFlag == 1 ? "<span class='badge badge-warning rounded-pill px-2'>تقديري</span>" : "فعلي",


            }).ToList();

            var output = new
            {
                datatable.Draw,
                recordsTotal = total,
                recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
                data = table
            };


            return Json(output);

        }


        [HttpGet("Manager/View/{Id}")]
        [Can("finance-manager")]
        public IActionResult FinanceManagerRequestView(string Id)
        {

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];



            _v.Page.Title = "عمل اضافي";
            _v.Page.Active = "hr_overtime";
            //_v.Page.Layout = "_Popup";
            _v.Page.Reload = true;
            _v.Page.Back = $"/OverTime/Finance/Manager";

            if (Rel == "Con")
            {



            }

            if (Rel == "Emp")
            {
                var requestEmp = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .First();

                if (requestEmp == null)
                    return StatusCode(404);

                _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

                _v.OvertimeReqDtls = _db.OvertimeReqDtls
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .OrderBy(o => o.OtDate)
                .ToList();

                _v.UniOvertimeReqDtls = AutoMapper.MapList<OvertimeReqDtls, UnifiOvertimeReqDtls>(_v.OvertimeReqDtls);
                _v.EmpEarning = _db.EmpEarnings
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null))

                    .FirstOrDefault();
            }

            _v.Page.Breadcrumb = new List<Breadcrumb> {
              new Breadcrumb {Label="العمل الاضافي", Url=$"/OverTime/Finance/Manager"},
             new Breadcrumb {Label=_v._h.StaffData(_v.UniOvertimeReqMas.EmpNo).EmpNameA, Url=$"/OverTime/"},
        };


            if (_v.UniOvertimeReqMas.EmpNo >= 1000)
            {


            }
            else
            {
                var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null)).FirstOrDefault();
                ViewBag.MaxEmpEarnAmt = EmpEarn.EarnAmt / 2;

                ViewBag.ExtraAmountSum = _db.OvertimeReqMas
                .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .Sum(max => max.ExtraAmountSum);

                ViewBag.ExtraSum = _db.OvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                    .Sum(max => max.ExtraSum);

                ViewBag.RequestCount = _db.OvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.CancelFlag == 0 && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                    .Count();

                ViewBag.newsum = _db.OvertimeReqDtls.Where(ro =>
                                                    ro.InYear == InYear &&
                                                    ro.InMailNo == InMailNo &&
                                                    ro.InDocSlNo == InDocSlNo &&
                                                    ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                                                    .Sum(sum => sum.ComputedAmount);

            }

            _v.OvertimeComments = _db.OvertimeComments
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .OrderBy(o => o.TimeStamp)
                .ToList();


            return View(_v);
        }



    }
}
