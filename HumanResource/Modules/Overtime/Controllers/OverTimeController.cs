﻿using HumanResource.Core.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using HumanResource.Modules.Overtime.ViewModels;

using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Overtime.Models.Entities;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.Models.Entities.Unifi;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Mapping;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Core.UI.Models;


namespace HumanResource.Modules.Overtime.Controllers;

[Area("Overtime")]
[Route("Overtime")]
public class OverTimeController : BaseController
{
    public OverTimeViewModel _v;

    public OverTimeController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper )
        : base(context, httpContextAccessor, helper)
    {

        _v = new OverTimeViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "hr_overtime";

    }


    [Route("My")]
    public IActionResult MyRequests()
    {

        var Exclude = _db.OvertimeReqDtls
            .Where(ro => ro.EmpNo == _v.Profile.EmpNo)
            .Select(d => d.OtDate)
            .ToList();

        var Exclude2 = _db.OvertimeReqPreDtls
            .Where(ro => ro.EmpNo == _v.Profile.EmpNo)
            .Select(d => d.OtDate)
            .ToList();



        _v.EmpWorkHours = _db.EmpWorkHours
            .Where(ro => 
                ro.EmpNo == _v.Profile.EmpNo 
                && ro.Extra >= 1 
                && ro.Day >= DateTime.Now.AddMonths(-2) 
                && !Exclude.Contains(ro.Day)
                && !Exclude2.Contains(ro.Day)
            )
            .ToList();

        
        _v.OvertimeReqMass = _db.OvertimeReqMas
        .Where(ro => ro.EmpNo == _v.Profile.EmpNo )
        .ToList();

        _v.UniOvertimeReqMass = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(_v.OvertimeReqMass);
        

        _v.OvertimePays = _context.OvertimePays.Where(a => a.EmpNo == _v.Profile.EmpNo && a.PayYear == DateTime.Now.Year).Select(x => new OvertimePay { EmpNo = x.EmpNo, PayMonth = x.PayMonth, Amt = x.Amt, PayYear = x.PayYear }).ToList();

        _v.Page.Active = "";
        _v.Page.Title = "طلبات العمل الاضافي";
        _v.Page.Breadcrumb = new List<Breadcrumb>
        {
            new Breadcrumb { Label = "العمل الاضافي", Url = "/OverTime/My/" },
        };

        return View(_v);
    }   

    [HttpPost]
    [Route("My/Create")]
    public  IActionResult MyRequestCreate([FromForm] IFormCollection post)
    {

        if (!IsValid(ModelState) )
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        if (!post.ContainsKey("ManagerNo"))
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Invalid department manager") },
                action = "",
            });
        }

        if (_v.Profile.EmpNo > 1000)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Not supported") },
                action = "",
            });
        }


        // Validate the manager is allowed
        var Managers = _h.Managers(_v.Profile.EmpNo);

        bool ValidManager = Managers.Any(item => item.EmpNo == int.Parse(post["ManagerNo"]));

        if (!ValidManager)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Invalid department manager") },
                action = "",
            });
        }

        // Format list of days
        var selectedDays = new List<DateTime>();

        if (post.ContainsKey("days"))
        {
            foreach (var dayString in post["days"])
            {
                if(DateTime.TryParse(dayString, out DateTime day))
                {
                    selectedDays.Add(day);
                }
            }
        }

        if (selectedDays.Count == 0)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Please select days") },
                action = "",
            });
        }

        bool daysCheck = true;

        var Exclude = _db.OvertimeReqDtls
            .Where(ro => ro.EmpNo == _v.Profile.EmpNo)
            .Select(d => d.OtDate)
            .ToList();

        var Exclude2 = _db.OvertimeReqPreDtls
            .Where(ro => ro.EmpNo == _v.Profile.EmpNo)
            .Select(d => d.OtDate)
            .ToList();


        // Validate selected days
        foreach (var day in selectedDays)
        {
            var check = _db.EmpWorkHours
            .Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.Extra >= 1 && ro.Day == day && !Exclude.Contains(ro.Day) && !Exclude2.Contains(ro.Day))
            .FirstOrDefault();

            if (check == null)
            {
                daysCheck = false;
            }
        }

        if (!daysCheck)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Invalid overtime day") },
                action = "",
            });
        }

        // Validate all days in the same month
        int refYear = selectedDays[0].Year;
        int refYear2Dig = selectedDays[0].Year-2000;
        int refMonth = selectedDays[0].Month;

        bool isAllInSameMonth = selectedDays.All(date => date.Year == refYear && date.Month == refMonth);

        if(!isAllInSameMonth)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("All days has to be in the same month") },
                action = "",
            });
        }

        TincomingMail tincoming = _h.Mail().Create(97);


        var eNewRequest = new OvertimeReqMas
        {
            InYear = tincoming.InYear,
            InDocSlNo = (int)tincoming.LastDocSlNo,
            InDeptInd = 1,
            InMailNo = tincoming.InMailNo,

            EmpNo = _v.Profile.EmpNo.Value,
            ManagerNo = int.Parse(post["ManagerNo"]),
            OrderMonth = refMonth,
            OrderYear = refYear2Dig,
            ReqStatus = 1,

        };

        _db.OvertimeReqMas.Add(eNewRequest);

            _db.SaveChanges();

        List<EmpWorkHours> EmpWorkHours = _db.EmpWorkHours
            .Where(ro => ro.EmpNo == _v.Profile.EmpNo 
            && ro.Extra >= 1 
            //&& ro.Day >= DateTime.Now.AddMonths(-2) 
            && selectedDays.Contains(ro.Day))
            .ToList();

        eNewRequest = _db.OvertimeReqMas
            .Where(ro => ro.InYear == tincoming.InYear && ro.InDocSlNo == (int)tincoming.LastDocSlNo && ro.InMailNo == tincoming.InMailNo)
            .FirstOrDefault();



        foreach (var day in EmpWorkHours)
        {

            var Rate = _v.GetOverTimeHourPrice(_v.Profile.EmpNo.Value, day.Day);

            var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.EarnCode == 1 && ro.ToDate==null).OrderByDescending(o=>o.ToDate).FirstOrDefault();

            int HolydayFlag = _h.IsHoliday(day.Day) ? 1 : 0;

            var NewOvertimeReqPreDtls = new OvertimeReqPreDtls
            {
                InYear = eNewRequest.InYear,
                InDocSlNo = eNewRequest.InDocSlNo.Value,
                InDeptInd = 1,
                InMailNo = eNewRequest.InMailNo,

                EmpNo = _v.Profile.EmpNo.Value,
                HolydayFlag= HolydayFlag,
                OtDuration = day.Extra,
                OtDate = day.Day,
                OtRate = Rate,
                ComputedAmount = Rate * day.Extra,

                BasicAtm = EmpEarn.EarnAmt.Value,
            };

            _db.OvertimeReqPreDtls.Add(NewOvertimeReqPreDtls);

            _db.SaveChanges();
        }

        Log(
            eNewRequest.InYear + "-" + eNewRequest.InMailNo + "-" + eNewRequest.InDocSlNo,
            "Overtime",
            "عمل اضافي جديد"
        );
        
    
        return Json(new
        {
            success = true,
            message = new List<string> { _("Request created successfully") },
            action = "reload",
        });

    }

    [Route("My/View/{Id}")]
    public IActionResult MyRequestView(string Id)
    {

        string[] rawcode = Id.Split("-");
       

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];



        if (Rel == "Con")
        {
            return StatusCode(404);
        }

        
        var requestEmp = _db.OvertimeReqMas
        .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo  && ro.EmpNo == _v.Profile.EmpNo)
        .First();


        if (requestEmp == null)
            return StatusCode(404);

        _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

        var Days = _db.OvertimeReqPreDtls
        .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
        .OrderBy(o => o.OtDate)
        .ToList();

        // Get all approved dates for this request in a single query
        var approvedDates = _db.OvertimeReqDtls
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
            .Select(ro => ro.OtDate)
            .ToHashSet();

        // Set IsApproved property efficiently
        foreach (var day in Days)
        {
            day.IsApproved = approvedDates.Contains(day.OtDate);
        }

        _v.UniOvertimeReqPreDtls = AutoMapper.MapList<OvertimeReqPreDtls, UnifiOvertimeReqPreDtls>(Days);

        _v.Page.Title = "طلبات العمل الاضافي";
        // _v.Page.Layout = "_Popup";
        _v.Page.Reload = true;
        _v.Page.Active = "";
        _v.Page.Back = "/OverTime/My/";
        _v.Page.Breadcrumb = new List<Breadcrumb>
        {
            new Breadcrumb { Label = "العمل الاضافي", Url = "/OverTime/My/" },
            new Breadcrumb { Label = "عرض طلب" }
        };


        return View(_v);
    }

    [Route("My/Delete/{Id}")]
    public IActionResult MyRequestDelete(string Id)
    {

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];

        if (Rel == "Con")
        {
            return StatusCode(404);
        }

        if (Rel == "Emp")
        {
            var request = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag != 1 && ro.EmpNo == _v.Profile.EmpNo.Value && (ro.SignAuthCode.Value < 1 || ro.SignAuthCode == null))
            .First();

            if (request == null)
                return StatusCode(404);

            var daysPre = _db.OvertimeReqPreDtls
                .Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .ToList();

            _db.OvertimeReqPreDtls.RemoveRange(daysPre);

            _db.SaveChanges();

            var days = _db.OvertimeReqDtls
                .Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .ToList();

            _db.OvertimeReqDtls.RemoveRange(days);

            _db.OvertimeReqMas.Remove(request);

            _db.SaveChanges();
        }


        Log(
            InYear + "-" + InMailNo + "-" + InDocSlNo,
            "Overtime",
            "حذف عمل اضافي"
        );

        return Content("<script>window.opener.location.href='/OverTime/My/?success=Action completed';window.close()</script>", "text/html");

    }

    [Route("Manager")]
    public IActionResult ManagerRequests(DateTime? from, DateTime? to)
    {

        if (!Can("overtime-admin|department-manager"))
            return StatusCode(403);


        if (from == null){
            from = DateTime.Now.AddDays(-100);
        }

        if (to == null){
            to = DateTime.Now;
        }

        var empReq = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(
            _db.OvertimeReqMas
             .Where(ro => ro.ManagerNo == _v.Profile.EmpNo  && ro.EmpNo > 0 && ro.EmpNo !=null && ro.TimeStamp >= from && ro.TimeStamp <= to)
             .ToList()
             );

   
        if (Can("overtime-admin"))
        {

            List<OvertimeReqMas> emplist = _db.OvertimeReqMas
             .Where(ro =>  ro.EmpNo > 0 && ro.EmpNo != null && ro.TimeStamp >= from && ro.TimeStamp <= to)
             .ToList();
            empReq = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(
            emplist
             );

        }


        List<UnifiOvertimeReqMas> allReq = empReq;

        _v.UniOvertimeReqMass = allReq.OrderByDescending(o => o.OrderYear).ThenByDescending(o => o.OrderMonth).ToList();
        _v.Page.Active = "";
        _v.Page.Title = "طلبات العمل الاضافي";

        _v.Page.Breadcrumb = new List<Breadcrumb>
        {
            new Breadcrumb { Label = "العمل الاضافي", Url = "/OverTime/Manager/" },

        };

        return View(_v);
    }

    [Route("Manager/View/{Id}")]
    public IActionResult ManagerRequestView(string Id)
    {

        if (!Can("overtime-admin|department-manager"))
            return StatusCode(403);

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];


        _v.Page.Title = "طلبات العمل الاضافي";
        _v.Page.Reload = true;
        _v.Page.Active = "";
        _v.Page.Back = "/OverTime/Manager/";
        _v.Page.Breadcrumb = new List<Breadcrumb>
        {
            new Breadcrumb { Label = "العمل الاضافي", Url = "/OverTime/Manager/" },
            new Breadcrumb { Label = "عرض طلب" }
        };

        if (Can("overtime-admin"))
        {
            if (Rel == "Con")
            {
                return StatusCode(404);
            }

            if (Rel == "Emp")
            {
                var requestEmp = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo )
                .First();


                if (requestEmp == null)
                    return StatusCode(404);

                _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

                var Days = _db.OvertimeReqPreDtls
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .OrderBy(o => o.OtDate)
                .ToList();

                // Get all approved dates for this request in a single query
                var approvedDates = _db.OvertimeReqDtls
                    .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                    .Select(ro => ro.OtDate)
                    .ToHashSet();

                // Set IsApproved property efficiently
                foreach (var day in Days)
                {
                    day.IsApproved = approvedDates.Contains(day.OtDate);
                }

                _v.UniOvertimeReqPreDtls = AutoMapper.MapList<OvertimeReqPreDtls, UnifiOvertimeReqPreDtls>(Days);
            }
        }
        else
        {
            if (Rel == "Con")
            {
                return StatusCode(404);
            }

            if (Rel == "Emp")
            {
                var requestEmp = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.ManagerNo == _v.Profile.EmpNo.Value)
                .First();


                if (requestEmp == null)
                    return StatusCode(404);


                _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

                var Days = _db.OvertimeReqPreDtls
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .OrderBy(o => o.OtDate)
                .ToList();

                // Get all approved dates for this request in a single query
                var approvedDates = _db.OvertimeReqDtls
                    .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                    .Select(ro => ro.OtDate)
                    .ToHashSet();

                // Set IsApproved property efficiently
                foreach (var day in Days)
                {
                    day.IsApproved = approvedDates.Contains(day.OtDate);
                }

                _v.UniOvertimeReqPreDtls = AutoMapper.MapList<OvertimeReqPreDtls, UnifiOvertimeReqPreDtls>(Days);
            }
        }

        _v.OvertimeComments = _db.OvertimeComments
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
            .OrderBy(o => o.TimeStamp)
            .ToList();

        _v.Page.Active = "";

        return View(_v);
    }

    [HttpPost]
    [Route("Manager/Approve/{Id}")]
    public async Task<IActionResult> ManagerRequestApprove(string Id, [FromForm] IFormCollection post)
    {

        if (!Can("overtime-admin|department-manager"))
            return StatusCode(403);

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];

        int EmpNo = 0;

        if (Rel == "Con")
        {
            return StatusCode(404);

        }
        
        var rEmp = _db.OvertimeReqMas
        .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.CancelFlag == 0
            && ro.ReqStatus == 1
            && ro.ManagerNo == _v.Profile.EmpNo.Value
        )
        .FirstOrDefault();

        if (Can("overtime-admin"))
        {
            rEmp = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0 && ro.ReqStatus == 1)
            .FirstOrDefault();
        }

        EmpNo = rEmp.EmpNo.Value;
        
        var selectedDays = new List<DateTime>();

        if (post.ContainsKey("days"))
        {
            foreach (var dayString in post["days"])
            {
                if (DateTime.TryParse(dayString, out DateTime day))
                {
                    selectedDays.Add(day);
                }
            }
        }

        if (selectedDays.Count == 0)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Please select days") },
                action = "",
            });
        }

        bool daysCheck = true;

        var Exclude = _db.OvertimeReqDtls
            .Where(ro => ro.EmpNo == EmpNo)
            .Select(d => d.OtDate)
            .ToList();

        var Inxclude = _db.OvertimeReqPreDtls
            .Where(ro => ro.EmpNo == EmpNo)
            .Select(d => d.OtDate)
            .ToList();


        // Validate selected days
        foreach (var day in selectedDays)
        {
            var check = _db.EmpWorkHours
            .Where(ro => ro.EmpNo == EmpNo && ro.Extra >= 1 && ro.Day == day && !Exclude.Contains(ro.Day) && Inxclude.Contains(ro.Day))
            .FirstOrDefault();

            if (check == null)
            {
                daysCheck = false;
            }
        }

        if (!daysCheck)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("Invalid overtime day") },
                action = "",
            });
        }

        // Validate all days in the same month
        int refYear = selectedDays[0].Year;
        int refYear2Dig = selectedDays[0].Year - 2000;
        int refMonth = selectedDays[0].Month;

        bool isAllInSameMonth = selectedDays.All(date => date.Year == refYear && date.Month == refMonth);

        if (!isAllInSameMonth)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("يجب أن يكون كل الأيام في نفس الشهر") },
                action = "",
            });
        }

        var request = _db.OvertimeReqMas
        .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.CancelFlag == 0
            && ro.ReqStatus == 1
            && ro.ManagerNo == _v.Profile.EmpNo.Value
        )
        .FirstOrDefault();

        if (Can("overtime-admin"))
        {
            request = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0 && ro.ReqStatus == 1)
            .First();
        }

        if (request == null)
            return StatusCode(404);

        request.OtStatus = "Approved by department manager";
        request.SignAuthCode = _v.Profile.EmpNo.Value;
        //request.SignAuthCode = 213;
        request.SignDate = DateTime.Now;
        request.ReqStatus = 20;

        _db.Update(request);
        await _db.SaveChangesAsync();


        var Days = _db.OvertimeReqPreDtls
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && selectedDays.Contains(ro.OtDate))
            .ToList();

        foreach (var pre in Days)
        {
            int HolydayFlag = _h.IsHoliday(pre.OtDate) ? 1 : 0;

            var NewOvertimeReqDtls = new OvertimeReqDtls
            {
                InYear = pre.InYear,
                InDocSlNo = pre.InDocSlNo,
                InDeptInd = 1,
                InMailNo = pre.InMailNo,

                EmpNo = pre.EmpNo,
                HolydayFlag= HolydayFlag,
                OtDuration = pre.OtDuration,
                OtDate = pre.OtDate,
                OtRate = pre.OtRate,
                ComputedAmount = pre.ComputedAmount,

                BasicAtm = pre.BasicAtm,

            };

            _db.OvertimeReqDtls.Add(NewOvertimeReqDtls);

            await _db.SaveChangesAsync();
        }

        var RequestToUpdate = _db.OvertimeReqMas
            .Where(ro =>
            ro.InYear == request.InYear &&
            ro.InMailNo == request.InMailNo &&
            ro.InDocSlNo == request.InDocSlNo &&
            ro.EmpNo == request.EmpNo).FirstOrDefault();

        RequestToUpdate.ExtraSum = _db.OvertimeReqDtls
                                        .Where(ro =>
                                        ro.InYear == request.InYear &&
                                        ro.InMailNo == request.InMailNo &&
                                        ro.InDocSlNo == request.InDocSlNo &&
                                        ro.EmpNo == request.EmpNo)
                                        .Sum(sum => sum.OtDuration);

        RequestToUpdate.ExtraAmountSum = _db.OvertimeReqDtls
                                            .Where(ro =>
                                            ro.InYear == request.InYear &&
                                            ro.InMailNo == request.InMailNo &&
                                            ro.InDocSlNo == request.InDocSlNo &&
                                            ro.EmpNo == request.EmpNo)
                                            .Sum(sum => sum.ComputedAmount);

        _db.OvertimeReqMas.Update(RequestToUpdate);

        await _db.SaveChangesAsync();

        await _h.Notify().Create(
            empNo: RequestToUpdate.EmpNo.Value,
            title: "العمل الاضافي",
            text: "اعتماد من السؤول المباشر",
            url: "/Overtime/My"
        );

        if (post.ContainsKey("comment"))
        {
            var newComment = new OvertimeComment
            {
                InYear = InYear,
                InMailNo = InMailNo,
                InDocSlNo = InDocSlNo,
                Comment = post["comment"],
                EmpNo = EmpNo,
                CreatedBy = _v.Profile.EmpNo.Value
            };

            _db.OvertimeComments.Add(newComment);
            await _db.SaveChangesAsync();
        }


        Log(
            InYear + "-" + InMailNo + "-" + InDocSlNo,
            "Overtime",
            "اعتماد المسؤول المباشر"
        );

        return Json(new
        {
            success = true,
            message = new List<string> { _("Request updated successfully") },
            action = "reload",
        });

    }

    [Route("Manager/Decline/{Id}")]
    public IActionResult DeclineByManager(string Id)
    {

        if (!Can("overtime-admin|department-manager"))
            return StatusCode(403);

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];

        if (Can("overtime-admin"))
        {

            var request = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0 && ro.ReqStatus == 1 && (ro.SignAuthCode.Value < 1 || ro.SignAuthCode == null))
            .First();

            if (request == null)
                return StatusCode(404);

            var days = _db.OvertimeReqDtls
                .Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .ToList();

            _db.OvertimeReqDtls.RemoveRange(days);

            _db.SaveChanges();

            var daysPre = _db.OvertimeReqPreDtls
                .Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .ToList();

            _db.OvertimeReqPreDtls.RemoveRange(daysPre);

            _db.SaveChanges();

            _db.OvertimeReqMas.Remove(request);

            _db.SaveChanges();
            
        }
        else
        {

            var request = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0 && ro.ManagerNo == _v.Profile.EmpNo.Value && ro.ReqStatus == 1 && (ro.SignAuthCode.Value < 1 || ro.SignAuthCode == null))
            .First();

            if (request == null)
                return StatusCode(404);

            var days = _db.OvertimeReqDtls
                .Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .ToList();

            _db.OvertimeReqDtls.RemoveRange(days);

            _db.SaveChanges();

            var daysPre = _db.OvertimeReqPreDtls
                .Where(ro => ro.EmpNo == _v.Profile.EmpNo && ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .ToList();

            _db.OvertimeReqPreDtls.RemoveRange(daysPre);

            _db.SaveChanges();

            _db.OvertimeReqMas.Remove(request);

            _db.SaveChanges();
            
        }

        Log(
            InYear + "-" + InMailNo + "-" + InDocSlNo,
            "Overtime",
            "رفض  المسؤول المباشر"
        );

        return Content("<script>window.location.href='/OverTime/Manager/?success=';window.close()</script>", "text/html");

    }


    [Route("Comment/Create/{Id}")]
    [HttpPost]
    public IActionResult CreateComment(string Id, [FromForm] IFormCollection post)
    {

        if (!Can("overtime-admin|department-manager|overtime-department"))
            return StatusCode(403);

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];


        var EmpNo = 0;

        if(Rel == "Con")
            return StatusCode(403);
        
        
        var rEmp = _db.OvertimeReqMas
        .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.CancelFlag == 0
            && ro.ManagerNo == _v.Profile.EmpNo.Value
        )
        .FirstOrDefault();

        if (Can("overtime-admin"))
        {
            rEmp = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0)
            .FirstOrDefault();
        }

        EmpNo = rEmp.EmpNo.Value;
        

        if (post.ContainsKey("comment"))
        {
            var newComment = new OvertimeComment
            {
                InYear = InYear,
                InMailNo = InMailNo,
                InDocSlNo = InDocSlNo,
                Comment = post["comment"],
                EmpNo = EmpNo,
                CreatedBy = _v.Profile.EmpNo.Value
            };

            _db.OvertimeComments.Add(newComment);
            _db.SaveChanges();
        }

        return Json(new
        {
            success = true,
            action = "location.reload()",
            message = new List<string> { }
        });
    }


    [Route("Dg")]
    public IActionResult DgRequests()
    {

        if (!Can("overtime-dg"))
            return StatusCode(403);

        _v.Page.Class = "  ";
        _v.Page.Active = "dg";
        _v.Page.Reload = true;
     

        return View(_v);
    }

    [Route("Dg/Datatable")]
    [HttpPost]
    public IActionResult DgRequestsDatatable([FromForm] DataTableHelper datatable, DateTime? from, DateTime? to)
    {

        if (!Can("overtime-dg"))
            return StatusCode(403);

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (from != null)
        {

            _v.Page.Filter.DateFrom = from;

        }

        if (to != null)
        {

            to = to.Value.Date.Add(new TimeSpan(23, 59, 59));
            _v.Page.Filter.DateTo = to;

        }


        var query = _db.OvertimeReqMas.Where(
                r =>
                r.EmpNo > 0
                && (r.SignAuthCode > 0 || r.SignAuthCode != null)
   
                && r.CancelFlag == 0
                && r.EstimatedFlag == 1
                && r.ReqStatus == 30
                && r.HrManagerSign == 0

                );


        if (!string.IsNullOrEmpty(datatable.Search.Value))
        {
            query = query.Where(f =>
            f.EmpNo.ToString().Contains(datatable.Search.Value)

            );


        }

        var total = query.Count();


        var empReq = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(
          query.ToList()
            );



        List<UnifiOvertimeReqMas> allReq = empReq;

        allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).ToList();

        var data = allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();

        switch (datatable.Order[0].Column)
        {

            case 1:
                if (datatable.Order[0].Dir == "asc")
                {
                    data = allReq.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                }
                else
                {
                    data = allReq.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                }
                break;

            case 2:
                if (datatable.Order[0].Dir == "asc")
                {
                    data = allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                }
                else
                {
                    data = allReq.OrderBy(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                }
                break;

            case 3:
                if (datatable.Order[0].Dir == "asc")
                {
                    data = allReq.OrderByDescending(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                }
                else
                {
                    data = allReq.OrderBy(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                }
                break;

    
            default:
                if (datatable.Order[0].Dir == "asc")
                {
                    data = allReq.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                }
                else
                {
                    data = allReq.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                }

                break;
        }

        var table = data.Select(ro => new
        {


            id = "<a href='/OverTime/Dg/View/" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "-" + ro.Rel + "'  >" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "</a>",
            emp = "#" + ro.EmpNo + "<br>" + _h.StaffData(ro.EmpNo).EmpNameA,
            createdAt = ro.OrderYear.Value + "-" + ro.OrderMonth.Value,

            extraSum = ro.ExtraSum,
         

        }).ToList();


        var output = new
        {
            datatable.Draw,
            recordsTotal = total,
            recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
            data = table
        };


        return Json(output);

    }

    [Route("Dg/View/{Id}")]
    public IActionResult DgRequestView(string Id)
    {

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];

        if(Rel == "Con")
            return StatusCode(403);


        if (!Can("overtime-dg"))
            return StatusCode(403);

        _v.Page.Title = "Over Time request";
        _v.Page.Active = "";
        //_v.Page.Layout = "_Popup";
        _v.Page.Reload = true;
        _v.Page.Back = $"/OverTime/Dg";

     
        var requestEmp = _db.OvertimeReqMas
        .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EstimatedFlag==1 && ro.ReqStatus >= 30)
        .First();

        if (requestEmp == null)
            return StatusCode(404);

        _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

        _v.OvertimeReqDtls = _db.OvertimeReqDtls
        .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
        .OrderBy(o => o.OtDate)
        .ToList();

        _v.UniOvertimeReqDtls = AutoMapper.MapList<OvertimeReqDtls, UnifiOvertimeReqDtls>(_v.OvertimeReqDtls);
        _v.EmpEarning = _db.EmpEarnings
            .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null))

            .FirstOrDefault();
        


        var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null)).FirstOrDefault();
        ViewBag.MaxEmpEarnAmt = EmpEarn.EarnAmt / 2;

        ViewBag.ExtraAmountSum = _db.OvertimeReqMas
        .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
        .Sum(max => max.ExtraAmountSum);

        ViewBag.ExtraSum = _db.OvertimeReqMas
            .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
            .Sum(max => max.ExtraSum);

        ViewBag.RequestCount = _db.OvertimeReqMas
            .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.SignAuthCode > 0 && ro.CancelFlag == 0 && ro.AuditStat == 1 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
            .Count();

        ViewBag.newsum = _db.OvertimeReqDtls.Where(ro =>
                                            ro.InYear == InYear &&
                                            ro.InMailNo == InMailNo &&
                                            ro.InDocSlNo == InDocSlNo &&
                                            ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                                            .Sum(sum => sum.ComputedAmount);

        

        _v.OvertimeComments = _db.OvertimeComments
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
            .OrderBy(o => o.TimeStamp)
            .ToList();


        return View(_v);
    }

    [Route("Dg/Approve/{Id}")]
    public async Task<IActionResult> DgRequestApprove(string Id)
    {

        if (!Can("overtime-dg"))
            return StatusCode(403);

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];


        if (Rel == "Con")
        {
            return StatusCode(403);
        }

        if (Rel == "Emp")
        {

            var requestEmp = _db.OvertimeReqMas
           .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0  &&  ro.EstimatedFlag == 1 && ro.ReqStatus == 30 && ro.HrManagerSign == 0)
           .First();

            if (requestEmp == null)
                return StatusCode(404);

            ProccessDgApproval(Id,50);

            await _h.Notify().Create(
                empNo: requestEmp.EmpNo.Value,
                title: "العمل الاضافي",
                text: "اعتماد من المدير العام ",
                url: "/Overtime/My"
            );
        }

        Log(
            InYear + "-" + InMailNo + "-" + InDocSlNo,
            "Overtime",
            "اعتماد المدير العام"
        );

        

        return Json(new
        {
            success = true,
            message = new List<string> { _("تم التحديث بنجاح") },
            action = "reload",
        });

    }

    [Route("DG/Decline/{Id}")]
    [HttpPost]
    public async Task<IActionResult> DgDecline(string Id, [FromForm] IFormCollection post)
    {

        if (!Can("overtime-dg"))
            return StatusCode(403);

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];


        if (Rel == "Con")
        {

            return StatusCode(403);
        }

        if (Rel == "Emp")
        {

            var requestEmp = _db.OvertimeReqMas
           .Where(ro =>
               ro.InYear == InYear
               && ro.InMailNo == InMailNo
               && ro.InDocSlNo == InDocSlNo
               && ro.CancelFlag == 0
               && ro.ReqStatus == 30
               && ro.EstimatedFlag == 1
           //&& ro.ManagerNo == _v.Profile.EmpNo.Value
           )
           .First();

            if (requestEmp == null)
                return StatusCode(404);

            requestEmp.OtStatus = "رفض المدير العام";
            //request.SignAuthCode = _v.Profile.EmpNo.Value;
            //requestEmp.UserId = "213";
            requestEmp.ReqStatus = 51;
            requestEmp.CancelFlag = 1;


            _db.Update(requestEmp);
            _db.SaveChanges();

            await _h.Notify().Create(
                empNo: requestEmp.EmpNo.Value,
                title: "العمل الاضافي",
                text: "رفض من المدير العام ",
                url: "/Overtime/My"
            );
        }

        Log(
            InYear + "-" + InMailNo + "-" + InDocSlNo,
            "Overtime",
            "رفض المدير العام"
        );

        return Json(new
        {
            success = true,
            message = new List<string> { _("Request updated successfully") },
            action = "reload",
        });
    }

    [Route("Dg/Return/{Id}")]
    [HttpPost]
    public async Task<IActionResult> DgRequestReturn(string Id, [FromForm] IFormCollection post)
    {
        return await ProcessReturnRequest(
            Id: Id,
            post: post,
            permission: "overtime-dg",
            allowedStatuses: new int[] { 30 }, 
            sendNotification: true,
            logMessage: "ارجاع عمل اضافي",
            estimatedFlagFilter: 1,
            hrManagerSignFilter: 0,
            redirectUrl: "/OverTime/Dg"
        );
    }


    protected bool ProccessDgApproval(string Id,int statusCode)
    {
        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];

        var requestEmp = _db.OvertimeReqMas
           .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0)
           .First();

        if (requestEmp == null)
            return false;

        var EmpEarn = _db.EmpEarnings
            .Where(ro => ro.EmpNo == requestEmp.EmpNo && ro.EarnCode == 1 && ro.ToDate == null)
            .FirstOrDefault();

        float limitAmt = (float)(EmpEarn.EarnAmt / 2);

        var empDate = _db.VempDtls.Find(requestEmp.EmpNo);

        List<string> highGrades = new List<string>();

        highGrades.Add("الأولى");
        highGrades.Add("الثانية");
        highGrades.Add("الثالثة");
        highGrades.Add("الرابعة");
        highGrades.Add("الخامسة");


        if (highGrades.Contains(empDate.GradRank))
        {
            limitAmt = (float)(EmpEarn.EarnAmt / 4);
        }

        float currentAmt = (float)(_db.OvertimeReqMas
        .Where(ro => ro.EmpNo == requestEmp.EmpNo && ro.OrderYear == requestEmp.OrderYear && ro.OrderMonth == requestEmp.OrderMonth && ro.HrManagerSign > 0)
        .Sum(max => max.ExtraAmountSum));

        var daysList = _db.OvertimeReqDtls
       .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
       .ToList();

        float current = currentAmt;

        foreach (var item in daysList)
        {

            float amount = 0;

            if (current >= limitAmt)
            {
                amount = 0;
            }
            else if ((current + item.ComputedAmount) >= limitAmt)
            {
                amount = limitAmt - current;
            }
            else
            {
                amount = item.ComputedAmount;
            }

            current += item.ComputedAmount;


            item.OtAmount = amount;

            _db.Update(item);
            _db.SaveChanges();

        }


        requestEmp = _db.OvertimeReqMas
        .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0)
        .First();


        requestEmp.OtStatus = "Approved by HR manager";
        requestEmp.ReqStatus = statusCode;
        //request.SignAuthCode = _v.Profile.EmpNo.Value;
        requestEmp.HrManagerSign = _v.Profile.EmpNo;
        requestEmp.HrManagerSignDate = DateTime.Now;



        requestEmp.ExtraSum = _db.OvertimeReqDtls
                                        .Where(ro =>
                                        ro.InYear == InYear &&
                                        ro.InMailNo == InMailNo &&
                                        ro.InDocSlNo == InDocSlNo &&
                                        ro.EmpNo == requestEmp.EmpNo)
                                        .Sum(sum => sum.OtDuration);

        requestEmp.ExtraAmountSum = _db.OvertimeReqDtls
                                            .Where(ro =>
                                            ro.InYear == InYear &&
                                            ro.InMailNo == InMailNo &&
                                            ro.InDocSlNo == InDocSlNo &&
                                            ro.EmpNo == requestEmp.EmpNo)
                                            .Sum(sum => sum.OtAmount);

        _db.Update(requestEmp);
        _db.SaveChanges();

        return true;
    }



    [Route("GetOvertTimeDays")]
    public IActionResult GetOvertTimeDays(int year, int month)
    {
        // Early validation
        if (year == 0 || month == 0)
        {
            return StatusCode(404);
        }

        // Check employee eligibility early
        if (_v.Profile.EmpNo > 1000)
        {
            return StatusCode(404);
        }

        var empNo = _v.Profile.EmpNo.Value;
        var dateFrom = new DateTime(year, month, 1);
        var dateTo = new DateTime(year, month, DateTime.DaysInMonth(year, month));
        var threeMonthsAgo = DateTime.Now.AddMonths(-3);

        // Single optimized query with joins to exclude already requested dates
        var days = _db.EmpWorkHours
            .Where(ro => 
                ro.EmpNo == empNo
                && ro.Day >= threeMonthsAgo
                && ro.Extra >= 1
                && ro.Day >= dateFrom
                && ro.Day <= dateTo
            )
            .Where(ro => 
                !_db.OvertimeReqDtls.Any(dtl => dtl.OtDate == ro.Day && dtl.EmpNo == empNo) &&
                !_db.OvertimeReqPreDtls.Any(pre => pre.OtDate == ro.Day && pre.EmpNo == empNo)
            )
            .OrderByDescending(ro => ro.Day)
            .ToList();

        return Json(new { data = days });
    }

    [Route("GetOvertTimeDaysEmp")]
    public IActionResult GetOvertTimeDaysEmp(int empno, int year, int month)
    {
        // Early validation
        if (year == 0 || month == 0 || empno < 1)
        {
            return StatusCode(404);
        }

        // Check employee eligibility early
        if (empno > 1000)
        {
            return StatusCode(404);
        }

        var dateFrom = new DateTime(year, month, 1);
        var dateTo = new DateTime(year, month, DateTime.DaysInMonth(year, month));

        // Single optimized query with joins to exclude already requested dates
        var days = _db.EmpWorkHours
            .Where(ro =>
                ro.EmpNo == empno
                && ro.Extra >= 1
                && ro.Day >= dateFrom
                && ro.Day <= dateTo
            )
            .Where(ro =>
                !_db.OvertimeReqDtls.Any(dtl => dtl.OtDate == ro.Day && dtl.EmpNo == empno) &&
                !_db.OvertimeReqPreDtls.Any(pre => pre.OtDate == ro.Day && pre.EmpNo == empno)
            )
            .OrderByDescending(ro => ro.Day)
            .ToList();

        // Calculate rates for each day
        foreach (var day in days)
        {
            day.Rate = _v._amount(day.Extra * _v.GetOverTimeHourPrice(empno, day.Day));
        }

        return Json(new { data = days });
    }

    [Route("GetOvertTimeDaysEstimatedEmp")]
    public IActionResult GetOvertTimeDaysEstimatedEmp(int empno, int year, int month)
    {
        if (year == 0 || month == 0 || empno < 1 || empno > 1000)
        {
            return StatusCode(404);
        }

        DateTime dateFrom = new DateTime(year, month, 1);
        DateTime dateTo = new DateTime(year, month, DateTime.DaysInMonth(year, month));

        var Exclude = _db.OvertimeReqDtls
            .Where(ro => ro.EmpNo == empno)
            .Select(d => d.OtDate)
            .ToList();
        var Exclude2 = _db.OvertimeReqPreDtls
            .Where(ro => ro.EmpNo == empno)
            .Select(d => d.OtDate)
            .ToList();


        var days = new List<EmpWorkHours>();

        var monthRecords = _db.EmpWorkHours.Where(r => r.EmpNo == empno && r.Day >= dateFrom && r.Day <= dateTo).ToList();

        for (DateTime date = dateFrom; date <= dateTo; date = date.AddDays(1))
        {
            var dayRecord = monthRecords.Where(r => r.Day == date).FirstOrDefault();

            if (dayRecord != null)
            {
                days.Add(new EmpWorkHours
                {
                    EmpNo = empno,
                    Day = date,
                    FirstEntry = dayRecord.FirstEntry,
                    LastEntry = dayRecord.LastEntry,
                });
            }
            else
            {
                days.Add(new EmpWorkHours
                {
                    EmpNo = empno,
                    Day = date,
                });
            }

        }

        var days2 = days
            .Where(ro =>
                ro.EmpNo == empno
                //&& ro.Day >= DateTime.Now.AddMonths(-3)
                //&& (ro.Extra > 0 || ro.Hours > 0)
                && ro.Day >= dateFrom
                && ro.Day <= dateTo
                && !Exclude.Contains(ro.Day)
                && !Exclude2.Contains(ro.Day)
            )
            .OrderByDescending(ro => ro.Day)
            .ToList();

        foreach (var day in days2)
        {
            day.Rate = _v._amount(day.Extra * _v.GetOverTimeHourPrice(empno, day.Day));
        }

        return Json(new
        {
            data = days2
        });

    }

    [Route("GetFpRecords")]
    public IActionResult GetFpRecords(string EmpNo, string Day)
    { 
        DateTime date = DateTime.Parse(Day);
        
        // Get fingerprint records for display
        var records = _db.FPRecords.Where(ro=> ro._EmpNo == EmpNo && ro.Date== date).OrderByDescending(ro=>ro.CreatedAt).ToList();
        
        // Get actual overtime hours from EmpWorkHours table
        float? overtimeHours = null;
        var empWorkHour = _db.EmpWorkHours.FirstOrDefault(ewh => 
            ewh.EmpNo == int.Parse(EmpNo) && 
            ewh.Day.Date == date.Date);
            
        if (empWorkHour != null)
        {
            overtimeHours = empWorkHour.Extra; // This is the OVERTIME_HOURS column value
        }

        return Json(new {
            data = records,
            overtimeHours = overtimeHours, // Pre-calculated overtime hours from the database
            calculatedFromDatabase = overtimeHours.HasValue // Flag to indicate if we have database calculated value
        });
    }


    [HttpGet("Report")]
    [Can("overtime-report")]
    public IActionResult Report(int Dg = 0, int Dept = 0, int EmpNo = 0, int PayStatus = -1, 
        string ChartType = "bar", string ViewType = "summary", DateTime? from = null, DateTime? to = null)
    {
        if (from == null)
        {
            from = DateTime.Today.AddMonths(-6);
        }

        if (to == null)
        {
            to = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        }
        else
        {
            to = to.Value.Date.Add(new TimeSpan(23, 59, 59));
        }

        _v.Page.Filter.DateFrom = from;
        _v.Page.Filter.DateTo = to;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="العمل الاضافي", Url=$"#"},
            new Breadcrumb {Label="تقرير العمل الاضافي", Url=$"/Overtime/Report"},
        };

        _v.Page.Title = "تقرير العمل الاضافي";
        _v.Page.Reload = true;

        // Pass the filter parameters to the view
        ViewBag.Dg = Dg;
        ViewBag.Dept = Dept;
        ViewBag.EmpNo = EmpNo;
        ViewBag.PayStatus = PayStatus;
        ViewBag.ChartType = ChartType;
        ViewBag.ViewType = ViewType;
        ViewBag.FromDate = from.Value.ToString("yyyy-MM-dd");
        ViewBag.ToDate = to.Value.ToString("yyyy-MM-dd");

        // Get all DGs and Departments for filter dropdowns
        ViewBag.DgList = _db.TdgCodes.ToList();
        ViewBag.DeptList = Dg > 0 
            ? _db.TDeptCode.Where(r => r.DgCode == Dg && r.DeptCode > 0).ToList() 
            : _db.TDeptCode.Where(r => r.DeptCode > 0).ToList();

        // Get employee list for filter dropdown based on selected department/DG
        if (Dept > 0)
        {
            ViewBag.EmployeeList = _db.VempDtls.Where(e => e.DeptCode == Dept && e.EmpNo > 1 && e.EmpNo < 1000).ToList();
        }
        else if (Dg > 0)
        {
            ViewBag.EmployeeList = _db.VempDtls.Where(e => e.DgCode == Dg && e.EmpNo > 1 && e.EmpNo < 1000).ToList();
        }
        else
        {
            ViewBag.EmployeeList = new List<object>();
        }

        // Build base query for overtime data
        var query = _db.OvertimeReqMas.Where(r =>
            r.TimeStamp >= from &&
            r.TimeStamp <= to &&
            r.EmpNo > 0 &&
            r.CancelFlag != 1 &&
            (r.ReqStatus == 60 || r.ReqStatus == 50 || r.ReqStatus == 40)
        );

        // Apply filters
        if (EmpNo > 0)
        {
            query = query.Where(r => r.EmpNo == EmpNo);
        }
        else
        {
            if (Dept > 0)
            {
                var empInDept = _db.VempDtls.Where(e => e.DeptCode == Dept && e.EmpNo > 1 && e.EmpNo < 1000).Select(e => e.EmpNo).ToList();
                query = query.Where(r => empInDept.Contains(r.EmpNo.Value));
            }
            else if (Dg > 0)
            {
                var empInDg = _db.VempDtls.Where(e => e.DgCode == Dg && e.EmpNo > 1 && e.EmpNo < 1000).Select(e => e.EmpNo).ToList();
                query = query.Where(r => empInDg.Contains(r.EmpNo.Value));
            }
        }

        if (PayStatus >= 0)
        {
            // Filter by payment status (this would need to be implemented)
            // For now, just note that we'll handle this in the view
            ViewBag.PayStatusFilter = PayStatus;
        }

        // Map results to view model
        var requests = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(query.ToList());

        // Prepare data for charts
        if (ViewType == "summary")
        {
            // Group by month
            var monthlySummary = requests
                .GroupBy(r => new { r.OrderYear, r.OrderMonth })
                .Select(g => new
                {
                    Period = $"{g.Key.OrderYear}-{g.Key.OrderMonth}",
                    TotalHours = g.Sum(r => r.ExtraSum),
                    TotalAmount = g.Sum(r => r.ExtraAmountSum)
                })
                .OrderBy(r => r.Period)
                .ToList();

            ViewBag.ChartLabels = monthlySummary.Select(m => m.Period).ToList();
            ViewBag.ChartHoursData = monthlySummary.Select(m => m.TotalHours).ToList();
            ViewBag.ChartAmountData = monthlySummary.Select(m => m.TotalAmount).ToList();
        }
        else if (ViewType == "byDepartment")
        {
            // Group by department using a different approach
            var empDepartments = new Dictionary<int, string>();
            foreach (var emp in _db.VempDtls.Where(e => e.EmpNo > 1 && e.EmpNo < 1000).ToList())
            {
                if (emp.EmpNo.HasValue && !string.IsNullOrEmpty(emp.DeptDespA))
                {
                    empDepartments[emp.EmpNo.Value] = emp.DeptDespA;
                }
            }

            var deptSummary = requests
                .GroupBy(r => r.EmpNo.HasValue && empDepartments.ContainsKey(r.EmpNo.Value) 
                    ? empDepartments[r.EmpNo.Value] 
                    : "Unknown Department")
                .Select(g => new
                {
                    Department = g.Key,
                    TotalHours = g.Sum(r => r.ExtraSum),
                    TotalAmount = g.Sum(r => r.ExtraAmountSum)
                })
                .OrderByDescending(d => d.TotalHours)
                .ToList();

            ViewBag.ChartLabels = deptSummary.Select(d => d.Department).ToList();
            ViewBag.ChartHoursData = deptSummary.Select(d => d.TotalHours).ToList();
            ViewBag.ChartAmountData = deptSummary.Select(d => d.TotalAmount).ToList();
        }
        else if (ViewType == "byEmployee")
        {
            // Group by employee
            var empSummary = requests
                .GroupBy(r => r.EmpNo)
                .Select(g => new
                {
                    EmployeeId = g.Key,
                    EmployeeName = _h.StaffData(g.Key).EmpNameA,
                    TotalHours = g.Sum(r => r.ExtraSum),
                    TotalAmount = g.Sum(r => r.ExtraAmountSum)
                })
                .OrderByDescending(e => e.TotalHours)
                .Take(10) // Top 10 employees
                .ToList();

            ViewBag.ChartLabels = empSummary.Select(e => e.EmployeeName).ToList();
            ViewBag.ChartHoursData = empSummary.Select(e => e.TotalHours).ToList();
            ViewBag.ChartAmountData = empSummary.Select(e => e.TotalAmount).ToList();
        }

        // Pass detailed data for table view
        ViewBag.DetailedData = requests;

        // Get overall summary statistics
        ViewBag.TotalOvertime = requests.Sum(r => r.ExtraSum);
        ViewBag.TotalAmount = requests.Sum(r => r.ExtraAmountSum);
        ViewBag.RequestCount = requests.Count();

        return View(_v);
    }


    [HttpGet("departments")]
    public IActionResult GetDepartmentsByDg(int dgCode)
    {
        if (dgCode <= 0)
        {
            return Json(new List<object>());
        }

        var departments = _db.TDeptCode
            .Where(d => d.DgCode == dgCode && d.DeptCode > 0)
            .OrderBy(d => d.DeptDespA)
            .Select(d => new { deptCode = d.DeptCode, deptDespA = d.DeptDespA })
            .ToList();

        return Json(departments);
    }

    [HttpGet("employees/by-department")]
    public IActionResult GetEmployeesByDepartment(int deptCode)
    {
        if (deptCode <= 0)
        {
            return Json(new List<object>());
        }

        var employees = _db.VempDtls
            .Where(e => e.DeptCode == deptCode && e.EmpNo > 1 && e.EmpNo < 1000)
            .OrderBy(e => e.EmpNameA)
            .Select(e => new { empNo = e.EmpNo, empNameA = e.EmpNameA })
            .ToList();

        return Json(employees);
    }

    [HttpGet("employees/by-dg")]
    public IActionResult GetEmployeesByDg(int dgCode)
    {
        if (dgCode <= 0)
        {
            return Json(new List<object>());
        }

        var employees = _db.VempDtls
            .Where(e => e.DgCode == dgCode && e.EmpNo > 1 && e.EmpNo < 1000)
            .OrderBy(e => e.EmpNameA)
            .Select(e => new { empNo = e.EmpNo, empNameA = e.EmpNameA })
            .ToList();

        return Json(employees);
    }

    /// <summary>
    /// Unified return handler for all overtime request return operations
    /// </summary>
    /// <param name="Id">Request ID in format: InYear-InMailNo-InDocSlNo-Rel</param>
    /// <param name="post">Form collection containing optional comment</param>
    /// <param name="permission">Required permission string for authorization</param>
    /// <param name="allowedStatuses">Array of allowed request statuses to filter records</param>
    /// <param name="sendNotification">Whether to send notification to employee</param>
    /// <param name="redirectUrl">URL to redirect after successful return</param>
    /// <param name="logMessage">Message to log for the action</param>
    /// <param name="responseMessage">Success message to return in JSON response</param>
    /// <param name="estimatedFlagFilter">Filter by EstimatedFlag (null means no filter)</param>
    /// <param name="hrManagerSignFilter">Filter by HrManagerSign (null means no filter)</param>
    /// <param name="customReqStatusLogic">Custom function to determine ReqStatus (null means use default 20)</param>
    /// <returns>JSON result with success status and redirect action</returns>
    protected async Task<IActionResult> ProcessReturnRequest(
        string Id, 
        IFormCollection post, 
        string permission,
        int[] allowedStatuses,
        bool sendNotification = false,
        string redirectUrl = null,
        string logMessage = "ارجاع عمل اضافي",
        string responseMessage = null,
        int? estimatedFlagFilter = null,
        int? hrManagerSignFilter = null,
        Func<dynamic, int> customReqStatusLogic = null)
    {
        // Parse ID components
        string[] rawcode = Id.Split("-");
        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];

        // Check permissions
        if (!Can(permission))
            return StatusCode(403);

        int EmpNo = 0;

        // Handle Employee requests only (Con handling is empty in all existing methods)
        if (Rel == "Emp")
        {
            // Build query with base conditions
            var query = _db.OvertimeReqMas
                .Where(ro =>
                    ro.InYear == InYear
                    && ro.InMailNo == InMailNo
                    && ro.InDocSlNo == InDocSlNo
                    && ro.CancelFlag == 0
                    && allowedStatuses.Contains(ro.ReqStatus.Value));

            // Apply optional filters
            if (estimatedFlagFilter.HasValue)
                query = query.Where(ro => ro.EstimatedFlag == estimatedFlagFilter.Value);


            var requestEmp = query.FirstOrDefault();

            if (requestEmp == null)
                return StatusCode(404);

            EmpNo = requestEmp.EmpNo.Value;

            // Update request status
            requestEmp.OtStatus = "Approved by department manager";
            requestEmp.HrManagerSign = 0;
            
            // Use custom logic if provided, otherwise default to 20
            requestEmp.ReqStatus = customReqStatusLogic?.Invoke(requestEmp) ?? 20;
            
            requestEmp.UserId = null;

            _db.Update(requestEmp);
            _db.SaveChanges();

            // Send notification if required
            if (sendNotification)
            {
                // await _h.Notify().Create(
                //     empNo: requestEmp.EmpNo.Value,
                //     title: "العمل الاضافي",
                //     text: "ارجاع عمل اضافي",
                //     url: "/Overtime/My"
                // );
            }
        }

        // Add comment if provided
        if (post.ContainsKey("comment"))
        {
            var newComment = new OvertimeComment
            {
                InYear = InYear,
                InMailNo = InMailNo,
                InDocSlNo = InDocSlNo,
                Comment = post["comment"],
                EmpNo = EmpNo,
                CreatedBy = _v.Profile.EmpNo.Value
            };

            _db.OvertimeComments.Add(newComment);
            _db.SaveChanges();
        }

        // Log the action
        Log(
            InYear + "-" + InMailNo + "-" + InDocSlNo,
            "Overtime",
            logMessage
        );

        // Prepare response
        var response = new
        {
            success = true,
            message = new List<string> { _(responseMessage ?? "Request updated successfully") }
        };

        // Add redirect action if specified
        if (!string.IsNullOrEmpty(redirectUrl))
        {
            return Json(new
            {
                response.success,
                response.message,
                action = $"location.href='{redirectUrl}'"
            });
        }

        return Json(new
        {
            response.success,
            response.message,
            action = "reload"
        });
    }
}

