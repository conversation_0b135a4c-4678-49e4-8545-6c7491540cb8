﻿using HumanResource.Core.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using HumanResource.Modules.Overtime.ViewModels;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Overtime.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.Unifi;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Mapping;
using HumanResource.Core.Contexts;
using HumanResource.Core.UI.Models;


namespace HumanResource.Modules.Overtime.Controllers
{
    [Can("audit")]
    [Area("Overtime")]
    [Route("Overtime/Audit")]
    public class AuditController : OverTimeController
    {


        public AuditController(
            hrmsContext context,
            IHttpContextAccessor httpContextAccessor, AppHelper helper )
            : base(context, httpContextAccessor, helper)
        {

            _v = new OverTimeViewModel(context, httpContextAccessor, helper);

            _v.Page.Active = "audit";

        }


        [HttpGet("")]
        public IActionResult AuditRequests(int? year, string actionFilter = "all")
        {

            if (!Can("audit"))
                return StatusCode(403);


            if (year == null)
            {
                year = DateTime.Now.Year;
            }

            _v.Page.Class = "  ";
            _v.Page.Active = "Audit";
            _v.Page.Reload = true;

            _v.Page.Breadcrumb = new List<Breadcrumb> {

             new Breadcrumb {Label="التدقيق", Url=$"/OverTime/Audit"},
             new Breadcrumb {Label="العمل الاضافي", Url=$"/OverTime/Audit"},
        };

            ViewBag.Year = year;

            return View(_v);
        }

        [Can("audit")]
        [HttpPost("Datatable")]
        public IActionResult AuditRequestsDatatable([FromForm] DataTableHelper datatable, int? year, string actionFilter = "all")
        {
            year ??= DateTime.Now.Year;

            // Get action filter from query string if not provided in form
            if (string.IsNullOrEmpty(actionFilter))
            {
                actionFilter = HttpContext.Request.Query["actionFilter"].ToString();
                if (string.IsNullOrEmpty(actionFilter))
                    actionFilter = "all";
            }

            // Build base query
            var query = _db.OvertimeReqMas.Where(r =>
                r.EmpNo > 0
                && r.CancelFlag != 1
                && (r.ReqStatus == 40 || r.ReqStatus == 50 || r.ReqStatus >= 60)
                && r.OrderYear == (year - 2000)
            );

            // Apply action filter based on request status
            if (actionFilter == "needs-action")
            {
                // Requests that need action (audit status but not yet processed)
                query = query.Where(r => r.ReqStatus == 40 || r.ReqStatus == 50);
            }
            // If "all", no additional filtering needed

            // Apply search filter
            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                query = query.Where(f => f.EmpNo.ToString().Contains(datatable.Search.Value));
            }

            var total = query.Count();
            var filteredTotal = total;

            // Convert to unified model
            var allReq = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(query.ToList());

            // Apply ordering based on column
            var orderedQuery = AuditApplyOrdering(allReq.AsQueryable(), datatable.Order[0].Column, datatable.Order[0].Dir);

            // Apply pagination
            var data = orderedQuery.Skip(datatable.Start).Take(datatable.Length).ToList();

            // Transform data for table
            var table = data.Select(ro => new
            {
                id = $"<a href='/OverTime/Audit/View/{ro.InYear}-{ro.InMailNo}-{ro.InDocSlNo}-{ro.Rel}'>{ro.InYear}-{ro.InMailNo}-{ro.InDocSlNo}</a>",
                emp = $"#{ro.EmpNo}<br>{_h.StaffData(ro.EmpNo).EmpNameA}",
                createdAt = $"{ro.OrderYear}-{ro.OrderMonth}",
                extraSum = _h.FormatHour(ro.ExtraSum),
                extraAmountSum = _v._amount(ro.ExtraAmountSum.Value),
                estematedFlag = ro.EstimatedFlag == 1
                    ? "<span class='badge badge-warning rounded-pill px-2'>تقديري</span>"
                    : "فعلي",
                status = _v.renderSatus(ro.ReqStatus, new int[] { 50, 40 })
            }).ToList();

            var output = new
            {
                datatable.Draw,
                recordsTotal = total,
                recordsFiltered = filteredTotal,
                data = table
            };

            return Json(output);
        }

        private IQueryable<UnifiOvertimeReqMas> AuditApplyOrdering(IQueryable<UnifiOvertimeReqMas> query, int column, string direction)
        {
            var isAscending = direction == "asc";

            return column switch
            {
                1 => isAscending ? query.OrderBy(o => o.EmpNo) : query.OrderByDescending(o => o.EmpNo),
                2 => isAscending ? query.OrderBy(o => o.OrderYear + o.OrderMonth) : query.OrderByDescending(o => o.OrderYear + o.OrderMonth),
                3 => isAscending ? query.OrderBy(o => o.ExtraSum) : query.OrderByDescending(o => o.ExtraSum),
                4 => isAscending ? query.OrderBy(o => o.ExtraAmountSum) : query.OrderByDescending(o => o.ExtraAmountSum),
                5 => isAscending ? query.OrderBy(o => o.EstimatedFlag) : query.OrderByDescending(o => o.EstimatedFlag),
                6 => isAscending ? query.OrderBy(o => o.ReqStatus) : query.OrderByDescending(o => o.ReqStatus),
                _ => isAscending ? query.OrderBy(o => o.ReqStatus) : query.OrderByDescending(o => o.ReqStatus)
            };
        }


        [HttpGet("View/{Id}")]
        public IActionResult AuditRequestView(string Id)
        {

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (!Can("audit"))
                return StatusCode(403);

            _v.Page.Title = "طلب العمل الاضافي";
            _v.Page.Active = "Audit";
            //_v.Page.Layout = "_Popup";
            _v.Page.Reload = true;
            _v.Page.Back = $"/OverTime/Audit";

            if (Rel == "Con")
            {
                var requestCon = _db.ConOvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .First();

                if (requestCon == null)
                    return StatusCode(404);

                _v.ConOvertimeReqMas = requestCon;

                _v.UniOvertimeReqMas = AutoMapper.MapProperties<ConOvertimeReqMas, UnifiOvertimeReqMas>(_v.ConOvertimeReqMas);

                _v.ConOvertimeReqDtls = _db.ConOvertimeReqDtls
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .OrderBy(o => o.OtDate)
                .ToList();

                _v.UniOvertimeReqDtls = AutoMapper.MapList<ConOvertimeReqDtls, UnifiOvertimeReqDtls>(_v.ConOvertimeReqDtls);


            }

            if (Rel == "Emp")
            {
                var requestEmp = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .First();

                if (requestEmp == null)
                    return StatusCode(404);

                _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

                _v.OvertimeReqDtls = _db.OvertimeReqDtls
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .OrderBy(o => o.OtDate)
                .ToList();

                _v.UniOvertimeReqDtls = AutoMapper.MapList<OvertimeReqDtls, UnifiOvertimeReqDtls>(_v.OvertimeReqDtls);
                _v.EmpEarning = _db.EmpEarnings
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null))

                    .FirstOrDefault();
            }

            _v.Page.Breadcrumb = new List<Breadcrumb> {
              new Breadcrumb {Label="العمل الاضافي", Url=$"/OverTime/Audit"},
             new Breadcrumb {Label=_v._h.StaffData(_v.UniOvertimeReqMas.EmpNo).EmpNameA, Url=$"/OverTime/"},
        };


            if (_v.UniOvertimeReqMas.EmpNo >= 1000)
            {
                var EmpEarn = _db.ConPayrollDtls
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnDepCode == 100)
                    .OrderByDescending(ro2 => ro2.EndDate)
                    .FirstOrDefault();

                ViewBag.MaxEmpEarnAmt = EmpEarn.Amount / 2;

                ViewBag.ExtraAmountSum = _db.ConOvertimeReqMas
                .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .Sum(max => max.ExtraAmountSum);

                ViewBag.ExtraSum = _db.ConOvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .Sum(max => max.ExtraSum);

                ViewBag.RequestCount = _db.ConOvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.ReqStatus == 60 && ro.CancelFlag == 0 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                    .Count();

            }
            else
            {
                var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null)).FirstOrDefault();
                ViewBag.MaxEmpEarnAmt = EmpEarn.EarnAmt / 2;

                ViewBag.ExtraAmountSum = _db.OvertimeReqMas
                .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .Sum(max => max.ExtraAmountSum);

                ViewBag.ExtraSum = _db.OvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                    .Sum(max => max.ExtraSum);

                ViewBag.RequestCount = _db.OvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.CancelFlag == 0 && ro.ReqStatus == 60 && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                    .Count();

                ViewBag.newsum = _db.OvertimeReqDtls.Where(ro =>
                                                    ro.InYear == InYear &&
                                                    ro.InMailNo == InMailNo &&
                                                    ro.InDocSlNo == InDocSlNo &&
                                                    ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                                                    .Sum(sum => sum.ComputedAmount);

            }

            _v.OvertimeComments = _db.OvertimeComments
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .OrderBy(o => o.TimeStamp)
                .ToList();


            return View(_v);
        }


        [HttpPost("Approve/{Id}")]
        public IActionResult AuditRequestApprove(string Id, [FromForm] IFormCollection post)
        {

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (!Can("audit"))
                return StatusCode(403);

            int EmpNo = 0;


            if (Rel == "Con")
            {


            }

            if (Rel == "Emp")
            {

                var requestEmp = _db.OvertimeReqMas
               .Where(ro =>
                   ro.InYear == InYear
                   && ro.InMailNo == InMailNo
                   && ro.InDocSlNo == InDocSlNo
                   && ro.CancelFlag == 0
                   && (ro.ReqStatus == 40 || ro.ReqStatus == 50)
               //&& ro.ManagerNo == _v.Profile.EmpNo.Value
               )
               .First();

                EmpNo = requestEmp.EmpNo.Value;

                if (requestEmp == null)
                    return StatusCode(404);

                //requestEmp.OtStatus = "Approved by audit";
                //request.SignAuthCode = _v.Profile.EmpNo.Value;
                //requestEmp.UserId = "213";
                requestEmp.ReqStatus = 60;
                requestEmp.FirstAuditStat = 1;
                requestEmp.FirstAuditDate = DateTime.Now;
                requestEmp.FirstAuditUserId = _v.Profile.EmpNo.Value.ToString();

                _db.Update(requestEmp);
                _db.SaveChanges();

            }

            if (post.ContainsKey("comment"))
            {
                var newComment = new OvertimeComment
                {
                    InYear = InYear,
                    InMailNo = InMailNo,
                    InDocSlNo = InDocSlNo,
                    Comment = post["comment"],
                    EmpNo = EmpNo,
                    CreatedBy = _v.Profile.EmpNo.Value
                };

                _db.OvertimeComments.Add(newComment);
                _db.SaveChanges();
            }

            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "اعتماد المراجعة الداخلية"
            );

            return Json(new
            {
                success = true,
                message = new List<string> { _("تم الموافقة") },
                action = "reload",
            });

        }


        [HttpPost("Decline/{Id}")]
        public IActionResult AuditDecline(string Id, [FromForm] IFormCollection post)
        {

            if (!Can("audit"))
                return StatusCode(403);


            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];

            if (Rel == "Con")
            {



            }

            if (Rel == "Emp")
            {

                var requestEmp = _db.OvertimeReqMas
               .Where(ro =>
                   ro.InYear == InYear
                   && ro.InMailNo == InMailNo
                   && ro.InDocSlNo == InDocSlNo
                   && ro.CancelFlag == 0
                   && (ro.ReqStatus == 40 || ro.ReqStatus == 50)
               //&& ro.ManagerNo == _v.Profile.EmpNo.Value
               )
               .First();

                if (requestEmp == null)
                    return StatusCode(404);

                requestEmp.OtStatus = "Declined by audit";
                //request.SignAuthCode = _v.Profile.EmpNo.Value;
                //requestEmp.UserId = "213";
                requestEmp.ReqStatus = 61;
                requestEmp.CancelFlag = 1;
                requestEmp.AuditRem = post["Note"];


                _db.Update(requestEmp);
                _db.SaveChanges();

            }

            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "رفض المراجعة الداخلية"
            );

            return Json(new
            {
                success = true,
                message = new List<string> { _("تم الرفض") },
                action = "reload",
            });

        }

        [HttpPost("Return/{Id}")]
        public IActionResult AuditRequestReturn(string Id, [FromForm] IFormCollection post)
        {
            return ProcessReturnRequest(
                Id: Id,
                post: post,
                permission: "audit",
                allowedStatuses: new int[] { 40, 50 },
                sendNotification: false,
                logMessage: "ارجاع لقسم الاجازات"
            ).Result;
        }


    }
}
