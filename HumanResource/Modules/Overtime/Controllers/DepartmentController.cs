﻿using HumanResource.Core.Helpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis;
using HumanResource.Modules.Overtime.ViewModels;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Overtime.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.Unifi;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Mapping;
using HumanResource.Core.Contexts;
using HumanResource.Core.UI.Models;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;
using HumanResource.Modules.Employees.Models.Entities;

namespace HumanResource.Modules.Overtime.Controllers
{
    [Can("overtime-department|overtime-hr-manager")]
    [Area("Overtime")]
    [Route("Overtime/Department")]
    public class DepartmentController : OverTimeController
    {
        public DepartmentController(
            hrmsContext context,
            IHttpContextAccessor httpContextAccessor, AppHelper helper)
            : base(context, httpContextAccessor, helper)
        {

            _v = new OverTimeViewModel(context, httpContextAccessor, helper);

            _v.Page.Active = "hr_overtime";
        }


        [HttpGet("")]
        public IActionResult DepartmentRequests(DateTime? from, DateTime? to, string actionFilter = "all")
        {

            if (!Can("overtime-department"))
                return StatusCode(403);

            _v.Page.Class = "  ";
            _v.Page.Active = "hr_overtime";
            _v.Page.Reload = true;

            _v.Page.Breadcrumb = new List<Breadcrumb> {
                new() {Label="العمل الاضافي", Url=$"/OverTime/Department"},
            };

            _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
            _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

            if (from != null)
            {

                _v.Page.Filter.DateFrom = from;
            }

            if (to != null)
            {

                to = to.Value.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }

            // Get grouped data with proper null handling
            var reportData = _db.OvertimeReqMas
                 .Where(r =>
                  r.TimeStamp >= _v.Page.Filter.DateFrom && r.TimeStamp <= _v.Page.Filter.DateTo &&
                  r.EmpNo > 0 &&
        
                  r.ReqStatus > 39 &&
                  !string.IsNullOrEmpty(r.UserId)) // Filter out null/empty UserIds
                 .GroupBy(g => g.UserId)
                 .Select(ro => new
                 {
                     UserId = ro.Key,
                     Count = ro.Count(),
                 })
                 .OrderByDescending(o => o.Count)
                 .ToList();

            // Process results with proper error handling
            var report = new List<dynamic>();
            foreach (var item in reportData)
            {
                if (int.TryParse(item.UserId, out int empNo))
                {
                    try
                    {
                        var employee = _h.StaffData(empNo);
                        report.Add(new
                        {
                            Employee = employee,
                            Count = item.Count
                        });
                    }
                    catch
                    {
                        // Skip if employee data cannot be retrieved
                        continue;
                    }
                }
            }

            var reportList = new List<_OvrtimeDepartmentReport>();

            foreach (var item in report)
            {
                reportList.Add(new _OvrtimeDepartmentReport
                {
                    Employee = item.Employee,
                    Count = item.Count

                });
            }

            _v.OvrtimeDepartmentReport = reportList;

            _v.VempDtls = _db.VempDtls.ToList();

            return View(_v);
        }


        [Can("overtime-department")]
        [HttpPost("Datatable")]
        public IActionResult DepartmentRequestsDatatable([FromForm] DataTableHelper datatable, DateTime? from, DateTime? to, string actionFilter = "all")
        {

            _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
            _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

            var fromQueryString = HttpContext.Request.Query["from"].ToString();
            var toQueryString = HttpContext.Request.Query["to"].ToString();

            // Get action filter from query string if not provided in form
            if (string.IsNullOrEmpty(actionFilter))
            {
                actionFilter = HttpContext.Request.Query["actionFilter"].ToString();
                if (string.IsNullOrEmpty(actionFilter))
                    actionFilter = "all";
            }

            if (from != null)
            {

                _v.Page.Filter.DateFrom = from;
            }

            if (to != null)
            {

                to = to.Value.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }

            var query = _db.OvertimeReqMas.Where(
                    r =>
                    r.EmpNo > 0
                    && (r.SignAuthCode > 0 || r.SignAuthCode != null)
                    && r.TimeStamp >= _v.Page.Filter.DateFrom
                    && r.TimeStamp <= _v.Page.Filter.DateTo

                    );

            // Apply action filter based on request status
            if (actionFilter == "needs-action")
            {
                // Requests that need action (pending, submitted, etc.)
                query = query.Where(r => r.ReqStatus == 20); // Adjust threshold as needed
            }



            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                query = query.Where(f =>
                f.EmpNo.ToString().Contains(datatable.Search.Value)

                );
            }

            var total = query.Count();

            var empReq = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(
              query.ToList()
                );


            List<UnifiOvertimeReqMas> allReq = empReq;

            allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).ToList();

            var data = allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();

            switch (datatable.Order[0].Column)
            {

                case 1:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 2:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 3:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 4:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EstimatedFlag).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EstimatedFlag).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;


                case 5:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.ReqStatus).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.ReqStatus).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                default:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }

                    break;
            }

            var table = data.Select(ro => new
            {


                id = "<a href='/OverTime/Department/View/" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "-" + ro.Rel + "'  >" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "</a>",
                emp = "#" + ro.EmpNo + "<br>" + _h.StaffData(ro.EmpNo).EmpNameA,
                createdAt = ro.OrderYear.Value + "-" + ro.OrderMonth.Value,

                extraSum = _h.FormatHour(ro.ExtraSum),
                //extraAmountSum = _v._amount(ro.ExtraAmountSum.Value),
                estematedFlag = ro.EstimatedFlag == 1 ? "<span class='badge badge-warning rounded-pill px-2'>تقديري</span>" : "فعلي",

                status = _v.renderSatus(ro.ReqStatus, new int[] { 20 }),

            }).ToList();


            var output = new
            {
                datatable.Draw,
                recordsTotal = total,
                recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
                data = table
            };


            return Json(output);

        }

        [HttpGet("View/{Id}")]
        public IActionResult DepartmentRequestView(string Id)
        {
            if (!Can("overtime-department"))
                return StatusCode(403);

            // Parse request ID
            string[] rawcode = Id.Split("-");
            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];

            // Get overtime request
            var requestEmp = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .FirstOrDefault();

            if (requestEmp == null)
                return StatusCode(404);

            // Map request data
            _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

            // Get overtime details
            _v.OvertimeReqDtls = _db.OvertimeReqDtls
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .OrderBy(o => o.OtDate)
                .ToList();
            _v.UniOvertimeReqDtls = AutoMapper.MapList<OvertimeReqDtls, UnifiOvertimeReqDtls>(_v.OvertimeReqDtls);

            // Get comments
            _v.OvertimeComments = _db.OvertimeComments
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .OrderBy(o => o.TimeStamp)
                .ToList();

            // Calculate statistics based on employee type
            if (_v.UniOvertimeReqMas.EmpNo >= 1000)
            {
                var empEarn = _db.ConPayrollDtls
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnDepCode == 100)
                    .OrderByDescending(ro => ro.EndDate)
                    .FirstOrDefault();

                ViewBag.MaxEmpEarnAmt = empEarn?.Amount / 2 ?? 0;
                ViewBag.ExtraAmountSum = _db.ConOvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1)
                    .Sum(max => max.ExtraAmountSum);
                ViewBag.ExtraSum = _db.ConOvertimeReqMas
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1)
                    .Sum(max => max.ExtraSum);
                ViewBag.RequestCount = _db.ConOvertimeReqMas
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.SignAuthCode > 0 && ro.CancelFlag == 0 && ro.AuditStat == 1)
                    .Count();
            }
            else
            {
                var empEarn = _db.EmpEarnings
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null))
                    .FirstOrDefault();

                ViewBag.MaxEmpEarnAmt = empEarn?.EarnAmt / 2 ?? 0;
                ViewBag.ExtraAmountSum = _db.OvertimeReqMas
                    .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1)
                    .Sum(max => max.ExtraAmountSum);
                ViewBag.ExtraSum = _db.OvertimeReqMas
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1)
                    .Sum(max => max.ExtraSum);
                ViewBag.RequestCount = _db.OvertimeReqMas
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.SignAuthCode > 0 && ro.CancelFlag == 0 && ro.AuditStat == 1)
                    .Count();
            }

            // Set page properties
            var staff = _h.StaffData(_v.UniOvertimeReqMas.EmpNo);
            ViewBag.CanApprove = _v.UniOvertimeReqMas.ReqStatus == 20;
            ViewBag.CanDelete = _v.UniOvertimeReqMas.ReqStatus == 20;
            ViewBag.CanUpdate = _v.UniOvertimeReqMas.ReqStatus == 20;

            _v.Page.Title = "طلب العمل الاضافي";
            _v.Page.Reload = true;
            _v.Page.Active = "hr_overtime";
            _v.Page.Back = "/OverTime/Department";
            _v.Page.Breadcrumb = new List<Breadcrumb> {
            new() { Label = "العمل الاضافي", Url = "/OverTime/Department" },
            new() { Label = staff.EmpNameA }
        };

            return View(_v);
        }

        [HttpPost("Approve/{Id}")]
        public async Task<IActionResult> DepartmentRequestApprove(string Id, [FromForm] IFormCollection post)
        {

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (!Can("overtime-department"))
                return StatusCode(403);

            int EmpNo = 0;

            if (Rel == "Emp")
            {

                var requestEmp = _db.OvertimeReqMas
               .Where(ro =>
                   ro.InYear == InYear
                   && ro.InMailNo == InMailNo
                   && ro.InDocSlNo == InDocSlNo
                   && ro.CancelFlag == 0
                   && ro.ReqStatus == 20
               //&& ro.ManagerNo == _v.Profile.EmpNo.Value
               )
               .First();

                EmpNo = requestEmp.EmpNo.Value;

                if (requestEmp == null)
                    return StatusCode(404);

                requestEmp.OtStatus = "Approved by leaves department";
                requestEmp.UserId = _v.Profile.EmpNo.ToString();
                //requestEmp.UserId = "213";
                requestEmp.ReqStatus = 30;

                _db.Update(requestEmp);
                _db.SaveChanges();

                await _h.Notify().Create(
                    empNo: requestEmp.EmpNo.Value,
                    title: "العمل الاضافي",
                    text: "اعتماد من قسم الاجازات وملفات الخدمة",
                    url: "/Overtime/My"
                );

            }

            if (post.ContainsKey("comment"))
            {
                var newComment = new OvertimeComment
                {
                    InYear = InYear,
                    InMailNo = InMailNo,
                    InDocSlNo = InDocSlNo,
                    Comment = post["comment"],
                    EmpNo = EmpNo,
                    CreatedBy = _v.Profile.EmpNo.Value
                };

                _db.OvertimeComments.Add(newComment);
                _db.SaveChanges();
            }


            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "موافقة قسم الاجازات"
            );

            return Json(new
            {
                success = true,
                message = new List<string> { _("تم الموافقة") },
                action = "reload",
            });

        }


        [HttpPost("Decline/{Id}")]
        public async Task<IActionResult> DepartmentRequestDecline(string Id, [FromForm] IFormCollection post)
        {

            if (!Can("overtime-department"))
                return StatusCode(403);


            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];



            if (Rel == "Con")
            {


            }

            if (Rel == "Emp")
            {

                var requestEmp = _db.OvertimeReqMas
               .Where(ro =>
                   ro.InYear == InYear
                   && ro.InMailNo == InMailNo
                   && ro.InDocSlNo == InDocSlNo
                   && ro.CancelFlag == 0
               //&& ro.ManagerNo == _v.Profile.EmpNo.Value
               )
               .First();

                if (requestEmp == null)
                    return StatusCode(404);

                requestEmp.OtStatus = "Declined by overtime department";
                //request.SignAuthCode = _v.Profile.EmpNo.Value;
                requestEmp.UserId = _v.Profile.EmpNo.ToString();
                requestEmp.CancelFlag = 1;
                requestEmp.ReqStatus = 31;
                requestEmp.SignRem = post["Note"];


                _db.Update(requestEmp);
                _db.SaveChanges();

                await _h.Notify().Create(
                    empNo: requestEmp.EmpNo.Value,
                    title: "العمل الاضافي",
                    text: "رفض من قسم الاجازات وملفات الخدمة",
                    url: "/Overtime/My"
                );

            }

            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "رفض قسم الاجازات"
            );

            return Json(new
            {
                success = true,
                message = new List<string> { _("تم الرفض") },
                action = "reload",
            });

        }


        [HttpPost("Create")]
        public async Task<IActionResult> DepartmentRequestCreate([FromForm] IFormCollection post)
        {

            if (!Can("overtime-department"))
                return StatusCode(403);

            if (!IsValid(ModelState))
            {
                var response = new
                {
                    success = false,
                    message = ValidateErrors(ModelState),
                    action = "",
                };

                return Json(response);
            }

            if (!post.ContainsKey("EmpNo"))
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Invalid employee") },
                    action = "",
                });
            }

            if (!post.ContainsKey("ManagerNo"))
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Invalid department manager") },
                    action = "",
                });
            }

            int EmpNo = int.Parse(post["EmpNo"]);


            // Validate the manager is allowed
            var Managers = _h.Managers(EmpNo);

            bool ValidManager = Managers.Any(item => item.EmpNo == int.Parse(post["ManagerNo"]));

            if (!ValidManager && EmpNo != 91 && EmpNo != 384 && EmpNo != 382 && EmpNo != 387 && EmpNo != 103)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Invalid department manager") },
                    action = "",
                });
            }

            // Format list of days
            var selectedDays = new List<DateTime>();

            if (post.ContainsKey("days"))
            {
                foreach (var dayString in post["days"])
                {
                    if (DateTime.TryParse(dayString, out DateTime day))
                    {
                        selectedDays.Add(day);
                    }
                }
            }

            if (selectedDays.Count == 0)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Please select days") },
                    action = "",
                });
            }

            bool daysCheck = true;

            var Exclude = _db.OvertimeReqDtls
                .Where(ro => ro.EmpNo == EmpNo)
                .Select(d => d.OtDate)
                .ToList();

            var Exclude2 = _db.OvertimeReqPreDtls
                .Where(ro => ro.EmpNo == EmpNo)
                .Select(d => d.OtDate)
                .ToList();

            if (EmpNo > 1000)
            {
                Exclude = _db.ConOvertimeReqDtls
                .Where(ro => ro.EmpNo == EmpNo)
               .Select(d => d.OtDate)
               .ToList();

                Exclude2 = _db.ConOvertimeReqPreDtls
                 .Where(ro => ro.EmpNo == EmpNo)
                .Select(d => d.OtDate)
                .ToList();
            }

            // Validate selected days
            foreach (var day in selectedDays)
            {
                var check = _db.EmpWorkHours
                .Where(ro => ro.EmpNo == EmpNo && ro.Extra >= 1 && ro.Day == day && !Exclude.Contains(ro.Day) && !Exclude2.Contains(ro.Day))
                .FirstOrDefault();

                if (check == null)
                {
                    daysCheck = false;
                }
            }

            if (!daysCheck)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Invalid overtime day") },
                    action = "",
                });
            }

            // Validate all days in the same month
            int refYear = selectedDays[0].Year;
            int refYear2Dig = selectedDays[0].Year - 2000;
            int refMonth = selectedDays[0].Month;

            bool isAllInSameMonth = selectedDays.All(date => date.Year == refYear && date.Month == refMonth);

            if (!isAllInSameMonth)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("All days has to be in the same month") },
                    action = "",
                });
            }

            // Handle file upload if provided
            string fileGuid = null;
            if (post.Files.Count > 0 && post.Files["attachment"] != null)
            {
                var file = post.Files["attachment"];
                if (file.Length > 0)
                {
                    try
                    {
                        fileGuid = await _h.UploadAsync(file, EmpNo, "Overtimes");
                    }
                    catch (Exception ex)
                    {
                        return Json(new
                        {
                            success = false,
                            message = new List<string> { _("File upload failed: ") + ex.Message },
                            action = "",
                        });
                    }
                }
            }

            TincomingMail tincoming = _h.Mail().Create(97);

            if (EmpNo > 1000)
            {


            }

            if (EmpNo < 1000)
            {
                var eNewRequest = new OvertimeReqMas
                {
                    InYear = tincoming.InYear,
                    InDocSlNo = (int)tincoming.LastDocSlNo,
                    InDeptInd = 1,
                    InMailNo = tincoming.InMailNo,

                    EmpNo = EmpNo,
                    ManagerNo = int.Parse(post["ManagerNo"]),
                    OrderMonth = refMonth,
                    OrderYear = refYear2Dig,
                    OtStatus = "New",
                    SignAuthCode = int.Parse(post["ManagerNo"]),
                    SignDate = DateTime.Now,
                    ReqStatus = 20,
                    FileGuid = fileGuid

                };

                _db.OvertimeReqMas.Add(eNewRequest);

                await _db.SaveChangesAsync();

                List<EmpWorkHours> EmpWorkHours = _db.EmpWorkHours
                    .Where(ro => ro.EmpNo == EmpNo && ro.Extra >= 1 && selectedDays.Contains(ro.Day))
                    .ToList();

                eNewRequest = _db.OvertimeReqMas
                    .Where(ro => ro.InYear == tincoming.InYear && ro.InDocSlNo == (int)tincoming.LastDocSlNo && ro.InMailNo == tincoming.InMailNo)
                    .FirstOrDefault();



                foreach (var day in EmpWorkHours)
                {

                    var Rate = _v.GetOverTimeHourPrice(EmpNo, day.Day);

                    var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == EmpNo && ro.EarnCode == 1 && ro.ToDate == null).OrderByDescending(o => o.ToDate).FirstOrDefault();

                    int HolydayFlag = _h.IsHoliday(day.Day) ? 1 : 0;

                    var NewOvertimeReqPreDtls = new OvertimeReqPreDtls
                    {
                        InYear = eNewRequest.InYear,
                        InDocSlNo = eNewRequest.InDocSlNo.Value,
                        InDeptInd = 1,
                        InMailNo = eNewRequest.InMailNo,

                        EmpNo = EmpNo,
                        HolydayFlag = HolydayFlag,
                        OtDuration = day.Extra,
                        OtDate = day.Day,
                        OtRate = Rate,
                        ComputedAmount = Rate * day.Extra,

                        BasicAtm = EmpEarn.EarnAmt.Value,
                    };

                    _db.OvertimeReqPreDtls.Add(NewOvertimeReqPreDtls);

                    await _db.SaveChangesAsync();
                }

                var Days = _db.OvertimeReqPreDtls
                    .Where(ro =>
                    ro.InYear == eNewRequest.InYear
                    && ro.InMailNo == eNewRequest.InMailNo
                    && ro.InDocSlNo == eNewRequest.InDocSlNo)
                    .ToList();

                foreach (var pre in Days)
                {

                    int HolydayFlag = _h.IsHoliday(pre.OtDate) ? 1 : 0;

                    var NewOvertimeReqDtls = new OvertimeReqDtls
                    {
                        InYear = pre.InYear,
                        InDocSlNo = pre.InDocSlNo,
                        InDeptInd = 1,
                        InMailNo = pre.InMailNo,

                        EmpNo = pre.EmpNo,
                        HolydayFlag = HolydayFlag,
                        OtDuration = pre.OtDuration,
                        OtDate = pre.OtDate,
                        OtRate = pre.OtRate,
                        ComputedAmount = pre.ComputedAmount,

                        BasicAtm = pre.BasicAtm,

                    };

                    _db.OvertimeReqDtls.Add(NewOvertimeReqDtls);

                    await _db.SaveChangesAsync();
                }


                var RequestToUpdate = _db.OvertimeReqMas
                    .Where(ro =>
                    ro.InYear == eNewRequest.InYear &&
                    ro.InMailNo == eNewRequest.InMailNo &&
                    ro.InDocSlNo == eNewRequest.InDocSlNo &&
                    ro.EmpNo == eNewRequest.EmpNo).FirstOrDefault();

                RequestToUpdate.ExtraSum = _db.OvertimeReqDtls
                                                .Where(ro =>
                                                ro.InYear == eNewRequest.InYear &&
                                                ro.InMailNo == eNewRequest.InMailNo &&
                                                ro.InDocSlNo == eNewRequest.InDocSlNo &&
                                                ro.EmpNo == eNewRequest.EmpNo)
                                                .Sum(sum => sum.OtDuration);

                RequestToUpdate.ExtraAmountSum = _db.OvertimeReqDtls
                                                    .Where(ro =>
                                                    ro.InYear == eNewRequest.InYear &&
                                                    ro.InMailNo == eNewRequest.InMailNo &&
                                                    ro.InDocSlNo == eNewRequest.InDocSlNo &&
                                                    ro.EmpNo == eNewRequest.EmpNo)
                                                    .Sum(sum => sum.ComputedAmount);

                _db.OvertimeReqMas.Update(RequestToUpdate);

                await _db.SaveChangesAsync();

                await _h.Notify().Create(
                    empNo: RequestToUpdate.EmpNo.Value,
                    title: "العمل الاضافي",
                    text: "عمل اضافي جديد",
                    url: "/Overtime/My"
                );

                Log(
                    eNewRequest.InYear + "-" + eNewRequest.InMailNo + "-" + eNewRequest.InDocSlNo,
                    "Overtime",
                    "انشاء بواسطة قسم الاجازات"
                );
            }

            return Json(new
            {
                success = true,
                message = new List<string> { _("Request created successfully") },
                action = "reload",
            });
        }


        [HttpPost("CreateEtimated")]
        public async Task<IActionResult> DepartmentRequestCreateEtimated([FromForm] IFormCollection post)
        {

            if (!Can("overtime-department"))
                return StatusCode(403);

            if (!IsValid(ModelState))
            {
                var response = new
                {
                    success = false,
                    message = ValidateErrors(ModelState),
                    action = "",
                };

                return Json(response);
            }

            if (!post.ContainsKey("EmpNo"))
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Invalid employee") },
                    action = "",
                });
            }

            if (!post.ContainsKey("ManagerNo"))
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Invalid department manager") },
                    action = "",
                });
            }

            int EmpNo = int.Parse(post["EmpNo"]);


            // Validate the manager is allowed
            var Managers = _h.Managers(EmpNo);

            bool ValidManager = Managers.Any(item => item.EmpNo == int.Parse(post["ManagerNo"]));

            if (!ValidManager && EmpNo != 91 && EmpNo != 384 && EmpNo != 382 && EmpNo != 387 && EmpNo != 103)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("Invalid department manager") },
                    action = "",
                });
            }



            // Format list of days
            var overtimes = new List<KeyValuePair<DateTime, float>>();



            for (int i = 0; post.ContainsKey($"overtime[day][{i}]"); i++)
            {

                var dateString = post[$"overtime[day][{i}]"];
                var hoursString = post[$"overtime[hours][{i}]"];


                if (DateTime.TryParse(dateString, out DateTime day) && float.TryParse(hoursString, out float hours))
                {
                    if (hours > 0)
                    {
                        overtimes.Add(new KeyValuePair<DateTime, float>(day, hours));
                    }

                }
            }


            if (overtimes.Count == 0)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("يرجى اضافة ساعات") },
                    action = "",
                });
            }


            // Validate selected days
            foreach (var overtimeDay in overtimes)
            {

                var day = overtimeDay.Key;

                var Exclude = _db.OvertimeReqDtls
                .Where(ro => ro.EmpNo == EmpNo && ro.OtDate == day)
                .Select(d => d.OtDate)
                .ToList();

                var Exclude2 = _db.OvertimeReqPreDtls
                    .Where(ro => ro.EmpNo == EmpNo && ro.OtDate == day)
                    .Select(d => d.OtDate)
                    .ToList();

                if (EmpNo > 1000)
                {
                    Exclude = _db.ConOvertimeReqDtls
                    .Where(ro => ro.EmpNo == EmpNo && ro.OtDate == day)
                   .Select(d => d.OtDate)
                   .ToList();

                    Exclude2 = _db.ConOvertimeReqPreDtls
                     .Where(ro => ro.EmpNo == EmpNo && ro.OtDate == day)
                    .Select(d => d.OtDate)
                    .ToList();
                }

                if (Exclude.Count != 0 || Exclude2.Count != 0)
                {
                    return Json(new
                    {
                        success = false,
                        message = new List<string> { _("Invalid overtime day") },
                        action = "",
                    });
                }

            }


            int refYear = overtimes[0].Key.Year;
            int refYear2Dig = overtimes[0].Key.Year - 2000;
            int refMonth = overtimes[0].Key.Month;

            bool isAllInSameMonth = overtimes.All(overtime => overtime.Key.Year == refYear && overtime.Key.Month == refMonth);

            if (!isAllInSameMonth)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("يجب ان يكون الايام في نفس الشهر") },
                    action = "",
                });
            }

            // Handle file upload if provided
            string fileGuid = null;
            if (post.Files.Count > 0 && post.Files["attachment"] != null)
            {
                var file = post.Files["attachment"];
                if (file.Length > 0)
                {
                    try
                    {
                        fileGuid = await _h.UploadAsync(file, EmpNo, "Overtimes");
                    }
                    catch (Exception ex)
                    {
                        return Json(new
                        {
                            success = false,
                            message = new List<string> { _("File upload failed: ") + ex.Message },
                            action = "",
                        });
                    }
                }
            }

            TincomingMail tincoming = _h.Mail().Create(97);

            if (EmpNo > 1000)
            {


            }

            if (EmpNo < 1000)
            {
                var eNewRequest = new OvertimeReqMas
                {
                    InYear = tincoming.InYear,
                    InDocSlNo = (int)tincoming.LastDocSlNo,
                    InDeptInd = 1,
                    InMailNo = tincoming.InMailNo,

                    EmpNo = EmpNo,
                    ManagerNo = int.Parse(post["ManagerNo"]),
                    OrderMonth = refMonth,
                    OrderYear = refYear2Dig,
                    OtStatus = "New",
                    SignAuthCode = int.Parse(post["ManagerNo"]),
                    SignDate = DateTime.Now,
                    ReqStatus = 20,
                    EstimatedFlag = 1,
                    FileGuid = fileGuid

                };

                _db.OvertimeReqMas.Add(eNewRequest);

                await _db.SaveChangesAsync();

                //List<EmpWorkHours> EmpWorkHours = _db.EmpWorkHours
                //    .Where(ro => ro.EmpNo == EmpNo && ro.Extra >= 1 && selectedDays.Contains(ro.Day))
                //    .ToList();

                eNewRequest = _db.OvertimeReqMas
                    .Where(ro => ro.InYear == tincoming.InYear && ro.InDocSlNo == (int)tincoming.LastDocSlNo && ro.InMailNo == tincoming.InMailNo)
                    .FirstOrDefault();

                foreach (var overtime in overtimes)
                {

                    var Rate = _v.GetOverTimeHourPrice(EmpNo, overtime.Key);

                    var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == EmpNo && ro.EarnCode == 1 && ro.ToDate == null).OrderByDescending(o => o.ToDate).FirstOrDefault();

                    int HolydayFlag = _h.IsHoliday(overtime.Key) ? 1 : 0;

                    var NewOvertimeReqPreDtls = new OvertimeReqPreDtls
                    {
                        InYear = eNewRequest.InYear,
                        InDocSlNo = eNewRequest.InDocSlNo.Value,
                        InDeptInd = 1,
                        InMailNo = eNewRequest.InMailNo,

                        EmpNo = EmpNo,
                        HolydayFlag = HolydayFlag,
                        OtDuration = overtime.Value,
                        OtDate = overtime.Key,
                        OtRate = Rate,
                        ComputedAmount = Rate * overtime.Value,

                        BasicAtm = EmpEarn.EarnAmt.Value,
                    };

                    _db.OvertimeReqPreDtls.Add(NewOvertimeReqPreDtls);

                    await _db.SaveChangesAsync();
                }

                var Days = _db.OvertimeReqPreDtls
                    .Where(ro =>
                    ro.InYear == eNewRequest.InYear
                    && ro.InMailNo == eNewRequest.InMailNo
                    && ro.InDocSlNo == eNewRequest.InDocSlNo)
                    .ToList();

                foreach (var pre in Days)
                {

                    int HolydayFlag = _h.IsHoliday(pre.OtDate) ? 1 : 0;

                    var NewOvertimeReqDtls = new OvertimeReqDtls
                    {
                        InYear = pre.InYear,
                        InDocSlNo = pre.InDocSlNo,
                        InDeptInd = 1,
                        InMailNo = pre.InMailNo,

                        EmpNo = pre.EmpNo,
                        HolydayFlag = HolydayFlag,
                        OtDuration = pre.OtDuration,
                        OtDate = pre.OtDate,
                        OtRate = pre.OtRate,
                        ComputedAmount = pre.ComputedAmount,

                        BasicAtm = pre.BasicAtm,

                    };

                    _db.OvertimeReqDtls.Add(NewOvertimeReqDtls);

                    await _db.SaveChangesAsync();
                }


                var RequestToUpdate = _db.OvertimeReqMas
                    .Where(ro =>
                    ro.InYear == eNewRequest.InYear &&
                    ro.InMailNo == eNewRequest.InMailNo &&
                    ro.InDocSlNo == eNewRequest.InDocSlNo &&
                    ro.EmpNo == eNewRequest.EmpNo).FirstOrDefault();

                RequestToUpdate.ExtraSum = _db.OvertimeReqDtls
                                                .Where(ro =>
                                                ro.InYear == eNewRequest.InYear &&
                                                ro.InMailNo == eNewRequest.InMailNo &&
                                                ro.InDocSlNo == eNewRequest.InDocSlNo &&
                                                ro.EmpNo == eNewRequest.EmpNo)
                                                .Sum(sum => sum.OtDuration);

                RequestToUpdate.ExtraAmountSum = _db.OvertimeReqDtls
                                                    .Where(ro =>
                                                    ro.InYear == eNewRequest.InYear &&
                                                    ro.InMailNo == eNewRequest.InMailNo &&
                                                    ro.InDocSlNo == eNewRequest.InDocSlNo &&
                                                    ro.EmpNo == eNewRequest.EmpNo)
                                                    .Sum(sum => sum.ComputedAmount);

                _db.OvertimeReqMas.Update(RequestToUpdate);

                await _db.SaveChangesAsync();

                await _h.Notify().Create(
                    empNo: RequestToUpdate.EmpNo.Value,
                    title: "العمل الاضافي",
                    text: "عمل اضافي جديد",
                    url: "/Overtime/My"
                );


                Log(
                    eNewRequest.InYear + "-" + eNewRequest.InMailNo + "-" + eNewRequest.InDocSlNo,
                    "Overtime",
                    "انشاء بواسطة قسم الاجازات"
                );
            }

            return Json(new
            {
                success = true,
                message = new List<string> { _("Request created successfully") },
                action = "reload",
            });
        }



        [HttpGet("Delete/{Id}")]
        public IActionResult DepartmentRequestDelete(string Id)
        {

            if (!Can("overtime-department"))
                return StatusCode(403);

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (Rel == "Emp")
            {
                var request = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && (ro.HrManagerSign == 0))
                .First();

                if (request == null)
                    return StatusCode(404);

                var days = _db.OvertimeReqDtls
                    .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                    .ToList();

                _db.OvertimeReqDtls.RemoveRange(days);

                _db.SaveChanges();

                var daysPre = _db.OvertimeReqPreDtls
                    .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                    .ToList();

                _db.OvertimeReqPreDtls.RemoveRange(daysPre);

                _db.SaveChanges();

                _db.OvertimeReqMas.Remove(request);

                _db.SaveChanges();
            }

            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "حذف من قبل فسم الاجازات"
            );


            return Content("<script>window.location.href='/OverTime/Department/?success=Action completed';</script>", "text/html");

        }

        [HttpPost("UpdateHours/{Id}")]
        public async Task<IActionResult> DepartmentUpdateHours(string Id, [FromForm] IFormCollection post)
        {
            if (!Can("overtime-department"))
                return StatusCode(403);

            string[] rawcode = Id.Split("-");
            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);

            // Check if request can be updated (not approved by department yet)
            var request = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo
                    && ro.CancelFlag == 0)
                .FirstOrDefault();

            if (request == null)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("لا يمكن تحديث الطلب") },
                    action = ""
                });
            }

            bool canUpdate = request.ReqStatus == 20;

            if (!canUpdate)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("لا يمكن تحديث الطلب") },
                    action = ""
                });
            }

            try
            {
                for (int i = 0; post.ContainsKey($"updates[{i}][date]"); i++)
                {
                    var dateString = post[$"updates[{i}][date]"];
                    var hoursString = post[$"updates[{i}][hours]"];
                    var action = post[$"updates[{i}][action]"];

                    if (!DateTime.TryParse(dateString, out DateTime date) ||
                        !float.TryParse(hoursString, out float hours))
                        continue;

                    var existingDetail = _db.OvertimeReqDtls
                        .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.OtDate == date)
                        .FirstOrDefault();

                    var existingPreDetail = _db.OvertimeReqPreDtls
                        .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.OtDate == date)
                        .FirstOrDefault();

                    if (action == "delete")
                    {
                        if (existingDetail != null)
                        {
                            _db.OvertimeReqDtls.Remove(existingDetail);
                        }
                        if (existingPreDetail != null)
                        {
                            _db.OvertimeReqPreDtls.Remove(existingPreDetail);
                        }
                    }
                    else if (action == "update" && hours > 0)
                    {
                        var rate = _v.GetOverTimeHourPrice(request.EmpNo.Value, date);
                        var empEarn = _db.EmpEarnings
                            .Where(ro => ro.EmpNo == request.EmpNo && ro.EarnCode == 1 && ro.ToDate == null)
                            .OrderByDescending(o => o.ToDate)
                            .FirstOrDefault();

                        int holidayFlag = _h.IsHoliday(date) ? 1 : 0;

                        if (existingDetail != null)
                        {
                            existingDetail.OtDuration = hours;
                            existingDetail.ComputedAmount = rate * hours;
                            existingDetail.HolydayFlag = holidayFlag;
                            _db.Update(existingDetail);
                        }
                        else
                        {
                            var newDetail = new OvertimeReqDtls
                            {
                                InYear = InYear,
                                InDocSlNo = InDocSlNo,
                                InDeptInd = 1,
                                InMailNo = InMailNo,
                                EmpNo = request.EmpNo.Value,
                                HolydayFlag = holidayFlag,
                                OtDuration = hours,
                                OtDate = date,
                                OtRate = rate,
                                ComputedAmount = rate * hours,
                                BasicAtm = empEarn?.EarnAmt ?? 0
                            };
                            _db.OvertimeReqDtls.Add(newDetail);
                        }

                        if (existingPreDetail != null)
                        {
                            existingPreDetail.OtDuration = hours;
                            existingPreDetail.ComputedAmount = rate * hours;
                            existingPreDetail.HolydayFlag = holidayFlag;
                            _db.Update(existingPreDetail);
                        }
                        else
                        {
                            var newPreDetail = new OvertimeReqPreDtls
                            {
                                InYear = InYear,
                                InDocSlNo = InDocSlNo,
                                InDeptInd = 1,
                                InMailNo = InMailNo,
                                EmpNo = request.EmpNo.Value,
                                HolydayFlag = holidayFlag,
                                OtDuration = hours,
                                OtDate = date,
                                OtRate = rate,
                                ComputedAmount = rate * hours,
                                BasicAtm = empEarn?.EarnAmt ?? 0
                            };
                            _db.OvertimeReqPreDtls.Add(newPreDetail);
                        }
                    }
                }

                await _db.SaveChangesAsync();

                request.ExtraSum = _db.OvertimeReqDtls
                    .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                    .Sum(sum => sum.OtDuration);

                request.ExtraAmountSum = _db.OvertimeReqDtls
                    .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                    .Sum(sum => sum.ComputedAmount);

                _db.Update(request);
                await _db.SaveChangesAsync();

                Log(
                    InYear + "-" + InMailNo + "-" + InDocSlNo,
                    "Overtime",
                    "تحديث ساعات العمل الاضافي => " + request.ExtraSum + " ساعة"
                );

                return Json(new
                {
                    success = true,
                    message = new List<string> { _("تم تحديث الساعات") },
                    action = "reload"
                });
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("خطأ في تحديث الساعات: ") + ex.Message },
                    action = ""
                });
            }
        }



        [HttpGet("Manager")]
        public IActionResult DepartmentManagerRequests()
        {

            if (!Can("overtime-hr-manager"))
                return StatusCode(403);

            _v.Page.Class = "  ";
            _v.Page.Active = "hr_overtime";
            _v.Page.Reload = true;

            _v.Page.Breadcrumb = new List<Breadcrumb> {

                new() {Label="العمل الاضافي", Url=$"/OverTime/Department/Manager"},
            };



            _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
            _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

            var fromQueryString = HttpContext.Request.Query["from"].ToString();
            var toQueryString = HttpContext.Request.Query["to"].ToString();


            if (!string.IsNullOrEmpty(fromQueryString))
            {
                if (DateTime.TryParse(fromQueryString, out DateTime from))
                {
                    _v.Page.Filter.DateFrom = from;
                }
            }

            if (!string.IsNullOrEmpty(toQueryString))
            {
                if (DateTime.TryParse(toQueryString, out DateTime to))
                {
                    to = to.Date.Add(new TimeSpan(23, 59, 59));
                    _v.Page.Filter.DateTo = to;
                }
            }


            return View(_v);
        }

        [HttpPost("Manager/Datatable")]
        public IActionResult DepartmentManagerRequestsDatatable([FromForm] DataTableHelper datatable)
        {

            if (!Can("overtime-hr-manager"))
                return StatusCode(403);

            _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
            _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

            var fromQueryString = HttpContext.Request.Query["from"].ToString();
            var toQueryString = HttpContext.Request.Query["to"].ToString();


            if (!string.IsNullOrEmpty(fromQueryString))
            {
                if (DateTime.TryParse(fromQueryString, out DateTime from))
                {
                    _v.Page.Filter.DateFrom = from;
                }
            }

            if (!string.IsNullOrEmpty(toQueryString))
            {
                if (DateTime.TryParse(toQueryString, out DateTime to))
                {
                    to = to.Date.Add(new TimeSpan(23, 59, 59));
                    _v.Page.Filter.DateTo = to;
                }
            }


            var query = _db.OvertimeReqMas.Where(
                    r =>
                    r.EmpNo > 0
                     && r.SignAuthCode > 0
                     && r.ReqStatus == 30

                    && r.TimeStamp >= _v.Page.Filter.DateFrom
                    && r.TimeStamp <= _v.Page.Filter.DateTo
                    && r.AuditStat != 1

                    );

            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                query = query.Where(f =>
                f.EmpNo.ToString().Contains(datatable.Search.Value)

                );


            }

            var total = query.Count();

            var empReq = AutoMapper.MapList<OvertimeReqMas, UnifiOvertimeReqMas>(
              query.ToList()
                );

            List<UnifiOvertimeReqMas> allReq = empReq;

            allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).ToList();

            var data = allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();

            switch (datatable.Order[0].Column)
            {

                case 1:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 2:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.OrderYear + o.OrderMonth).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 3:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.ExtraSum).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                case 4:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EstimatedFlag).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EstimatedFlag).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;


                case 5:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.ReqStatus).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.ReqStatus).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    break;

                default:
                    if (datatable.Order[0].Dir == "asc")
                    {
                        data = allReq.OrderByDescending(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }
                    else
                    {
                        data = allReq.OrderBy(o => o.EmpNo).Skip(datatable.Start).Take(datatable.Length).ToList();
                    }

                    break;
            }

            var table = data.Select(ro => new
            {


                id = "<a href='/OverTime/Department/Manager/View/" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "-" + ro.Rel + "'  >" + ro.InYear + "-" + ro.InMailNo + "-" + ro.InDocSlNo + "</a>",
                emp = "#" + ro.EmpNo + "<br>" + _h.StaffData(ro.EmpNo).EmpNameA,
                createdAt = ro.OrderYear.Value + "-" + ro.OrderMonth.Value,

                extraSum = _h.FormatHour(ro.ExtraSum),

                estematedFlag = ro.EstimatedFlag == 1 ? "<span class='badge badge-warning rounded-pill px-2'>تقديري</span>" : "فعلي",
                //status = "<span class=\"badge  rounded-pill px-2  " + (((ro.HrManagerSign > 0 && ro.UserId!=null) || ro.CancelFlag == 1 || ro.EstimatedFlag==1) ? "" : "badge-danger") + " \"><strong class=\"\">" + _v._(ro.OtStatus) + "</strong></span>",
                status = _v.renderSatus(ro.ReqStatus, new int[] { 30 }),

            }).ToList();


            var output = new
            {
                datatable.Draw,
                recordsTotal = total,
                recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
                data = table
            };


            return Json(output);

        }


        [HttpGet("Manager/View/{Id}")]
        public IActionResult DepartmentManRequestView(string Id)
        {

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (!Can("overtime-hr-manager"))
                return StatusCode(403);

            _v.Page.Title = "طلب العمل الاضافي";
            //_v.Page.Layout = "_Popup";
            _v.Page.Active = "hr_overtime";
            _v.Page.Reload = true;
            _v.Page.Back = $"/OverTime/Department/Manager";

            if (Rel == "Con")
            {

            }

            if (Rel == "Emp")
            {
                var requestEmp = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .First();

                if (requestEmp == null)
                    return StatusCode(404);

                _v.UniOvertimeReqMas = AutoMapper.MapProperties<OvertimeReqMas, UnifiOvertimeReqMas>(requestEmp);

                _v.OvertimeReqDtls = _db.OvertimeReqDtls
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
                .OrderBy(o => o.OtDate)
                .ToList();

                _v.UniOvertimeReqDtls = AutoMapper.MapList<OvertimeReqDtls, UnifiOvertimeReqDtls>(_v.OvertimeReqDtls);
            }

            _v.Page.Breadcrumb = new List<Breadcrumb> {


             new() {Label="العمل الاضافي", Url=$"/OverTime/Department/Manager"},
             new() {Label=_v._h.StaffData(_v.UniOvertimeReqMas.EmpNo).EmpNameA, Url=$"/OverTime/"},
        };


            if (_v.UniOvertimeReqMas.EmpNo >= 1000)
            {


            }
            else
            {
                var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.EarnCode == 1 && ro.FromDate < _v.UniOvertimeReqMas.OrderDate && (ro.ToDate > _v.UniOvertimeReqMas.OrderDate || ro.ToDate == null)).FirstOrDefault();
                ViewBag.MaxEmpEarnAmt = EmpEarn.EarnAmt / 2;

                ViewBag.ExtraAmountSum = _db.OvertimeReqMas
                .Where(ro => ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1)
                .Sum(max => max.ExtraAmountSum);

                ViewBag.ExtraSum = _db.OvertimeReqMas
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.AuditStat == 1)
                    .Sum(max => max.ExtraSum);

                ViewBag.RequestCount = _db.OvertimeReqMas
                    .Where(ro => ro.EmpNo == _v.UniOvertimeReqMas.EmpNo && ro.OrderYear == _v.UniOvertimeReqMas.OrderYear && ro.OrderMonth == _v.UniOvertimeReqMas.OrderMonth && ro.SignAuthCode > 0 && ro.CancelFlag == 0 && ro.AuditStat == 1)
                    .Count();

            }

            _v.OvertimeComments = _db.OvertimeComments
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.EmpNo == _v.UniOvertimeReqMas.EmpNo)
                .OrderBy(o => o.TimeStamp)
                .ToList();


            return View(_v);
        }

        [HttpGet("Manager/Approve/{Id}")]
        public IActionResult DepartmentManRequestApprove(string Id)
        {

            if (!Can("overtime-hr-manager"))
                return StatusCode(403);

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (Rel == "Con")
            {


            }

            if (Rel == "Emp")
            {

                var requestEmp = _db.OvertimeReqMas
               .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0 && ro.EstimatedFlag != 1 && ro.HrManagerSign == 0)
               .First();

                if (requestEmp == null)
                    return StatusCode(404);

                ProccessDgApproval(Id, 40);
            }

            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "اعتماد مدير دائرة الموارد البشرية"
            );

            return Content("<script>window.location.href='/OverTime/Department/Manager?success=" + _("تم الاعتماد") + "';</script>", "text/html");
        }

        [HttpPost("Manager/Decline/{Id}")]
        public IActionResult DepartmentManRequestDecline(string Id, [FromForm] IFormCollection post)
        {

            if (!Can("overtime-hr-manager"))
                return StatusCode(403);

            string[] rawcode = Id.Split("-");

            int InYear = int.Parse(rawcode[0]);
            int InMailNo = int.Parse(rawcode[1]);
            int InDocSlNo = int.Parse(rawcode[2]);
            string Rel = rawcode[3];


            if (Rel == "Con")
            {


            }

            if (Rel == "Emp")
            {
                var requestEmp = _db.OvertimeReqMas
                .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.CancelFlag == 0)
                .First();

                if (requestEmp == null)
                    return StatusCode(404);

                requestEmp.OtStatus = "Declined by HR manager";
                //request.SignAuthCode = _v.Profile.EmpNo.Value;
                requestEmp.CancelFlag = 1;
                requestEmp.ReqStatus = 41;
                requestEmp.SignRem = post["Note"];


                _db.Update(requestEmp);
                _db.SaveChanges();

            }

            Log(
                InYear + "-" + InMailNo + "-" + InDocSlNo,
                "Overtime",
                "رفض مدير دائرة الموارد البشرية"
            );

            return Json(new
            {
                success = true,
                message = new List<string> { _("Request updated successfully") },
                action = "reload",
            });

        }

        [HttpPost("Manager/Return/{Id}")]
        public async Task<IActionResult> DepartmentManRequestReturn(string Id, [FromForm] IFormCollection post)
        {
            return await ProcessReturnRequest(
                Id: Id,
                post: post,
                permission: "overtime-hr-manager",
                allowedStatuses: new int[] { 30 },
                sendNotification: true,
                redirectUrl: "/OverTime/Department/Manager/",
                logMessage: "ارجاع عمل اضافي",
                responseMessage: "تم الارجاع"
            );
        }



    }
}
