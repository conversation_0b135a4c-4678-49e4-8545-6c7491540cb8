﻿using HumanResource.Modules.Overtime.Models.Entities;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.Unifi;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Overtime.ViewModels;
public class OverTimeViewModel : BaseViewModel
{

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
    
    // Cache for payment status to avoid repeated database calls
    private static readonly Dictionary<string, int> _paymentStatusCache = new Dictionary<string, int>();
    private static DateTime _cacheLastCleared = DateTime.Now;
    public OverTimeViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _db = context;


    }

    public List<_OvrtimeDepartmentReport> OvrtimeDepartmentReport { get; set; }

    public EmpWorkHours EmpWorkHour { get; set; }
    public List<EmpWorkHours> EmpWorkHours { get; set; }



    public OvertimeReqMas OvertimeReqMas { get; set; }
    public ConOvertimeReqMas ConOvertimeReqMas { get; set; }
    
    public TEmpEarning EmpEarning { get; set; }
    public List<OvertimeReqMas> OvertimeReqMass { get; set; }
    public List<ConOvertimeReqMas> ConOvertimeReqMass { get; set; }
    public List<OvertimeReqDtls> OvertimeReqDtls { get; set; }
    public List<OvertimeReqPreDtls> OvertimeReqPreDtls { get; set; }
    public OvertimeReqDtls OvertimeReqDtl { get; set; }
    public OvertimeReqPreDtls OvertimeReqPreDtl { get; set; }
    public List<ConOvertimeReqDtls> ConOvertimeReqDtls { get; set; }
    public List<ConOvertimeReqPreDtls> ConOvertimeReqPreDtls { get; set; }
    public ConOvertimeReqDtls ConOvertimeReqDtl { get; set; }
    public ConOvertimeReqPreDtls ConOvertimeReqPreDtl { get; set; }

    public UnifiOvertimeReqDtls UniOvertimeReqDtl { get; set; }
    public List<UnifiOvertimeReqDtls> UniOvertimeReqDtls { get; set; }

    public UnifiOvertimeReqPreDtls UniOvertimeReqPreDtl { get; set; }
    public List<UnifiOvertimeReqPreDtls> UniOvertimeReqPreDtls { get; set; }

    public UnifiOvertimeReqMas UniOvertimeReqMas { get; set; }
    public List<UnifiOvertimeReqMas> UniOvertimeReqMass { get; set; }
    public List<OvertimeComment> OvertimeComments { get; set; }
    public OvertimeComment OvertimeComment { get; set; }

    public List<OvertimePay> OvertimePays { get; set; }


    public string renderOvertimeTimeline(string Id)
    {
        string[] rawcode = Id.Split("-");


        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];


        if (Rel == "Emp")
        {
            var requestEmp = _db.OvertimeReqMas
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo)
            .First();


            if (requestEmp == null)
                return "d";


            

            var html = "<div class='d-flex align-content-center align-items-center'>  <span class='badge badge-success rounded-pill px-2 mx-2'>عمل اضافي</span> - ";

            
            if (requestEmp.ReqStatus > 29 || requestEmp.ReqStatus == 20)
                html += "  <span class='badge  border-success border rounded-pill px-2 mx-2'> المسؤول المباشر </span>  - ";
            else if(requestEmp.ReqStatus == 21)
                html += " <i class='fa fa-times text-danger mx-2'></i> - <span class='badge border-danger border rounded-pill px-2 mx-2'> المسؤول المباشر </span> - ";
            else
                html += " <i class='fa fa-spinner mx-2'></i> - <span class='badge border rounded-pill px-2 mx-2'> المسؤول المباشر </span> - ";



            if (requestEmp.ReqStatus > 39 || requestEmp.ReqStatus == 30)
                html += "  <span class='badge border-success border rounded-pill px-2 mx-2'>  قسم ملفات الخدمة </span> - ";
            else if (requestEmp.ReqStatus == 31)
                html += " <i class='fa fa-times text-danger px-2 mx-2'></i> - <span class='badge border-danger border rounded-pill px-2 mx-2'> قسم ملفات الخدمة </span> - ";
            else
                html += " <i class='fa fa-spinner px-2 mx-2'></i> - <span class='badge border rounded-pill px-2 mx-2'> قسم ملفات الخدمة </span> - ";


            if(requestEmp.EstimatedFlag != 1)
            {
                if (requestEmp.ReqStatus > 49 || requestEmp.ReqStatus == 40)
                    html += "  <span class='badge border-success border rounded-pill px-2 mx-2'>  مدير دائرة الموارد البشرية  </span> - ";
                else if (requestEmp.ReqStatus == 41)
                    html += " <i class='fa fa-times text-danger px-2 mx-2'></i> - <span class='badge border-danger border rounded-pill px-2 mx-2'> مدير دائرة الموارد البشرية </span> - ";
                else
                    html += " <i class='fa fa-spinner px-2 mx-2'></i> - <span class='badge border rounded-pill px-2 mx-2'>  مدير دائرة الموارد البشرية  </span> - ";
            }

            if (requestEmp.EstimatedFlag == 1)
            {
                if (requestEmp.ReqStatus > 59 || requestEmp.ReqStatus == 50)
                    html += "  <span class='badge border-success border rounded-pill px-2 mx-2'>  مدير عام الموارد البشري </span> - ";
                else if (requestEmp.ReqStatus == 51)
                    html += " <i class='fa fa-times text-danger px-2 mx-2'></i> - <span class='badge border-danger border rounded-pillpx-2 mx-2'> مدير عام الموارد البشري </span> - ";
                else
                    html += " <i class='fa fa-spinner px-2 mx-2'></i> - <span class='badge border rounded-pill px-2 mx-2'> مدير عام الموارد البشري </span> - ";
            }


            if (requestEmp.ReqStatus == 60 )
                html += " <span class='badge border-success border rounded-pill px-2 mx-2'>  المراجعة الداخلية </span>  ";
            else if (requestEmp.ReqStatus == 61)
                html += " <i class='fa fa-times text-danger px-2 mx-2'></i> - <span class='badge border-danger border rounded-pill px-2 mx-2'> المراجعة الداخلية </span>  ";
            else
                html += " <i class='fa fa-spinner px-2 mx-2'></i>  - <span class='badge border rounded-pill px-2 mx-2'> المراجعة الداخلية </span>  ";

           


            return html + "</div>" ;

        }


        return "ss";
    }

    public float GetOverTimeHourPrice(int EmpNo,DateTime Day)
    {
        //SELECT ROUND((DECODE(:b2.holiday_flag, 1, 1.5, 1.25) * :b2.basic_amt* ot_duration_hrs_ln) / (189) +
        //   (DECODE(:b2.holiday_flag, 1, 1.5, 1.25) * :b2.basic_amt* ot_duration_mns_ln *5 / 3) / (189),3)


        var emp = _h.Employee().Get(EmpNo);

        if (emp == null)
            return 0;



        float rate = 0;
        bool isHoloday = _h.IsHoliday(Day);

        if (EmpNo > 1000)
        {
            var conEmpEarn = _db.ConPayrollDtls
               .Where(ro => ro.EmpNo == EmpNo && ro.EarnDepCode == 100)
               .OrderByDescending(ro2 => ro2.EndDate)
               .FirstOrDefault();


            if (isHoloday)
            {
                rate = (float)(1.5 * conEmpEarn.Amount / 189);
            }
            else
            {
                rate = (float)(1.25 * conEmpEarn.Amount / 189);
            }
        }

        if (EmpNo < 1000)
        {
            var EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == EmpNo && ro.EarnCode == 1 && ro.FromDate <= Day).OrderByDescending(o=>o.FromDate).FirstOrDefault();

            if (EmpEarn == null) {
                EmpEarn = _db.EmpEarnings.Where(ro => ro.EmpNo == EmpNo && ro.EarnCode == 1 && ro.ToDate == null).FirstOrDefault();
            }

            if (isHoloday)
            {
                rate = (float)(1.5 * EmpEarn.EarnAmt.Value / 189);
            }
            else
            {
                rate = (float)(1.25 * EmpEarn.EarnAmt.Value / 189);
            }
        }

        return rate;
    }


    public int payStatus(string Id)
    {
   

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);
        string Rel = rawcode[3];

        if (Rel == "Con")
        {

            return 0;
        }

        // Check if request exists first (optimized)
        var requestExists = _db.OvertimeReqMas
            .Any(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo);

        if (!requestExists)
        {
            return 0;
        }

        // Optimized: Check first record and count unpaid records
        var firstDetail = _db.OvertimeReqDtls
            .Where(ro => ro.InYear == InYear && ro.InMailNo == InMailNo && ro.InDocSlNo == InDocSlNo && ro.OtAmount > 0)
            .FirstOrDefault();

        if (firstDetail == null)
        {
            return 0;
        }

       
            
        return firstDetail.PayStat >= 1 ? 2 : 0;
        
  
    }


    

}

public class _OvrtimeDepartmentReport
{
    public VempDtl Employee { get; set; }
    public int Count { get; set; }
}