using HumanResource.Modules.Overtime.Providers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Overtime.Configuration
{
    /// <summary>
    /// Configuration for the Overtime module
    /// </summary>
    public static class OvertimeConfiguration
    {
        /// <summary>
        /// Registers all Overtime module services
        /// </summary>
        public static IServiceCollection AddOvertimeServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, OvertimeNavigationProvider>();

            // Register task providers
            services.AddScoped<ITaskProvider, OvertimeTaskProvider>();

            // Register module services
            // services.AddScoped<OvertimeService>();

            return services;
        }
    }
} 