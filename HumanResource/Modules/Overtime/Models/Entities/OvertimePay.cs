﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Overtime.Models.Entities;

[Keyless]
public partial class OvertimePay
{
    [Column("PAY_YEAR")]
    // [Precision(4)]
    public int? PayYear { get; set; } = null;

    [Column("PAY_MONTH")]
    [Precision(2)]
    public byte PayMonth { get; set; }

    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    [Column("AMT")]
    public decimal? Amt { get; set; }
}
