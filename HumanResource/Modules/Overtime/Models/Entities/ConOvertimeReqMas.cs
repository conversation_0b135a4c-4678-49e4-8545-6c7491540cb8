﻿using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;


namespace HumanResource.Modules.Overtime.Models.Entities;

[Keyless]
[Table("CON_OVERTIME_REQ_MAS")]
public partial class ConOvertimeReqMas
{
    [Column("IN_YEAR")]
    public int InYear { get; set; }

    [Column("IN_DEPT_IND")]
    public int InDeptInd { get; set; }

    [Column("IN_MAIL_NO")]
    public int InMailNo { get; set; }

    [Column("IN_DOC_SL_NO")]
    public int? InDocSlNo { get; set; }

    //[Column("UNIT_CODE")]
    //public int UnitCode { get; set; }

    [Column("EMP_NO")]
    public int? EmpNo { get; set; }

    [Column("ORDER_TYPE")]
    public int OrderType { get; set; } = 97;


    [Column("REQ_STAT")]
    public int ReqStatus { get; set; } = 0; // see Tstat_code

    [Column("TXN_DATE")]
    public DateTime TxnDate { get; set; } = DateTime.Now;

    [Column("ORDER_YEAR")]
    public int? OrderYear { get; set; } = 1;

    [Column("ORDER_MONTH")]
    public int? OrderMonth { get; set; } = 0;

    [Column("ORDER_DEPT_IND")]
    public int? OrderDeptInd { get; set; } = 1;

    [Column("ORDER_SL_NO")]
    public int OrderSlNo { get; set; } = 988;

    [Column("ORDER_DATE")]
    public DateTime? OrderDate { get; set; } = DateTime.Now;

    [Column("SIGN_AUTH_CODE")]
    public int? SignAuthCode { get; set; } = null; // رئيس القسم

    [Column("SIGN_BY_CODE")]
    public int? SignByCode { get; set; } = 1; // الشيخ

    [Column("SIGN_DATE")]
    public DateTime? SignDate { get; set; } = null;


    [Column("SIGN_REM")]
    public string SignRem { get; set; } = "-";


    [Column("CANCEL_FLAG")]
    public int CancelFlag { get; set; } = 0;

    [Column("ESTIMATED_FLAG")]
    public int? EstimatedFlag { get; set; } = 0;

    [Column("AUDIT_STAT")]
    public int AuditStat { get; set; } = 0;

    [Column("AUDIT_DATE")]
    public DateTime? AuditDate { get; set; } = null;

    [Column("AUDIT_USER_ID")]
    public string AuditUserId { get; set; } = null;

    [Column("AUDIT_REM")]
    public string AuditRem { get; set; } = null;


#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

    [Column("AUDIT_TIME_STAMP")]
    public DateTime? AuditTimeStamp { get; set; } = null;

    [Column("FIRST_AUDIT_STAT")]
    public int FirstAuditStat { get; set; } = 0;

    [Column("FIRST_AUDIT_DATE")]
    public DateTime? FirstAuditDate { get; set; } = null;

    [Column("FIRST_AUDIT_USER_ID")]
    public string FirstAuditUserId { get; set; } = null;

    [Column("USER_ID")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public string UserId { get; set; } = null; // قسم الاجازات
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

    [Column("TIME_STAMP")]
    public DateTime? TimeStamp { get; set; } = DateTime.Now;


    [Column("HR_MANAGER_SING")]
    public int? HrManagerSign { get; set; } = 0; // مدير الموارد البشرة

    [Column("HR_MANAGER_SING_DATE")]
    public DateTime? HrManagerSignDate { get; set; } = null;

    [Column("MANAGER_NO")]
    public int? ManagerNo { get; set; } = 0;

    [Column("EXTRA_SUM")]
    public float? ExtraSum { get; set; } = 0;

    [Column("EXTRA_AMOUNT_SUM")]
    public float? ExtraAmountSum { get; set; } = 0;

    [Column("OT_STATUS")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public string OtStatus { get; set; } = "New";

    [Column("FINANCE_MANAGER")]
    public int FinanceManager { get; set; } = 0;
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

    [NotMapped]
    public string Rel { get; set; } = "Con";



}

