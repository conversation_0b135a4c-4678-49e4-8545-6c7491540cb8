﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Overtime.Models.Entities
{
    [Keyless]
    [Table("TOVERTIME_COMMENTS")]
    public class OvertimeComment
    {
        [Column("IN_YEAR")]
        public int InYear { get; set; }

        [Column("IN_DEPT_IND")]
        public int InDeptInd { get; set; }

        [Column("IN_MAIL_NO")]
        public int InMailNo { get; set; }

        [Column("IN_DOC_SL_NO")]
        public int InDocSlNo { get; set; }

        [Column("UNIT_CODE")]
        public int UnitCode { get; set; }

        [Column("EMP_NO")]
        public int EmpNo { get; set; }

        [Column("CREATED_BY")]
        public int CreatedBy { get; set; }

        [Column("TIME_STAMP")]
        public DateTime TimeStamp { get; set; } = DateTime.Now;

        [Column("COMMENT")]
        public string Comment { get; set; }

    }
}
