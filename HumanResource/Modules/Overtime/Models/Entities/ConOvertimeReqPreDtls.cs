﻿using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System;


namespace HumanResource.Modules.Overtime.Models.Entities;

[Keyless]
[Table("CON_OVERTIME_REQ_PRE_DTLS")]
public partial class ConOvertimeReqPreDtls
{
    [Column("IN_YEAR")]
    public int InYear { get; set; }

    [Column("IN_DEPT_IND")]
    public int InDeptInd { get; set; }

    [Column("IN_MAIL_NO")]
    public int InMailNo { get; set; }

    [Column("IN_DOC_SL_NO")]
    public int InDocSlNo { get; set; }

    [Column("UNIT_CODE")]
    public int UnitCode { get; set; }

    [Column("EMP_NO")]
    public int EmpNo { get; set; }

    [Column("OT_DATE")]
    public DateTime OtDate { get; set; }


    [Column("OT_FROM_TIME")]
    public double OtFromTime { get; set; }

    [Column("OT_TO_TIME")]
    public double OtToDate { get; set; }

    [Column("PAY_STAT")]
    public int payStat { get; set; } = 0;

    [Column("PAY_YEAR")]
    public int payYear { get; set; } = 0;

    [Column("PAY_MONTH")]
    public int PayMonth { get; set; } = 0;

    [Column("RUN_NO")]
    public int? RunNo { get; set; } = null;

    [Column("COMMITMENT_DATE")]
    public DateTime? CommitmentDate { get; set; } = null;

    [Column("COMMITMENT_NO")]
    public int? CommitmentNo { get; set; } = null;

    [Column("USER_ID")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public string UserId { get; set; } = null; // قسم الاجازات
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.


    [Column("TIME_STAMP")]
    public DateTime TimeStamp { get; set; } = DateTime.Now;

    [Column("OT_DURATION")]
    public float OtDuration { get; set; } = 0;

    [Column("OT_AMOUNT")]
    public float OtAmount { get; set; } = 0;

    [Column("OT_RATE")]
    public float? OtRate { get; set; } = 0;

    [Column("HOLIDAY_FLAG")]
    public int HolydayFlag { get; set; } = 0;

    [Column("BASIC_AMT")]
    public float BasicAtm { get; set; } = 0;

    [Column("OVERTIME_EXCEED_FLAG")]
    public int OvertimeExceedFlag { get; set; } = 0;

    [Column("COMPUTED_AMOUNT")]
    public float ComputedAmount { get; set; } = 0;



    [NotMapped]
    public bool IsApproved { get; set; } = false;

}

