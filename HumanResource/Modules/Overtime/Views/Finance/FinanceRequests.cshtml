@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel


<div class="d-flex justify-content-between my-2">
    <h3>
        @Model._("العمل الاضافي")
    </h3>

</div>

<div class="btn-group mb-1" role="group" aria-label="Payment Status Filter">
                <a href="/OverTime/Finance?payStatusFilter=0&year=@ViewBag.Year&month=@ViewBag.Month" 
                   class="btn btn-@( ViewBag.payStatusFilter == 0 ? "danger":"outline-danger")">
                    <i class="fas fa-pause-circle"></i> @Model._("متوقف")
                </a>
                <a href="/OverTime/Finance?payStatusFilter=2&year=@ViewBag.Year&month=@ViewBag.Month" 
                   class="btn btn-@( ViewBag.payStatusFilter == 2 ? "success":"outline-success")">
                    <i class="fas fa-check-circle"></i> @Model._("مصروف")
                </a>
                @* <a href="/OverTime/Finance?payStatusFilter=1&from=@Model.Page.Filter.DateFrom.Value.ToString("yyyy-MM-dd")&to=@Model.Page.Filter.DateTo.Value.ToString("yyyy-MM-dd")" 
                   class="btn btn-@( ViewBag.payStatusFilter == 1 ? "warning":"outline-warning")">
                    <i class="fas fa-clock"></i> @Model._("صرف جزئي")
                </a> *@
                <a href="/OverTime/Finance?payStatusFilter=3&year=@ViewBag.Year&month=@ViewBag.Month" 
                   class="btn btn-@( ViewBag.payStatusFilter == 3 ? "primary":"outline-primary")">
                    <i class="fas fa-list"></i> @Model._("الكل")
                </a>
            </div>

<div class="card shadow">
   <div class="card-header">
        <div class="d-flex">
                <div class="form-group m-0 p-0">
                    <select name="year" class="form-control" onchange="window.location.href = '/OverTime/Finance?year=' + this.value;">
                    @for (int i = 2000; i <= DateTime.Now.Year; i++)
                    {
                        <option value="@i" selected="@(i == ViewBag.Year)">@i</option>
                    }
                    </select>
                </div>
                <div class="form-group m-0 p-0">
                    <select name="month" class="form-control" onchange="window.location.href = '/OverTime/Finance?year=@ViewBag.Year&month=' + this.value;">
                    @for (int i = 1; i <= 12; i++)
                    {
                        <option value="@i" selected="@(i == ViewBag.Month)">@i</option>
                    }
                    </select>
                </div>
        </div>
    </div>
  
        <table class="table table-striped table-hover mb-0" id="datatable">
            <thead class="bg-primary">
                <tr>
                    <th>@Model._("الرقم")</th>
                    <th>@Model._("الموظف")</th>
                    <th>@Model._("الشهر/السنة")</th>
                    <th>@Model._("الساعات الإضافية")</th>
                    <th>@Model._("المبلغ المستحق") (ر.ع)</th>
                    <th>@Model._("النوع")</th>
                    <th>@Model._("حالة الصرف")</th>
                    <th>@Model._("رقم الدفعة")</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    
</div>

<script>
    $(document).ready(function(){
        var table = createDatatable('#datatable', true, {
            ajax: {
                url: "/OverTime/Finance/Datatable?year=@ViewBag.Year&month=@ViewBag.Month&payStatusFilter=@ViewBag.payStatusFilter",
               type: "POST",
            },
            order: [[2, 'desc']], // Default sort by month/year descending
            columns:[
                {
                    "data": "id",
                    "title": "@Model._("الرقم")",
                    "orderable": true,
                    "width": "10%"
                },
                {
                    "data": "emp",
                    "title": "@Model._("الموظف")",
                    "orderable": true,
                    "width": "18%"
                },
                {
                    "data": "createdAt",
                    "title": "@Model._("الشهر/السنة")",
                    "orderable": true,
                    "width": "12%",
                    "type": "date",
                    "render": function(data, type, row) {
                        if (type === 'display') {
                            // Format as YYYY-MM for better display
                            var parts = data.split('-');
                            if (parts.length === 2) {
                                var year = parts[0];
                                var month = parts[1];
                                var monthNames = {
                                    '1': 'يناير', '2': 'فبراير', '3': 'مارس', '4': 'أبريل',
                                    '5': 'مايو', '6': 'يونيو', '7': 'يوليو', '8': 'أغسطس',
                                    '9': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
                                };
                                return '<span class="">' + monthNames[month] + ' ' + year + '</span>';
                            }
                        }
                        return data;
                    }
                },
                {
                    "data": "extraSum",
                    "title": "@Model._("الساعات ")",
                    "orderable": true,
                    "width": "12%",
                    "className": "text-center"
                },
                {
                    "data": "extraAmountSum",
                    "title": "@Model._("المبلغ المستحق") (ر.ع)",
                    "orderable": true,
                    "width": "13%",
                    "className": "text-end"
                },
                {
                    "data": "estematedFlag",
                    "title": "@Model._("النوع")",
                    "orderable": true,
                    "width": "10%",
                    "className": "text-center"
                },
                {
                    "data": "payStatus",
                    "title": "@Model._("حالة الصرف")",
                    "orderable": true,
                    "width": "12%",
                    "className": "text-center"
                },
                {
                    "data": "runNo",
                    "title": "@Model._("رقم التشغيل")",
                    "orderable": false,
                    "width": "15%",
                    "className": "text-center"
                }
            ],
  
            
            pageLength: 25,
  
        });

  
       
    });

  
</script>

