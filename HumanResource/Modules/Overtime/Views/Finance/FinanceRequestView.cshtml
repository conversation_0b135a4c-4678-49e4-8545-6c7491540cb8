@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@{
    Layout = Model.Page.Layout;
}



<h3>@Model.UniOvertimeReqMas.OrderYear - @Model.UniOvertimeReqMas.OrderMonth</h3>
<div>
    <div class="card card-body">
        @Html.Raw(Model.renderOvertimeTimeline(Model.UniOvertimeReqMas.InYear+"-"+Model.UniOvertimeReqMas.InMailNo+"-"+Model.UniOvertimeReqMas.InDocSlNo+"-"+Model.UniOvertimeReqMas.Rel))
    </div>
    
</div>
<div class="card shadow">

    <div class="card-body">
        <div class="row">

            <div class="col-lg-4">
                <div class=" d-flex align-items-center justify-content-start">
                    <i class="far fa-clock fa-2x mr-3"></i>
                    <h5><strong>@Model._l("عمل اضافي"):</strong> @ViewBag.ExtraSum @Model._("ساعات")</h5>
                </div>
            </div>

            <div class="col-lg-4">
                <div class=" d-flex align-items-center justify-content-start">
                    <i class="fas fa-dollar-sign fa-2x mr-3"></i>
                    <h5><strong>@Model._l("المبلغ"):</strong> <span class="text-sucess">@Model._amount(ViewBag.MaxEmpEarnAmt)</span> / <span class="text-danger">@Model._amount(ViewBag.ExtraAmountSum)</span> OMR</h5>
                </div>


            </div>

            <div class="col-lg-4">

                <div class=" d-flex align-items-center justify-content-start">
                    <i class="fas fa-file-alt fa-2x mr-3"></i>
                    <h5><strong>@Model._l("طلبات العمل الاضافي"):</strong> @ViewBag.RequestCount </h5>
                </div>

            </div>

        </div>
    </div>
</div>


<div class="row">
    <div class="col-md-6">
        <div class="card shadow">
            <table class="table table-borderless">
                <tr>
                    <td>
                        @Model._("No")
                    </td>
                    <td>
                        @<EMAIL><EMAIL>
                    </td>
                </tr>
                <tr>
                    <td>
                        @Model._("الموظف")
                    </td>
                    <td>
                        @{
                            var s2 = Model._h.StaffData(Model.UniOvertimeReqMas.EmpNo);
                        }

                        #@s2.EmpNo <br> @s2.EmpNameA
                    </td>
                </tr>
                <tr>
                    <td>
                        @Model._("الراتب الاساسي")
                    </td>
                    <td>
                        @Model.EmpEarning.EarnAmt
                    </td>
                </tr>
                <tr>
                    <td>
                        @Model._("الحالة")
                    </td>
                    <td>
                        @Model._h.renderSatus(Model.UniOvertimeReqMas.ReqStatus)

                        @if (Model.UniOvertimeReqMas.CancelFlag == 1)
                        {
                            <span>@Model.UniOvertimeReqMas.SignRem</span>
                        }
                    </td>
                </tr>

                <tr>
                    <td>
                        @Model._("المسؤل")
                    </td>
                    <td>
                        @{
                            var s1 = Model._h.StaffData(Model.UniOvertimeReqMas.ManagerNo);
                        }

                        #@s1.EmpNo <br> @s1.EmpNameA
                    </td>
                </tr>


            </table>
        </div>

        <div>
            <p class="px-3 mb-0 text-muted"></p>
            <table class=" table-sm table-borderless text-muted">
                @foreach (var log in Model._h.GetLogs(Model.UniOvertimeReqMas.InYear + "-" + Model.UniOvertimeReqMas.InMailNo + "-" + Model.UniOvertimeReqMas.InDocSlNo, "Overtime"))
                {
                    <tr>
                        <td><i class="far fa-clock"></i></td>
                        <td>
                            @log.Rem
                        </td>
                        <td dir="ltr">
                            @Model._dt(log.TimeStamp)
                        </td>

                        <td>
                            @Model._h.StaffData(log.UserId).EmpNameA
                        </td>
                    </tr>
                }
            </table>
        </div>
    </div>
    <div class="col-md-6">



        <div class="card shadow">
            <table class="table table-sm">
                <thead class="bg-primary">
                    <tr>
                        <td>@Model._("اليوم")</td>
                        <td>
                            @Model._("الساعات")
                            @if (Model.UniOvertimeReqMas.EstimatedFlag == 1)
                            {
                                <span class="text-danger">( @Model._("تقديري") )</span>
                            }
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                </thead>
                <tbody>


                    @foreach (var day in Model.UniOvertimeReqDtls)
                    {

                        <tr>
                            <td>
                                <a href="#" onclick="getFpRecords('@Model._d(day.OtDate)')"> @Model._ddd(day.OtDate)</a>
                            </td>
                            <td>
                                @Model._h.FormatHour(day.OtDuration)
                            </td>
                            <td>
                                @Model._amount(day.OtRate)
                            </td>
                            <td>
                                @if (Model._amount(day.ComputedAmount) != Model._amount(day.OtAmount))
                                {
                                    <span class="p-1 badge-warning">@Model._amount(day.ComputedAmount) => @Model._amount(day.OtAmount)</span>
                                }
                                else
                                {
                                    <span class="p-1">@Model._amount(day.ComputedAmount) => @Model._amount(day.OtAmount)</span>
                                }

                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr>

                        <td>@Model._("الساعات الكلية")</td>
                        <td>@Model.UniOvertimeReqMas.ExtraSum.Value</td>
                        <td></td>
                        <td>
                            @if (Model._amount(ViewBag.newsum) != Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value))
                            {
                                <span class="p-1 badge-warning"> @Model._amount(ViewBag.newsum)=> @Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value)  </span>
                            }
                            else
                            {
                                <span class="p-1 "> @Model._amount(ViewBag.newsum) => @Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value)  </span>
                            }
                        </td>
                    </tr>
                </tfoot>

            </table>
        </div>

        <div class="card shadow">
            <div class="card-body">
                <div class="row ">


                    @foreach (var comment in Model.OvertimeComments)
                    {
                        var createdBy = Model._h.StaffData(comment.CreatedBy);

                        <div class="col-7 @(comment.CreatedBy==Model.Profile.EmpNo.Value?"mr-auto":"ml-auto") ">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user"></i> <span class="mx-2 mt-2">@createdBy.EmpNo - @createdBy.EmpNameA</span>
                            </div>
                            <p class="text-muted m-0 py-0 " dir="ltr"><small>@Model._dt(@comment.TimeStamp)</small></p>
                            <p class="p-2 border rounded">
                                @comment.Comment
                            </p>
                        </div>
                    }
                </div>

                <hr>



            </div>
        </div>




    </div>


</div>

<div class="modal fade" id="fp-records-modal" tabindex="-1" role="dialog" aria-labelledby="fp-records-modalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="fp-records-modalLabel">@Model._("البصمات ") ({{ selected_date }})</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">

                <table class="table table-sm table-striped border">
                    <tr v-for="record in fp_records">
                        <td><span dir="ltr">{{ _dt(record.createdAt) }}</span></td>

                    </tr>
                </table>


            </div>
            <div class="modal-footer d-felx">
                <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("اغلاق")</button>
            </div>
        </div>
    </div>
</div>



<script>

    let app = new Vue({
        el: "#fp-records-modal",
        data: {
            fp_records: [],
            selected_date: '',
        },
        methods: {
            _dt(date) {
                return window._dt(date)
            }
        }
    });

    function getFpRecords(date) {

        $.get("/OverTime/GetFpRecords?EmpNo=@Model.UniOvertimeReqMas.EmpNo&Day=" + date, function (data) {
            app.fp_records = data.data;
            app.selected_date = date;

            $("#fp-records-modal").modal("show")
        }, 'json');

    }


</script>