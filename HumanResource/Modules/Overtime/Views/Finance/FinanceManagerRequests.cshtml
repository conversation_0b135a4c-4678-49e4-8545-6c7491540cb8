@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

<div class="d-flex justify-content-between my-2">
    <h3>
        @Model._("إعتماد صرف العمل الاضافي")
    </h3>
    <div>
        <button class="btn btn-outline-primary me-2" type="button" data-toggle="collapse" data-target="#trendDashboard" aria-expanded="true" aria-controls="trendDashboard">
            <i class="fas fa-chart-line"></i> @Model._("تقرير ")
        </button>

    </div>
</div>

<!-- Trend Analysis Dashboard - Collapsible -->
<div class="collapse  mb-4" id="trendDashboard">
    <div class="">
        
        <div class="">
            <div class="row g-0">
         <!-- Current Month vs Last Month -->
     <div class="col-md-4">
         <div class="card shadow-sm border-0 trend-card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="fas fa-calendar-alt"></i> @Model._("مقارنة الشهر الحالي مع الشهر الماضي")</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-muted">@Model._("الشهر الحالي")</h6>
                        <div id="currentMonthStats">
                            <div class="mb-2">
                                <span class="" id="currentAmount">0 ر.ع</span>
                                <small class="d-block text-muted">@Model._("المبلغ المدفوع")</small>
                            </div>
                            <div class="mb-2">
                                <span class="" id="currentPendingAmount">0 ر.ع</span>
                                <small class="d-block text-muted">@Model._("في انتظار الدفع")</small>
                            </div>
                            <div class="mb-2">
                                <span class="" id="currentHours">0</span>
                                <small class="d-block text-muted">@Model._("الساعات")</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted">@Model._("الشهر الماضي")</h6>
                        <div id="lastMonthStats">
                            <div class="mb-2">
                                <span class="" id="lastAmount">0 ر.ع</span>
                                <small class="d-block text-muted">@Model._("المبلغ المدفوع")</small>
                            </div>
                            <div class="mb-2">
                                <span class="" id="lastPendingAmount">0 ر.ع</span>
                                <small class="d-block text-muted">@Model._("في انتظار الدفع")</small>
                            </div>
                            <div class="mb-2">
                                <span class="" id="lastHours">0</span>
                                <small class="d-block text-muted">@Model._("الساعات")</small>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <h6>@Model._("التغيير")</h6>
                    <div id="trendChanges">
                        <div class="mb-1">
                            <span id="amountChange" class="fw-bold">0%</span>
                            <small class="text-muted">@Model._("تغيير المبلغ")</small>
                        </div>
                        <div class="mb-1">
                            <span id="hoursChange" class="fw-bold">0%</span>
                            <small class="text-muted">@Model._("تغيير الساعات")</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

   

         <!-- Pending Payments Alert -->
     <div class="col-md-4">
         <div class="card shadow-sm border-0 trend-card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0"><i class="fas fa-exclamation-triangle"></i> @Model._("المدفوعات المعلقة")</h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning text-center mb-3">
                    <h5 class="mb-1" id="totalPendingAmount">0 ر.ع</h5>
                    <small>@Model._("إجمالي المبلغ المعلق للدفع")</small>
                </div>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="mb-2">
                            <span class="badge bg-warning" id="currentMonthPending">0 ر.ع</span>
                            <small class="d-block text-muted">@Model._("الشهر الحالي")</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-2">
                            <span class="badge bg-secondary" id="lastMonthPending">0 ر.ع</span>
                            <small class="d-block text-muted">@Model._("الشهر الماضي")</small>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        @Model._("الطلبات المعتمدة وفي انتظار الدفع")
                    </small>
                </div>
            </div>
        </div>
                 </div>

                 <div class="col-4">
                 <div class="card shadow-sm border-0 border-top">
                     <div class="card-header bg-primary text-white">
                         <h6 class="mb-0"><i class="fas fa-chart-bar"></i> @Model._("الرسم البياني")</h6>
                     </div>
                     <div class="card-body">
                         <canvas id="trendChart" height="100"></canvas>
                     </div>
                 </div>
             </div>
         </div>
         
         
     </div>
 </div>
</div>


<div class="card shadow">
    
   
        <table class="table table-striped" id="datatable">
            <thead class="bg-primary text-white">
                <tr>
                    <td>@Model._("الرقم") </td>
                    <td>@Model._("الموظف") </td>
                    <td>@Model._("الشهر") </td>
                    <td>@Model._("الساعات الكلية")</td>
                    <td>@Model._("المبلغ القابل للصرف") (ر.ع)</td>
                    <td>@Model._("النوع")</td>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
   
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>



<script>
    let trendChart;
    
    $(document).ready(function(){
        // Initialize DataTable
        createDatatable('#datatable',true, {
            ajax: "/OverTime/Finance/Manager/Datatable?from=@Model.Page.Filter.DateFrom&to=@Model.Page.Filter.DateTo",
            ordaring:false,
            columns:[
                {"data":"id","title":"@Model._("الرقم")"},
                {"data":"emp","title":"@Model._("الموظف")"},
                {"data":"createdAt","title":"@Model._("الشهر")"},
                {"data":"extraSum","title":"@Model._("الساعات ")"},
                {"data":"extraAmountSum","title":"@Model._("المبلغ  ")"},
                { "data": "estematedFlag", "title": "@Model._("النوع")" },
            ],
        });

        // Load trend data
        loadTrendData();
    });

    function loadTrendData() {
        $.ajax({
            url: '/OverTime/Finance/Manager/TrendData',
            type: 'GET',
            success: function(data) {
                updateTrendDisplay(data);
                createTrendChart(data);
            },
            error: function(xhr, status, error) {
                console.error('Error loading trend data:', error);
                showErrorMessage('@Model._("خطأ في تحميل بيانات الاتجاهات")');
            }
        });
    }

    function updateTrendDisplay(data) {
        // Current Month Stats
        $('#currentAmount').text(formatCurrency(data.currentMonth.totalAmount));
        $('#currentPendingAmount').text(formatCurrency(data.currentMonth.pendingAmount));
        $('#currentHours').text(formatHours(data.currentMonth.totalHours));

        // Last Month Stats
        $('#lastAmount').text(formatCurrency(data.lastMonth.totalAmount));
        $('#lastPendingAmount').text(formatCurrency(data.lastMonth.pendingAmount));
        $('#lastHours').text(formatHours(data.lastMonth.totalHours));

        // Year to Date Stats
        $('#yearTotalAmount').text(formatCurrency(data.yearToDate.totalAmount));
        $('#yearPendingAmount').text(formatCurrency(data.yearToDate.pendingAmount));
        $('#yearTotalHours').text(formatHours(data.yearToDate.totalHours));
        $('#yearRequestCount').text(data.yearToDate.requestCount);

        // Pending Payments
        $('#totalPendingAmount').text(formatCurrency(data.yearToDate.pendingAmount));
        $('#currentMonthPending').text(formatCurrency(data.currentMonth.pendingAmount));
        $('#lastMonthPending').text(formatCurrency(data.lastMonth.pendingAmount));

        // Trend Changes
        updateTrendChange('#amountChange', data.trends.amountChangePercent, data.trends.amountChange);
        updateTrendChange('#hoursChange', data.trends.hoursChangePercent, data.trends.hoursChange);
    }

    function updateTrendChange(selector, percentChange, absoluteChange) {
        const element = $(selector);
        const isPositive = percentChange > 0;
        const isNegative = percentChange < 0;
        
        element.removeClass('text-success text-danger text-muted');
        
        if (isPositive) {
            element.addClass('text-success');
            element.html(`<i class="fas fa-arrow-up"></i> +${percentChange}%`);
        } else if (isNegative) {
            element.addClass('text-danger');
            element.html(`<i class="fas fa-arrow-down"></i> ${percentChange}%`);
        } else {
            element.addClass('text-muted');
            element.html(`<i class="fas fa-minus"></i> 0%`);
        }
    }

    function createTrendChart(data) {
        const ctx = document.getElementById('trendChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (trendChart) {
            trendChart.destroy();
        }

        const chartData = {
            labels: [
                `"الشهر الماضي" (${data.lastMonth.year}-${data.lastMonth.month})`,
                `"الشهر الحالي" (${data.currentMonth.year}-${data.currentMonth.month})`
            ],
            datasets: [
                {
                    label: "المبلغ المدفوع",
                    data: [data.lastMonth.totalAmount, data.currentMonth.totalAmount],
                    backgroundColor: 'rgba(40, 167, 69, 0.8)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 2,
                    yAxisID: 'y'
                },
                {
                    label: "في انتظار الدفع",
                    data: [data.lastMonth.pendingAmount, data.currentMonth.pendingAmount],
                    backgroundColor: 'rgba(255, 193, 7, 0.8)',
                    borderColor: 'rgba(255, 193, 7, 1)',
                    borderWidth: 2,
                    yAxisID: 'y'
                },
                {
                    label: "الساعات",
                    data: [data.lastMonth.totalHours, data.currentMonth.totalHours],
                    backgroundColor: 'rgba(23, 162, 184, 0.8)',
                    borderColor: 'rgba(23, 162, 184, 1)',
                    borderWidth: 2,
                    type: 'line',
                    yAxisID: 'y1'
                }
            ]
        };

        const config = {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "مقارنة اتجاهات العمل الإضافي"
                    },
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: "المبلغ"
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: "الساعات"
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        };

        trendChart = new Chart(ctx, config);
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('ar-OM', {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount) + ' ر.ع';
    }

    function formatHours(hours) {
        return new Intl.NumberFormat('ar-OM', {
            style: 'decimal',
            minimumFractionDigits: 1,
            maximumFractionDigits: 1
        }).format(hours);
    }

    function refreshTrendData() {
        const button = event.target.closest('button');
        const originalHtml = button.innerHTML;
        
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> @Model._("جاري التحديث...")';
        button.disabled = true;
        
        loadTrendData();
        
        setTimeout(() => {
            button.innerHTML = originalHtml;
            button.disabled = false;
        }, 2000);
    }

    function showErrorMessage(message) {
        // You can implement your preferred notification system here
        alert(message);
    }


</script>