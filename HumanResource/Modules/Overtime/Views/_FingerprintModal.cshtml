@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

<div class="modal fade" id="fp-records-modal" tabindex="-1" role="dialog" 
     aria-labelledby="fp-records-modalLabel" aria-hidden="true">
    <div class="modal-dialog " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="fp-records-modalLabel">
                    @Model._("البصمات") ({{ selected_date }})
                </h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="fp-timeline">
                    <div v-for="(record, index) in sortedRecords" :key="index" class="fp-record-item">
                        <!-- Fingerprint Record -->
                        <div class="fp-record-card text-center">
                            <div class="fp-time">
                                <i class="fas fa-fingerprint text-primary mr-2"></i>
                                <span dir="ltr" class="time-text">{{ _dt(record.createdAt) }}</span>
                            </div>
                        </div>
                        
                        <!-- Time Duration Arrow (except for last record) -->
                        <div v-if="index < sortedRecords.length - 1" class="fp-duration-arrow">
                            <div class="arrow-line text-center">
                                <i class="fas fa-arrow-down text-success"></i>
                            </div>
                            <div class="duration-badge text-center">
                                <span class="badge badge-success">
                                    {{ getTimeDifference(sortedRecords[index + 1], record) }}
                                </span>
                            </div>
                            <div class="arrow-line text-center">
                                <i class="fas fa-arrow-down text-success"></i>
                            </div>
                        </div>
                    </div>

                    <div v-if="sortedRecords.length == 0">
                        <div class="alert alert-info">
                            @Model._l("لا يوجد بصمات لهذا اليوم")
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" 
                        data-dismiss="modal">
                    @Model._l("اغلاق")
                </button>
            </div>
        </div>
    </div>
</div> 



<script>
    let app = new Vue({
        el: "#fp-records-modal",
        data: {
            fp_records: [],
            selected_date: '',
        },
        computed: {
            sortedRecords() {
                // Sort records by createdAt in ascending order (earliest first)
                return this.fp_records.slice().sort((a, b) => {
                    return new Date(a.createdAt) - new Date(b.createdAt);
                });
            }
        },
        methods: {
            _dt(date) {
                if (!date) return '';
                const d = new Date(date);
                let hours = d.getHours();
                const minutes = d.getMinutes();
                const ampm = hours >= 12 ? ' PM' : ' AM';
                hours = hours % 12;
                hours = hours ? hours : 12; // the hour '0' should be '12'
                const minutesStr = minutes < 10 ? '0' + minutes : minutes;
                return hours + ':' + minutesStr + ampm;
            },
            getTimeDifference(currentRecord, previousRecord) {
                if (!currentRecord.createdAt || !previousRecord.createdAt) {
                    return '-';
                }
                
                const current = new Date(currentRecord.createdAt);
                const previous = new Date(previousRecord.createdAt);
                const diffMs = Math.abs(current - previous);
                
                const hours = Math.floor(diffMs / (1000 * 60 * 60));
                const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                
                if (hours > 0) {
                    return `${hours}س ${minutes}د`;
                } else {
                    return `${minutes}د`;
                }
            }
        }
    });

    function getFpRecords(date) {
        $.get("/OverTime/GetFpRecords?EmpNo=@Model.UniOvertimeReqMas.EmpNo&Day=" + date, function(data) {
            app.fp_records = data.data;
            app.selected_date = date;
            $("#fp-records-modal").modal("show")
        }, 'json');
    }
</script>