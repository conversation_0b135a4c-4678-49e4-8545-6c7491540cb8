@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

<div class="card shadow">
    <div class="card-body">
        @foreach(var comment in Model.OvertimeComments.Where(c => !string.IsNullOrWhiteSpace(c.Comment)))
        {
            var createdBy = Model._h.<PERSON>(comment.CreatedBy);
            <div class="col-7 @(comment.CreatedBy == Model.Profile.EmpNo.Value ? "mr-auto" : "ml-auto")">
                <div class="d-flex align-items-center">
                    <i class="fas fa-user"></i> 
                    <span class="mx-2 mt-2">@createdBy.EmpNo - @createdBy.EmpNameA</span>
                </div>
                <p class="text-muted m-0 py-0" dir="ltr">
                    <small>@Model._dt(comment.TimeStamp)</small>
                </p>
                <p class="p-2 border rounded">
                    @comment.Comment
                </p>
            </div>
        }

    </div>
</div> 