@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@{
    Layout = Model.Page.Layout;
}



<div class="d-flex justify-content-between my-2">
    <div>
        @Model._("العمل الاضافي")
    </div>
    <div>
        @if (Model.UniOvertimeReqMas.SignAuthCode == null || Model.UniOvertimeReqMas.SignAuthCode == 0)
        {
            <a href="/OverTime/My/Delete/@<EMAIL><EMAIL><EMAIL>" class="btn btn-danger rounded-pill btn-sm after-confirm"><i class="fa fa-trash-alt"></i> @Model._("حذف")</a>
        }
    </div>
</div>

<div>
    مسار الطلب
    <div class="card card-body">
        @Html.Raw(Model.renderOvertimeTimeline(Model.UniOvertimeReqMas.InYear+"-"+Model.UniOvertimeReqMas.InMailNo+"-"+Model.UniOvertimeReqMas.InDocSlNo+"-"+Model.UniOvertimeReqMas.Rel))
    </div>
    <br>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow">
            <table class="table table-borderless">
                <tr>
                    <td>
                        @Model._("الرقم")
                    </td>
                    <td>
                        @<EMAIL><EMAIL>
                    </td>
                </tr>
 

                <tr>
                    <td>
                        @Model._("السؤول المباشر")
                    </td>
                    <td>
                        @{
                            var s1 = Model._h.StaffData(Model.UniOvertimeReqMas.ManagerNo);
                        }

                        #@s1.EmpNo <br> @s1.EmpNameA
                    </td>
                </tr>
            </table>
        </div>

    </div> 

    <div class="col-md-6">
        <div class="card shadow">
            <table class="table table-sm">
                <thead class="bg-primary">
                    <tr>
                        <td></td>
                        <td>@Model._("الايام")</td>
                        @* <td>@Model._("الساعات")</td> *@
   
                    </tr>
                </thead>
                <tbody>


                    @foreach (var day in Model.UniOvertimeReqPreDtls)
                    {

                        if (day.IsApproved)
                        {
                            <tr class="">
                                <td>
                                    <i class="fas fa-check"></i>
                                </td>
                                <td>
                                    @Model._d(day.OtDate)
                                </td>
                                @* <td>
                                    @Model._h.FormatHour(day.OtDuration)
                                </td> *@

                            </tr>
                        }
                        else
                        {
                            <tr>
                                <td></td>
                                <td>
                                    @Model._ddd(day.OtDate)
                                </td>
                                @* <td>
                                    @Model._h.FormatHour(day.OtDuration)
                                </td> *@

                            </tr>
                        }

                    }
                </tbody>


            </table>
        </div>



    </div>


</div>

