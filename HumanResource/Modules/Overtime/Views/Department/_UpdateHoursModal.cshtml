@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

<div class="modal fade" id="update-hours-modal" tabindex="-1" role="dialog" 
     aria-labelledby="update-hours-modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header ">
                <h5 class="modal-title" id="update-hours-modalLabel">
                    @Model._("تحديث ساعات العمل الاضافي")
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="~/OverTime/Department/UpdateHours/@<EMAIL><EMAIL><EMAIL>" 
                  method="post" class="ajax" id="update-hours-form">
                <div class="modal-body">
                 
                </div>
                <div>
                    
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead class="bg-primary">
                                <tr>
                                    <th>@Model._("اليوم")</th>
                                    <th>@Model._("الساعات الحالية")</th>
                                    <th>@Model._("الساعات الجديدة")</th>
                                    <th>@Model._("البصمات والوقت المحسوب")</th>
                                    <th>@Model._("إجراء")</th>
                                </tr>
                            </thead>
                            <tbody id="hours-update-table">
                                @for(int i = 0; i < Model.UniOvertimeReqDtls.Count; i++)
                                {
                                    var day = Model.UniOvertimeReqDtls[i];
                                    var currentHours = (int)Math.Floor(day.OtDuration);
                                    var currentMinutes = (int)((day.OtDuration - currentHours +0.001) * 60);
                                    <tr data-date="@Model._d(day.OtDate)" data-index="@i">
                                        <td>
                                            <strong>@Model._ddd(day.OtDate)</strong>
                                            <input type="hidden" name="updates[@i][date]" value="@Model._d(day.OtDate)" />
                                            <input type="" name="updates[@i][hours]" class="combined-hours" value="@day.OtDuration" />
                                        </td>
                                        <td>
                                            
                                                @Model._h.FormatHour(day.OtDuration)
                                        
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="input-group input-group-sm" style="width: 120px;">
                                                    <input type="number" 
                                                           class="form-control hours-input" 
                                                           value="@currentHours" 
                                                           min="0" 
                                                           max="12"
                                                           placeholder="ساعة"
                                                           data-index="@i"
                                                           style="border-radius:0 !important" />
                                                    <div class="input-group-append">
                                                        <span class="input-group-text">س</span>
                                                    </div>
                                                </div>
                                                <div class="input-group input-group-sm ml-2" style="width: 120px;">
                                                    <input type="number" 
                                                           class="form-control minutes-input" 
                                                           value="@currentMinutes" 
                                                           min="0" 
                                                           max="59"
                                                           step="1"
                                                           placeholder="دقيقة"
                                                           data-index="@i"
                                                           style="border-radius:0 !important" />
                                                    <div class="input-group-append">
                                                        <span class="input-group-text">د</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-secondary" 
                                                    onclick="showFingerprintsInModal('@Model._d(day.OtDate)', @i)">
                                                <i class="fas fa-fingerprint"></i> @Model._("عرض البصمات")
                                            </button>
                                            <div id="calculated-time-@i" class="mt-1 small text-muted"></div>
                                        </td>
                                        <td>
                                            <input type="hidden" name="updates[@i][action]" value="update" class="action-input" />
                                            <button type="button" class="btn btn-sm btn-danger delete-row" 
                                                    onclick="markForDeletion(@i)">
                                                <i class="fas fa-trash"></i> @Model._("حذف")
                                            </button>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- Fingerprints Display Area -->
                    <div id="fingerprints-display" class="mt-3" style="display: none;">
                        <div class="card">
                            <div class="card-header ">
                                <h6 class="mb-0">
                                    <i class="fas fa-fingerprint"></i> 
                                    @Model._("بصمات اليوم") <span id="selected-date-display"></span>
                                    <span id="total-calculated-time" class="float-right"></span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row" id="fingerprints-content">
                                    <!-- Fingerprints will be loaded here -->
                                </div>
                                <div id="time-calculation-summary" class="mt-3" style="display: none;">
                                    <div class="alert alert-success">
                                        <h6><i class="fas fa-calculator"></i> @Model._("حساب الوقت")</h6>
                                        <div id="time-breakdown"></div>
                                        <hr>
                                        <strong id="total-overtime-display"></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        @Model._("إلغاء")
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> @Model._("حفظ التحديثات")
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openUpdateModal() {
    $('#update-hours-modal').modal('show');
}

function markForDeletion(index) {
    const row = $(`tr[data-index="${index}"]`);
    const actionInput = row.find('.action-input');
    
    if (actionInput.val() === 'delete') {
        // Restore row
        actionInput.val('update');
        row.removeClass('table-danger');
        row.find('.delete-row').html('<i class="fas fa-trash"></i> @Model._("حذف")');
        row.find('.hours-input, .minutes-input').prop('disabled', false);
    } else {
        // Mark for deletion
        actionInput.val('delete');
        row.addClass('table-danger');
        row.find('.delete-row').html('<i class="fas fa-undo"></i> @Model._("استرجاع")');
        row.find('.hours-input, .minutes-input').prop('disabled', true);
    }
}

// Update combined hours when individual inputs change
$(document).on('input', '.hours-input, .minutes-input', function() {
    const index = $(this).data('index');
    const hours = parseInt($(`tr[data-index="${index}"] .hours-input`).val()) || 0;
    const minutes = parseInt($(`tr[data-index="${index}"] .minutes-input`).val()) || 0;
    
    // Convert to decimal hours (e.g., 1 hour 30 minutes = 1.5 hours)
    const totalHours = hours + (minutes / 60) + 0.001;
    
    // Update the hidden field
    $(`tr[data-index="${index}"] .combined-hours`).val(totalHours.toFixed(3));
});

function showFingerprintsInModal(date, rowIndex) {
    $('#selected-date-display').text(date);
    $('#fingerprints-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> @Model._("جاري التحميل...")</div>');
    $('#fingerprints-display').show();
    $('#time-calculation-summary').hide();
    
    $.get("/OverTime/GetFpRecords?EmpNo=@Model.UniOvertimeReqMas.EmpNo&Day=" + date, function(data) {
        let content = '';
        let timeBreakdown = '';
        let totalOvertimeMinutes = 0;
        let overtimeHours = 0;
        let overtimeRemainingMinutes = 0;
        
        // Use database calculated overtime hours if available
        if (data.calculatedFromDatabase && data.overtimeHours !== null) {
            // Convert decimal hours to hours and minutes
            const totalMinutes = Math.round(data.overtimeHours * 60);
            overtimeHours = Math.floor(totalMinutes / 60);
            overtimeRemainingMinutes = totalMinutes % 60;
            totalOvertimeMinutes = totalMinutes;
            
            // Show that we're using database calculated values
            timeBreakdown = `
                <div class="row">
                    <div class="col-md-12">
                        <div class="">
                            <i class="fas fa-database"></i> <strong>@Model._("ساعات العمل الإضافي المحسوبة من النظام")</strong><br>
                            <strong>@Model._("ساعات العمل الإضافي"):</strong> ${overtimeHours} ساعة و ${overtimeRemainingMinutes} دقيقة
                            <small class="d-block mt-1 text-muted">تم حساب هذه القيم تلقائياً من نظام الحضور والانصراف</small>
                        </div>
                    </div>
                </div>
            `;
        }
        
        if (data.data && data.data.length > 0) {
            // Sort records by time
            const sortedRecords = data.data.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
            
            sortedRecords.forEach(function(record, index) {
                const time = new Date(record.createdAt);
                const timeString = time.toLocaleTimeString('ar-SA', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                });
                
                content += `
                    <div class="col-md-2 mb-2">
                        <div class="card card-body text-center bg-light">
                            <i class="fas fa-clock text-primary"></i>
                            <small class="font-weight-bold">${timeString}</small>
                            <small class="text-muted">${index === 0 ? 'دخول' : (index === sortedRecords.length - 1 ? 'خروج' : 'بصمة')}</small>
                        </div>
                    </div>
                `;
            });
            
            // If we don't have database calculated values, fall back to manual calculation
            if (!data.calculatedFromDatabase || data.overtimeHours === null) {
                // Calculate time differences and overtime (legacy calculation)
                if (sortedRecords.length >= 2) {
                    const firstEntry = new Date(sortedRecords[0].createdAt);
                    const lastEntry = new Date(sortedRecords[sortedRecords.length - 1].createdAt);
                    
                    // Calculate total time worked
                    const totalMinutesWorked = Math.floor((lastEntry - firstEntry) / (1000 * 60));
                    const totalHoursWorked = Math.floor(totalMinutesWorked / 60);
                    const remainingMinutes = totalMinutesWorked % 60;
                    
                    // Assume standard work day is 7 hours (420 minutes) - updated from 8 hours
                    const standardWorkMinutes = 7 * 60;
                    const overtimeMinutesCalc = Math.max(0, totalMinutesWorked - standardWorkMinutes);
                    overtimeHours = Math.floor(overtimeMinutesCalc / 60);
                    overtimeRemainingMinutes = overtimeMinutesCalc % 60;
                    
                    totalOvertimeMinutes = overtimeMinutesCalc;
                    
                    timeBreakdown = `
                        <div class="row">
                            <div class="col-md-6">
                                <strong>@Model._("وقت الدخول"):</strong> ${firstEntry.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit', hour12: true})}<br>
                                <strong>@Model._("وقت الخروج"):</strong> ${lastEntry.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit', hour12: true})}<br>
                            </div>
                            <div class="col-md-6">
                                <strong>@Model._("إجمالي ساعات العمل"):</strong> ${totalHoursWorked} ساعة و ${remainingMinutes} دقيقة<br>
                                <strong>@Model._("ساعات العمل الإضافي"):</strong> ${overtimeHours} ساعة و ${overtimeRemainingMinutes} دقيقة
                                <small class="d-block mt-1 text-warning">⚠️ محسوب يدوياً من البصمات</small>
                            </div>
                        </div>
                    `;
                }
            }
            
            // Update the calculated time display in the row
            if (totalOvertimeMinutes > 0) {
                $(`#calculated-time-${rowIndex}`).html(`
                    <span class="badge badge-info">
                        المحسوب: ${overtimeHours}س ${overtimeRemainingMinutes}د
                        ${data.calculatedFromDatabase ? '<i class="fas fa-database ml-1" title="من قاعدة البيانات"></i>' : '<i class="fas fa-calculator ml-1" title="محسوب يدوياً"></i>'}
                    </span>
                `);
            }
            
            // Show suggestion button to use calculated time
            if (totalOvertimeMinutes > 0) {
                timeBreakdown += `
                    <div class="mt-2">
                        <button type="button" class="btn btn-sm btn-success" 
                                onclick="useCalculatedTime(${rowIndex}, ${overtimeHours}, ${overtimeRemainingMinutes})">
                            <i class="fas fa-magic"></i> @Model._("استخدام الوقت المحسوب")
                        </button>
                        ${data.calculatedFromDatabase ? 
                            '<small class="d-block mt-1 text-muted"><i class="fas fa-info-circle"></i> القيم مأخوذة من قاعدة بيانات نظام الحضور والانصراف</small>' : 
                            '<small class="d-block mt-1 text-warning"><i class="fas fa-exclamation-triangle"></i> تم الحساب يدوياً من البصمات - قد لا تكون دقيقة</small>'}
                    </div>
                `;
            }
                
            $('#time-calculation-summary').show();
            
            // Show time differences between consecutive records
            if (sortedRecords.length > 1) {
                content += '<div class="col-12"><hr><h6>@Model._("الفترات الزمنية")</h6></div>';
                
                for (let i = 1; i < sortedRecords.length; i++) {
                    const prevTime = new Date(sortedRecords[i-1].createdAt);
                    const currentTime = new Date(sortedRecords[i].createdAt);
                    const diffMinutes = Math.floor((currentTime - prevTime) / (1000 * 60));
                    const diffHours = Math.floor(diffMinutes / 60);
                    const diffRemainingMinutes = diffMinutes % 60;
                    
                    content += `
                        <div class="col-md-4 mb-2">
                            <div class="card card-body text-center">
                                <small class="font-weight-bold">
                                    ${prevTime.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})} - 
                                    ${currentTime.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'})}
                                </small>
                                <small class="text-dark">
                                    ${diffHours} ساعة و ${diffRemainingMinutes} دقيقة
                                </small>
                            </div>
                        </div>
                    `;
                }
            }
        } else {
            content = '<div class="col-12 text-center text-muted">@Model._("لا توجد بصمات لهذا اليوم")</div>';
            
            // Even if no fingerprints, we might still have database calculated overtime
            if (data.calculatedFromDatabase && data.overtimeHours !== null) {
                const totalMinutes = Math.round(data.overtimeHours * 60);
                overtimeHours = Math.floor(totalMinutes / 60);
                overtimeRemainingMinutes = totalMinutes % 60;
                totalOvertimeMinutes = totalMinutes;
                
                if (totalOvertimeMinutes > 0) {
                    timeBreakdown = `
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-database"></i> <strong>@Model._("ساعات العمل الإضافي المحسوبة من النظام")</strong><br>
                                    <strong>@Model._("ساعات العمل الإضافي"):</strong> ${overtimeHours} ساعة و ${overtimeRemainingMinutes} دقيقة
                                    <small class="d-block mt-1 text-muted">تم حساب هذه القيم تلقائياً من نظام الحضور والانصراف</small>
                                </div>
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-success" 
                                            onclick="useCalculatedTime(${rowIndex}, ${overtimeHours}, ${overtimeRemainingMinutes})">
                                        <i class="fas fa-magic"></i> @Model._("استخدام الوقت المحسوب")
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    $(`#calculated-time-${rowIndex}`).html(`
                        <span class="badge badge-info">
                            المحسوب: ${overtimeHours}س ${overtimeRemainingMinutes}د
                            <i class="fas fa-database ml-1" title="من قاعدة البيانات"></i>
                        </span>
                    `);
                    
                    $('#time-calculation-summary').show();
                }
            }
        }
        
        $('#fingerprints-content').html(content);
        $('#time-breakdown').html(timeBreakdown);
        
        if (totalOvertimeMinutes > 0) {
            $('#total-overtime-display').html(`@Model._("إجمالي العمل الإضافي"): ${overtimeHours} ساعة و ${overtimeRemainingMinutes} دقيقة`);
            $('#total-calculated-time').html(`(${overtimeHours}س ${overtimeRemainingMinutes}د)`);
        }
        
    }, 'json').fail(function() {
        $('#fingerprints-content').html('<div class="col-12 text-center text-danger">@Model._("خطأ في تحميل البصمات")</div>');
    });
}

function useCalculatedTime(rowIndex, hours, minutes) {
    $(`tr[data-index="${rowIndex}"] .hours-input`).val(hours).trigger('input');
    $(`tr[data-index="${rowIndex}"] .minutes-input`).val(minutes).trigger('input');
    
    // Show success message
    toastr.success('@Model._("تم تطبيق الوقت المحسوب بنجاح")');
}

// Form submission handler to ensure combined hours are calculated
$('#update-hours-form').on('submit', function(e) {
    // Ensure all combined hours are up to date
    $('.hours-input').each(function() {
        $(this).trigger('input');
    });
});
</script> 