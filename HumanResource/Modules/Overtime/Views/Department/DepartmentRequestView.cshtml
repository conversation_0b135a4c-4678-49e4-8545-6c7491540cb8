@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@{
    Layout = Model.Page.Layout;
    var staff = Model._h.StaffData(Model.UniOvertimeReqMas.EmpNo);
    var manager = Model._h.StaffData(Model.UniOvertimeReqMas.ManagerNo);
    var canApprove = ViewBag.CanApprove;
    var canDelete = ViewBag.CanDelete;
}

<br>

<!-- Header Actions -->
<div class="d-flex justify-content-between my-2">
    <div></div>
    <div>
        @if (canDelete)
        {
            <a href="/OverTime/Department/Delete/@<EMAIL><EMAIL><EMAIL>" 
               class="btn btn-danger after-confirm">
                <i class="fa fa-trash-alt"></i> @Model._("حذف")
            </a>
        }
    </div>
</div>

<!-- Timeline -->
<div class="card card-body mb-3">
    @Html.Raw(Model.renderOvertimeTimeline(Model.UniOvertimeReqMas.InYear+"-"+Model.UniOvertimeReqMas.InMailNo+"-"+Model.UniOvertimeReqMas.InDocSlNo+"-"+Model.UniOvertimeReqMas.Rel))
</div>

<!-- Page Title -->
<h3>@Model.UniOvertimeReqMas.OrderYear - @Model.UniOvertimeReqMas.OrderMonth</h3>

<!-- Statistics Summary -->
<div class="card shadow mb-3">
    <div class="card-body">
        <div class="row">
            <div class="col-lg-4">
                <div class="d-flex align-items-center justify-content-start">  
                    <i class="far fa-clock fa-2x mr-3"></i>
                    <h5><strong>@Model._l("العمل الاضافي"):</strong> @Model._h.FormatHour(ViewBag.ExtraSum) @Model._("ساعة")</h5>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="d-flex align-items-center justify-content-start"> 
                    <i class="fas fa-file-alt fa-2x mr-3"></i>
                    <h5><strong>@Model._l("طلبات العمل الاضافي"):</strong> @ViewBag.RequestCount</h5>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Left Column: Request Information & Logs -->
    <div class="col-md-6">
        <!-- Request Information -->
        <div class="card shadow mb-3">
            <table class="table table-borderless"> 
                <tr>
                    <td>@Model._("رقم الطلب")</td>
                    <td>@<EMAIL><EMAIL></td>
                </tr>
                <tr>
                    <td>@Model._("الموظف")</td>
                    <td>#@staff.EmpNo <br> @staff.EmpNameA</td>
                </tr>
                <tr>
                    <td>@Model._("الحالة")</td>
                    <td>
                        @Model.renderSatus(Model.UniOvertimeReqMas.ReqStatus)
                        @if(Model.UniOvertimeReqMas.CancelFlag == 1)
                        {
                            <span>@Model.UniOvertimeReqMas.SignRem</span>
                        }
                    </td>
                </tr>
                <tr>
                    <td>@Model._("المسؤول المباشر")</td>
                    <td>#@manager.EmpNo <br> @manager.EmpNameA</td>
                </tr>

                <tr>
                    <td>@Model._("الملف المرفق")</td>
                    <td>
                        @if(Model.UniOvertimeReqMas.FileGuid != null)
                        {
                            <a href="@Model._h.GetFile(Model.UniOvertimeReqMas.FileGuid)" target="_blank" class="btn btn-primary">
                                <i class="fas fa-download"></i> @Model._("تحميل")
                            </a>
                        }
                        else
                        {
                            <span class="text-muted">@Model._("لا يوجد ملف مرفق")</span>
                        }
                    </td>
                </tr>
            </table>
        </div>

        <!-- Activity Logs -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">@Model._("سجل النشاط")</h6>
            </div>
            <div class="card-body">
                <table class="table-sm table-borderless text-muted">
                    @foreach(var log in Model._h.GetLogs(Model.UniOvertimeReqMas.InYear+"-"+Model.UniOvertimeReqMas.InMailNo+"-"+Model.UniOvertimeReqMas.InDocSlNo,"Overtime"))
                    {
                        <tr>
                            <td><i class="far fa-clock"></i></td>
                            <td>@log.Rem</td>   
                            <td dir="ltr">@Model._dt(log.TimeStamp)</td>
                            <td>@Model._h.StaffData(log.UserId).EmpNameA</td>
                        </tr>
                    }
                </table>
            </div>
        </div>
    </div>

    <!-- Right Column: Overtime Details & Actions -->
    <div class="col-md-6">
        @if (canApprove)
        {
            <!-- Approval Form -->
                         <form action="~/OverTime/Department/Approve/@<EMAIL><EMAIL><EMAIL>" 
                  method="post" class="ajax">
                
                @await Html.PartialAsync("_OvertimeDetailsTable", Model)
                @await Html.PartialAsync("_CommentsSection", Model)
                
                <!-- Comment Input for Approval -->
                <div class="card shadow">
                    <div class="card-body">
                        <hr>
                        <textarea name="comment" class="form-control" rows="3" 
                                  placeholder='@Model._("اكتب ملاحظة . . .")'></textarea>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="mb-3">
                    <button class="btn btn-success   after-confirm mx-1 mb-1">
                       <i class="fas fa-check"></i> @Model._("موافقة")
                    </button>
                    <a href="#" data-toggle="modal" data-target="#department-manager-decline-modal" 
                       class="btn btn-danger   mx-1">
                      <i class="fas fa-times"></i> @Model._("رفض")
                    </a>
                </div>
            </form>
        }
        else
        {
            <!-- View Only Mode -->
            @await Html.PartialAsync("_OvertimeDetailsTable", Model)
            @if(Model.OvertimeComments.Count > 0){
                @await Html.PartialAsync("_CommentsSection", Model)
            }
            
            <!-- Standalone Comment Form -->
            <form action="~/OverTime/Comment/Create/@<EMAIL><EMAIL><EMAIL>" 
                  method="post" class="ajax">
                <div class="card shadow">
                    <div class="card-body">
                        <textarea name="comment" class="form-control" rows="3" 
                                  placeholder='@Model._("اكتب ملاحظة . . .")'></textarea>
                        <button class="btn btn-primary rounded-pill btn-sm mt-2">
                            <i class="fas fa-paper-plane"></i> @Model._("ارسال")
                        </button>
                    </div>
                </div>
            </form>
        }
    </div>
</div>

<!-- Fingerprint Records Modal -->
@await Html.PartialAsync("_FingerprintModal", Model)

<!-- Decline Modal -->
@await Html.PartialAsync("_DeclineModal", Model)

<!-- Update Hours Modal -->
@if (ViewBag.CanUpdate)
{
    @await Html.PartialAsync("_UpdateHoursModal", Model)
}

