@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel


<div class="d-flex justify-content-between my-2">
    <h3>
        @Model._("مدير دائرة الموارد البشرية")
    </h3>
    <div>
   
    </div>
</div>

<div class="card shadow">
    <table class="table " id="datatable">
        <thead class="bg-primary">
            <tr>
                <td>@Model._("رقم الطلب") </td>
                <td>@Model._("الموظف") </td>
                <td>@Model._("الشهر") </td>
                <td>@Model._("الساعات الكلية")</td>
                <td>@Model._("النوع")</td>
                <td>@Model._("الحالة")</td>
            </tr>
        </thead>

        <tbody>

    
            
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function(){
        createDatatable('#datatable',true, {
            ajax:"/OverTime/Department/Manager/Datatable",
            columns:[
                {"data":"id","title":"#"},
                {"data":"emp","title":"@Model._("الموظف")"},
                {"data":"createdAt","title":"@Model._("الشهر")"},
                {"data":"extraSum","title":"@Model._("الساعات")"},
                @* {"data":"extraAmountSum","title":"@Model._("Payable")"}, *@
               
                {"data":"estematedFlag","title":"@Model._("النوع")"},
                {"data":"status","title":"@Model._("الحالة")"},
            ]
        });
    })
</script>