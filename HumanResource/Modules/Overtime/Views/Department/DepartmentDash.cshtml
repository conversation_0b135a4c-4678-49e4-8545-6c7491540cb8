@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

<br>
<br>
<br>

<div class="row">
    <div class="col-md-3">
        <div class="card bg-primary shadow border-0">
     
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <h2>@Model._("All Requests") (@ViewBag.NewCount)</h2>
                    <i class="far fa-file-alt fa-3x"></i>
                </div>
            </div>
            <div class="card-footer">
                <a href="~/OverTime/Department/List" class="text-white"> @Model._("View") <i class="fas fa-arrow-left"> </i>  </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-primary shadow border-0">
     
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <h2>@Model._("HR manager") (@ViewBag.HrManagerCount)</h2>
                    <i class="far fa-file-alt fa-3x"></i>
                </div>
            </div>
            <div class="card-footer">
                <a href="~/OverTime/Department/Manager" class="text-white"> @Model._("View") <i class="fas fa-arrow-left"> </i>  </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-primary shadow border-0">
          
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <h2>@Model._("Audit")</h2>
                    <i class="far fa-file-chart-line fa-3x"></i>
                </div>
            </div>
            <div class="card-footer">
                <a href="~/OverTime/Department/Audit" class="text-white"> @Model._("View") <i class="fas fa-arrow-left"> </i>  </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-primary shadow border-0">
          
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <h2>@Model._("Report")</h2>
                    <i class="far fa-file-chart-line fa-3x"></i>
                </div>
            </div>
            <div class="card-footer">
                <a href="~/OverTime/Department/Report" class="text-white"> @Model._("View") <i class="fas fa-arrow-left"> </i>  </a>
            </div>
        </div>
    </div>

    
</div> 