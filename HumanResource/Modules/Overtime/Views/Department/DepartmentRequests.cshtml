@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel


<div class="d-flex justify-content-between my-2">
    <h3>
        @Model._("العمل الاضافي")
    </h3>
    <div>
        <a class="btn btn-link text-primary border border-priary mb-1" data-toggle="collapse" href="#report" role="button" aria-expanded="false" aria-controls="report"><i class="far fa-file-chart-line"></i></a>
        <a href="#" class="btn btn-primary " data-toggle="modal" data-target="#new-request-modal"><i class="fa fa-plus"></i> @Model._("طلب جديد")</a>
        <a href="#" class="btn btn-primary l" data-toggle="modal" data-target="#new-estimated-request-modal"><i class="fa fa-plus"></i> @Model._("العمل الاضافي التقديري")</a>
    </div>
</div>
<div class="collapse" id="report">
    <div class="row">
        @foreach (var reportCard in Model.OvrtimeDepartmentReport)
        {
            if (reportCard.Employee.EmpNo != 1018 && reportCard.Employee.EmpNo != 213)
            {
                <div class="col-md-2">
                    <div class="card shadow">
                        <div class="card-body text-center">

                            <samll>@reportCard.Employee.EmpNameA</samll>
                            <h2 class=" ">
                                @reportCard.Count
                            </h2>
                        </div>
                    </div>
                </div>
            }
        }

    </div>
</div>
<div class="card shadow">
    <div class="card-body">
         <div class="form-check-inline">
            <input type="radio" class="form-check-input" id="filter-needs-action" checked name="request-filter" value="needs-action">
            <label class="form-check-label" for="filter-needs-action">تحتاج إجراء</label>
        </div>
        <div class="form-check-inline">
            <input type="radio" class="form-check-input" id="filter-all" name="request-filter" value="all" >
            <label class="form-check-label" for="filter-all">الكل</label>
        </div>
       
    </div>
    <table class="table " id="datatable">
        <thead class="bg-primary">
            <tr>
                <td>@Model._("الرقم") </td>
                <td>@Model._("الموظف") </td>
                <td>@Model._("تاريخ الطلب") </td>
                <td>@Model._("الساعات ")</td>
                <td>@Model._("النوع")</td>
                <td>@Model._("الحالة")</td>
            </tr>
        </thead>

        <tbody>

            @* @foreach(var request in Model.OverTimeRequests){
            <tr>
            <td><a href="~/OverTime/View/@request.Id" class="popup" data-size="1000x720">#@request.Id</a></td>
            <td>
            @{
            var s1 = Model._h.StaffData(request.EmpNo);
            }
            #@s1.EmpNo
            <br>
            @s1.EmpNameA
            </td>
            <td>@request.ExtraSum</td>
            <td>@request.ExtraSum</td>
            <td>@request.ExtraAmountSum</td>
            <td>@request.Status</td>
            </tr>
            } *@

        </tbody>
    </table>
</div>

<div id="app">


    <form action="~/OverTime/Department/CreateEtimated" method="post" class="ajax" enctype="multipart/form-data">
        <div class="modal fade" id="new-estimated-request-modal" role="dialog" aria-labelledby="new-estimated-request-modalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="new-request-modalLabel">@Model._l("العمل الاضافي التقديري")</h3>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">

                        <div class="row">

                            <div class="col-md-6">
                                <div>
                                    <label for="">@Model._l("الموظف")</label>
                                    <select name="EmpNo" class="select2" required onchange="app.get_days_estimate()">
                                        <option value="0" selected disabled hidden>@Model._l("اختر الموظف")</option>
                                        @foreach (var staff in Model.VempDtls)
                                        {
                                            <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                        }
                                    </select>
                                </div>
                                <br>
                            </div>

                            <div class="col-md-6">
                                <div>
                                    <label for="">@Model._l("المسؤول المباشر")</label>
                                    <select name="ManagerNo" class="select2" required>
                                        <option value="0" selected disabled hidden>@Model._l("اختر الموظف")</option>
                                        @foreach (var staff in Model.VempDtls)
                                        {
                                            <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                        }
                                    </select>
                                </div>
                                <br>
                            </div>

                            <div class="col-md-6">
                                <div>
                                    <label for="">@Model._l("الشهر")</label>
                                    <input type="month" class="form-control" id="new-estimated-request-month" required @@change="get_days_estimate()">
                                </div>
                                <br>
                            </div>

                            <div class="col-md-12">
                                <div>
                                    <label for="">@Model._l("مرفق (اختياري)")</label>
                                    <input type="file" name="attachment" class="form-control" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <small class="form-text text-muted">@Model._l("يمكنك إرفاق ملف داعم للطلب (PDF, Word, صورة)")</small>
                                </div>
                                <br>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">

                                <h5>@Model._("الأيام المتاحة")</h5>

                                <table class="table table-sm table-striped table-hover border">

                                    <thead>
                                        <tr>

                                            <td>@Model._("الأيام")</td>
                                            <td>@Model._("الدخول")</td>
                                            <td class="bg-danger-300">@Model._("الخروج")</td>
                                            <td>@Model._("العمل الاضافي")</td>

                                        </tr>
                                    </thead>

                                    <tbody>

                                        <tr v-for="(day,index) in days_estimated_list">


                                            <td>{{_d(day.day)}}</td>
                                            <td>
                                                <div class="d-flex">
                                                    <div class="mx-1">
                                                        <label>الساعة</label>
                                                        <input 
                                                            type="number" 
                                                            class="form-control" 
                                                            :value="getHours(day.firstEntry)"
                                                            v-model="day.inH"
                                                            step="1" min="0" max="24">
                                                    </div>
                                                    <div class="mx-1">
                                                        <label>دقائق</label>
                                                        <input 
                                                            type="number" 
                                                            class="form-control form-control-sm" 
                                                            :value="getMinutes(day.firstEntry)" 
                                                            v-model="day.inI"
                                                            step="1" 
                                                            min="0" max="60">
                                                    </div>

                                                </div>
                                            </td>
                                            <td class="bg-danger-300"> 
                                                <div class="d-flex">
                                                    <div class="mx-1">
                                                        <label>الساعة</label>
                                                        <input 
                                                            type="number" 
                                                            class="form-control" 
                                                            :value="getHours(day.lastEntry)" 
                                                            v-model="day.outH" step="1" min="0" max="24">
                                                    </div>
                                                    <div class="mx-1">
                                                        <label>دقائق</label>
                                                        <input 
                                                            type="number" 
                                                            class="form-control" 
                                                            :value="getMinutes(day.lastEntry)"
                                                               v-model="day.outI"
                                                             step="1" min="0" max="60">
                                                    </div>

                                                </div>
                                            </td>
                                            <td >
                                                <div class="d-flex">
                                                    <div>
                                                        <button 
                                                            type="button" 
                                                            class="btn btn-sm btn-link text-dark"
                                                                @@click="estReCalculate(index)">
                                                            <i class="fas fa-calculator"></i>
                                                        </button>
                                                    </div>
                                                    <div class="mx-1">
                                                        <label>ساعات</label>
                                                        <input type="number" class="form-control" v-model="day.totalH" @@change="updateDiff(index)" @@keyup="updateDiff(index)" step="1" min="0" max="12">
                                                    </div>
                                                    <div class="mx-1">
                                                        <label>دقائق</label>
                                                        <input type="number" class="form-control" :name="'overtime[minutes]['+index+']'" @@change="updateDiff(index)" @@keyup="updateDiff(index)" v-model="day.totalI" step="1" min="0" max="60">
                                                    </div>

                                                </div>
                                                <input type="hidden" class="form-control" :name="'overtime[hours]['+index+']'" v-model="day.dif" step="1" min="0" max="12">
                                                <input type="hidden" class="form-control" :name="'overtime[day]['+index+']'" :value="_d(day.day)">
                                            </td>

                                        </tr>


                                        <tr v-show="days_estimated_list.length==0">
                                            <td colspan="5" class="text-center">@Model._("لا يوجد أيام متاحة للاختيار")</td>
                                        </tr>

                                    </tbody>

                                </table>

                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-felx">
                        <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("اغلاق")</button>
                        <button type="submit" class="btn btn-primary ">@Model._l("ارسال")</button>
                    </div>
                </div>
            </div>
        </div>
    </form>


    <form action="~/OverTime/Department/Create" method="post" class="ajax" enctype="multipart/form-data">
        <div class="modal fade" id="new-request-modal" role="dialog" aria-labelledby="new-request-modalLabel"
             aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="new-request-modalLabel">@Model._l("طلب جديد")</h3>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">

                        <div class="row">

                            <div class="col-md-6">
                                <div>
                                    <label for="">@Model._l("الموظف")</label>
                                    <select name="EmpNo" class="select2" required onchange="app.get_days()">
                                        <option value="0" selected disabled hidden>@Model._l("اختر الموظف")</option>
                                        @foreach (var staff in Model.VempDtls)
                                        {
                                            <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                        }
                                    </select>
                                </div>
                                <br>
                            </div>

                            <div class="col-md-6">
                                <div>
                                    <label for="">@Model._l("المسؤول المباشر")</label>
                                    <select name="ManagerNo" class="select2" required>
                                        <option value="0" selected disabled hidden>@Model._l("اختر الموظف")</option>
                                        @foreach (var staff in Model.VempDtls)
                                        {
                                            <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                        }
                                    </select>
                                </div>
                                <br>
                            </div>

                            <div class="col-md-6">
                                <div>
                                    <label for="">@Model._l("الشهر")</label>
                                    <input type="month" class="form-control" id="new-request-month" required @@change="get_days()">
                                </div>
                                <br>
                            </div>

                            <div class="col-md-12">
                                <div>
                                    <label for="">@Model._l("مرفق (اختياري)")</label>
                                    <input type="file" name="attachment" class="form-control" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <small class="form-text text-muted">@Model._l("يمكنك إرفاق ملف داعم للطلب (PDF, Word, صورة)")</small>
                                </div>
                                <br>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-12">

                                <h5>@Model._("الأيام المتاحة")</h5>

                                <table class="table table-sm table-striped table-hover border">

                                    <thead>
                                        <tr>
                                            <td></td>
                                            <td>@Model._("الأيام")</td>
                                            @*<td>@Model._("Real houers")</td>*@
                                            <td>@Model._("العمل الاضافي")</td>

                                        </tr>
                                    </thead>

                                    <tbody>

                                        <tr v-for="(day,index) in days_list">
                                            <td>
                                                <input type="checkbox" name="days" :value="_d(day.day)">
                                            </td>
                                            <td>{{_d(day.day)}}</td>
                                            @*<td>{{day.hours}}</td>*@
                                            <td>{{day.extra}}</td>

                                        </tr>


                                        <tr v-show="days_list.length==0">
                                            <td colspan="5" class="text-center">@Model._("لا يوجد أيام متاحة للاختيار")</td>
                                        </tr>

                                    </tbody>

                                </table>

                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-felx">
                        <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("اغلاق")</button>
                        <button type="submit" class="btn btn-primary ">@Model._l("ارسال")</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>

    let app = new Vue({
        el: "#app",
        data: {
            days_list: [],
            days_estimated_list: [],
            month: ""
        },
        methods: {
            get_days() {

                let job = this;

                var inputDate = new Date($("#new-request-month").val());

                var year = inputDate.getFullYear();
                var month = inputDate.getMonth() + 1;

                var empno = $("#new-request-modal select[name=EmpNo]").val();

                if (empno > 1) {

                    $.get("/OverTime/GetOvertTimeDaysEmp?empno=" + empno + "&year=" + year + "&month=" + month, function (data) {

                        job.days_list = data.data
                    }, 'json');
                }


            },

            get_days_estimate() {

                let job = this;

                var inputDate = new Date($("#new-estimated-request-month").val());

                var year = inputDate.getFullYear();
                var month = inputDate.getMonth() + 1;

                var empno = $("#new-estimated-request-modal select[name=EmpNo]").val();

                if (empno > 1) {

                    $.get("/OverTime/GetOvertTimeDaysEstimatedEmp?empno=" + empno + "&year=" + year + "&month=" + month, function (data) {

                        job.days_estimated_list = data.data
                        
                        for (var i=0; i<job.days_estimated_list.length; i++){
                            job.days_estimated_list[i].inI = job.getMinutes(job.days_estimated_list[i].firstEntry);
                            job.days_estimated_list[i].inH = job.getHours(job.days_estimated_list[i].firstEntry);
                            job.days_estimated_list[i].outI = job.getMinutes(job.days_estimated_list[i].lastEntry);
                            job.days_estimated_list[i].outH = job.getHours(job.days_estimated_list[i].lastEntry);

                            dif = job.estCalculate(job.days_estimated_list[i]);

                            job.days_estimated_list[i].totalH = 0;
                            job.days_estimated_list[i].totalI = 0;
                            //job.days_estimated_list[i].dif = dif.dif;

                        }
                        

                            console.log(job.days_estimated_list)

                    }, 'json');
                }
            },
            estCalculate(day){

                startTotal = parseFloat(day.inH) * 60 + parseFloat(day.inI);
                endTotal = parseFloat(day.outH) * 60 + parseFloat(day.outI);

                dif = endTotal - startTotal;

                if (dif<=0){

                }
                console.log(dif)
                
                hours = 0;
                minutes = 0;

                hours = Math.floor(dif/60);
                minutes = dif % 60;


                return{
                    hours: hours,
                    minutes: minutes,
                    dif: parseFloat(dif/60).toFixed(2)
                }


                
            },

            estReCalculate(index) {

                let job = this;

                dif = this.estCalculate(job.days_estimated_list[index]);

                job.days_estimated_list[index].totalH = dif.hours;
                job.days_estimated_list[index].totalI = dif.minutes;
                job.days_estimated_list[index].dif = dif.dif;

                this.$set(this.days_estimated_list, index, {
                    ...this.days_estimated_list[index],
                    dif: dif.dif

                })

                console.log(job.days_estimated_list)
            },
            updateDiff(index) {

               

                var newdif = parseFloat(this.days_estimated_list[index].totalH) + parseFloat(parseFloat(this.days_estimated_list[index].totalI) / 60);

                newdif = parseFloat(newdif).toFixed(2);

                this.$set(this.days_estimated_list, index, {
                    ...this.days_estimated_list[index],
                    dif: newdif

                })
            },





            _d(date) {
                return window._d(date);
            },
            _ddd(date) {
                return window._ddd(date);
            },
            _dt(date) {
                const notime = "0001-01-01T00:00:00";

                if (typeof date === 'string') {
                    if (date === notime) {
                        return "--";
                    }
                }

                return window._dt(date)
            },
            getMinutes(date){
                const notime = "0001-01-01T00:00:00";

                if (typeof date === 'string') {
                    if (date === notime) {
                        return 0;
                    }
                }

                timePart = date.split("T")[1];
                const [h, i] = timePart.split(":");

                return i;

            },

            getHours(date) {
                const notime = "0001-01-01T00:00:00";

                if (typeof date === 'string') {
                    if (date === notime) {
                        return 0;
                    }
                }

                timePart = date.split("T")[1];
                const [h, i] = timePart.split(":");

                return h;

            }
        },

    });


</script>

<script>
    $(document).ready(function () {
        var table = createDatatable('#datatable', true, {
            ajax: {
                url: "/OverTime/Department/Datatable",
                type: "POST",
                data: function(d) {
                    d.from = "@Model.Page.Filter.DateFrom";
                    d.to = "@Model.Page.Filter.DateTo";
                    d.actionFilter = $('input[name="request-filter"]:checked').val();
                }
            },
            columns: [
                { "data": "id", "title": "@Model._("الرقم")" },
                { "data": "emp", "title": "@Model._("الموظف")" },
                { "data": "createdAt", "title": "@Model._("تاريخ الطلب")" },
                { "data": "extraSum", "title": "@Model._("الساعات الكلية")" },
                { "data": "estematedFlag", "title": "@Model._("النوع")" },
                { "data": "status", "title": "@Model._("الحالة")" },
            ]
        });

        // Handle radio button filtering
        $('input[name="request-filter"]').on('change', function() {
            // Reload the datatable with the new filter
            table.ajax.reload();
        });
    })
</script>
