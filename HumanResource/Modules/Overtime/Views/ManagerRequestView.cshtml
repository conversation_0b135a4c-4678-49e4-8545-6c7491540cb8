@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@{
    @* Layout = Model.Page.Layout; *@
    var staff = Model._h.StaffData(Model.UniOvertimeReqMas.EmpNo);
    var manager = Model._h.StaffData(Model.UniOvertimeReqMas.ManagerNo);
    var canApprove = Model.UniOvertimeReqMas.CancelFlag == 0 && (Model.UniOvertimeReqMas.SignAuthCode == 0 || Model.UniOvertimeReqMas.SignAuthCode == null);
}


<div>
    مسار الطلب
    <div class="card card-body">
        @Html.Raw(Model.renderOvertimeTimeline(Model.UniOvertimeReqMas.InYear+"-"+Model.UniOvertimeReqMas.InMailNo+"-"+Model.UniOvertimeReqMas.InDocSlNo+"-"+Model.UniOvertimeReqMas.Rel))
    </div>
    <br>
</div>
<div class="row">
    <!-- Request Information Card -->
    <div class="col-md-6">
        <div class="card shadow">
            <table class="table table-borderless"> 
                <tr>
                    <td>@Model._("الرقم")</td>
                    <td>@<EMAIL><EMAIL></td>
                </tr>
                <tr>
                    <td>@Model._("الموظف")</td>
                    <td>#@staff.EmpNo <br> @staff.EmpNameA</td>
                </tr>
                <tr>
                    <td>@Model._("الحالة")</td>
                    <td>@Model.renderSatus(Model.UniOvertimeReqMas.ReqStatus)</td>
                </tr>
                <tr>
                    <td>@Model._("المدير المباشر")</td>
                    <td>#@manager.EmpNo <br> @manager.EmpNameA</td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Overtime Days and Actions -->
    <div class="col-md-6">
        @if (canApprove)
        {
            
            <form action="~/OverTime/Manager/Approve/@<EMAIL><EMAIL><EMAIL>" method="post" class="ajax">
                
                
                <div class="card shadow">
                    <table class="table table-sm">
                        <thead class="bg-primary">
                            <tr>
                                <td></td>
                                <td>@Model._("اليوم")</td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var day in Model.UniOvertimeReqPreDtls)
                            {
                                <tr class="@(day.IsApproved ? "bg-primary" : "")">
                                    <td>
                                        @if (day.IsApproved)
                                        {
                                            <i class="fas fa-check"></i>
                                        }
                                        else
                                        {
                                            <input type="checkbox" name="days" value="@Model._d(day.OtDate)">
                                        }
                                    </td>
                                    <td>
                                        @(day.IsApproved ? Model._d(day.OtDate) : Model._ddd(day.OtDate))
                                    </td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="2">
                                    <div class="alert alert-warning">
                                        @Model._("يجب عليك اختيار الأيام التي تريد التوصية بها")
                                    </div>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Comments Section for Approval -->
                <div class="card shadow">
                    <div class="card-body">
                        @foreach (var comment in Model.OvertimeComments.Where(c => !string.IsNullOrWhiteSpace(c.Comment)))
                        {
                            var createdBy = Model._h.StaffData(comment.CreatedBy);
                            <div class="col-7 @(comment.CreatedBy == Model.Profile.EmpNo.Value ? "mr-auto" : "ml-auto")">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user"></i> 
                                    <span class="mx-2 mt-2">@createdBy.EmpNo - @createdBy.EmpNameA</span>
                                </div>
                                <p class="text-muted m-0 py-0" dir="ltr">
                                    <small>@Model._dt(comment.TimeStamp)</small>
                                </p>
                                <p class="p-2 border rounded">@comment.Comment</p>
                            </div>
                        }
                        
                        <hr>
                        <textarea name="comment" class="form-control" rows="3" placeholder='@Model._("اكتب ملاحظة . . .")'></textarea>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="">
                    <button class="btn btn-success  after-confirm mx-1 mb-1">
                        <i class="fas fa-check"></i>
                        @Model._("اعتماد")
                    </button>
                    <a href="#" data-toggle="modal" data-target="#department-manager-decline-modal" class="btn btn-danger  mx-1">
                        <i class="fas fa-times"></i>
                        @Model._("رفض")
                    </a>
                </div>
            </form>
        }
        else
        {
            <!-- View Only Mode -->
            
            <!-- Overtime Days Table for View -->
            <div class="card shadow">
                <table class="table table-sm">
                    <thead class="bg-primary">
                        <tr>
                            <td></td>
                            <td>@Model._("اليوم")</td>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var day in Model.UniOvertimeReqPreDtls)
                        {
                            <tr class="@(day.IsApproved ? "" : "")">
                                <td>
                                    @if (day.IsApproved)
                                    {
                                        <i class="fas fa-check"></i>
                                    }
                                </td>
                                <td>@Model._d(day.OtDate)</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Comments Section for View -->
            <div class="card shadow">
                <div class="card-header">
                    @Model._("الملاحظات")
                </div>
                <div class="card-body">
                    @foreach (var comment in Model.OvertimeComments.Where(c => !string.IsNullOrWhiteSpace(c.Comment)))
                    {
                        var createdBy = Model._h.StaffData(comment.CreatedBy);
                        <div class="col-7 @(comment.CreatedBy == Model.Profile.EmpNo.Value ? "mr-auto" : "ml-auto")">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user"></i> 
                                <span class="mx-2 mt-2">@createdBy.EmpNo - @createdBy.EmpNameA</span>
                            </div>
                            <p class="text-muted m-0 py-0" dir="ltr">
                                <small>@Model._dt(comment.TimeStamp)</small>
                            </p>
                            <p class="p-2 border rounded">@comment.Comment</p>
                        </div>
                    }
                    
                    
                    <!-- Standalone Comment Form -->
                    <form action="~/OverTime/Comment/Create/@<EMAIL><EMAIL><EMAIL>" method="post" class="ajax">
                        <textarea name="comment" class="form-control" rows="3" placeholder='@Model._("اكتب ملاحظة . . .")'></textarea>
                        <button class="btn btn-primary  mt-2">
                            <i class="fas fa-paper-plane"></i> @Model._("ارسال")
                        </button>
                    </form>
                </div>
            </div>
        }
    </div>
</div>

<!-- Decline Modal -->
<form action="~/OverTime/Manager/Decline/@<EMAIL><EMAIL><EMAIL>" class="ajax" method="post">
    <div class="modal fade" id="department-manager-decline-modal" role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="department-manager-decline-modalLabel">
                        @Model._l("رفض بواسطة المسؤول المباشر")
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>   
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظة الرفض")</label>
                    <textarea name="Note" class="form-control"></textarea>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">
                        @Model._l("Cancel")
                    </button>
                    <button type="submit" class="btn btn-primary ">
                        @Model._l("ارسال")
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

