@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@{
    Layout = Model.Page.Layout;
}


<div>
    <div class="card card-body">
        @Html.Raw(Model.renderOvertimeTimeline(Model.UniOvertimeReqMas.InYear+"-"+Model.UniOvertimeReqMas.InMailNo+"-"+Model.UniOvertimeReqMas.InDocSlNo+"-"+Model.UniOvertimeReqMas.Rel))
    </div>
</div>
<h3>@Model.UniOvertimeReqMas.OrderYear - @Model.UniOvertimeReqMas.OrderMonth</h3>

<div class="card shadow">

    <div class="card-body">
        <div class="row">

            <div class="col-lg-4">
                <div class=" d-flex align-items-center justify-content-start">
                    <i class="far fa-clock fa-2x mr-3"></i>
                    <h5><strong>@Model._l("العمل الاضافي"):</strong> @ViewBag.ExtraSum @Model._("ساعة")</h5>
                </div>
            </div>



            <div class="col-lg-4">

                <div class=" d-flex align-items-center justify-content-start">
                    <i class="fas fa-file-alt fa-2x mr-3"></i>
                    <h5><strong>@Model._l("طلبات العمل الاضافي"):</strong> @ViewBag.RequestCount </h5>
                </div>

            </div>

        </div>
    </div>
</div>


<div class="row">
    <div class="col-md-6">
        <div class="card shadow">
            <table class="table table-borderless">
                <tr>
                    <td>
                        @Model._("رقم الطلب")
                    </td>
                    <td>
                        @<EMAIL><EMAIL>
                    </td>
                </tr>
                <tr>
                    <td>
                        @Model._("الموظف")
                    </td>
                    <td>
                        @{
                            var s2 = Model._h.StaffData(Model.UniOvertimeReqMas.EmpNo);
                        }

                        #@s2.EmpNo <br> @s2.EmpNameA
                    </td>
                </tr>

                <tr>
                    <td>
                        @Model._("الحالة")
                    </td>
                    <td>
                        @Model.renderSatus(Model.UniOvertimeReqMas.ReqStatus)

                        @if (Model.UniOvertimeReqMas.CancelFlag == 1)
                        {
                            <span>@Model.UniOvertimeReqMas.SignRem</span>
                        }
                    </td>
                </tr>

                <tr>
                    <td>
                        @Model._("المسؤول المباشر")
                    </td>
                    <td>
                        @{
                            var s1 = Model._h.StaffData(Model.UniOvertimeReqMas.ManagerNo);
                        }

                        #@s1.EmpNo <br> @s1.EmpNameA
                    </td>
                </tr>
                <tr>
                    <td>@Model._("الملف المرفق")</td>
                    <td>
                        @if(Model.UniOvertimeReqMas.FileGuid != null)
                        {
                            <a href="@Model._h.GetFile(Model.UniOvertimeReqMas.FileGuid)" target="_blank" class="btn btn-primary">
                                <i class="fas fa-download"></i> @Model._("تحميل")
                            </a>
                        }
                        else
                        {
                            <span class="text-muted">@Model._("لا يوجد ملف مرفق")</span>
                        }
                    </td>
                </tr>
            </table>
        </div>

     
    </div>
    <div class="col-md-6">


        @if (Model.UniOvertimeReqMas.SignAuthCode > 0 && Model.UniOvertimeReqMas.UserId != null && Model.UniOvertimeReqMas.CancelFlag == 0 && Model.UniOvertimeReqMas.AuditDate == null && Model.UniOvertimeReqMas.AuditStat == 0 && Model.UniOvertimeReqMas.HrManagerSign < 1)
        {
            <form action="~/OverTime/Dg/Approve/@<EMAIL><EMAIL><EMAIL>" method="post" class="ajax">

                <div class="card shadow">
                    <table class="table table-sm">
                        <thead class="bg-primary">
                            <tr>
                                <td>@Model._("اليوم")</td>
                                <td>
                                    @Model._("الساعات")

                                    @if (Model.UniOvertimeReqMas.EstimatedFlag == 1)
                                    {
                                        <span class="badge badge-danger">@Model._("تقديري") </span>
                                    }
                                </td>
                            </tr>
                        </thead>
                        <tbody>


                            @foreach (var day in Model.UniOvertimeReqDtls)
                            {

                                <tr>
                                    <td>
                                        <a href="#" onclick="getFpRecords('@Model._d(day.OtDate)')"> @Model._ddd(day.OtDate)</a>
                                    </td>
                                    <td>
                                        @Model._h.FormatHour(day.OtDuration)
                                    </td>

                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr>

                                <td>@Model._("الاجمالي")</td>
                                <td>@Model._h.FormatHour(Model.UniOvertimeReqMas.ExtraSum.Value)</td>
                             

                            </tr>
                        </tfoot>

                    </table>
                </div>

                <div class="card shadow">
                    <div class="card-body">

                        @foreach (var comment in Model.OvertimeComments)
                        {
                            var createdBy = Model._h.StaffData(comment.CreatedBy);

                            <div class="col-7 @(comment.CreatedBy==Model.Profile.EmpNo.Value?"mr-auto":"ml-auto") ">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user"></i> <span class="mx-2 mt-2">@createdBy.EmpNo - @createdBy.EmpNameA</span>
                                </div>
                                <p class="text-muted m-0 py-0 " dir="ltr"><small>@Model._dt(@comment.TimeStamp)</small></p>
                                <p class="p-2 border rounded">
                                    @comment.Comment
                                </p>
                            </div>
                        }

                        <hr>

                        <textarea name="comment" class="form-control" rows="3" placeholder='@Model._("اكتب ملاحظة . . .")'></textarea>

                    </div>
                </div>

                <div class="">

                    <button class="btn btn-success  after-confirm mx-1 "><i class="fas fa-check"></i> @Model._("موافقة")</button>

                    <a href="#" data-toggle="modal" data-target="#return-modal" class="btn btn-warning  "><i class="fas fa-undo"></i> @Model._("ارجاع ")</a>
                    <a href="#" data-toggle="modal" data-target="#department-manager-decline-modal" class="btn btn-danger  "><i class="fas fa-times"></i> @Model._("مرفوض")</a>



                </div>

            </form>
        }
    </div>


</div>



<form action="~/OverTime/Dg/Decline/@<EMAIL><EMAIL><EMAIL>" class="ajax" method="post">
    <div class="modal fade" id="department-manager-decline-modal" role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("Declined by DG")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظة المرفوض")</label>
                    <textarea name="Note" class="form-control"></textarea>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("مرفوض")</button>
                </div>
            </div>
        </div>
    </div>
</form>
<form action="~/OverTime/Dg/Return/@<EMAIL><EMAIL><EMAIL>" class="ajax" method="post">
    <div class="modal fade" id="return-modal" role="dialog" aria-labelledby="return-modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header ">

                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظة الارجاع")</label>
                    <textarea name="comment" class="form-control"></textarea>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("ارجاع")</button>
                </div>
            </div>
        </div>
    </div>
</form>


<!-- Fingerprint Records Modal -->
@await Html.PartialAsync("_FingerprintModal", Model)