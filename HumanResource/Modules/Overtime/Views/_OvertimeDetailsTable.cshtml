@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@{
    var canUpdate = ViewBag.CanUpdate;
}

<div class="card shadow">
    @if (canUpdate)
    {
        <div class="card-header d-flex justify-content-between align-items-center">
            <span>@Model._("تفاصيل العمل الاضافي")</span>
            <button class="btn btn-sm btn-primary" type="button" onclick="openUpdateModal()">
                <i class="fas fa-edit"></i> @Model._("تحديث الساعات")
            </button>
        </div>
    }
    
    <table class="table table-sm">
        <thead class="bg-primary">
            <tr>
                <td>@Model._("اليوم")</td>
                <td>
                    @Model._("الساعات")
                    @if(Model.UniOvertimeReqMas.EstimatedFlag == 1)
                    {
                        <span class="badge badge-danger">@Model._("تقديري")</span>
                    }
                </td>
               
                    <td></td>
                
            </tr>
        </thead>
        <tbody>
            @foreach(var day in Model.UniOvertimeReqDtls)
            {
                <tr>
                    <td>
                        @Model._ddd(day.OtDate)
                    </td>
                    <td>
                        @Model._h.FormatHour(day.OtDuration)
                    </td>
                    
                        <td>
                            <button class="btn btn-sm btn-secondary" type="button" onclick="getFpRecords('@Model._d(day.OtDate)')">
                                <i class="fas fa-fingerprint"></i> @Model._("عرض")
                            </button>
                        </td>
                    
                    
                </tr>
            }
        </tbody>
        <tfoot>
            <tr>
                <td>@Model._("الساعات الكلية")</td>
                <td>@Model._h.FormatHour(Model.UniOvertimeReqMas.ExtraSum.Value)</td>
                @if (canUpdate)
                {
                    <td></td>
                }
            </tr>
        </tfoot>
    </table>
</div> 