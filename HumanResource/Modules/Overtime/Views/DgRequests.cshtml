@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel
@Html.Partial("_DgAppTabs")

<div class="card shadow">
    <table class="table " id="datatable">
        <thead class="bg-primary">
            <tr>
                <td>@Model._("الرقم") </td>
                <td>@Model._("الموظف") </td>
                <td>@Model._("تاريخ الطلب") </td>
                <td>@Model._("الساعات ")</td>
            </tr>
        </thead>

        <tbody>

     
            
        </tbody>
    </table>
</div>


<script>
    $(document).ready(function(){
        createDatatable('#datatable',true, {
            ajax: "/OverTime/Dg/Datatable?<EMAIL>&to=@Model.Page.Filter.DateTo",
            columns:[
                {"data":"id","title":"الرقم"},
                {"data":"emp","title":"@Model._("الموظف")"},
                {"data":"createdAt","title":"@Model._("تاريخ الطلب")"},
                {"data":"extraSum","title":"@Model._("الساعات ")"},

            ]
        });
    })
</script>
