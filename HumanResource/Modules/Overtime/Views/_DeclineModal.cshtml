@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

<form action="~/OverTime/Department/Decline/@<EMAIL><EMAIL><EMAIL>" 
      class="ajax" method="post">
    <div class="modal fade" id="department-manager-decline-modal" role="dialog" 
         aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="department-manager-decline-modalLabel">
                        @Model._l("رفض بواسطة الإجازات")
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("سبب الرفض")</label>
                    <textarea name="Note" class="form-control" required></textarea>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " 
                            data-dismiss="modal">
                        @Model._l("Cancel")
                    </button>
                    <button type="submit" class="btn btn-primary ">
                        @Model._l("ارسال")
                    </button>
                </div>
            </div>
        </div>
    </div>
</form> 