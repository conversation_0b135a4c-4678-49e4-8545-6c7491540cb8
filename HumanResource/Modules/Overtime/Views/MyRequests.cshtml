﻿@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel


@{

  var list = Model.OvertimeReqMass;

  //if(Model.Profile.EmpNo>1000){
  //   list = Model.ConOvertimeReqMass;
  //}

}

<div class="d-flex justify-content-between my-2">
    <h3>
        @Model._("العمل الاضافي")
    </h3>
    <div>
        <a href="#" class="btn btn-primary" data-toggle="modal" data-target="#new-request-modal"><i class="fa fa-plus"></i> @Model._("طلب جديد")</a>
    </div>
</div>


<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <table class="table datatable">
                <thead class="bg-primary">
                    <tr>
                        <td>@Model._("الرقم") </td>
                        <td>@Model._("الشهر")</td>
                        <td>@Model._("الحالة")</td>

                  
                      
                    </tr>
                </thead>

                <tbody>

                    @foreach (var request in Model.UniOvertimeReqMass)
                    {
                        <tr>
                            <td><a href="~/OverTime/My/View/@<EMAIL><EMAIL><EMAIL>" >@<EMAIL><EMAIL></a></td>
                            <td>@<EMAIL></td>

                            <td>

                              @if(Model.payStatus(request.InYear + "-" + request.InMailNo + "-" + request.InDocSlNo + "-" + request.Rel)!=0){
                             
                                @if(Model.payStatus(request.InYear + "-" + request.InMailNo + "-" + request.InDocSlNo + "-" + request.Rel) == 2){
                                  <span class="badge badge-success rounded-pill px-2">تم الصرف</span>
                                }else{
                                  <span class="badge badge-warning rounded-pill px-2">صرف جزئي</span>
                                }

                              }else{
                                <span>@Model.renderSatus(request.ReqStatus)</span>
                              }

                            </td>

                           
                            
                            
                          
                        </tr>
                    }

                </tbody>
            </table>
        </div>

    </div>
    <!-- <div class="col-md-6">
        <div class="card shadow">

            <div class="card-header">
                <h5 class="card-title"> @Model._l("Payments")</h5>
            </div>


            <div class="table-responsive">
                <table class="table datatable table-sm" showlength="true" length="10,20,30">
                    <thead>
                        <tr>
                            <th>@Model._l("Year")</th>
                            <th>@Model._l("Month")</th>
                            <th>@Model._l("Total")</th>

                        </tr>
                    </thead>
                    <tbody>

                        @foreach (var item in Model.OvertimePays)
                        {

                            <tr>

                                <td>
                                    @item.PayYear
                                </td>
                                <td>
                                    @item.PayMonth
                                </td>
                                <td>
                                    @item.Amt
                                </td>

                            </tr>

                        }
                    </tbody>

                </table>
            </div>

        </div>
    </div> -->
    
</div>


<form action="~/OverTime/My/Create" method="post" class="ajax" id="app">
  <div class="modal fade" id="new-request-modal" role="dialog" aria-labelledby="new-vehicle-modalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._("طلب جديد")</h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">

          <div class="row">
            <div class="col-md-6">
              <div>
                <label for="">@Model._("السؤول المباشر")</label>
                <select name="ManagerNo" class="select2"  required>
                  <option value="0" selected disabled hidden>@Model._("اختر الموظف")</option>
                  @foreach (var staff in Model._h.Managers(Model.Auth.EmpNo))
                  {
                    <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                  }
                </select>
              </div>
              <br>
            </div>

            <div class="col-md-6">
              <div>
                <label for="">@Model._("الشهر")</label>
                <input type="month" class="form-control" id="new-request-month" required @@change="get_days()">
              </div>
              <br>
            </div>
          </div>
          <div class="row">
            <div class="col-lg-12">
              
              <h5>@Model._("الايام المتاحة")</h5>

              <table class="table table-sm table-striped table-hover border">

                <thead>
                  <tr>
                    <td></td>
                    <td>@Model._("الايام")</td>
                 
                    
                  </tr>
                </thead>

                <tbody>

                  <tr v-for="(day,index) in days_list">
                    <td>
                      <input type="checkbox" name="days" :value="_d(day.day)">
                    </td>
                    <td>{{_d(day.day)}}</td>
                  
               
                  </tr>


                  <tr v-show="days_list.length==0">
                    <td colspan="5" class="text-center">@Model._("لا يوجد ايام متاحة للاختيار")</td>
                  </tr>

                </tbody>

              </table>

            </div>
          </div>
        </div>
        <div class="modal-footer ">
          <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._("اغلاق")</button>
          <button type="submit" class="btn btn-primary "><i class="fa fa-save"></i> @Model._("ارسال")</button>
        </div>
      </div>
    </div>
  </div>
</form>


<script>

  let app = new Vue({
    el:"#app",
    data:{
      days_list:[],
      month:""
    },
    methods:{
      get_days(){

        let job = this;

        var inputDate = new Date($("#new-request-month").val());

        var year = inputDate.getFullYear();
        var month = inputDate.getMonth()+1;

        $.get("/OverTime/GetOvertTimeDays?year="+year+"&month="+month,function(data){
          job.days_list=data.data
        },'json');

      },
      _d(date){
        return window._d(date);
      }
    },

  })


</script>