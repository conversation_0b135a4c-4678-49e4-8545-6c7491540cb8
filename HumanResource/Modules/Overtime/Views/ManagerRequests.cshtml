﻿@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@Html.Partial("_MangerAppTabs")

<div class="card shadow">
    <table class="table datatable">
        <thead class="bg-primary">
            <tr>
                <td>@Model._("الرقم") </td>
                <td>@Model._("الموظف") </td>
                <td>@Model._("الشهر")</td>
            
                <td>@Model._("الحالة")</td>
            </tr>
        </thead>

        <tbody>

          @foreach(var request in Model.UniOvertimeReqMass){
            <tr>
                <td><a href="~/OverTime/Manager/View/@<EMAIL><EMAIL><EMAIL>">@<EMAIL><EMAIL></a></td>
                <td>
                  @{
                    var s1 = Model._h.Staff<PERSON>(request.EmpNo);
                  }
                  #@s1.EmpNo.Value
                  <br>
                  @s1.EmpNameA
                </td>
                <td>@<EMAIL></td>
           
                <td>
                  @Model.renderSatus(request.ReqStatus, new int[1])
               

                </td>
            </tr>
          }
            
        </tbody>
    </table>
</div>

