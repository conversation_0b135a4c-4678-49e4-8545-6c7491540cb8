﻿@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel
@removeTagHelper Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    ViewData["Title"] = "تقرير العمل الاضافي";
}

<style>
    .chart-container {
        position: relative;
        height: 400px;
        margin-bottom: 30px;
        background-color: #fff;
        border-radius: 0.3rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        padding: 15px;
    }

    .summary-box {
        background-color: #fff;
        border-radius: 0.3rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s;
    }

    .summary-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }

    .summary-number {
        font-size: 1.8rem;
        font-weight: bold;
        color: #3f51b5;
    }

    .filter-section {
        background-color: #fff;
        border-radius: 0.3rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        padding: 20px;
        margin-bottom: 30px;
    }

    .report-table {
        background-color: #fff;
        border-radius: 0.3rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        padding: 20px;
    }

    .chart-tabs .nav-link {
     
        padding: 0.75rem 1.5rem;
        font-weight: 500;
    }

    .chart-tabs .nav-link.active {
        background-color: var(--primary);
        color: white;
    }

    .chart-type-toggle .btn {
        padding: 0.375rem 1rem;
    }
</style>

<div class="container-fluid">
    <!-- Summary Section -->
    <div class="row">
        <div class="col-md-4">
            <div class="summary-box text-center">
                <div><i class="fas fa-clock fa-2x mb-2 text-primary"></i></div>
                <div class="summary-number">@ViewBag.TotalOvertime.ToString("N2")</div>
                <div class="text-muted">إجمالي ساعات العمل الإضافي</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="summary-box text-center">
                <div><i class="fas fa-money-bill-wave fa-2x mb-2 text-success"></i></div>
                <div class="summary-number">@Model._amount(ViewBag.TotalAmount)</div>
                <div class="text-muted">إجمالي مبلغ العمل الإضافي</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="summary-box text-center">
                <div><i class="fas fa-file-alt fa-2x mb-2 text-info"></i></div>
                <div class="summary-number">@ViewBag.RequestCount</div>
                <div class="text-muted">عدد طلبات العمل الإضافي</div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <h4 class="mb-4"><i class="fas fa-filter"></i> خيارات التصفية</h4>
        <form id="reportForm" method="get" action="/OverTime/Report">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>المديرية العامة</label>
                        <select class="form-control" id="Dg" name="Dg" onchange="updateDepartments()">
                            <option value="0">-- الكل --</option>
                            @foreach (var dg in ViewBag.DgList)
                            {
                                <option value="@dg.DgCode" @(ViewBag.Dg == dg.DgCode ? "selected" : "")>@dg.DgDespA</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>الدائرة</label>
                        <select class="form-control" id="Dept" name="Dept" onchange="updateEmployees()">
                            <option value="0">-- الكل --</option>
                            @foreach (var dept in ViewBag.DeptList)
                            {
                                <option value="@dept.DeptCode" @(ViewBag.Dept == dept.DeptCode ? "selected" : "")>@dept.DeptDespA</option>
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>الموظف</label>
                        <select class="form-control" id="EmpNo" name="EmpNo">
                            <option value="0">-- الكل --</option>
                            @if (ViewBag.EmployeeList != null)
                            {
                                foreach (var emp in ViewBag.EmployeeList)
                                {
                                    <option value="@emp.EmpNo" @(ViewBag.EmpNo == emp.EmpNo ? "selected" : "")>@emp.EmpNameA</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>حالة الصرف</label>
                        <select class="form-control" id="PayStatus" name="PayStatus">
                            <option value="-1" @(ViewBag.PayStatus == -1 ? "selected" : "")>-- الكل --</option>
                            <option value="0" @(ViewBag.PayStatus == 0 ? "selected" : "")>لم يتم الصرف</option>
                            <option value="1" @(ViewBag.PayStatus == 1 ? "selected" : "")>تم الصرف جزئياً</option>
                            <option value="2" @(ViewBag.PayStatus == 2 ? "selected" : "")>تم الصرف بالكامل</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>من تاريخ</label>
                        <input type="date" class="form-control" id="from" name="from" value="@ViewBag.FromDate">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>إلى تاريخ</label>
                        <input type="date" class="form-control" id="to" name="to" value="@ViewBag.ToDate">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>نوع العرض</label>
                        <select class="form-control" id="ViewType" name="ViewType">
                            <option value="summary" @(ViewBag.ViewType == "summary" ? "selected" : "")>ملخص شهري</option>
                            <option value="byDepartment" @(ViewBag.ViewType == "byDepartment" ? "selected" : "")>حسب الدائرة</option>
                            <option value="byEmployee" @(ViewBag.ViewType == "byEmployee" ? "selected" : "")>حسب الموظف</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>نوع الرسم البياني</label>
                        <select class="form-control" id="ChartType" name="ChartType">
                            <option value="bar" @(ViewBag.ChartType == "bar" ? "selected" : "")>أعمدة</option>
                            <option value="line" @(ViewBag.ChartType == "line" ? "selected" : "")>خط</option>
                            <option value="pie" @(ViewBag.ChartType == "pie" ? "selected" : "")>دائري</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12 text-center">
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="fas fa-search"></i> عرض التقرير
                    </button>
                    <button type="button" class="btn btn-secondary px-4 ms-2" onclick="resetFilters()">
                        <i class="fas fa-redo-alt"></i> إعادة تعيين
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Chart Section -->
    <div class="chart-tabs mb-4">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active rounded" id="hours-tab" data-toggle="tab" data-target="#hours" type="button" role="tab" aria-controls="hours" aria-selected="true">
                    <i class="fas fa-clock"></i> ساعات العمل الإضافي
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link rounded" id="amount-tab" data-toggle="tab" data-target="#amount" type="button" role="tab" aria-controls="amount" aria-selected="false">
                    <i class="fas fa-money-bill-wave"></i> مبالغ العمل الإضافي
                </button>
            </li>
        </ul>
    </div>

    <div class="tab-content">
        <div class="tab-pane fade show active" id="hours" role="tabpanel" aria-labelledby="hours-tab">
            <div class="chart-container">
                <canvas id="hoursChart"></canvas>
            </div>
        </div>
        <div class="tab-pane fade" id="amount" role="tabpanel" aria-labelledby="amount-tab">
            <div class="chart-container">
                <canvas id="amountChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Chart Summary Table -->
    <div class="report-table mt-4">
        <h4 class="mb-4"><i class="fas fa-table"></i> بيانات الرسم البياني</h4>
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="chartDataTable">
                <thead class="bg-primary text-white">
                    <tr>
                        <th>@(ViewBag.ViewType == "summary" ? "الشهر" : (ViewBag.ViewType == "byDepartment" ? "الدائرة" : "الموظف"))</th>
                        <th>ساعات العمل الإضافي</th>
                        <th>مبلغ العمل الإضافي</th>
                    </tr>
                </thead>
                <tbody>
                    @if (ViewBag.ChartLabels != null)
                    {
                        for (int i = 0; i < ViewBag.ChartLabels.Count; i++)
                        {
                            <tr>
                                <td>@ViewBag.ChartLabels[i]</td>
                                <td>@Model._h.FormatHour(ViewBag.ChartHoursData[i])</td>
                                <td>@Model._amount(ViewBag.ChartAmountData[i])</td>
                            </tr>
                        }
                    }
                </tbody>
                <tfoot class="bg-light">
                    <tr class="fw-bold">
                        <td>المجموع</td>
                        <td>@Model._h.FormatHour(ViewBag.TotalOvertime)</td>
                        <td>@Model._amount(ViewBag.TotalAmount)</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    
    @if (ViewBag.DetailedData != null && ViewBag.DetailedData.Count < 150)
    {
        @* <div class="report-table mt-4">
        <h4 class="mb-4"><i class="fas fa-table"></i> تفاصيل طلبات العمل الإضافي</h4>
        <div class="table-responsive">
            <table class="table table-striped table-hover " id="detailedTable">
                <thead class="bg-primary ">
                    <tr>
                        <th>رقم الطلب</th>
                        <th>الموظف</th>
                        <th>الشهر</th>
                        <th>الساعات</th>
                        <th>المبلغ</th>
                        <th>النوع</th>
           
                        <th>حالة الصرف</th>
                    </tr>
                </thead>
                <tbody>
                    @if (ViewBag.DetailedData != null)
                    {
                        foreach (var item in ViewBag.DetailedData)
                        {
                            var payStatus = Model.payStatus($"{item.InYear}-{item.InMailNo}-{item.InDocSlNo}-{item.Rel}");
                            var payStatusBadge = payStatus == 0
                                ? "<span class=\"badge bg-danger rounded-pill px-2\">متوقف</span>"
                                : (payStatus == 2
                                    ? "<span class=\"badge bg-success rounded-pill px-2\">تم الصرف</span>"
                                    : "<span class=\"badge bg-warning rounded-pill px-2\">صرف جزئي</span>");

                            <tr>
                                <td>
                                    <a href="/OverTime/Finance/View/@<EMAIL><EMAIL><EMAIL>">
                                        @<EMAIL><EMAIL>
                                    </a>
                                </td>
                                <td>#@item.EmpNo<br>@Model._h.StaffData(item.EmpNo).EmpNameA</td>
                                <td>@<EMAIL></td>
                                <td>@Model._h.FormatHour(item.ExtraSum)</td>
                                <td>@Model._amount(item.ExtraAmountSum)</td>
                                <td>
                                    @if(item.EstimatedFlag == 1)
                                    {
                                        <span class="badge bg-warning rounded-pill px-2">تقديري</span>
                                    }
                                    else
                                    {
                                        <span>فعلي</span>
                                    }
                                </td>
                    
                                <td>@Html.Raw(payStatusBadge)</td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div> *@
    }
</div>


    <script>
        $(document).ready(function () {
            // Initialize DataTable
            createDatatable('#detailedTable');
            createDatatable('#chartDataTable');

            // Initialize Charts
            drawCharts();
        });

        function drawCharts() {
            const labels = @Html.Raw(Json.Serialize(ViewBag.ChartLabels ?? new string[] { }));
            const hoursData = @Html.Raw(Json.Serialize(ViewBag.ChartHoursData ?? new float[] { }));
            const amountData = @Html.Raw(Json.Serialize(ViewBag.ChartAmountData ?? new float[] { }));
            const chartType = '@ViewBag.ChartType';
            
            // Register Chart.js plugins
            Chart.register(ChartDataLabels);
            
            // Hours Chart
            const hoursCtx = document.getElementById('hoursChart').getContext('2d');
            const hoursChart = new Chart(hoursCtx, {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'ساعات العمل الإضافي',
                        data: hoursData,
                        backgroundColor: getColors(labels.length, 0.7),
                        borderColor: getColors(labels.length, 1),
                        borderWidth: 1
                    }]
                },
                options: getChartOptions('ساعات العمل الإضافي')
            });

            // Amount Chart
            const amountCtx = document.getElementById('amountChart').getContext('2d');
            const amountChart = new Chart(amountCtx, {
                type: chartType,
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'مبالغ العمل الإضافي',
                        data: amountData,
                        backgroundColor: getColors(labels.length, 0.7),
                        borderColor: getColors(labels.length, 1),
                        borderWidth: 1
                    }]
                },
                options: getChartOptions('مبالغ العمل الإضافي (ر.ع)')
            });
        }

        function getChartOptions(title) {
            const chartType = '@ViewBag.ChartType';
            
            let options = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: chartType === 'pie',
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: title,
                        font: {
                            size: 18
                        }
                    },
                    datalabels: {
                        display: true,
                        color: function(context) {
                            return chartType === 'pie' ? '#fff' : '#000';
                        },
                        font: {
                            weight: 'bold'
                        },
                        formatter: (value) => {
                            return value.toFixed(2);
                        }
                    }
                }
            };
            
            if (chartType !== 'pie') {
                options.scales = {
                    y: {
                        beginAtZero: true
                    }
                };
            }
            
            return options;
        }

        function getColors(count, alpha) {
            const colors = [
                `rgba(54, 162, 235, ${alpha})`,
                `rgba(255, 99, 132, ${alpha})`,
                `rgba(255, 206, 86, ${alpha})`,
                `rgba(75, 192, 192, ${alpha})`,
                `rgba(153, 102, 255, ${alpha})`,
                `rgba(255, 159, 64, ${alpha})`,
                `rgba(199, 199, 199, ${alpha})`,
                `rgba(83, 102, 255, ${alpha})`,
                `rgba(40, 159, 64, ${alpha})`,
                `rgba(210, 199, 199, ${alpha})`
            ];
            
            if (count <= colors.length) {
                return colors.slice(0, count);
            }
            
            // If we need more colors than predefined, generate them
            let result = [...colors];
            for (let i = colors.length; i < count; i++) {
                const r = Math.floor(Math.random() * 255);
                const g = Math.floor(Math.random() * 255);
                const b = Math.floor(Math.random() * 255);
                result.push(`rgba(${r}, ${g}, ${b}, ${alpha})`);
            }
            
            return result;
        }

        function updateDepartments() {
            const dgCode = $('#Dg').val();
            $.ajax({
                url: '/overtime/departments',
                type: 'GET',
                data: { dgCode: dgCode },
                success: function(data) {
                    let options = '<option value="0">-- الكل --</option>';
                    data.forEach(dept => {
                        options += `<option value="${dept.deptCode}">${dept.deptDespA}</option>`;
                    });
                    $('#Dept').html(options);
                    updateEmployees();
                },
                error: function() {
                    // If API fails, just submit the form with current values
                    $('#reportForm').submit();
                }
            });
        }

        function updateEmployees() {
            const deptCode = $('#Dept').val();
            if (deptCode <= 0) {
                const dgCode = $('#Dg').val();
                if (dgCode <= 0) {
                    $('#EmpNo').html('<option value="0">-- الكل --</option>');
                    return;
                }
                
                $.ajax({
                    url: '/overtime/employees/by-dg',
                    type: 'GET',
                    data: { dgCode: dgCode },
                    success: function(data) {
                        populateEmployeeDropdown(data);
                    },
                    error: function() {
                        // If API fails, just submit the form with current values
                        $('#reportForm').submit();
                    }
                });
                return;
            }
            
            $.ajax({
                url: '/overtime/employees/by-department',
                type: 'GET',
                data: { deptCode: deptCode },
                success: function(data) {
                    populateEmployeeDropdown(data);
                },
                error: function() {
                    // If API fails, just submit the form with current values
                    $('#reportForm').submit();
                }
            });
        }

        function populateEmployeeDropdown(employees) {
            let options = '<option value="0">-- الكل --</option>';
            employees.forEach(emp => {
                options += `<option value="${emp.empNo}">${emp.empNameA}</option>`;
            });
            $('#EmpNo').html(options);
        }

        function resetFilters() {
            $('#Dg').val(0);
            $('#Dept').val(0);
            $('#EmpNo').val(0);
            $('#PayStatus').val(-1);
            $('#ViewType').val('summary');
            $('#ChartType').val('bar');
            
            // Set date filters to default (last 6 months to today)
            const today = new Date();
            const sixMonthsAgo = new Date();
            sixMonthsAgo.setMonth(today.getMonth() - 6);
            
            $('#to').val(formatDate(today));
            $('#from').val(formatDate(sixMonthsAgo));
            
            // Submit the form
            $('#reportForm').submit();
        }

        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

    </script>


