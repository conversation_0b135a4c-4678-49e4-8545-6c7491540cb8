@using HumanResource.Modules.Overtime.ViewModels
@using System
@model OverTimeViewModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@await Html.PartialAsync("_AuditAppTabs")


<div class="card shadow">
    <div class="card-header">
        <div class="d-flex ">
            <div class="form-group m-0 p-0">
                <select name="year" class="form-control" onchange="window.location.href = '/OverTime/Audit?year=' + this.value;">
                @for (int i = 2000; i <= DateTime.Now.Year; i++)
                {
                    <option value="@i" selected="@(i == ViewBag.Year)">@i</option>
                }
                </select>
            </div>
            <div class="d-flex mx-2">
                <div class="form-check-inline">
                    <input type="radio" class="form-check-input" id="filter-needs-action" checked name="request-filter" value="needs-action">
                    <label class="form-check-label" for="filter-needs-action">تحتاج إجراء</label>
                </div>
                <div class="form-check-inline">
                    <input type="radio" class="form-check-input" id="filter-all" name="request-filter" value="all">
                    <label class="form-check-label" for="filter-all">الكل</label>
                </div>
            </div>
        </div>
    </div>
    <div class="">
    <table class="table " id="datatable">
        <thead class="bg-primary">
            <tr>
                <td>@Model._("الرقم") </td>
                <td>@Model._("الموظف") </td>
                <td>@Model._("الشهر") </td>
                <td>@Model._("الساعات الاضافية")</td>
                <td>@Model._("المبلغ ") (ر.ع)</td>
                <td>@Model._("النوع")</td>
                <td>@Model._("الحالة")</td>
            </tr>
        </thead>

        <tbody>


            
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function(){
        var table = createDatatable('#datatable', true, {
            ajax: {
                url: "/OverTime/Audit/Datatable",
                type: "POST",
                data: function(d) {
                    d.year = "@ViewBag.Year";
                    d.actionFilter = $('input[name="request-filter"]:checked').val();
                }
            },
            ordaring: false,
            columns: [
                {"data": "id", "title": "@Model._("الرقم")"},
                {"data": "emp", "title": "@Model._("الموظف")"},
                {"data": "createdAt", "title": "@Model._("الشهر")"},
                {"data": "extraSum", "title": "@Model._("الساعات الاضافية")"},
                {"data": "extraAmountSum", "title": "@Model._("المبلغ ")"},
                {"data": "estematedFlag", "title": "@Model._("النوع")"},
                {"data": "status", "title": "@Model._("الحالة")"},
            ]
        });

        // Handle radio button filtering
        $('input[name="request-filter"]').on('change', function() {
            // Reload the datatable with the new filter
            table.ajax.reload();
        });
    })
</script>