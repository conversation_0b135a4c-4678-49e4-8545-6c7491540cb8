@using HumanResource.Modules.Overtime.ViewModels
@model OverTimeViewModel

@{
    Layout = Model.Page.Layout;
    var staff = Model._h.StaffData(Model.UniOvertimeReqMas.EmpNo);
    var manager = Model._h.StaffData(Model.UniOvertimeReqMas.ManagerNo);
    var canApprove = Model.UniOvertimeReqMas.ReqStatus == 40 || Model.UniOvertimeReqMas.ReqStatus == 50;
}

<br>

<!-- Timeline -->
<div class="card card-body mb-3">
    @Html.Raw(Model.renderOvertimeTimeline(Model.UniOvertimeReqMas.InYear+"-"+Model.UniOvertimeReqMas.InMailNo+"-"+Model.UniOvertimeReqMas.InDocSlNo+"-"+Model.UniOvertimeReqMas.Rel))
</div>

<!-- Page Title -->
<h3>@Model.UniOvertimeReqMas.OrderYear - @Model.UniOvertimeReqMas.OrderMonth</h3>

<!-- Statistics Summary -->
<div class="card shadow mb-3">
    <div class="card-body">
        <div class="row">
            <div class="col-lg-4">
                <div class="d-flex align-items-center justify-content-start">
                    <i class="far fa-clock fa-2x mr-3"></i>
                    <h5><strong>@Model._l("العمل الاضافي"):</strong> @ViewBag.ExtraSum @Model._("ساعات")</h5>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="d-flex align-items-center justify-content-start">
                    <i class="fas fa-dollar-sign fa-2x mr-3"></i>
                    <h5><strong>@Model._l("المبلغ الشهري"):</strong> <span class="text-sucess">@Model._amount(ViewBag.MaxEmpEarnAmt)</span> / <span class="text-danger">@Model._amount(ViewBag.ExtraAmountSum)</span> OMR</h5>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="d-flex align-items-center justify-content-start">
                    <i class="fas fa-file-alt fa-2x mr-3"></i>
                    <h5><strong>@Model._l("طلبات العمل الاضافي"):</strong> @ViewBag.RequestCount</h5>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Left Column: Request Information & Logs -->
    <div class="col-md-6">
        <!-- Request Information -->
        <div class="card shadow mb-3">
            <table class="table table-borderless">
                <tr>
                    <td>@Model._("الرقم")</td>
                    <td>@<EMAIL><EMAIL></td>
                </tr>
                <tr>
                    <td>@Model._("الموظف")</td>
                    <td>#@staff.EmpNo <br> @staff.EmpNameA</td>
                </tr>
                <tr>
                    <td>@Model._("الراتب الاساسي")</td>
                    <td>@Model._amount(Model.EmpEarning.EarnAmt)</td>
                </tr>
                <tr>
                    <td>@Model._("الحالة")</td>
                    <td>
                        @Model.renderSatus(Model.UniOvertimeReqMas.ReqStatus)
                        @if (Model.UniOvertimeReqMas.CancelFlag == 1)
                        {
                            <span>@Model.UniOvertimeReqMas.SignRem</span>
                        }
                    </td>
                </tr>
                <tr>
                    <td>@Model._("المسؤول المباشر")</td>
                    <td>#@manager.EmpNo <br> @manager.EmpNameA</td>
                </tr>
                <tr>
                    <td>@Model._("الملف المرفق")</td>
                    <td>
                        @if(Model.UniOvertimeReqMas.FileGuid != null)
                        {
                            <a href="@Model._h.GetFile(Model.UniOvertimeReqMas.FileGuid)" target="_blank" class="btn btn-primary">
                                <i class="fas fa-download"></i> @Model._("تحميل")
                            </a>
                        }
                        else
                        {
                            <span class="text-muted">@Model._("لا يوجد ملف مرفق")</span>
                        }
                    </td>
                </tr>
            </table>
        </div>

        <!-- Activity Logs -->
        <div class="card shadow">
            <div class="card-header">
                <h6 class="mb-0">@Model._("سجل النشاط")</h6>
            </div>
            <div class="card-body">
                <table class="table-sm table-borderless text-muted">
                    @foreach (var log in Model._h.GetLogs(Model.UniOvertimeReqMas.InYear + "-" + Model.UniOvertimeReqMas.InMailNo + "-" + Model.UniOvertimeReqMas.InDocSlNo, "Overtime"))
                    {
                        <tr>
                            <td><i class="far fa-clock"></i></td>
                            <td>@log.Rem</td>
                            <td dir="ltr">@Model._dt(log.TimeStamp)</td>
                            <td>@Model._h.StaffData(log.UserId).EmpNameA</td>
                        </tr>
                    }
                </table>
            </div>
        </div>
    </div>

    <!-- Right Column: Overtime Details & Actions -->
    <div class="col-md-6">
        @if (canApprove)
        {
            <form action="~/OverTime/Audit/Approve/@<EMAIL><EMAIL><EMAIL>" method="post" class="ajax">
                <!-- Overtime Details Table -->
                <div class="card shadow mb-3">
                    <table class="table table-sm">
                        <thead class="bg-primary">
                            <tr>
                                <td>@Model._("اليوم")</td>
                                <td>
                                    @Model._("الساعات")
                                    @if (Model.UniOvertimeReqMas.EstimatedFlag == 1)
                                    {
                                        <span class="badge badge-danger">@Model._("تقديري")</span>
                                    }
                                </td>
                                <td>@Model._("سعر الساعة")</td>
                                <td>@Model._("المبلغ")</td>
                                <td></td>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var day in Model.UniOvertimeReqDtls)
                            {
                                <tr>
                                    <td>
                                        <a href="#" onclick="getFpRecords('@Model._d(day.OtDate)')">@Model._ddd(day.OtDate)</a>
                                    </td>
                                    <td>@Model._h.FormatHour(day.OtDuration)</td>
                                    <td>@Model._amount(day.OtRate)</td>
                                    <td>
                                        @if (Model._amount(day.ComputedAmount) != Model._amount(day.OtAmount))
                                        {
                                            <span class="p-1 badge-warning">@Model._amount(day.ComputedAmount) => @Model._amount(day.OtAmount)</span>
                                        }
                                        else
                                        {
                                            <span class="p-1"> @Model._amount(day.OtAmount)</span>
                                        }
                                    </td>
                                    <td>
                                    <button class="btn btn-sm btn-secondary" type="button" onclick="getFpRecords('@Model._d(day.OtDate)')">
                                        <i class="fas fa-fingerprint"></i> @Model._("عرض")
                                        </button>
                                </td>
                                </tr>
                            }
                        </tbody>
                        <tfoot>
                            <tr>
                                <td>@Model._("اجمالي الساعات")</td>
                                <td>@Model._h.FormatHour(Model.UniOvertimeReqMas.ExtraSum.Value)</td>
                                <td></td>
                                <td>
                                    @if (Model._amount(ViewBag.newsum) != Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value))
                                    {
                                        <span class="p-1 badge-warning">@Model._amount(ViewBag.newsum)=> @Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value)</span>
                                    }
                                    else
                                    {
                                        <span class="p-1">@Model._amount(ViewBag.newsum) => @Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value)</span>
                                    }
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Comments Section -->
                @if(Model.OvertimeComments.Any(c => !string.IsNullOrWhiteSpace(c.Comment)))
                {
                    <div class="card shadow mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">@Model._("التعليقات")</h6>
                        </div>
                        <div class="card-body">
                            @foreach (var comment in Model.OvertimeComments.Where(c => !string.IsNullOrWhiteSpace(c.Comment)))
                            {
                                var createdBy = Model._h.StaffData(comment.CreatedBy);
                                
                                <div class="col-7 @(comment.CreatedBy==Model.Profile.EmpNo.Value?"mr-auto":"ml-auto") mb-3">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user"></i> 
                                        <span class="mx-2 mt-2">@createdBy.EmpNo - @createdBy.EmpNameA</span>
                                    </div>
                                    <p class="text-muted m-0 py-0" dir="ltr">
                                        <small>@Model._dt(@comment.TimeStamp)</small>
                                    </p>
                                    <p class="p-2 border rounded">
                                        @comment.Comment
                                    </p>
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- Add Comment Section -->
                <div class="card shadow mb-3">
                    <div class="card-body">
                        <textarea name="comment" class="form-control" rows="3" placeholder='@Model._("اكتب ملاحظة . . .")'></textarea>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mb-3">
                    <button class="btn btn-success   after-confirm mx-1 ">
                        <i class="fas fa-check"></i> @Model._("موافقة")
                    </button>
                    <a href="#" data-toggle="modal" data-target="#return-modal" class="btn btn-warning   mx-1 ">
                        <i class="fas fa-undo"></i> @Model._("ارجاع")
                    </a>
                    <a href="#" data-toggle="modal" data-target="#department-manager-decline-modal" class="btn btn-danger   mx-1">
                        <i class="fas fa-times"></i> @Model._("رفض")
                    </a>
                </div>
            </form>
        }
        else
        {
            <!-- View Only Mode -->
            <div class="card shadow mb-3">
                <table class="table table-sm">
                    <thead class="bg-primary">
                        <tr>
                            <td>@Model._("اليوم")</td>
                            <td>
                                @Model._("الساعات")
                                @if (Model.UniOvertimeReqMas.EstimatedFlag == 1)
                                {
                                    <span class="badge badge-danger">@Model._("تقديري")</span>
                                }
                            </td>
                            <td>@Model._("سعر الساعة")</td>
                            <td>@Model._("المبلغ")</td>
                            <td></td>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var day in Model.UniOvertimeReqDtls)
                        {
                            <tr>
                                <td>
                                    <a href="#" onclick="getFpRecords('@Model._d(day.OtDate)')">@Model._ddd(day.OtDate)</a>
                                </td>
                                <td>@Model._h.FormatHour(day.OtDuration)</td>
                                <td>@Model._amount(day.OtRate)</td>
                                <td>
                                    @if (Model._amount(day.ComputedAmount) != Model._amount(day.OtAmount))
                                    {
                                        <span class="p-1 badge-warning">@Model._amount(day.ComputedAmount) => @Model._amount(day.OtAmount)</span>
                                    }
                                    else
                                    {
                                        <span class="p-1">@Model._amount(day.ComputedAmount) => @Model._amount(day.OtAmount)</span>
                                    }
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-secondary" type="button" onclick="getFpRecords('@Model._d(day.OtDate)')">
                                        <i class="fas fa-fingerprint"></i> @Model._("عرض")
                                        </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td>@Model._("اجمالي الساعات")</td>
                            <td>@Model._h.FormatHour(Model.UniOvertimeReqMas.ExtraSum.Value)</td>
                            <td></td>
                            <td>
                                @if (Model._amount(ViewBag.newsum) != Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value))
                                {
                                    <span class="p-1 badge-warning">@Model._amount(ViewBag.newsum)=> @Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value)</span>
                                }
                                else
                                {
                                    <span class="p-1">@Model._amount(ViewBag.newsum) => @Model._amount(Model.UniOvertimeReqMas.ExtraAmountSum.Value)</span>
                                }
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Comments Section -->
            @if(Model.OvertimeComments.Any(c => !string.IsNullOrWhiteSpace(c.Comment)))
            {
                <div class="card shadow mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">@Model._("التعليقات")</h6>
                    </div>
                    <div class="card-body">
                        @foreach (var comment in Model.OvertimeComments.Where(c => !string.IsNullOrWhiteSpace(c.Comment)))
                        {
                            var createdBy = Model._h.StaffData(comment.CreatedBy);
                            
                            <div class="col-7 @(comment.CreatedBy==Model.Profile.EmpNo.Value?"mr-auto":"ml-auto") mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user"></i> 
                                    <span class="mx-2 mt-2">@createdBy.EmpNo - @createdBy.EmpNameA</span>
                                </div>
                                <p class="text-muted m-0 py-0" dir="ltr">
                                    <small>@Model._dt(@comment.TimeStamp)</small>
                                </p>
                                <p class="p-2 border rounded">
                                    @comment.Comment
                                </p>
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Add Comment Form (View Only Mode) -->
            <div class="card shadow">
                <div class="card-body">
                    <form action="~/OverTime/Comment/Create/@<EMAIL><EMAIL><EMAIL>" method="post" class="ajax">
                        <textarea name="comment" class="form-control" rows="3" placeholder='@Model._("اكتب ملاحظة . . .")'></textarea>
                        <button class="btn btn-primary  btn-sm mt-2">
                            <i class="fas fa-paper-plane"></i> @Model._("ارسال")
                        </button>
                    </form>
                </div>
            </div>
        }
    </div>
</div>

<!-- Fingerprint Records Modal -->
@await Html.PartialAsync("_FingerprintModal", Model)

<!-- Decline Modal -->
<form action="~/OverTime/Audit/Decline/@<EMAIL><EMAIL><EMAIL>" class="ajax" method="post">
    <div class="modal fade" id="department-manager-decline-modal" role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("Declined by audit")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظة الرفض")</label>
                    <textarea name="Note" class="form-control" rows="3"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-danger">@Model._l("رفض")</button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Return Modal -->
<form action="~/OverTime/Audit/Return/@<EMAIL><EMAIL><EMAIL>" class="ajax" method="post">
    <div class="modal fade" id="return-modal" role="dialog" aria-labelledby="return-modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="return-modalLabel">@Model._l("ارجاع")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظة الارجاع")</label>
                    <textarea name="comment" class="form-control" rows="3"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary">@Model._l("ارجاع")</button>
                </div>
            </div>
        </div>
    </div>
</form>
