using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;
namespace HumanResource.Modules.Overtime.Providers
{
    /// <summary>
    /// Navigation provider for the Overtime module
    /// </summary>
    public class OvertimeNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Overtime";
        public int Priority => 21;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // HR Group navigation items
            var hrNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "hr_overtime",
                    Label = "العمل الإضافي",
                    Active = "hr_overtime",
                    Icon = "<i class=\"far fa-clock\"></i>",
                    Rights = new List<Right> { Right.OvertimeDepartment, Right.OvertimeAdmin, Right.OvertimeHrManager, Right.OvertimeReport, Right.FinanceDepartment, Right.FinanceManager, Right.Audit },
                    Priority = 15,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "overtime_department",
                            Label = "العمل الإضافي (القسم)",
                            Rights = new List<Right> { Right.OvertimeDepartment },
                            Url = "/OverTime/Department"
                        },
                        new NavLink
                        {
                            Name = "overtime_manager",
                            Label = "العمل الإضافي (المدير)",
                            Rights = new List<Right> { Right.OvertimeHrManager },
                            Url = "/OverTime/Department/Manager"
                        },
                        new NavLink
                        {
                            Name = "overtime_report",
                            Label = "تقرير العمل الإضافي",
                            Rights = new List<Right> { Right.OvertimeReport },
                            Url = "/OverTime/Report"
                        },
                        new NavLink
                        {
                            Name = "overtime_finance",
                            Label = "الصرف",
                            Rights = new List<Right> { Right.FinanceDepartment, Right.FinanceManager },
                            Url = "/OverTime/Finance"
                        },
                        new NavLink
                        {
                            Name = "overtime_finance_manager",
                            Label = "اعتماد الصرف",
                            Rights = new List<Right> { Right.FinanceManager },
                            Url = "/OverTime/Finance/Manager"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.HR] = hrNavItems;

            return navigation;
        }
    }
} 