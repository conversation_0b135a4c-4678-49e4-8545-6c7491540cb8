using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Modules.Shared.Models.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Overtime.Providers
{
    /// <summary>
    /// Task provider for the Overtime module
    /// </summary>
    public class OvertimeTaskProvider : ITaskProvider
    {
        private readonly hrmsContext _context;
        private readonly AppHelper _appHelper;

        public string ModuleName => "Overtime";

        public OvertimeTaskProvider(hrmsContext context, AppHelper appHelper)
        {
            _context = context;
            _appHelper = appHelper;
        }

        public async Task<List<UserTask>> GetTasksAsync()
        {
            var tasks = new List<UserTask>();

            try
            {
                // Get current user info
                var currentUser = _appHelper.Auth();
                if (currentUser?.EmpNo == null)
                {
                    return tasks;
                }

                // DG approvals - estimated overtime requests needing DG approval
                if (_appHelper.Can("overtime-dg"))
                {
                    var dgOvertime = await _context.OvertimeReqMas
                        .Where(ro => ro.EstimatedFlag == 1 
                                  && ro.CancelFlag != 1 
                                  && ro.ReqStatus == 30 
                                  && ro.EmpNo > 1)
                        .CountAsync();

                    if (dgOvertime > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "المدير العام",
                            Text = $"عمل اضافي تقديري ({dgOvertime})",
                            Url = "/Overtime/Dg/",
                            Priority = TaskPriority.Medium
                        });
                    }
                }

                // HR Manager approvals
                if (_appHelper.Can("overtime-hr-manager"))
                {
                    var hrOvertime = await _context.OvertimeReqMas
                        .Where(ro => ro.EmpNo > 0
                                  && ro.CancelFlag != 1
                                  && ro.ReqStatus == 30 
                                  && ro.EstimatedFlag == 0
                                  && ro.UserId != null)
                        .CountAsync();

                    if (hrOvertime > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "مدير الموارد البشرية",
                            Text = $"عمل اضافي ({hrOvertime})",
                            Url = "/Overtime/Department/Manager/",
                            Priority = TaskPriority.Medium
                        });
                    }
                }

                // Department processing
                if (_appHelper.Can("overtime-department"))
                {
                    var departmentOvertime = await _context.OvertimeReqMas
                        .Where(ro => ro.EmpNo > 0 
                                  && ro.CancelFlag != 1 
                                  && ro.ReqStatus == 20 
                        ).CountAsync();

                    if (departmentOvertime > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "مهام القسم",
                            Text = $"عمل اضافي ({departmentOvertime})",
                            Url = "/Overtime/Department/",
                            Priority = TaskPriority.Medium
                        });
                    }
                }

                // Finance manager approvals
                if (_appHelper.Can("finance-manager"))
                {
                    var financeOvertime = await _context.OvertimeReqMas
                        .Where(ro => ro.EmpNo > 0 
                                  && ro.CancelFlag != 1 
                                  && ro.ReqStatus == 60 
                                  && ro.AuditStat != 1)
                        .CountAsync();

                    if (financeOvertime > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "المالية",
                            Text = $"عمل اضافي ({financeOvertime})",
                            Url = "/Overtime/Finance/Manager",
                            Priority = TaskPriority.Medium
                        });
                    }
                }

                // Department manager tasks
                if (_appHelper.Can("department-manager"))
                {
                    var managerOvertime = await _context.OvertimeReqMas
                        .Where(ro => ro.EmpNo == currentUser.EmpNo 
                                  && ro.ReqStatus == 1)
                        .CountAsync();

                    if (managerOvertime > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "رئيس القسم",
                            Text = $"عمل اضافي ({managerOvertime})",
                            Url = "/Overtime/Manager",
                            Priority = TaskPriority.Medium
                        });
                    }
                }

                // Audit tasks
                if (_appHelper.Can("audit"))
                {
                    var auditOvertime = await _context.OvertimeReqMas
                        .Where(ro => ro.EmpNo > 0 
                                  && ro.CancelFlag != 1 
                                  && (ro.ReqStatus == 40 || ro.ReqStatus == 50))
                        .CountAsync();

                    if (auditOvertime > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "التدقيق",
                            Text = $"عمل اضافي ({auditOvertime})",
                            Url = "/Overtime/Audit",
                            Priority = TaskPriority.Medium
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break task loading
                Console.WriteLine($"Error loading overtime tasks: {ex.Message}");
            }

            return tasks;
        }
    }
} 