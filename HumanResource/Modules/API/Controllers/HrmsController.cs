﻿using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Data;
using System.Net;
using Microsoft.CodeAnalysis;
using Microsoft.AspNetCore.Authorization;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Core.Helpers.Utilities;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Controllers;

namespace HumanResource.Modules.API.Controllers;

[Area("API")]
[Route("api/hrms")]
[ApiController]
[AllowAnonymous]
[ApiWhiteList]
public class HrmsController : ControllerBase
{



    protected ClaimsIdentity Identity { get; private set; }

    public readonly hrmsContext _context;
    public hrmsContext _db;
    public bcContext _bc;
    public readonly IHttpContextAccessor _http;




    public HrmsController(hrmsContext context, IHttpContextAccessor httpContextAccessor)
    {

        _context = context;
        _db = context;
        _http = httpContextAccessor;

    }


    [HttpGet("employees/get")]
    [AllowAnonymous]
    public IActionResult Employees()
    {

        var employees = _db.VempDtl.ToList();

        return Ok(new
        {
            success=true,
            data= employees
        });
    }

    [HttpGet("employees/get/{Id}")]
    [AllowAnonymous]
    public IActionResult Employees(int Id)
    {
        StorageHelper storage = new StorageHelper(_db);


        var employees = _db.VempDtl.Where(r=> r.EmpNo == Id).ToList();

        var data = employees.Select(emp => new
        {
            emp.EmpNo,
            emp.EmpNameA,
            emp.DgCode,
            emp.DgDespA,
            emp.DeptCode,
            emp.DeptDespA,
            emp.DesgnCode,
            emp.DesgCode,
            emp.DesgType,
            emp.EmailId,
            emp.NatId,
            emp.GradRank,
            emp.BirthDate,
            emp.AppointDate,
            emp.DesgnHierLevel,
            emp.GscAppointDate,
            emp.ProfileImage,
            ProfileImageUrl = $"~"+storage.Get(emp.ProfileImage),
            Qualifications = _db.VempQuals.Where(ro => ro.EmpNo == emp.EmpNo),
        }).ToList();


        return Ok(new
        {
            success = true,
            data
        });
    }



}

