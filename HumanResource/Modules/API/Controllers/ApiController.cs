using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Modules.Shared.Controllers;
using Microsoft.AspNetCore.Mvc;

namespace HumanResource.Modules.API.Controllers;

[Area("API")]
[Route("api")]
public class ApiController : BaseController
{
    public ApiController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, 
        AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
    }


} 