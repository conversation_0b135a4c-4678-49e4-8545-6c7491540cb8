using HumanResource.Modules.Dashboard.Providers;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Dashboard.Configuration
{
    /// <summary>
    /// Configuration for the Dashboard module
    /// </summary>
    public static class DashboardConfiguration
    {
        /// <summary>
        /// Registers all Dashboard module services
        /// </summary>
        public static IServiceCollection AddDashboardServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, DashboardNavigationProvider>();

            // Register module services
            // services.AddScoped<DashboardService>();

            return services;
        }
    }
} 