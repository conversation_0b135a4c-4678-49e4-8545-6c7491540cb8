using HumanResource.Core.Data;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;

namespace HumanResource.Modules.Dashboard.Providers
{
    /// <summary>
    /// Navigation provider for the Dashboard module
    /// </summary>
    public class DashboardNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Dashboard";
        public int Priority => 10;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Home Group navigation items
            var homeNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "Home",
                    Label = "الرئيسية",
                    Active = "dashboard",
                    Icon = "<i class=\"far fa-home\"></i>",
                    Rights = new List<Right>(),
                    Priority = 10,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "Home",
                            Label = "الرئيسية",
                            Rights = new List<Right>(),
                            Url = "/Account/UserDashboard"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Home] = homeNavItems;

            return navigation;
        }
    }
} 