﻿using Microsoft.AspNetCore.Http;
using HumanResource.Modules.Purchases.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Shared.Models.Entities.BC;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Purchases.ViewModels;


public class PurchasesViewModel : BaseViewModel
{


#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    bcContext _bc;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
    public PurchasesViewModel(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, bccontext, httpContextAccessor, helper)
        {
            _db = context;
            _bc = bccontext;


    }


    public List<PurchasesRequest> PurchasesRequests { get; set; }

    public PurchasesRequest PurchasesRequest { get; set; }


    public int OriginApprovedBy { get; set; } = 0;
    public DateTime? OriginApprovedAt { get; set; }



    public List<RequestItem> RequestItems { get; set; }

    public RequestItem RequestItem { get; set; }


    public List<Supplier> Suppliers { get; set; }
    public Supplier Supplier { get; set; }

    public List<PurchaseQuotation> PurchaseQuotations { get; set; }
    public PurchaseQuotation PurchaseQuotation { get; set; }


    public List<PurchasesRequest> PurchasesList { get; set; }
    public PurchasesRequest Purchases { get; set; }
    public List<BcAccount> BcAccounts { get; set; }
    public BcAccount BcAccount { get; set; }






    public BcAccount GetAccount(string code)
    {

        string[] rawcode = code.Split("-");


        int Type = int.Parse(rawcode[0]);
        int HeadCode = int.Parse(rawcode[1]);
        int SubHeadCode = int.Parse(rawcode[2]);
        int DetCode = int.Parse(rawcode[3]);
        int SubDetCode = int.Parse(rawcode[4]);

        var acc = _bc.BcAccount.Where(r => r.Type == Type && r.HeadCode == HeadCode && r.SubHeadCode == SubHeadCode && r.DetCode == DetCode && r.SubDetCode == SubDetCode).FirstOrDefault();


        if (acc != null)
        {
            return acc;
        }

        return null;
    }

    public BcAccount GetAccount(string code, int year)
    {

        string[] rawcode = code.Split("-");


        int Type = int.Parse(rawcode[0]);
        int HeadCode = int.Parse(rawcode[1]);
        int SubHeadCode = int.Parse(rawcode[2]);
        int DetCode = int.Parse(rawcode[3]);
        int SubDetCode = int.Parse(rawcode[4]);

        var acc = _bc.BcAccount.Where(r => r.Type == Type && r.HeadCode == HeadCode && r.SubHeadCode == SubHeadCode && r.DetCode == DetCode && r.SubDetCode == SubDetCode && r.BudgetYear == year).FirstOrDefault();


        if (acc != null)
        {
            return acc;
        }

        return null;
    }




    public Supplier GetSupplier(int Id)
    {
        var s = _bc.Supplier.FirstOrDefault(x => x.Id == Id);
        if (s != null)
            return s;

        s = new Supplier()
        {
            Id = 99,
            Name = "CC",
        };


        return s;
    }
}



