﻿using Microsoft.AspNetCore.Mvc;
using System.Dynamic;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Purchases.Models.Entities;
using HumanResource.Modules.Purchases.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.Models.Entities.BC;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Purchases.Forms;
using HumanResource.Modules.Shared.ViewModels;
using HumanResource.Core.UI.Models;
using HumanResource.Modules.Shared.Models.Entities.HRMS;

namespace HumanResource.Modules.Purchases.Controllers;

[Area("Purchases")]
[Route("Purchases")]
[Can("purchases-department|finance-manager")]
public class PurchasesController : BaseController
{


    public PurchasesViewModel _v;

    public PurchasesController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, <PERSON><PERSON><PERSON><PERSON>per helper, hrmsContext hrmsContext)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new PurchasesViewModel(context, bccontext, httpContextAccessor, helper);

        _v.Page.Active = "purchases";

    }


    [Route("Index")]
    public IActionResult Index(DateTime? from, DateTime to)
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Purchases", Url=$"/Purchases/Index"},
        };

        _v.Page.Title = "Purchases";

        _v.PurchasesRequests = _db.PurchasesRequest.ToList();

        var report = _db.PurchasesRequest
            .Where(r =>
            r.EmpNo > 0 && r.LpoStat == 1)
            .GroupBy(g => g.EmpNo)
            .Select(ro => new
            {
                Employee = _h.StaffData(ro.Key),
                Count = ro.Count(),
            }).OrderByDescending(o => o.Count).ToList();


        var reportList = new List<_EmptRequestReport>();


        foreach (var item in report)
        {
            reportList.Add(new _EmptRequestReport
            {
                Employee = item.Employee,
                Count = item.Count

            });
        }


        _v._EmptRequestReport = reportList;

        _v.VempDtls = _db.VempDtls.ToList();

        return View(_v);
    }

    [HttpGet("Create")]
    public IActionResult AnalysisCreate()
    {
        _v.Page.Reload = true;
        _v.Page.Back = $"/Purchases/Index";

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Purchases", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="التحليل", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="تحليل جديد", Url=$"/Purchases/Requests"},
        };

        _v.Page.Title = "تحليل جديد";


        return View(_v);
    }

    [HttpPost("Create")]
    public async Task<IActionResult> AnalysisCreate(PurchasesAnalysisForm post)
    {


        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }


        var NewRequest = new PurchasesRequest
        {
            Reference = post.PurchasesRequest.Reference,
            Date = post.PurchasesRequest.Date,
            Note = post.PurchasesRequest.Note,
            LastActionDate = DateTime.Now,
        };

        _db.PurchasesRequest.Add(NewRequest);
        await _db.SaveChangesAsync();


        foreach (var item in post.RequestItems)
        {
            var NewItem = new RequestItem
            {

                Name = item.Name,
                Quantity = item.Quantity,
                Note = item.Note,
                RequestId = NewRequest.Id,
                RelId = NewRequest.Id,
                RelType = "PurchasesRequest",
                CreatedBy = Auth().EmpNo.Value,

            };

            _db.RequestItem.Add(NewItem);
            await _db.SaveChangesAsync();

        }

        return Json(new
        {
            success = true,
            message = new List<string> { "Request created successfully." },
            action = $"location.replace('/Purchases/Update/{NewRequest.Id}')",
        });

    }

    [HttpGet("Update/{Id}")]
    public IActionResult AnalysisUpdate(int Id)
    {
        _v.Page.Reload = true;
        _v.Page.Back = $"/Purchases/Index";


        _v.PurchasesRequest = _db.PurchasesRequest.Find(Id);

        if (_v.PurchasesRequest == null)
            return StatusCode(404);

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="التحليل", Url=$"/Purchases/Index"},
            new Breadcrumb {Label=_v.PurchasesRequest.Reference, Url=$"/Purchases/Requests"},
        };

        _v.Page.Title = _v.PurchasesRequest.Reference;


        _v.RequestItems = _db.RequestItem.Where(r => r.RequestId == Id && r.RelType != "PurchasesRequestAnalysis" && r.RelId != Id).ToList();

        _v.VempDtls = _db.VempDtls.Where(r => r.DeptCode == _v.Profile.DeptCode || r.DeptCode == 2).ToList();


        var empListRaw = _db.VempDtls.Where(r => r.DeptCode == _v.Profile.DeptCode || r.DeptCode == 2).Select(ro => new
        {
            empNo = ro.EmpNo,
            empNameA = ro.EmpNameA,
            requestCount = _db.PurchasesRequest.Where(r2 => r2.EmpNo == ro.EmpNo && r2.DeliveryStat == 0).Count(),
        }).ToList();

        var empList = empListRaw.Select(ro =>
        {
            dynamic emp = new ExpandoObject();
            emp.empNo = ro.empNo;
            emp.empNameA = ro.empNameA;
            emp.requestCount = _db.PurchasesRequest.Where(r2 => r2.EmpNo == ro.empNo && r2.DeliveryStat == 0).Count();

            return emp;

        }).ToList();

        ViewBag.EmpList = empList;

        return View(_v);
    }

    [HttpPost("Update/{Id}")]
    public async Task<IActionResult> AnalysisUpdate(int Id, PurchasesRequest purchase, [FromForm] IFormCollection post)
    {

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);



        if (request == null)
            return StatusCode(404);

        request.Reference = purchase.Reference;
        request.Date = purchase.Date;
        request.EmpNo = purchase.EmpNo;



        _db.Update(request);
        await _db.SaveChangesAsync();




        return Json(new
        {
            success = true,
            message = new List<string> { "Request updated successfully." },
            action = "reload",
        });

    }

    [HttpGet("Update/Analysis/{Id}")]
    public IActionResult AnalysisUpdateAnalysis(int Id)
    {

        _v.PurchasesRequest = _db.PurchasesRequest.Find(Id);

        var items = _db.RequestItem.Where(r => r.RequestId == Id && r.RelType == "PurchasesRequestAnalysis").ToList();

        _v.RequestItems = items;

        _v.Page.Reload = true;
        _v.Page.Back = $"/Purchases/Index";

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="التحليل", Url=$"/Purchases/Index"},
            new Breadcrumb {Label=_v.PurchasesRequest.Reference, Url=$"/Purchases/Index"},
        };

        return View(_v);
    }

    [HttpPost("Update/Analysis/{Id}")]
    public async Task<IActionResult> AnalysisUpdateAnalysis(int Id, PurchasesAnalysisForm post)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.QuotationStat == 1)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { "لا يمكن اتمام هذا الاجراء" },
                action = "",
            });
        }

        request.AnalysisNote = post.PurchasesRequest.AnalysisNote;

        _db.Update(request);
        await _db.SaveChangesAsync();


        var items = _db.RequestItem.Where(r => r.RequestId == request.Id && r.RelType == "PurchasesRequestAnalysis").ToList();

        _db.RemoveRange(items);
        await _db.SaveChangesAsync();

        foreach (var item in post.RequestItems)
        {
            var NewItem = new RequestItem
            {
                Name = item.Name,
                Quantity = item.Quantity,
                Unit = item.Unit,
                Note = item.Note,
                RequestId = post.PurchasesRequest.Id,
                RelId = post.PurchasesRequest.Id,
                RelType = "PurchasesRequestAnalysis",
                CreatedBy = _v.Profile.EmpNo.Value,
            };

            _db.RequestItem.Add(NewItem);
            await _db.SaveChangesAsync();
        }

        Log(
            request.Id,
            "PurchaseRequest",
            "تحديث التحليل"
        );

        return Json(new
        {
            success = true,
            message = new List<string> { "تم تحديث التحليل" },
            action = "reload",
        });
    }


    [HttpGet("Update/Quotations/{Id}")]
    public IActionResult AnalysisUpdateQuotations(int Id)
    {


        _v.PurchasesRequest = _db.PurchasesRequest.Find(Id);
        _v.RequestItems = _db.RequestItem.Where(r => r.RequestId == Id).ToList();
        _v.Suppliers = _bc.Supplier.Where(s => s.Id < 8000 || s.Id > 9999).ToList();
        _v.PurchaseQuotations = _db.PurchaseQuotations.Where(r => r.RelId == Id && r.RelType == "Request").ToList();


        _v.Page.Reload = true;
        _v.Page.Back = $"/Purchases/Index";

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="التحليل", Url=$"/Purchases/Index"},
            new Breadcrumb {Label=_v.PurchasesRequest.Reference, Url=$"/Purchases/Requests"},
        };

        return View(_v);
    }

    [HttpPost("Update/Quotations/Create/{Id}")]
    public async Task<IActionResult> AnalysisUpdateCreateQuotation(int Id, PurchaseQuotationFrom post)
    {

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }


        string FileGuid = null;

        if (post.File != null && post.File.Length > 0)
        {
            FileGuid = await _h.UploadAsync(post.File);
        }

        var SupplierId = post.Quotation.SupplierId;

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.QuotationStat == 1)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { "لا يمكن اتمام هذا الاجراء" },
                action = "",
            });
        }

        var quotation = _db.PurchaseQuotations.Where(r => r.RelId == request.Id && r.RelType == "Request" && r.SupplierId == SupplierId).FirstOrDefault();

        if (quotation != null)
        {
            return Json(new
            {
                success = true,
                message = new List<string> { "تم اضافة هذا المورد بالفعل" },
                action = "",
            });
        }

        var newQutation = new PurchaseQuotation
        {
            SupplierId = SupplierId,
            TotalCost = post.Quotation.TotalCost,
            TotalTax = post.Quotation.TotalTax,
            Note = post.Quotation.Note,
            RelType = "Request",
            RelId = request.Id,
            FileGuid = FileGuid,
        };

        _db.PurchaseQuotations.Add(newQutation);
        await _db.SaveChangesAsync();

        Log(
            request.Id,
            "PurchaseRequest",
            "اضافة عرض جديد",
            ("رقم العرض", newQutation.Id)
        );

        return Json(new
        {
            success = true,
            message = new List<string> { "تم اضافة عرض الاسعار" },
            action = "reload",
        });
    }

    [HttpGet("Update/Quotations/Delete/{Id}/{QuotationId}")]
    public IActionResult AnalysisUpdateDeleteQuotation(int Id, int QuotationId)
    {
        var quotation = _db.PurchaseQuotations.Find(QuotationId);

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null || quotation == null)
            return StatusCode(404);

        if (request.QuotationStat == 1)
            return StatusCode(404);


        _db.Remove(quotation);

        _db.SaveChanges();

        var redirectUrl = $"/Purchases/Update/Quotations/{Id}/?success=true";

        return Redirect(redirectUrl);
    }


    [HttpGet("Update/Quotations/Approve/{Id}")]
    [Can("finance-manager")]
    public IActionResult AnalysisUpdateQuotationApprove(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.QuotationId == 0)
            return StatusCode(444);


        request.QuotationStat = 1;

        _db.Update(request);
        _db.SaveChanges();

        Log(
           request.Id,
           "PurchaseRequest",
           "اعتماد العروض والتحليل"
        );

        var redirectUrl = $"/Purchases/Update/Quotations/{Id}/?success=true";

        return Redirect(redirectUrl);
    }

    [HttpGet("Update/Quotations/Cancel/{Id}")]
    [Can("finance-manager")]
    public IActionResult AnalysisUpdateQuotationCancel(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.BudgetStat == 1)
            return StatusCode(403);

        request.QuotationStat = 0;

        _db.Update(request);
        _db.SaveChanges();

        Log(
           request.Id,
           "PurchaseRequest",
           "الغاء اعتماد العروض والتحليل"
        );

        var redirectUrl = $"/Purchases/Update/Quotations/{Id}/?success=true";

        return Redirect(redirectUrl);
    }

    [HttpGet("Update/Quotations/Select/{Id}/{QuotationId}")]
    public IActionResult AnalysisUpdateQuotationSelect(int Id, int QuotationId)
    {

        var request = _db.PurchasesRequest.Find(Id);
        var quotation = _db.PurchaseQuotations.Where(r => r.RelId == Id && r.RelType == "Request" && r.Id == QuotationId).FirstOrDefault();


        if (request == null || quotation == null)
            return StatusCode(404);


        if (request.QuotationStat == 1)
            return StatusCode(404);

        if (request.QuotationId == QuotationId && request.QuotationId != 0)
        {
            request.QuotationId = 0;

            _db.Update(request);
            _db.SaveChanges();

            Log(
               request.Id,
               "PurchaseRequest",
               "الغاء اختيار عرض الاسعار"
            );

            return Redirect($"/Purchases/Update/Quotations/{Id}/?success=true");
        }

        request.QuotationId = QuotationId;

        _db.Update(request);
        _db.SaveChanges();

        Log(
            request.Id,
            "PurchaseRequest",
            "اختيار عرض الاسعار",
            ("رقم العرض", QuotationId)
        );



        return Redirect($"/Purchases/Update/Quotations/{Id}/?success=true");
    }

    [HttpPost("Update/Quotations/Note/{Id}")]
    public IActionResult AnalysisUpdateQuotationNote(int Id, [FromForm] IFormCollection post)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);


        if (request.QuotationStat == 1)
            return StatusCode(404);

        request.QuotationNote = post["QuotationNote"];

        _db.Update(request);

        _db.SaveChanges();

        Log(
            request.Id,
            "PurchaseRequest",
            "تعديل ملاحظة العروض",
            ("الملاحظة", request.QuotationNote)
        );

        return Json(new
        {
            success = true,
            message = new List<string> { "تم حفظ الملاحظة" },
            action = "reload",
        });
    }

    [HttpGet("Update/Budget/{Id}")]
    public IActionResult AnalysisUpdateBudget(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.QuotationStat == 0)
            return StatusCode(404);

        _v.PurchasesRequest = request;
        _v.RequestItems = _db.RequestItem.Where(r => r.RequestId == Id).ToList();

        _v.Page.Reload = true;
        _v.Page.Back = $"/Purchases/Index";

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="التحليل", Url=$"/Purchases/Index"},
            new Breadcrumb {Label=_v.PurchasesRequest.Reference, Url=$"/Purchases/Requests"},
        };

        int currentYear = DateTime.Now.Year;

        List<int> years = new List<int>
        {
            currentYear - 1, currentYear, currentYear + 1,
        };

        ViewBag.Years = years;

        if (request.BudgetYear > 1000)
        {
            _v.BcAccounts = _bc.BcAccount.Where(r => r.BudgetYear == request.BudgetYear).ToList();
        }
        else
        {
            _v.BcAccounts = new List<BcAccount>();
        }

        return View(_v);
    }

    [HttpPost("Update/Budget/{Id}")]
    [Can("finance-manager")]
    public IActionResult AnalysisUpdateBudget(int Id, [FromForm] IFormCollection post)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);


        if (request.QuotationStat == 0 || request.BudgetStat == 1)
            return StatusCode(405);

        int currentYear = DateTime.Now.Year;

        List<int> years = new List<int>
        {
            currentYear - 1, currentYear, currentYear + 1,
        };

        // validate account
        var acc = _v.GetAccount(post["BudgetAccount"], int.Parse(post["BudgetYear"]));

        if (acc == null)
            return Json(new
            {
                success = true,
                message = new List<string> { "بيانات الحساب غير صحيحة" },
                action = "",
            });

        if (!years.Contains(int.Parse(post["BudgetYear"])))
            return Json(new
            {
                success = true,
                message = new List<string> { "يجب اختيار سنة مالية" },
                action = "",
            });


        request.BudgetNote = post["BudgetNote"];
        request.BudgetYear = int.Parse(post["BudgetYear"]);
        request.BudgetAccount = post["BudgetAccount"];

        _db.Update(request);
        _db.SaveChanges();

        Log(
            request.Id,
            "PurchaseRequest",
            "تعين حساب الموازنة",
            ("الحساب", request.BudgetAccount)
        );

        return Json(new
        {
            success = true,
            message = new List<string> { "تم حفظ الميزانية" },
            action = "reload",
        });
    }

    [HttpGet("Update/Budget/Approve/{Id}")]
    [Can("finance-manager")]
    public IActionResult AnalysisUpdateBudgetApprove(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.QuotationStat == 0 || request.BudgetYear < 2000)
            return StatusCode(405);

        request.BudgetStat = 1;

        _db.Update(request);
        _db.SaveChanges();

        Log(
           request.Id,
           "PurchaseRequest",
           "اعتماد الحساب المالي"
        );

        var redirectUrl = $"/Purchases/Update/Budget/{Id}/?success=true";

        return Redirect(redirectUrl);
    }

    [HttpGet("Update/Budget/Cancel/{Id}")]
    [Can("finance-manager")]
    public IActionResult AnalysisUpdateBudgetCancel(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.QuotationStat == 0 || request.BudgetStat == 0)
            return StatusCode(405);


        request.BudgetStat = 0;

        _db.Update(request);
        _db.SaveChanges();

        Log(
           request.Id,
           "PurchaseRequest",
           "الغاء اعتماد الحساب المالي"
        );

        var redirectUrl = $"/Purchases/Update/Budget/{Id}/?success=true";

        return Redirect(redirectUrl);
    }

    [HttpGet("Update/Lpo/{Id}")]
    public IActionResult AnalysisUpdateLPO(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        var items = _db.RequestItem.Where(r => r.RequestId == Id && r.RelType == "PurchasesRequestAnalysis").ToList();

        var quotation = _db.PurchaseQuotations.Find(request.QuotationId);

        var supplier = _bc.Supplier.Find(quotation.SupplierId);

        var acc = _v.GetAccount(request.BudgetAccount);


        _v.PurchasesRequest = request;
        _v.RequestItems = items;

        _v.Page.Reload = true;
        _v.Page.Back = $"/Purchases/Index";

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="التحليل", Url=$"/Purchases/Index"},
            new Breadcrumb {Label=_v.PurchasesRequest.Reference, Url=$"/Purchases/Index"},
        };

        _v.PurchaseQuotation = quotation;
        _v.BcAccount = acc;
        _v.Supplier = supplier;

        return View(_v);
    }

    [HttpPost("Update/Lpo/{Id}")]
    public async Task<IActionResult> AnalysisUpdateLPO(int Id, PurchasesAnalysisForm post)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.LpoStat == 1 || request.BudgetStat!=1)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { "لا يمكن اتمام هذا الاجراء" },
                action = "",
            });
        }

        var items = _db.RequestItem.Where(r => r.RequestId == request.Id && r.RelType == "PurchasesRequestAnalysis").ToList();

        double total_cost = 0;

        foreach (var item in post.RequestItems)
        {

            var _item = _db.RequestItem.Where(r => r.RequestId == request.Id && r.RelType == "PurchasesRequestAnalysis" && r.Id == item.Id).FirstOrDefault();

            if (_item != null)
            {
                _item.Cost = item.Cost;

                _db.Update(_item);
                await _db.SaveChangesAsync();

                total_cost += _item.Cost * _item.Quantity;
            }
        }

        request.TotalCost = double.Parse(_v._amount(total_cost));

        request.LpoDeliveryDate = post.PurchasesRequest.LpoDeliveryDate;
        request.LpoExpiry = post.PurchasesRequest.LpoExpiry;
        request.LpoWarrantyNote = post.PurchasesRequest.LpoWarrantyNote;
        request.LpoDeliveryLocation = post.PurchasesRequest.LpoDeliveryLocation;

        _db.Update(request);
        await _db.SaveChangesAsync();

        Log(
            request.Id,
            "PurchaseRequest",
            "تحديث LPO",
            ("المجموع الكلي", request.TotalCost),
            ("صلاحية الضمان", request.LpoWarrantyNote),
            ("تاريخ انتهاء التنفيذ", request.LpoExpiry),
            ("تاريخ الاستلام", request.LpoDeliveryDate)
        );

        return Json(new
        {
            success = true,
            message = new List<string> { "تم تحديث LPO" },
            action = "reload",
        });
    }

    [HttpGet("Update/Lpo/Approve/{Id}")]
    [Can("finance-manager")]
    public IActionResult AnalysisUpdateLpoApprove(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.BudgetStat == 0 || request.LpoStat == 1)
            return StatusCode(405);

        request.LpoStat = 1;

        _db.Update(request);
        _db.SaveChanges();

        Log(
           request.Id,
           "PurchaseRequest",
           "اعتماد امر الشراء"
        );

        var redirectUrl = $"/Purchases/Update/Lpo/{Id}/?success=true";

        return Redirect(redirectUrl);
    }

    [HttpGet("Update/Lpo/Cancel/{Id}")]
    [Can("finance-manager")]
    public IActionResult AnalysisUpdateLpoCancel(int Id)
    {

        var request = _db.PurchasesRequest.Find(Id);

        if (request == null)
            return StatusCode(404);

        if (request.BudgetStat == 0 || request.LpoStat == 0)
            return StatusCode(405);


        request.LpoStat = 0;

        _db.Update(request);
        _db.SaveChanges();

        Log(
           request.Id,
           "PurchaseRequest",
           "الغاء اعتماد امر الشراء"
        );

        var redirectUrl = $"/Purchases/Update/Lpo/{Id}/?success=true";

        return Redirect(redirectUrl);
    }


    [HttpGet("Update/Logs/{Id}")]
    public IActionResult AnalysisUpdateLogs(int Id)
    {


        _v.PurchasesRequest = _db.PurchasesRequest.Find(Id);
        _v.RequestItems = _db.RequestItem.Where(r => r.RequestId == Id).ToList();
        _v.Suppliers = _bc.Supplier.Where(s => s.Id < 8000 || s.Id > 9999).ToList();
        _v.PurchaseQuotations = _db.PurchaseQuotations.Where(r => r.RelId == Id && r.RelType == "Request").ToList();


        _v.Page.Reload = true;
        _v.Page.Back = $"/Purchases/Index";

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="التحليل", Url=$"/Purchases/Index"},
            new Breadcrumb {Label=_v.PurchasesRequest.Reference, Url=$"/Purchases/Requests"},
        };

        return View(_v);
    }


    [HttpGet("Items")]
    [Can("finance-manager")]
    public IActionResult Items()
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="الطلبات", Url=$"/Purchases/Requests"},
        };

        _v.Page.Title = "الطلبات";

        _v.RequestItems = _db.RequestItem.Where(r => r.PurchasesStat == 1 && r.FinanceManagerStat == 0).OrderBy(o => o.CreatedAt).ToList();

        ViewBag.ApprovedCount = _db.RequestItem.Where(r => r.PurchasesStat == 1 && r.FinanceManagerStat == 1 && r.RequestId == 0).Count();
        ViewBag.ItemsCount = _v.RequestItems.Count();


        return View(_v);
    }

    [HttpGet("Items/Approved")]
    [Can("finance-manager|purchases-department")]
    public IActionResult ItemsApproved()
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المشتريات", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="الطلبات", Url=$"/Purchases/Requests"},
        };

        _v.Page.Title = "الطلبات";

        _v.RequestItems = _db.RequestItem.Where(r => r.PurchasesStat == 1 && r.FinanceManagerStat == 1 && r.RequestId == 0).OrderBy(o => o.CreatedAt).ToList();

        ViewBag.ApprovedCount = _v.RequestItems.Count();
        ViewBag.ItemsCount = _db.RequestItem.Where(r => r.PurchasesStat == 1 && r.FinanceManagerStat == 0 && r.RequestId == 0).Count();

        _v.VempDtls = _db.VempDtls.Where(r => r.DeptCode == _v.Profile.DeptCode || r.DeptCode == 2).ToList();


        var empListRaw = _db.VempDtls.Where(r => r.DeptCode == _v.Profile.DeptCode || r.DeptCode == 2).Select(ro => new
        {
            empNo = ro.EmpNo,
            empNameA = ro.EmpNameA,
            requestCount = _db.PurchasesRequest.Where(r2 => r2.EmpNo == ro.EmpNo && r2.DeliveryStat == 0).Count(),
        }).ToList();

        var empList = empListRaw.Select(ro =>
        {
            dynamic emp = new ExpandoObject();
            emp.empNo = ro.empNo;
            emp.empNameA = ro.empNameA;
            emp.requestCount = _db.PurchasesRequest.Where(r2 => r2.EmpNo == ro.empNo && r2.DeliveryStat == 0).Count();

            return emp;

        }).ToList();

        ViewBag.EmpList = empList;

        return View(_v);
    }


    [HttpGet("Items/Approve/{Id}")]
    [Can("finance-manager")]
    public IActionResult ItemsApprove(int Id)
    {

        _v.RequestItems = _db.RequestItem.Where(r => r.PurchasesStat == 1 && r.FinanceManagerStat == 0).OrderBy(o => o.RequestId).ToList();

        var item = _db.RequestItem.Find(Id);

        if (item == null)
            return NotFound();


        item.FinanceManagerStat = 1;


        _db.Update(item);
        _db.SaveChanges();


        return Redirect("/Purchases/Items");
    }




    [HttpPost]
    [Route("Items/Convert")]
    public async Task<IActionResult> ItemsConvert(PurchasesRequest purchase, [FromForm] IFormCollection post)
    {

        if (!post.ContainsKey("items"))
        {
            return Json(new
            {
                success = false,
                message = new List<string> { "يجب اختياري طلب" },
                action = "",
            });
        }

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        var selectedItems = new List<int>();

        foreach (var postItems in post["items"])
        {
            if (int.TryParse(postItems, out int itemId))
            {
                selectedItems.Add(itemId);
            }
        }

        if (selectedItems.Count == 0)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { _("يجب اختياري طلب") },
                action = "",
            });
        }

        foreach (var itemId in selectedItems)
        {

            var check = _db.RequestItem.Where(r => r.RequestId == 0 && r.PurchasesStat == 1 && r.RequestId == 0 && r.FinanceManagerStat == 1 && r.Id == itemId).FirstOrDefault();

            if (check == null)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { _("طلب غير صحيح") },
                    action = "",
                });
            }
        }

        var NewRequest = new PurchasesRequest
        {
            Reference = purchase.Reference,
            EmpNo = purchase.EmpNo,
            LastActionDate = DateTime.Now,
            Date = DateTime.Now,
        };

        _db.PurchasesRequest.Add(NewRequest);
        await _db.SaveChangesAsync();


        foreach (var itemId in selectedItems)
        {

            var item = _db.RequestItem.Where(r => r.RequestId == 0 && r.PurchasesStat == 1 && r.RequestId == 0 && r.FinanceManagerStat == 1 && r.Id == itemId).FirstOrDefault();

            item.RequestId = NewRequest.Id;

            _db.Update(item);

            await _db.SaveChangesAsync();
        }


        return Json(new
        {
            success = true,
            message = new List<string> { "تم انشاء التحليل" },
            action = $"location.href='/Purchases/Items/Approved'",
        });
    }


    [Route("Requests")]
    public IActionResult Requests()
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Purchases", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="Requests", Url=$"/Purchases/Requests"},
        };

        _v.Page.Title = "Requests";

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }
        }

        _v.PurchasesRequests = _db.PurchasesRequest.ToList();


        return View(_v);
    }

    [Route("Print/Request/{Id}")]
    public IActionResult PrintRequest(int Id)
    {

        _v.PurchasesRequest = _db.PurchasesRequest.Find(Id);
        _v.RequestItems = _db.RequestItem.Where(r => r.RequestId == Id).ToList();
        _v.PurchaseQuotations = _db.PurchaseQuotations.Where(r => r.RelId == Id && r.RelType == "Request").ToList();

        _v.Page.Layout = "_PrintLayout";

        if (_v.PurchasesRequest.RelId != 0)
        {
            if (_v.PurchasesRequest.RelType == "CarsServices")
            {
                var CarsServices = _db.CarsServices.Find(_v.PurchasesRequest.RelId);

                _v.OriginApprovedBy = CarsServices.ApprovedBy;
                _v.OriginApprovedAt = CarsServices.ApprovedAt;
            }

            if (_v.PurchasesRequest.RelType == "InventoryRequest")
            {
                var InvRequest = _db.InventoryRequest.Find(_v.PurchasesRequest.RelId);

                _v.OriginApprovedBy = InvRequest.ManagerApproveBy;
            }
        }

        return View(_v);
    }


    [Route("Suppliers")]
    public IActionResult Suppliers()
    {

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="Purchases", Url=$"/Purchases/Index"},
            new Breadcrumb {Label="Suppliers", Url=$"/Purchases/Suppliers"},
        };

        _v.Page.Title = "Suppliers";

        _v.Suppliers = _bc.Supplier.ToList();


        return View(_v);
    }


    [Can("finance-manager")]
    [Route("GetAccounts/{Year}")]
    public IActionResult GetAccounts(int Year)
    {

        var accList = _bc.BcAccount.Where(r => r.BudgetYear == Year).ToList();


        return Json(new
        {
            success = true,
            data = accList,
        });
    }



}

