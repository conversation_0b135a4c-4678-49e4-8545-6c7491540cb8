@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel


@Html.Partial("Inc/_ItemsTabs")

<div class="card shadow">
    @if (Model.RequestItems.Count() > 0)
    {

        <table class="table ">
            <thead class="bg-primary">
                <tr>
                    <th>الرقم</th>
                    <th> مصدر الطلب</th>
                    <th>الطلب</th>
                    <th>العدد</th>
                    <th>ملاحظة</th>
                    <th>ملاحظة القسم</th>
                    <th> </th>
                    <td></td>
                </tr>
            </thead>
            @foreach (var item in Model.RequestItems)
            {
                <tr>
                    <td>@item.Id</td>
                    <td>
                        @if (item.RelType == "InventoryRequest")
                        {
                            <span>المخازن #@item.RelId</span>
                        }

                    </td>
                    <td>@item.Name</td>
                    <td>@item.Quantity</td>
                    <td>@item.Note</td>
                    <td>@item.InventoryNote</td>
                    <td>

                        @if (item.TechnicalRequired == 1 || item.TechnicalStat == 1)
                        {
                            if (item.TechnicalStat == 1)
                            {
                                <a href="#" data-toggle="modal" data-target="#<EMAIL>-mdoal"><span class="badge badge-success"><i class="fa fa-check"></i> ملاحظة فنية</span></a>

                                <div class="modal fade" id="<EMAIL>-mdoal" tabindex="-1" role="dialog" aria-labelledby="item-action-modal"
                                     aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h3 class="modal-title" id="new-vehicle-modalLabel">ملاحظة فنية</h3>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                @item.TechnicalNote

                                            </div>
                                            <div class="modal-footer ">
                                                <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("Close")</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            }
                            if (item.TechnicalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i> ملاحظة فنية</span>
                            }
                        }

                        @if (item.DgApprovalRequired == 1 || item.DgApprovalStat != 0)
                        {
                            if (item.DgApprovalStat == 1)
                            {
                                <span class="badge badge-success"><i class="fa fa-check"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 2)
                            {
                                <span class="badge badge-danger"><i class="fa fa-times"></i>  اعتماد المدير العام</span>
                            }
                        }


                    </td>
                    <td>
                        @if (item.FinanceManagerStat == 0)
                        {
                            <a href="~/Purchases/Items/Approve/@item.Id" class="btn btn-success btn-sm after-confirm"> <i class="fa fa-check"></i> قبول</a>
                        }

                    </td>
                </tr>
            }
        </table>
    }
    else
    {
        <div class="card-body ">
            <p class="text-center text-muted">لا يوجد طلبات جديدة</p>
        </div>
    }
</div>



