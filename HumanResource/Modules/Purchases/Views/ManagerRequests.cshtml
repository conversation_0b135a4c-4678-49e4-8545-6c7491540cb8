﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel

<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._("التحاليل المعلقة")</h3>

    <div>

    </div>
</div>

<div class="card shadow">
    <table class="table datatable">
        <thead><tr>
            <th>@Model._("No")</th>
            <th>@Model._("Date")</th>
            <th>@Model._("Reference")</th>
            
            <th>@Model._("Status")</th>

        </tr></thead>
        <tbody>
            @foreach(var request in Model.PurchasesRequests){
                <tr>
                    <td data-order="@request.Id"><a href="~/Purchases/Manager/ViewRequest/@request.Id">#@request.Id</a></td>
                    <td>@request.Date</td>
                    <td>@request.Reference</td>
                    <td>
                        @if (request.DepartmentManagerApproval == 1)
                        {
                            <span class=" ">
                            موافق عليه
                            </span>
                        }else{
                            <span class="badge badge-danger rounded-pill px-3 ">
                            جديد
                            </span>
                        }
                    </td>
                    <td>
                        
                    </td>

                    
                
                </tr>
            }

            
        </tbody>
    </table>


</div>