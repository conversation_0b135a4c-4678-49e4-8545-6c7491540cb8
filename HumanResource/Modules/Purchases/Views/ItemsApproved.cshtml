@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel

@Html.Partial("Inc/_ItemsTabs")

<form action="~/Purchases/Items/Convert" method="post" class="ajax">
    <div class="card shadow">
        <div class="card-header">
            <button class="btn btn-primary " type="button" data-toggle="modal" data-target="#new-modal">ارفاق الى تحليل</button>
        </div>
        <table class="table ">
            <thead>
                <tr>
                    <td></td>
                    <th>الرقم</th>
                    <th> مصدر الطلب</th>

                    <th>الطلب</th>
                    <th>العدد</th>
                    <th>ملاحظة</th>
                    <th>ملاحظة القسم</th>
                    <th> </th>
                </tr>
            </thead>
            @foreach (var item in Model.RequestItems)
            {
                <tr>
                    <td>
                        @if (item.RequestId == 0 && item.FinanceManagerStat == 1)
                        {
                            <input type="checkbox" name="items" value="@item.Id">
                        }

              

                    </td>
                    <td>@item.Id</td>
                    <td>
                        @if (item.RelType == "InventoryRequest")
                        {
                            <span>المخازن #@item.RelId</span>
                        }

                    </td>
                    <td>@item.Name</td>
                    <td>@item.Quantity</td>
                    <td>@item.Note</td>
                    <td>@item.InventoryNote</td>
                    <td>

                        @if (item.TechnicalRequired == 1 || item.TechnicalStat == 1)
                        {
                            if (item.TechnicalStat == 1)
                            {
                                <a href="#" data-toggle="modal" data-target="#<EMAIL>-mdoal"><span class="badge badge-success"><i class="fa fa-check"></i> ملاحظة فنية</span></a>

                                <div class="modal fade" id="<EMAIL>-mdoal" tabindex="-1" role="dialog" aria-labelledby="item-action-modal"
                                     aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h3 class="modal-title" id="new-vehicle-modalLabel">ملاحظة فنية</h3>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                @item.TechnicalNote

                                            </div>
                                            <div class="modal-footer ">
                                                <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("Close")</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            }
                            if (item.TechnicalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i> ملاحظة فنية</span>
                            }
                        }

                        @if (item.DgApprovalRequired == 1 || item.DgApprovalStat != 0)
                        {
                            if (item.DgApprovalStat == 1)
                            {
                                <span class="badge badge-success"><i class="fa fa-check"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 2)
                            {
                                <span class="badge badge-danger"><i class="fa fa-times"></i>  اعتماد المدير العام</span>
                            }
                        }


                    </td>
                </tr>
            }
        </table>
    </div>

    <div class="modal fade" id="new-modal"  role="dialog" aria-labelledby="new-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-modalLabel"></h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <label for="">المرجع/الموضوع</label>
                    <input type="text" class="form-control" name="Reference"><br>

                    <label>الموظف المسؤول</label>

                    <select class="form-control select2" name="EmpNo">
                        @foreach (var emp in ViewBag.EmpList as IEnumerable<dynamic>)
                        {
                            <option value="@emp.empNo">@emp.empNameA (يعمل على @emp.requestCount)</option>
                        }
                    </select>


                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><i class="fa fa-times"></i> @Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> @Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>