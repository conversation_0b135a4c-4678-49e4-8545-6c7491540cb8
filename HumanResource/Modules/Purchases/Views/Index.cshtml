﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel

<div class="d-flex justify-content-between py-2">



    <h3>@Model._("التحليل")</h3>

    <div>
        <a class="btn btn-sm btn-link text-primary border border-priary mb-1" data-toggle="collapse" href="#report" role="button" aria-expanded="false" aria-controls="report"><i class="far fa-file-chart-line"></i> تقرير</a>

        @*       <a href="~/Purchases/Create" class="btn btn-primary rounded-pill shadow btn-sm ">
        <i class="fas fa-plus"></i>  @Model._("تحليل جديد")
        </a>*@
    </div>
</div>

<div class="collapse" id="report">

    <div class="row">
        @foreach (var reportCard in Model._EmptRequestReport)
        {
            if (reportCard.Employee.EmpNo != 1018 && reportCard.Employee.EmpNo != 213)
            {
                <div class="col-md-3">
                    <div class="card shadow">
                        <div class="card-body ">
                            <div class="d-flex justify-content-between align-items-center">
                                <i class="fa fa-user fa-1x mr-1"></i>
                                <div>
                                    <samll>@reportCard.Employee.EmpNameA</samll>
                                    <h5 class="text-center">
                                        @reportCard.Count
                                    </h5>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            }
        }

    </div>
</div>

<div class="card shadow">
    <table class="table datatable">
        <thead>
            <tr>
                <th>@Model._("No")</th>

                <th>@Model._("Reference")</th>
                <th>الموظف المسؤول</th>
                <th>تاريخ بداية التحليل</th>

                <th>تقدم الطلب</th>

            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.PurchasesRequests)
            {
                <tr>
                    <td data-order="@request.Id"><a href="~/Purchases/Update/@request.Id">#@request.Id</a></td>

                    <td>@request.Reference</td>
                    <td>@Model._h.StaffData(request.EmpNo).EmpNameA</td>
                    <td>@request.Date</td>
                    <td>

                        @if (request.QuotationStat == 1)
                        {
                            <span class="text-success"><i class="fa fa-check"></i> التحليل</span>
                        }
                        else{


                        }

                        @if (request.BudgetStat == 1)
                        {
                            <span class="text-success"><i class="fa fa-check"></i> الموازنة</span>
                        }
                        else{


                        }


                        @if (request.LpoStat == 1)
                        {
                            <span class="text-success"><i class="fa fa-check"></i> امر الشراء</span>
                        }
                        else
                        {

                        }




                    </td>
                </tr>
            }
        </tbody>
    </table>



</div>