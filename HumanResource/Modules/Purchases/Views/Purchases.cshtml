@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel



<div class="d-flex justify-content-between py-2">
    
    <h3>@Model._("Purchases") [@Model.Purchases.Reference]</h3>

    <div>
        <a href="~/Purchases/Delete/@Model.Purchases.Id" class="btn btn-danger rounded-pill px-3 btn-sm after-confirm    ">@Model._("Delete")</a>
    </div>
</div>

<form action="~/Purchases/UpdatePurchases/" method="post" id="app" class="ajax">

<div class="card card-body shadow">
    <div class="row">
        <div class="col-lg-3">
            <label for="">@Model._("Date")</label>
            <input type="date" name="Purchases.Date" required class="form-control"  value="@Model.Purchases.Date.ToString("yyyy-MM-dd")">
        </div>

        <div class="col-lg-3">
            <label for="">@Model._("Reference")</label>
            <input type="text" name="Purchases.Reference" required class="form-control"  value="@Model.Purchases.Reference">
        </div>


        <div class="col-lg-3">
            <label for="">@Model._("Created At")</label>
            <input type="text" disabled  class="form-control"  value="@Model._dt(Model.Purchases.CreatedAt)" >
        </div>



    </div>
</div>

<div class="card card-body shadow">
    <div class="row">
        <div class="col-lg-3">

            <p>@Model.Supplier.Name</p>
          
            
        </div>




    </div>
</div>

<div class="card shadow" >
    
    
    <table class="table">
        <thead class="bg-primary">
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Item")</th>
                <th>@Model._("Description")</th>
                <th>@Model._("Quantity")</th>
                <th>@Model._("Cost")</th>
                <th>@Model._("VAT")</th>
                
                <th></th>
        
            </tr>
        </thead>

        <tbody class="fadeIn">
            <tr v-for="(item,index) in list" class="fadeIn" :style="{display: item.hide?'none':''}">
                <td>{{index+1}}</td>
                <td><textarea :name="'RequestItems['+index+'].Name'" required v-model="item.Name" class="form-control" ></textarea></td>
                <td><textarea :name="'RequestItems['+index+'].Note'" v-model="item.Note" class="form-control" ></textarea></td>
                <td><input type="number" :name="'RequestItems['+index+'].Quantity'" required v-model="item.Quantity" value="1" min="1" class="form-control"></td>
                <td><input type="number" :name="'RequestItems['+index+'].Cost'" required v-model="item.Cost" value="0" min="0" class="form-control"></td>
                <td><input type="number" :name="'RequestItems['+index+'].Tax'" required v-model="item.Tax" value="0" min="0" class="form-control"></td>
                <td class="text-center">
                    <button type="button" class="btn btn-outline-danger" @@click="list.splice(index,1)"><i class="fa fa-trash"></i></button>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                
                <td colspan="2" class="text-center"><button type="button" class="btn btn-secondary rounded-pill px-3" @@click="AddItem()"><i class="fa fa-plus"></i> @Model._("Add row")</button></td>
            </tr>
        </tfoot>
    </table>

  
    <div class="row">
        <div class="col-lg-6 px-4">
            <label for="">@Model._("Note")</label>
            <textarea name="Purchases.Note" class="form-control">@Model.Purchases.Note</textarea>
        </div>
    </div>
    <br>
    <br>

    <div class="card-footer">
        <button type="submit" class="btn btn-primary rounded-pill px-3">@Model._("Save")</button>
    </div>
</div>


<input type="hidden" name="Purchases.Id" value="@Model.Purchases.Id">
</form>


<script>
    let app = new Vue({
        el:"#app",
        data:{
            list:@Html.Raw(Model.ListToJson(Model.RequestItems)),
        },
        methods:{
            AddItem(){
                let job = this
                
                job.list.push({
                    hide:1
                });

                setTimeout(function(){
                    $("tr").fadeIn()
                },50);
            },

          
        }
    });
</script>