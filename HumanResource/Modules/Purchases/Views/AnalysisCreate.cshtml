@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel



<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._("New request")</h3>

    <div>
        
    </div>
</div>

<form action="~/Purchases/Create" method="post" id="app" class="ajax">

<div class="card card-body shadow">
    <div class="row">
        <div class="col-lg-3">
            <label for="">@Model._("Date")</label>
            <input type="date" name="PurchasesRequest.Date" class="form-control"  value="@DateTime.Now.ToString("yyyy-MM-dd")">
        </div>

        <div class="col-lg-3">
            <label for="">@Model._("Reference")</label>
            <input type="text" name="PurchasesRequest.Reference" class="form-control"  >
        </div>





    </div>
</div>

<div class="card shadow" >
    

    <table class="table">
        <thead class="bg-primary">
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Item")</th>
                <th>@Model._("Description")</th>
                <th>@Model._("Quantity")</th>
                
                <th></th>
        
            </tr>
        </thead>

        <tbody class="fadeIn">
            <tr v-for="(item,index) in list" class="fadeIn" :style="{display: item.hide==1?'none':''}">
                <td>{{index+1}}</td>
                <td><textarea :name="'RequestItems['+index+'].Name'" class="form-control" ></textarea></td>
                <td><textarea :name="'RequestItems['+index+'].Note'" class="form-control" ></textarea></td>
                <td><input type="number" :name="'RequestItems['+index+'].Quantity'" value="1" min="1" class="form-control"></td>
                <td class="text-center">
                    <button type="button" class="btn btn-outline-danger" @@click="list.splice(index,1)"><i class="fa fa-trash"></i></button>
                </td>
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td class="text-center"><button type="button" class="btn btn-secondary rounded-pill px-3" @@click="AddItem()"><i class="fa fa-plus"></i> @Model._("Add row")</button></td>
            </tr>
        </tfoot>
    </table>

  
    <div class="card-body">
            <div class="row">
                <div class="col-lg-6">
                    <label for="">@Model._("Note")</label>
                    <textarea name="PurchasesRequest.Note" class="form-control"></textarea>
                </div>
            </div>
            <br>
            <br>
    </div>

    <div class="card-footer">
        <button type="submit" class="btn btn-primary rounded-pill px-3">@Model._("Submit")</button>
    </div>
</div>

</form>


<script>
    let app = new Vue({
        el:"#app",
        data:{
            list:[
                {
                    hide:0
                }
            ],
        },
        methods:{
            AddItem(){
                let job = this
                
                job.list.push({
                    hide:1
                });

                setTimeout(function(){
                    $("tr").fadeIn()
                },50);
            },

          
        }
    });
</script>