@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel

@{
    Layout = Model.Page.Layout;
}


<style>
    @@page{
        margin: 10mm;
    }
    html{
        direction: rtl !important;
    }
    .border{
        border: 1px solid black !important;
    }

    .border-2{
        border: 2px solid black !important;
    }
</style>

<div class="text-center">
    <img  src="~/img/gsclogo.png"  style="height:100px" />
    <h3 class="mt-1">استمارة التعليمات وتحليل العروض</h3>
    <h5>دائرة الشؤون المالية</h5>
</div>

<div>
    <table class="table table-sm text-right border-2" >
        <thead class="border-2">
            <tr>
                <td class="border"></td>
                <td class="border">@Model._("Item")</td>
                <td class="border">@Model._("Description")</td>
                <td class="border">@Model._("Quantity")</td>
                <td class="border"></td>
                 
            </tr>
        </thead>
        <tbody>
            @foreach(var item in Model.RequestItems){
                <tr>
                    <td class="border"></td>
                    <td class="border">@item.Name</td>
                    <td class="border">@item.Note</td>
                    <td class="border">@item.Quantity</td>
                    <td class="border">
                        @if(item.InventoryNote!=null){
                            @if(item.InventoryNote.Length>0){
                                <p>
                                    @Model._("Inventory Note"):
                                    @item.InventoryNote
                                </p>
                            }
                        }
                        
                        @if(item.TechnicalNote!=null){
                            @if(item.TechnicalNote.Length>0){
                                <p>
                                    @Model._("Technical Note"):
                                    @item.TechnicalNote
                                    <br>
                                    @item.TechnicalDepartmentCode
                                </p>
                            }
                        }

                        @if(item.DgNo!=0){
                            <p>
                                @Model._("Director General"):
                                @item.DgApprovalStat
                            </p>
                        }
                        
                        
                    </td>
                 

                </tr>
            }
        </tbody>
    </table>


    <table class="table border-2 text-right" >
        <thead class="border-2">
            <tr class="border-2">
                <td class="border-2"></td>
                <td class="border-2">
                    @Model._("No.")
                </td>
                <td class="border-2">
                    @Model._("Supplier")
                </td>
                <td class="border-2">
                    @Model._("Price")
                </td>
                <td class="border-2">
                    @Model._("Tax")
                </td>
                <td class="border-2">
                    @Model._("Total")
                </td>
              
            </tr>
        </thead>
        <body>
            @foreach (var ana in Model.PurchaseQuotations)
            {
                 var supplier = Model.GetSupplier(ana.SupplierId);

                 <tr class="border">
                    <td class="border text-center">
                        @if(ana.SupplierId==Model.PurchasesRequest.QuotationId){
                            <span>&#10004</span>
                        }
                    </td>
                    <td class="border">@supplier.Id</td>
                    <td class="border">@supplier.Name</td>

                    <td class="border">@Model._amount(ana.TotalCost-ana.TotalTax)</td>
                    <td class="border">@Model._amount(ana.TotalTax)</td>
                    <td class="border" >@Model._amount(ana.TotalCost)</td>
                 </tr>
            }
        </body>
    </table>

    <br>

    <h5 class="text-right">@Model._("Analysis")</h5>
    <table class="table border-2 text-right">
        <tr>
            <td colspan="5" class="border">
                @Model.PurchasesRequest.AnalysisNote
            </td>
        </tr>
        <tr>
            <td class="border">@Model._("Prepared by")</td>
            <td class="border">@Model.PurchasesRequest.AnalysisBy</td>
            <td class="border">@Model._("Approved by")</td>
            @if(Model.PurchasesRequest.ManagerApproval==1){
                <td class="border">@Model.PurchasesRequest.ManagerNo</td>
            }else{
                <td class="border"></td>
                <td class="border"></td> 
            }
            
            
        </tr>
    </table>

    <h5 class="text-right">الموازنة</h5>
    <table class="table border-2 text-right">

        <thead class="border-2">
            <tr >
            <td class="border">@Model._("Budget")</td>
            <td class="border" colspan="2">@Model._("Account")</td>
           
            <td class="border">@Model._("By")</td>
            <td class="border">@Model._("Date")</td>
     
        </tr>
        </thead>
  
        <tr>
            <td class="border">@Model.PurchasesRequest.BudgetYear</td>
            <td class="border">@Model.PurchasesRequest.BudgetAccount</td>
            <td class="border">@Model.PurchasesRequest.BudgetAccountName</td>

           
            
            
        </tr>
    </table>

    <h5 class="text-right">لاستعلام قسم الخزينة</h5>

    <table class="table border-2 text-right">

        <thead class="border-2">
            <tr>
                <td class="border">رقم الحاسب الالي</td>
                <td class="border" colspan="2">رقم اليومية</td>
            
                <td class="border">رقم امر الصرف</td>
                <td class="border">التاريخ</td>
            </tr>
        </thead>
  
        <tr>
            <td class="border"></td>
            <td class="border"></td>
            <td class="border"></td>
            <td class="border"></td>
            <td class="border"></td>

        </tr>
    </table>


</div>