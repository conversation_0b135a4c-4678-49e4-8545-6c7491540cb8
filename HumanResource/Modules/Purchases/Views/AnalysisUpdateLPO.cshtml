﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel


@Html.Partial("Inc/_RequestTabs")




<form action="~/Purchases/Update/Lpo/@Model.PurchasesRequest.Id" method="post" id="app" class="ajax">

    

    <div class="card shadow">
        <div class="card-body">
            
            <div class="row">
                <div class="col-md-4">
                    <span>رقم الحاسب الالي</span>
                    <p><EMAIL></p>
                </div>
                <div class="col-md-4">
                    <span>تاريخ امر الشراء</span>
                    <p>@Model.PurchasesRequest.LpoDate</p>
                </div>

                <div class="col-md-4">
                </div>

                <div class="col-md-4">
                    <span>رقم المورد</span>
                    <p>@Model.Supplier.Id</p>
                </div>

                <div class="col-md-4">
                    <span>اسم المورد</span>
                    <p>@Model.Supplier.Name</p>
                </div>

                <div class="col-md-4">
                    <span> رقم السجل التجاري</span>
                    <p>@Model.Supplier.Id</p>
                </div>
                <div class="col-md-4">
                    <span> عنوان المورد</span>
                    <p>--</p>
                </div>

                <div class="col-md-4">
                    <span>  رقم الميزانية</span>
                    <p>@Model.BcAccount.Account</p>
                </div>

                <div class="col-md-4">
                    <span>  اسم الميزانية</span>
                    <p>@Model.BcAccount.Name</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow">


        <table class="table">
            <thead class="bg-primary">
                <tr>
                    <th>@Model._("No")</th>
                    <th>@Model._("Item")</th>
                    <th>@Model._("Description")</th>
                    <th>@Model._("Quantity")</th>
                    <th>@Model._("Unit")</th>
                    <th>@Model._("Price") (Inc TAX)</th>

                    <th>المجموع</th>

                </tr>
            </thead>

            <tbody class="fadeIn">
                <tr v-for="(item,index) in list" class="fadeIn" :style="{display: item.hide?'none':''}">
                    <td>
                        <input type="hidden" v-model="item.Id" :name="'RequestItems['+index+'].Id'" />
                        {{index+1}}
                    </td>
                    <td><textarea disabled v-model="item.Name" class="form-control"></textarea></td>
                    <td><textarea disabled v-model="item.Note" class="form-control"></textarea></td>
                    <td><input disabled v-model="item.Quantity" value="1" min="1" class="form-control"></td>
                    <td>
                        <input type="text" disabled class="form-control" v-model="item.Unit"  placeholder="@Model._("Unit")" />
                    </td>
                    <td>
                        <input type="number" step="any" v-model="item.Cost" :name="'RequestItems['+index+'].Cost'" class="form-control text-center text-monospace" placeholder="@Model._("Price")" />
                    </td>

                    <td class="text-center text-monospace">
                        {{ item.Cost*item.Quantity }}
                    </td>
                </tr>
            </tbody>
            
            <tfoot class="">
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td colspan="2" class="text-end"> المجموع الكلي</td>

                    
                    <th class="text-center text-monospace">
                        {{total_cost}}
                    </th>
                    
                    
                </tr>
            </tfoot>
            

        </table>


        <div class="card-body">
            <div class="row">
                <div class="col-lg-6 ">
                    <label for="">@Model._("Note")</label>
                    <textarea disabled class="form-control">@Model.PurchasesRequest.AnalysisNote</textarea>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-lg-4 p-1">
                    <label for="">مكان الاستلام</label>
                    <input type="text" class="form-control" name="PurchasesRequest.LpoDeliveryLocation" required value="@Model.PurchasesRequest.LpoDeliveryLocation" @(Model.PurchasesRequest.LpoStat != 0 ? "disabled" : "") />
                </div>
                <div class="col-lg-4 p-1">
                    <label for="">تاريخ الاستلام</label>
                    <input type="date" class="form-control" name="PurchasesRequest.LpoDeliveryDate" required value="@Model._d(Model.PurchasesRequest.LpoDeliveryDate)" @(Model.PurchasesRequest.LpoStat != 0 ? "disabled" : "") />
                </div>
                <div class="col-lg-4 p-1">
                    <label for="">تاريخ انتهاء التنفيذ</label>
                    <input type="date" class="form-control" name="PurchasesRequest.LpoExpiry" required value="@Model._d(Model.PurchasesRequest.LpoExpiry)" @(Model.PurchasesRequest.LpoStat != 0 ? "disabled" : "") />
                </div>
                <div class="col-lg-4 p-1">
                    <label for="">صلاحية الضمان</label>
                    <input type="text" class="form-control" name="PurchasesRequest.LpoWarrantyNote" required value="@Model.PurchasesRequest.LpoWarrantyNote" @(Model.PurchasesRequest.LpoStat != 0 ? "disabled" : "") />
                </div>
                <div class="col-lg-4 p-1">
                    <label for="">غرامة التاخير</label>
                    <input type="text" class="form-control" name="PurchasesRequest.LpoDeliveryLateNote" required value="@Model.PurchasesRequest.LpoDeliveryLateNote" @(Model.PurchasesRequest.LpoStat != 0 ? "disabled" : "") />
                </div>
            </div>
        </div>


        <br>

        @if (Model.PurchasesRequest.LpoStat == 0)
        {
            <div class="card-footer">

                <button type="submit" class="btn btn-primary "><i class="fa fa-save"></i> @Model._("Save")</button>

            </div>
        }
    </div>


</form>


@if (Model.Can("finance-manager") && Model.PurchasesRequest.BudgetStat == 1)
{
    <br />
    <h5>اجراء مدير دائرة المالية</h5>

    @if (Model.PurchasesRequest.LpoStat == 0)
    {
        <a href="~/Purchases/Update/Lpo/Approve/@Model.PurchasesRequest.Id" class="btn btn-success after-confirm"><i class="fa fa-check"></i> اعتماد امر الشراء</a>
    }
    else
    {
        <a href="~/Purchases/Update/Lpo/Cancel/@Model.PurchasesRequest.Id" class="btn btn-danger after-confirm"><i class="fa fa-times"></i> الغاء اعتماد امر الشراء</a>
    }
}

<script>
    let app = new Vue({
        el: "#app",
        data: {
            list: @Html.Raw(Model.ListToJson(Model.RequestItems)),
        },
        computed:{
            total_cost(){

                var total = 0;

                for (i = 0; i < this.list.length; i++) {
                    total += this.list[i].Cost * this.list[i].Quantity
                }

                return window._amount(total);
            }
        }
    });
</script>