﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel


@Html.Partial("Inc/_RequestTabs")




<div class="card shadow">
    <table class="table">
        <thead class="bg-primary">
            <tr>

                <td>

                </td>
                <th>
                    رقم العرض
                </th>
                <th>
                    المورد
                </th>
                <th>
                    السعر
                </th>
                <th>مرفق</th>
                <th>

                </th>
            </tr>
        </thead>
        <tbody>
            @foreach(var quotation in Model.PurchaseQuotations)
            {
                var supplier = Model.GetSupplier(quotation.SupplierId);
                <tr>
                    <td class="text-start">
                        

                        @if (Model.PurchasesRequest.QuotationStat == 0)
                        {
                            @if (Model.PurchasesRequest.QuotationId == quotation.Id)
                            {
                                <a class="after-confirm" href="~/Purchases/Update/Quotations/Select/@Model.PurchasesRequest.Id/@quotation.Id">
                                    <i class="far fa-check-circle fa-2x text-success"></i>
                                </a>
                            }
                            else
                            {
                                <a class="after-confirm" href="~/Purchases/Update/Quotations/Select/@Model.PurchasesRequest.Id/@quotation.Id">
                                    <i class="fal fa-circle fa-2x text-danger"></i>
                                </a>
                            }
                        }

                        @if (Model.PurchasesRequest.QuotationStat == 1)
                        {
                            @if (Model.PurchasesRequest.QuotationId == quotation.Id)
                            {
                                <i class="far fa-check-circle fa-2x text-success"></i>
                            }
                            else
                            {
                                <i class="fal fa-circle fa-2x text-danger"></i>
                            }
                        }
                    </td>
                    <td>
                        @quotation.Id
                    </td>
                    <td>
                        <p class="m-0">#@supplier.Id - @supplier.Name</p>
                    </td>
                    <td class="text-monospace">
                          @Model._amount(quotation.TotalCost)
                    </td>
                    <td>
                        @if (quotation.FileGuid != null)
                        {
                            
                            <div>

                                <a href="@Model._h.GetFile(quotation.FileGuid)"><i class="fas fa-paperclip fa-2x"></i></a>
                            </div>
                           
                        }
                    </td>
                    <td class="text-center">
                        @if (Model.PurchasesRequest.QuotationId != quotation.Id && Model.PurchasesRequest.QuotationStat == 0)
                        {
                            <a href="~/Purchases/Update/Quotations/Delete/@Model.PurchasesRequest.Id/@quotation.Id" class="btn btn-danger btn-sm after-confirm"><i class="fa fa-trash-alt"></i></a>
                        }
                    </td>
                </tr>
            }
        </tbody>
        <tfoot class="">
            <tr>
                <td></td>
                <td colspan="5" class="text-end">
                    @if (Model.PurchasesRequest.QuotationStat == 0)
                    {
                        <button class="btn btn-sm btn-secondary " data-toggle="modal" data-target="#new-qout-modal"><i class="fa fa-plus"></i> عرض اسعار جديد</button>
                    }
                </td>
            </tr>
        </tfoot>
    </table>
</div> 

<div class="">
    @if (Model.PurchasesRequest.ManagerApproval == 0)
    {

        <form action="~/Purchases/Update/Quotations/Create/@Model.PurchasesRequest.Id" method="post" class="ajax">

            <div class="modal fade" id="new-qout-modal" role="dialog"
                 aria-labelledby="new-qout-modalLabel" aria-hidden="true">

                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title" id="new-qout-modalLabel">
                                عرض اسعار جديد
                            </h3>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div>
                                        <label for="">الموردون</label>
                                        <select name="Quotation.SupplierId" class="select2">
                                            <option disabled selected hidden>اختر مورد</option>
                                            @foreach (var supplier in Model.Suppliers)
                                            {
                                                <option value="@supplier.Id" data-subtext="@supplier.Address"> @supplier.Id -  @supplier.Name</option>
                                            }

                                        </select>
                                    </div>
                                    <br>


                                    <label for="">مرفق</label>
                                    <input type="file" name="File" class="form-control">
                                    <br>
                                </div>

                                <div class="col-lg-6">

                                    <label for="">مجموع السعر</label>
                                    <input type="number" name="Quotation.TotalCost" required class="form-control" value="0" step="any"><br>

                                    <label for="">مجموع الضريبة</label>
                                    <input type="number" name="Quotation.TotalTax" class="form-control" value="0" step="any"><br>

                                    
                                </div>

                                <div class="col-lg-12">
                                    <label for="">ملاحظة</label>
                                    <textarea name="Quotatoin.Note" class="form-control"></textarea>
                                </div>
                            </div>


                        </div>
                        <div class="modal-footer d-flex">
                            <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("Close")</button>
                            <button type="submit" class="btn btn-primary l"><i class="fa fa-save"></i> @Model._l("Submit")</button>
                        </div>
                    </div>
                </div>
            </div>


        </form>

    }

</div>
<form action="~/Purchases/Update/Quotations/Note/@Model.PurchasesRequest.Id" method="post" class="ajax">
    <div class="card shadow">
        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <label for="">التحليل النهائي للعروض</label>

                    <textarea @(Model.PurchasesRequest.QuotationStat == 1 ? "disabled=''" : "") name="QuotationNote" class="form-control" rows="5" required>@Model.PurchasesRequest.QuotationNote</textarea>
                    <br>
                    @if (Model.PurchasesRequest.QuotationStat == 0)
                    {
                        <button class="btn btn-primary "><i class="fa fa-save"></i> @Model._("Save")</button>
                    }
                </div>


            </div>
        </div>
    </div>

</form>


@if (Model.Can("finance-manager") && Model.PurchaseQuotations.Count > 0 && Model.PurchasesRequest.QuotationId != 0)
{
    <br />
    <h5>اجراء مدير دائرة المالية</h5>

    @if (Model.PurchasesRequest.QuotationStat==0)
    {
        <a href="~/Purchases/Update/Quotations/Approve/@Model.PurchasesRequest.Id" class="btn btn-success after-confirm"><i class="fa fa-check"></i> اعتماد العروض والتحليل</a>
    }else{
        if (Model.PurchasesRequest.BudgetStat == 0)
        {
            <a href="~/Purchases/Update/Quotations/Cancel/@Model.PurchasesRequest.Id" class="btn btn-danger after-confirm"><i class="fa fa-times"></i> الغاء اعتماد العروض والتحليل</a>
        }else
        {
            <a href="#" disabled class="btn btn-danger disabled"><i class="fa fa-times"></i> الغاء اعتماد العروض والتحليل</a> <i class="fas fa-exclamation-circle mx-1" title="تم اعتماد الحساب المالي"></i>
        }
    }
}



