﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel


@Html.Partial("Inc/_RequestTabs")



<div>
    <p class="px-3 mb-0 text-muted"></p>
    <table class=" table-sm table-borderless text-muted">
        @foreach (var log in Model._h.GetLogs(Model.PurchasesRequest.Id, "PurchaseRequest"))
        {
            <tr>
                <td><i class="far fa-clock"></i></td>
                <td>
                    @log.Rem
                </td>
                <td dir="ltr">
                    @Model._dt(log.TimeStamp)
                </td>

                <td>
                    @Model._h.StaffData(log.UserId).EmpNameA
                </td>
            </tr>
        }
    </table>
</div>