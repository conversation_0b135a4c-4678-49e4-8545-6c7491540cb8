﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel







<div class="d-flex justify-content-end my-1">
    <div>

        @*@if( Model.PurchasesRequest.ManagerApproval!=0 && Model.PurchasesRequest.PurchasesId==0){
        <a href="~/Purchases/ConvertRequest/@Model.Ec(Model.PurchasesRequest.Id)" class="btn btn-primary rounded-pill px-3 btn-sm after-confirm  ">@Model._("Convert to Purchases")</a>
        }*@

        @if (Model.PurchasesRequest.ManagerApproval != 0 && Model.PurchasesRequest.PurchasesId == 0)
        {
            <a href="~/Purchases/DeleteRequest/@Model.PurchasesRequest.Id" class="btn btn-danger rounded-pill px-3 btn-sm after-confirm  ">@Model._("Delete")</a>
        }

    </div>
</div>

@Html.Partial("Inc/_RequestTabs")



<form action="~/Purchases/Update/@Model.PurchasesRequest.Id" method="post" id="app" class="ajax">

    <div class="card card-body shadow">
        <div class="row">
            <div class="col-lg-3 p-1">
                <label for="">@Model._("Date")</label>
                <input type="date" name="PurchasesRequest.Date" @(Model.PurchasesRequest.QuotationStat != 0 ? "disabled" : "") required class="form-control" value="@Model.PurchasesRequest.Date.ToString("yyyy-MM-dd")">
            </div>

            <div class="col-lg-3 p-1">
                <label for="">@Model._("Reference")</label>
                <input type="text" name="PurchasesRequest.Reference" @(Model.PurchasesRequest.QuotationStat != 0 ? "disabled" : "") required class="form-control" value="@Model.PurchasesRequest.Reference">
            </div>


            <div class="col-lg-3">
                <label>الموظف المسؤول</label>

                <select class="form-control select2" name="EmpNo">
                    @foreach (var emp in ViewBag.EmpList as IEnumerable<dynamic>)
                    {
                        if (emp.empNo == Model.PurchasesRequest.EmpNo)
                        {
                            <option selected value="@emp.empNo">@emp.empNameA (يعمل على @emp.requestCount)</option>
                        }
                        else
                        {
                            <option value="@emp.empNo">@emp.empNameA (يعمل على @emp.requestCount)</option>
                        }
                    }
                </select>
            </div>

            

        </div>

        <div>
            <button class="btn btn-primary"><i class="fa fa-save"></i> حفظ</button>
        </div>
    </div>

</form>



    <div class="card shadow">


        <table class="table ">
            <thead class="bg-primary">
                <tr>
                    <th>الرقم</th>
                    <th> مصدر الطلب</th>

                    <th>الطلب</th>
                    <th>العدد</th>
                    <th>ملاحظة</th>
                    <th>ملاحظة القسم</th>
                    <th> </th>
                </tr>
            </thead>
            @foreach (var item in Model.RequestItems)
            {
                <tr>

                    <td>@item.Id</td>
                    <td>
                        @if (item.RelType == "InventoryRequest")
                        {
                            <span>المخازن #@item.RelId</span>
                        }

                    </td>
                    <td>@item.Name</td>
                    <td>@item.Quantity</td>
                    <td>@item.Note</td>
                    <td>@item.InventoryNote</td>
                    <td>

                        @if (item.TechnicalRequired == 1 || item.TechnicalStat == 1)
                        {
                            if (item.TechnicalStat == 1)
                            {
                                <a href="#" data-toggle="modal" data-target="#<EMAIL>-mdoal"><span class="badge badge-success"><i class="fa fa-check"></i> ملاحظة فنية</span></a>

                                <div class="modal fade" id="<EMAIL>-mdoal" tabindex="-1" role="dialog" aria-labelledby="item-action-modal"
                                     aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h3 class="modal-title" id="new-vehicle-modalLabel">ملاحظة فنية</h3>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                @item.TechnicalNote

                                            </div>
                                            <div class="modal-footer ">
                                                <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("Close")</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            }
                            if (item.TechnicalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i> ملاحظة فنية</span>
                            }
                        }

                        @if (item.DgApprovalRequired == 1 || item.DgApprovalStat != 0)
                        {
                            if (item.DgApprovalStat == 1)
                            {
                                <span class="badge badge-success"><i class="fa fa-check"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 0)
                            {
                                <span class="badge badge-warning"><i class="fa fa-clock"></i>  اعتماد المدير العام</span>
                            }

                            if (item.DgApprovalStat == 2)
                            {
                                <span class="badge badge-danger"><i class="fa fa-times"></i>  اعتماد المدير العام</span>
                            }
                        }


                    </td>
                </tr>
            }
        </table>

    </div>

