﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel


<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._("Suppliers")</h3>

    <div>
        <a href="#" data-toggle="modal" data-target="#new-supplier-modal" class="btn btn-primary rounded-pill shadow btn-sm ">
            <i class="fas fa-plus"></i>  @Model._("New supplier")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable">
        <thead><tr>
            <th>@Model._("CR Number")</th>
            <th>@Model._("Fin Code")</th>
            <th>@Model._("Name")</th>
            <th>@Model._("Contact")</th>
            
            <th>@Model._("Status")</th>

        </tr></thead>
        <tbody>
            @foreach(var supplier in Model.Suppliers){
                <tr>
                    <td data-order="@supplier.Id"><a href="#">#@supplier.Id</a></td>
                    <td>@supplier.Id</td>
               
                    <td>@supplier.Name</td>
                
                
                </tr>
            }
        </tbody>
    </table>
</div>


<form action="~/Purchases/CreateSupplier/" method="post" class="ajax">

 <div class="modal fade" id="new-supplier-modal" role="dialog"
            aria-labelledby="new-supplier-modalLabel" aria-hidden="true">

        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-supplier-modalLabel">
                        @Model._("New Supplier")</h3>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            
                            <label for="">@Model._("Name")</label>
                            <input type="text" name="Supplier.Name" required class="form-control"><br>


                            <label for="">@Model._("CR Number")</label>
                            <input type="text" name="Supplier.CrNo" required class="form-control"><br>

                            @if(true){
                                <label for="">@Model._("Fin Code")</label>
                                <input type="text" name="Supplier.FinCode" required class="form-control"><br>
                            }
                        

                            
                        </div>

                        <div class="col-lg-6">
                            <label for="">@Model._("Contact name")</label>
                            <input type="text" class="form-control" name="Supplier.ContactName"><br>

                            <label for="">@Model._("Contact phone")</label>
                            <input type="text" required class="form-control" name="Supplier.ContactPhone"><br>

                            <label for="">@Model._("Contact email")</label>
                            <input type="text" required class="form-control" name="Supplier.ContactEmail"><br>


                            <label for="">@Model._("Attachment")</label>
                            <input type="file" name="File" class="form-control">
                            <br>
                        </div>

                        <div class="col-lg-12">
                            <label for="">@Model._("Decription")</label>
                            <textarea name="Supplier.Decription" class="form-control"></textarea><br>
                        </div>

                        <div class="col-lg-12">
                            <label for="">@Model._("Note")</label>
                            <textarea name="Supplier.Note" class="form-control"></textarea>
                        </div>
                    </div>


                </div>
                <div class="modal-footer d-flex">
                    
                    
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Add")</button>
                </div>
            </div>
        </div>
    </div>
</form>