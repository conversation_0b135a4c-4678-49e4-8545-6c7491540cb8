﻿
<div class="d-flex justify-content-between mb-1">
    
    <h3>التحليل #@Model.PurchasesRequest.Id [@Model.PurchasesRequest.Reference]</h3>

    
    <div >
        @if( Model.PurchasesRequest.ManagerApproval !=0){
          <a class="btn btn-success rounded-pill px-3 btn-sm" target="_blank" href="/Purchases/Print/Request/@Model.PurchasesRequest.Id"><i class="fa fa-print"></i> @Model._("Print")</a>

        }
    </div>

</div>


<div class="d-flex my-2">

    <div>
        <a class="btn btn-sm mr-1  btn-@(ViewContext.RouteData.Values["action"].ToString()=="AnalysisUpdate" ? "primary" : "secondary")" href="~/Purchases/Update/@Model.PurchasesRequest.Id">الطلب</a>

        <a class="btn btn-sm mr-1   btn-@(ViewContext.RouteData.Values["action"].ToString()=="AnalysisUpdateAnalysis" ? "primary" : "secondary")" href="~/Purchases/Update/Analysis/@Model.PurchasesRequest.Id">التحليل</a>

        <a class="btn btn-sm mr-1  btn-@(ViewContext.RouteData.Values["action"].ToString()=="AnalysisUpdateQuotations" ? "primary" : "secondary")" href="~/Purchases/Update/Quotations/@Model.PurchasesRequest.Id">العروض</a>

        <a class="btn  btn-sm mr-1  btn-@(ViewContext.RouteData.Values["action"].ToString()=="AnalysisUpdateBudget" ? "primary" : "secondary") @(Model.PurchasesRequest.QuotationStat==0 ? "disabled" : "") " href="~/Purchases/Update/Budget/@Model.PurchasesRequest.Id">الحساب المالي</a>
       
        <a class="btn  btn-sm mr-1  btn-@(ViewContext.RouteData.Values["action"].ToString()=="AnalysisUpdateLPO" ? "primary" : "secondary") @(Model.PurchasesRequest.BudgetStat==0 ? "disabled" : "") " href="~/Purchases/Update/LPO/@Model.PurchasesRequest.Id">امر الشراء</a>

        <a class="btn  btn-sm mr-1  btn-@(ViewContext.RouteData.Values["action"].ToString()=="AnalysisUpdateLogs" ? "primary" : "secondary") " href="~/Purchases/Update/Logs/@Model.PurchasesRequest.Id">سجل النشاط</a>
    </div>
    
</div>

