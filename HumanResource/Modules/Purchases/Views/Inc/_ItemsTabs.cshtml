﻿<div class="d-flex my-2">

    <div>
        <a class="btn btn-sm mr-1  btn-@(ViewContext.RouteData.Values["action"].ToString()=="ItemsApproved" ? "primary" : "secondary")" href="~/Purchases/Items/Approved">الطلبات 
            @if (ViewBag.ApprovedCount>0)
            {
                <span class="badge badge-danger">@ViewBag.ApprovedCount</span>
            }
        </a>

        <a class="btn btn-sm mr-1   btn-@(ViewContext.RouteData.Values["action"].ToString()=="Items" ? "primary" : "secondary")" href="~/Purchases/Items">
            الاعتماد
            @if (ViewBag.ItemsCount > 0)
            {
                <span class="badge badge-danger">@ViewBag.ItemsCount</span>
            }
        </a>


    </div>

</div>