﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel

<div class="d-flex justify-content-between py-2">



    <h3>@Model._("التحليل")</h3>

    <div>
        <a href="~/Purchases/NewRequest" class="btn btn-primary rounded-pill shadow btn-sm ">
            <i class="fas fa-plus"></i>  @Model._("تحليل جديد")
        </a>
    </div>
</div>

<div class="card shadow">
    <table class="table datatable">
        <thead>
            <tr>
                <th>@Model._("No")</th>
                <th>@Model._("Date")</th>
                <th>@Model._("Reference")</th>

                <th>الموظف المسؤول</th>
                <th>الموظف المسؤول</th>

            </tr>
        </thead>
        <tbody>
            @foreach (var request in Model.PurchasesRequests)
            {
                <tr>
                    <td data-order="@request.Id"><a href="~/Purchases/ViewRequest/@Model.Ec(request.Id)">#@request.Id</a></td>
                    <td>@request.Date</td>
                    <td>@request.Reference</td>
                    

                </tr>
            }
            </tbody>
        </table>



    </div>