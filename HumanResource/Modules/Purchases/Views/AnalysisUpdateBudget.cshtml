﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel




@Html.Partial("Inc/_RequestTabs")



<form action="/Purchases/Update/Budget/@Model.PurchasesRequest.Id" method="post" class="ajax">

<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-body">

                <div class="row">
                    <div class="col-md-3">
                        <label>السنة المالية</label>
                            <select class="form-control" name="BudgetYear" onchange="updateAccounts()" @(Model.PurchasesRequest.BudgetStat == 1 ? "disabled=''" : "")>
                            <option>اختر السنة المالية</option>

                            @foreach(var year in ViewBag.Years)
                            {
                                if (Model.PurchasesRequest.BudgetYear == year)
                                {
                                    <option selected value="@year">@year</option>
                                }
                                else
                                {
                                    <option  value="@year">@year</option>
                                }
                            }
                        </select>
                    </div>
                    <div class="col-md-5">
                        <label> الحساب المالي</label>
                            <select name="BudgetAccount" class="form-control select2" @(Model.PurchasesRequest.BudgetStat == 1 ? "disabled=''" : "")>
                        <option disabled hidden selected>اختر حساب مالي</option>
                            @foreach (var acc in Model.BcAccounts)
                            {
                                    if (Model.PurchasesRequest.BudgetAccount == acc.Account)
                                {
                                        <option selected value="@acc.Account">@acc.Name</option>
                                }
                                else
                                {
                                        <option  value="@acc.Account">@acc.Name</option>
                                }
                            }
                        </select>
                </div>
                </div>

                <br>

                <label for="">ملاحظة</label>
                    <textarea name="BudgetNote" @(Model.PurchasesRequest.BudgetStat == 1 ? "disabled=''" : "") rows="5" class="form-control" required>@Model.PurchasesRequest.BudgetNote</textarea> <br>


                <div class="row">
                    <div class="col-6">
                        @if (Model.PurchasesRequest.BudgetStat == 0)
                        {
                            <button class="btn btn-primary"><i class="fa fa-save"></i> @Model._("Save")</button>
                        }
                        
                    </div>
                    
                </div>

            </div>
        </div>
    </div>
</div>

</form>
@if (Model.Can("finance-manager") && Model.PurchasesRequest.BudgetYear > 1000 && Model.PurchasesRequest.QuotationStat == 1)
{
    <br />
    <h5>اجراء مدير دائرة المالية</h5>

    @if (Model.PurchasesRequest.BudgetStat == 0)
    {
        <a href="~/Purchases/Update/Budget/Approve/@Model.PurchasesRequest.Id" class="btn btn-success after-confirm"><i class="fa fa-check"></i> اعتماد الحساب المالي</a>
    }
    else
    {
        if (Model.PurchasesRequest.LpoStat == 0)
        {
            <a href="~/Purchases/Update/Budget/Cancel/@Model.PurchasesRequest.Id" class="btn btn-danger after-confirm"><i class="fa fa-times"></i> الغاء اعتماد الحساب المالي</a>
        }
        else
        {
            <a href="#" disabled class="btn btn-danger disabled"><i class="fa fa-times"></i> الغاء اعتماد  امر الشراء</a> <i class="fas fa-exclamation-circle mx-1" title="تم اعتماد امر الشراء "></i>
        }
        
    }
}

<script>


    function updateAccounts(){

        var year = $('select[name=BudgetYear]').val();

        $.get("/Purchases/GetAccounts/" + year,function(res){
            
            $('select[name=BudgetAccount]').empty();

            $('select[name=BudgetAccount]').append(`<option disabled selected value="">اختر حساب</option>`);

            res.data.forEach(function (item){
                $('select[name=BudgetAccount]').append(`<option value="${item.account}">${item.name}</option>`);
            });

        },'json');
    }
</script>