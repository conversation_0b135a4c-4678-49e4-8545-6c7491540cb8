﻿@using HumanResource.Modules.Purchases.ViewModels
@model PurchasesViewModel


@Html.Partial("Inc/_RequestTabs")




<form action="~/Purchases/Update/Analysis/@Model.PurchasesRequest.Id" method="post" id="app" class="ajax">

    

    <div class="card shadow">


        <table class="table">
            <thead class="bg-primary">
                <tr>
                    <th>@Model._("No")</th>
                    <th>@Model._("Item")</th>
                    <th>@Model._("Description")</th>
                    <th>@Model._("Quantity")</th>
                    <th>@Model._("Unit")</th>

                    <th></th>

                </tr>
            </thead>

            <tbody class="fadeIn">
                <tr v-for="(item,index) in list" class="fadeIn" :style="{display: item.hide?'none':''}">
                    <td>{{index+1}}</td>
                    <td><textarea @(Model.PurchasesRequest.QuotationStat != 0 ? "disabled" : "") :name="'RequestItems['+index+'].Name'" required v-model="item.Name" class="form-control"></textarea></td>
                    <td><textarea @(Model.PurchasesRequest.QuotationStat != 0 ? "disabled" : "") :name="'RequestItems['+index+'].Note'" v-model="item.Note" class="form-control"></textarea></td>
                    <td><input type="number" @(Model.PurchasesRequest.QuotationStat != 0 ? "disabled" : "") :name="'RequestItems['+index+'].Quantity'" required v-model="item.Quantity" value="1" min="1" class="form-control"></td>
                    <td><input type="text" @(Model.PurchasesRequest.QuotationStat != 0 ? "disabled" : "") :name="'RequestItems['+index+'].Unit'" required v-model="item.Unit"  class="form-control"></td>
                    <td class="text-center">
                        @if (Model.PurchasesRequest.QuotationStat == 0)
                        {
                            <button type="button" class="btn btn-outline-danger" @@click="list.splice(index,1)"><i class="fa fa-trash"></i></button>
                        }

                    </td>
                </tr>
            </tbody>
            
            <tfoot>
                <tr>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    @if (Model.PurchasesRequest.QuotationStat == 0)
                    {
                        <td class="text-center"><button type="button" class="btn btn-sm btn-secondary" @@click="AddItem()"><i class="fa fa-plus"></i> @Model._("Add row")</button></td>
                    }
                    
                </tr>
            </tfoot>
            

        </table>


        <div class="row">
            <div class="col-lg-6 px-4">
                <label for="">@Model._("Note")</label>
                <textarea @(Model.PurchasesRequest.QuotationStat != 0 ? "disabled" : "") name="PurchasesRequest.AnalysisNote" class="form-control">@Model.PurchasesRequest.AnalysisNote</textarea>
            </div>
        </div>
        <br>
        <br>
        @if (Model.PurchasesRequest.QuotationStat == 0)
        {
            <div class="card-footer">

                <button type="submit" class="btn btn-primary "><i class="fa fa-save"></i> @Model._("Save")</button>


            </div>
        }
    </div>


    <input type="hidden" name="PurchasesRequest.Id" value="@Model.PurchasesRequest.Id">
</form>


<script>
    let app = new Vue({
        el: "#app",
        data: {
            list: @Html.Raw(Model.ListToJson(Model.RequestItems)),
        },
        methods: {
            AddItem() {
                let job = this

                job.list.push({
                    hide: 1
                });

                setTimeout(function () {
                    $("tr").fadeIn()
                }, 50);
            },


        }
    });
</script>