﻿namespace HumanResource.Modules.Purchases.Models.Entities;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

[Table("PURCHASES_REQUEST")]
public class PurchasesRequest
{

    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Column("REL_ID")]
    public int RelId { get; set; } = 0;

    [Column("REL_TYPE")]
    public string RelType { get; set; } = "";


    [Required]
    [Column("REFERENCE")]
    public string Reference { get; set; } = "";


    [Required]
    [Column("REQUEST_DATE")]
    public DateTime Date { set; get; }

    [Column("LAST_ACTION_DATE")]
    public DateTime LastActionDate { set; get; }


    [StringLength(500)]
    [Column("NOTE")]
    public string Note { get; set; }

    [Column("TOTAL_COST")]
    public double TotalCost { get; set; } = 0;


    [Column("TOTAL_TAX")]
    public double TotalTax { get; set; } = 0;


    [Column("STATUS")]
    public string Status { get; set; } = "New";




    [Column("ORIGIN")]
    public string Origin { get; set; } = "";

    [Column("ANALYSIS_NOTE")]
    public string AnalysisNote { get; set; } = "";

    [Column("ANALYSIS_BY")]
    public int AnalysisBy { get; set; } = 0;

    [Column("APPROVED_BY")]
    public int ApprovedBy { get; set; } = 0;



    [Column("MANAGER_APPROVAL")]
    public int ManagerApproval { get; set; } = 0;
    [Column("MANAGER_NO")]
    public int ManagerNo { get; set; } = 0;

    [Column("BUDGET_ACCOUNT")]
    public string BudgetAccount { set; get; } = "";


    [Column("BUDGET_YEAR")]
    public int BudgetYear { set; get; } = 0;
    [Column("BUDGET_NOTE")]
    public string BudgetNote { set; get; } = "";

    [Column("BUDGET_STAT")]
    public int BudgetStat { get; set; } = 0;

    [Column("PURCHASE_ID")]
    public int PurchasesId { set; get; } = 0;


    [Column("DEPARTMENT_MANAGER_APPROVAL")]
    public int DepartmentManagerApproval { get; set; } = 0;

    [Column("CANCEL_FLAG")]
    public int CancelFlag { get; set; } = 0;

    [Column("QUOTATION_ID")]
    public int QuotationId { get; set; } = 0;

    [Column("QUOTATION_NOTE")]
    public string QuotationNote { get; set; } = "";

    [Column("QUOTATION_STAT")]
    public int QuotationStat { get; set; } = 0;

    [Required]
    [Column("EMP_NO")]
    public int EmpNo { get; set; } = 0;

    [Column("DELIVERY_STAT")]
    public int DeliveryStat { get; set; } = 0;

    [Column("DELIVERY_DATE")]
    public DateTime? DeliveryDate { get; set; }

    [Column("CREATED_AT")]
    public DateTime? CreatedAt { get; set; } = DateTime.Now;

    [Column("COST_ADJUST")]
    public double CostAdjust { get; set; } = 0;

    [Column("COST_ADJUST_NOTE")]
    public string CostAdjustNote { get; set; } = "";

    [Column("LPO_STAT")]
    public int LpoStat { get; set; } = 0;

    [Column("LPO_DATE")]
    public DateTime? LpoDate { get; set; }

    [Column("LPO_DELIVERY_DATE")]
    public DateTime? LpoDeliveryDate { get; set; }

    [Column("LPO_EXPIRY")]
    public DateTime? LpoExpiry { get; set; }

    [Column("LPO_DELIVERY_LOCATION")]
    public string LpoDeliveryLocation { get; set; } = "";

    [Column("LPO_WARRANTY_NOTE")]
    public string LpoWarrantyNote { get; set; } = "";

    [Column("LPO_DELIVERY_LATE_NOTE")]
    public string LpoDeliveryLateNote { get; set; } = "";



    [NotMapped]
    public string BudgetAccountName { set; get; } = "";







}

