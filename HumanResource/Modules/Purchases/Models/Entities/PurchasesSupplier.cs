﻿namespace HumanResource.Modules.Purchases.Models.Entities;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

public class PurchasesSupplier
{

    [Key]
    public int Id { get; set; }



    [Required]
    public string Name { set; get; }

    [StringLength(500)]
    public string Description { get; set; } = "";

    [StringLength(500)]
    public string Note { get; set; } = "";

    [Required]
    public string CrNo { get; set; }

    [Required]
    public string FinCode { get; set; } = "";

    [Required]
    public string TaxIN { get; set; } = "";


    [Required]
    public string ContactName { get; set; } = "";

    [Required]
    public string ContactPhone { get; set; } = "";

    [Required]
    public string ContactEmail { get; set; } = "";



    public string ContactName2 { get; set; } = "";


    public string ContactPhone2 { get; set; } = "";


    public string ContactEmail2 { get; set; } = "";


    public int CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;


    public int Deleted01 { get; set; } = 0;

    public int FileId { get; set; } = 0;
    public string Status { get; set; } = "New";
    public int ApprovedBy { get; set; } = 0;





}

