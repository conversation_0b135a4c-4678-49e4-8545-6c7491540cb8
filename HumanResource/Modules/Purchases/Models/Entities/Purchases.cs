﻿namespace HumanResource.Modules.Purchases.Models.Entities;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

public class Purchases
{

    [Key]
    public int Id { get; set; }


    public int RelId { get; set; } = 0;

    public string RelType { get; set; } = "";
    public int RequestId { get; set; } = 0;


    [Required]
    public string Reference { get; set; } = "";


    [Required]
    public DateTime Date { set; get; }


    public DateTime LastActionDate { set; get; }


    [StringLength(500)]
    public string Note { get; set; }

    public double TotalCost { get; set; } = 0;

    public double TotalTax { get; set; } = 0;

    public int CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    public int? UpdatedBy { get; set; }
    public DateTime? UpdatedAt { get; set; } = null;

    public int? DeletedBy { get; set; }

    public int Deleted01 { get; set; } = 0;

    public string Status { get; set; } = "New";


    [StringLength(500)]
    public string DeclineNote { get; set; }



    public int FileId { get; set; } = 0;

    public int SupplierId { get; set; } = 0;




}

