﻿namespace HumanResource.Modules.Purchases.Models.Entities;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;



[Table("PURCHASE_QUOTATIONS")]
public class PurchaseQuotation
{

    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Column("REL_ID")]
    public int RelId { get; set; } = 0;

    [Column("REL_TYPE")]
    public string RelType { get; set; } = "";


    [Required]
    [Column("SUPPLIER_ID")]
    public int SupplierId { get; set; }


    [Column("NOTE")]
    [StringLength(500)]
    public string Note { get; set; }

    [Column("TOTAL_COST")]
    public double TotalCost { get; set; } = 0;

    [Column("TOTAL_TAX")]
    public double TotalTax { get; set; } = 0;

    [Column("FILE_GUID")]
    public string FileGuid { get; set; } = null;

    [Column("SELECTED")]
    public int Selected { get; set; } = 0;






}

