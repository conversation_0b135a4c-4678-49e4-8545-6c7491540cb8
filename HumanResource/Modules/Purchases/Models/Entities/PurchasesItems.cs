﻿namespace HumanResource.Modules.Purchases.Models.Entities;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

public class PurchasesItem
{

    [Key]
    public int Id { get; set; }

    public int RelId { get; set; } = 0;

    public string RelType { get; set; } = "";

    public int ItemId { get; set; } = 0;

    [Required]
    public string Name { set; get; }

    [StringLength(500)]
    public string Note { get; set; } = "";

    [Required]
    public double Quantity { get; set; }

    public double Tax { get; set; } = 0;
    public double Cost { get; set; } = 0;

    public int CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;






}

