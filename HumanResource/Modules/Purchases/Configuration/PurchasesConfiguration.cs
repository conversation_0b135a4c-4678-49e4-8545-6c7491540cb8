using HumanResource.Modules.Purchases.Providers;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Purchases.Configuration
{
    /// <summary>
    /// Configuration for the Purchases module
    /// </summary>
    public static class PurchasesConfiguration
    {
        /// <summary>
        /// Registers all Purchases module services
        /// </summary>
        public static IServiceCollection AddPurchasesServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, PurchasesNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, PurchasesBadgeProvider>();

            // Register module services
            // services.AddScoped<PurchasesService>();

            return services;
        }
    }
} 