﻿using System.ComponentModel.DataAnnotations;
using HumanResource.Modules.Purchases.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Shared.Models.Entities.BC;

namespace HumanResource.Modules.Purchases.Forms;


public class PurchasesAnalysisForm
{

    [Required]
    public List<RequestItem> RequestItems { get; set; }
    public PurchasesRequest PurchasesRequest { get; set; }
}

public class SupplierForm
{
    public Supplier Supplier  { get; set; }

    public IFormFile File { get; set;  }
}


public class PurchaseQuotationFrom
{
    public PurchaseQuotation Quotation { get; set; }

    public IFormFile File { get; set; }
}

