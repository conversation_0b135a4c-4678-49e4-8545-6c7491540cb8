using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Purchases.Providers
{
    /// <summary>
    /// Navigation provider for the Purchases module
    /// </summary>
    public class PurchasesNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Purchases";
        public int Priority => 30;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Finance Group navigation items
            var financeNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "Purchases",
                    Label = "المشتريات",
                    Active = "purchases",
                    Icon = "<i class=\"far fa-file-invoice-dollar\"></i>",
                    Rights = new List<Right> { Right.PurchasesDepartment, Right.FinanceManager },
                    Priority = 10,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "Requests",
                            Label = "قائمة الطلبات",
                            Rights = new List<Right> { Right.PurchasesDepartment },
                            Url = "/Purchases/Items/Approved"
                        },
                        new NavLink
                        {
                            Name = "Analysis",
                            Label = "التحليل",
                            Rights = new List<Right> { Right.PurchasesDepartment, Right.FinanceManager },
                            Url = "/Purchases/Index"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Finance] = financeNavItems;

            return navigation;
        }
    }
} 