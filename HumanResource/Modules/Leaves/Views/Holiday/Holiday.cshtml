@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel
@{
    ViewData["Title"] = Model.Page.Title;
    Layout = "_Layout";
}

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title">الاجازات الرسمية</h3>
                <div class="card-tools">
                    <a href="/Leaves/Holiday/Create" class="btn btn-primary btn-sm">
                        <i class="fa fa-plus"></i> إضافة إجازة رسمية
                    </a>
                </div>
            </div>
        <div class="card">
            
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover text-nowrap datatable table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>اسم الإجازة</th>
                               
                                <th>خيارات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.Holidays != null && Model.Holidays.Any())
                            {
                                @foreach (var holiday in Model.Holidays.OrderBy(h => h.Holiday))
                                {
                                    <tr>
                                        <td>@(holiday.Holiday.HasValue ? holiday.Holiday.Value.ToString("yyyy-MM-dd") : "")</td>
                                        <td>@holiday.DespA</td>
                                     
                                        <td>
                                            <a href="/Leaves/Holiday/Delete/@(holiday.Holiday.HasValue ? holiday.Holiday.Value.ToString("yyyy-MM-dd") : "")" 
                                               class="btn btn-danger btn-sm delete-btn"
                                               data-confirm="هل أنت متأكد من حذف هذه الإجازة؟">
                                                <i class="fa fa-trash"></i> حذف
                                            </a>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="4" class="text-center">لا توجد إجازات رسمية</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>


    <script>
        $(document).ready(function() {
            $('.delete-btn').click(function(e) {
                if (!confirm($(this).data('confirm'))) {
                    e.preventDefault();
                }
            });
        });
    </script>
