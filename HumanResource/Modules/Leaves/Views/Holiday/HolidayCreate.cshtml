@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel
@{
    ViewData["Title"] = Model.Page.Title;
    Layout = "_Layout";
}

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title">إضافة إجازات رسمية</h3>
                <div class="card-tools">
             
                </div>
            </div>
        <div class="card">
            
            <div class="card-body">
                <form id="holidayForm" method="post" action="/Leaves/Holiday/Create" class="ajax">
                    <div class="row" id="holidays-container">
                        <div class="col-md-12 holiday-row" data-index="0">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>تاريخ الإجازة</label>
                                        <input type="date" class="form-control" name="holidays[0].Holiday" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>اسم الإجازة</label>
                                        <input type="text" class="form-control" name="holidays[0].DespA" required>
                                    </div>
                                </div>
        
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="button" class="btn btn-danger mb-2 remove-holiday" style="display:none;">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                            <hr />
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <button type="button" id="add-holiday" class="btn btn-info">
                                <i class="fa fa-plus"></i> إضافة إجازة أخرى
                            </button>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> حفظ
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


    <script>
        $(document).ready(function() {
            let index = 0;
            
            // Add new holiday row
            $("#add-holiday").click(function() {
                index++;
                let newRow = $(".holiday-row").first().clone();
                newRow.attr("data-index", index);
                
                // Get previous row's date value and add one day
                let prevDate = $(".holiday-row:last").find("input[type='date']").val();
                let nextDate = "";
                
                if (prevDate) {
                    // Parse the date and add one day
                    let date = new Date(prevDate);
                    date.setDate(date.getDate() + 1);
                    
                    // Format date as YYYY-MM-DD for input field
                    nextDate = date.toISOString().split('T')[0];
                }
                
                // Clear and update input names
                newRow.find("input").val("");
                newRow.find("input").each(function() {
                    let name = $(this).attr("name");
                    name = name.replace(/\[\d+\]/, "[" + index + "]");
                    $(this).attr("name", name);
                    
                    // Set the incremented date for the date input
                    if ($(this).attr("type") === "date" && nextDate) {
                        $(this).val(nextDate);
                    }
                });
                
                // Show remove button for all rows
                $(".remove-holiday").show();
                newRow.find(".remove-holiday").show();
                
                // Add to container
                $("#holidays-container").append(newRow);
            });
            
            // Remove holiday row
            $(document).on("click", ".remove-holiday", function() {
                if ($(".holiday-row").length > 1) {
                    $(this).closest(".holiday-row").remove();
                    
                    // If only one row left, hide its remove button
                    if ($(".holiday-row").length === 1) {
                        $(".remove-holiday").hide();
                    }
                }
            });

        });
    </script>
