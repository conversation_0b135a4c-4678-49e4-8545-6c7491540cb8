@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel

<br>
<br>
<br>
<h1>@Model._("Hr")</h1>
<div class="row">
    <div class="col-md-3">
        <div class="card bg-primary shadow border-0">
     
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <h2>@Model._("Leaves") </h2>
                    <i class="far fa-file-alt fa-3x"></i>
                </div>
            </div>
            <div class="card-footer">
                <a href="~/Leaves/Dash" class="text-white"> @Model._("View") <i class="fas fa-arrow-left"> </i>  </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-primary shadow border-0">
     
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <h2>@Model._("Over time")</h2>
                    <i class="far fa-file-alt fa-3x"></i>
                </div>
            </div>
            <div class="card-footer">
                <a href="~/OverTime/Dash" class="text-white"> @Model._("View") <i class="fas fa-arrow-left"> </i>  </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card bg-primary shadow border-0">

            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <h2>@Model._("Study and Training Request")</h2>
                    <i class="far fa-file-alt fa-3x"></i>
                </div>
            </div>
            <div class="card-footer">
                <a href="~/Training/CourseApproval" class="text-white"> @Model._("View") <i class="fas fa-arrow-left"> </i>  </a>
            </div>
        </div>
    </div>
    
    
</div> 