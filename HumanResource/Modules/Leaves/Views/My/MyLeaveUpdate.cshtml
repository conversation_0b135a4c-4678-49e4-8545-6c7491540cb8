﻿@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel


@if (Model.TleaveAplTx.LeaveCode == 1 && Model.TleaveAplTx.ReqStat == 6)
{

    if (ViewBag.IsReturnAllowed)
    {
        <div class="row">
            <div class="col-lg-12 d-flex justify-content-end mb-1">
                <button class="btn btn-primary" data-toggle="modal" data-target="#leave-return-modal">
                    <i class="fas fa-calendar-times"></i> @Model._l("طلب قطع أجازة")
                </button>
            </div>
        </div>
        <form id="leave-return-form" class="ajax" action="~/Leaves/My/Return/@<EMAIL><EMAIL>" method="post">
            <div class="modal fade" id="leave-return-modal" tabindex="-1" role="dialog"
                aria-labelledby="leave-return-modal-label" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="leave-return-modal-label">@Model._l("طلب قطع أجازة")</h5>
           
                        </div>
                        <div class="modal-body">

                            <div class="form-group">
                                <label for="leave-return-date">@Model._l("تاريخ العودة للعمل")</label>
                                <input type="date" class="form-control" id="leave-return-date" name="LeaveEndDate"
                                    min="@Model._d(Model.TleaveAplTx.LeaveStartDate)"
                                    max="@Model._d(Model.TleaveAplTx.LeaveEndDate)">
                            </div>

                        

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                <i class="fas fa-times"></i> @Model._l("إلغاء")
                            </button>
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> @Model._l("حفظ")</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    }
}

@* Leave Return Details Section *@
@if (Model.TleaveReturnTx != null)
{
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-white">
                            <i class="fas fa-calendar-times"></i> @Model._("تفاصيل طلب قطع الإجازة")
                        </h5>
                        <div>
                            @Html.Raw(Model.renderLeaveSatus(Model.TleaveReturnTx.ReqStat))
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-lg-6">
                            <label class="font-weight-bold">@Model._l("رقم طلب قطع الإجازة")</label>
                            <p class="mb-0">@($"{Model.TleaveReturnTx.InYear}-{Model.TleaveReturnTx.InMailNo}-{Model.TleaveReturnTx.InDocSlNo}")</p>
                        </div>
                        <div class="col-lg-6">
                            <label class="font-weight-bold">@Model._l("تاريخ تقديم الطلب")</label>
                            <p class="mb-0">@Model._d(Model.TleaveReturnTx.TimeStamp ?? DateTime.Now)</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-lg-4">
                            <label class="font-weight-bold">@Model._l("تاريخ بداية الإجازة الأصلية")</label>
                            <p class="mb-0">@Model._d(Model.TleaveReturnTx.PLeaveStartDate)</p>
                        </div>
                        <div class="col-lg-4">
                            <label class="font-weight-bold">@Model._l("تاريخ نهاية الإجازة الأصلية")</label>
                            <p class="mb-0">@Model._d(Model.TleaveReturnTx.PLeaveEndDate)</p>
                        </div>
                        <div class="col-lg-4">
                            <label class="font-weight-bold">@Model._l("تاريخ العودة الفعلي")</label>
                            <p class="mb-0 text-primary font-weight-bold">@Model._d(Model.TleaveReturnTx.LeaveEndDate)</p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-lg-4">
                            <label class="font-weight-bold">@Model._l("عدد أيام الإجازة المستخدمة")</label>
                            <p class="mb-0">@Model.TleaveReturnTx.NoLeaveDays أيام</p>
                        </div>
                        <div class="col-lg-4">
                            <label class="font-weight-bold">@Model._l("الأيام المسترجعة")</label>
                            @{
                                var daysToCredit = Model.TleaveReturnTx.PLeaveEndDate.HasValue && Model.TleaveReturnTx.LeaveEndDate.HasValue
                                    ? (Model.TleaveReturnTx.PLeaveEndDate.Value - Model.TleaveReturnTx.LeaveEndDate.Value).Days + 1
                                    : 0;
                            }
                            <p class="mb-0">
                                <span class="badge badge-success">@daysToCredit أيام</span>
                            </p>
                        </div>
                        <div class="col-lg-4">
                            <label class="font-weight-bold">@Model._l("عدد الأيام المرضية")</label>
                            <p class="mb-0">@(Model.TleaveReturnTx.NoHolidays ?? 0) أيام</p>
                        </div>
                    </div>

                    @if (Model.TleaveReturnTx.ApprovalDate.HasValue)
                    {
                        <div class="row mb-3">
                            <div class="col-lg-6">
                                <label class="font-weight-bold">@Model._l("تاريخ الموافقة")</label>
                                <p class="mb-0">@Model._d(Model.TleaveReturnTx.ApprovalDate)</p>
                            </div>
                            <div class="col-lg-6">
                                <label class="font-weight-bold">@Model._l("ملاحظات الموافقة")</label>
                                <p class="mb-0">@(Model.TleaveReturnTx.ApprovalRem ?? "-")</p>
                            </div>
                        </div>
                    }

                    @if (Model.TleaveReturnTx.AuditDate.HasValue)
                    {
                        <div class="row mb-3">
                            <div class="col-lg-6">
                                <label class="font-weight-bold">@Model._l("تاريخ المراجعة")</label>
                                <p class="mb-0">@Model._d(Model.TleaveReturnTx.AuditDate)</p>
                            </div>
                            <div class="col-lg-6">
                                <label class="font-weight-bold">@Model._l("ملاحظات المراجعة")</label>
                                <p class="mb-0">@(Model.TleaveReturnTx.AuditRem ?? "-")</p>
                            </div>
                        </div>
                    }

                    @if (Model.TleaveReturnTx.DutyResumeDate.HasValue)
                    {
                        <div class="row">
                            <div class="col-lg-12">
                                <label class="font-weight-bold">@Model._l("تاريخ استئناف العمل")</label>
                                <p class="mb-0">@Model._d(Model.TleaveReturnTx.DutyResumeDate)</p>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

<div class="row">
    <!-- Leave Details Card -->
    <div class="col-lg-12 ">

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-center ">
                    <h5 class="mb-0">@Model._("تفاصيل طلب الاجازة")</h5>
                    <div>
                        @Html.Raw(Model.renderLeaveSatus(Model.TleaveAplTx.ReqStat))
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-lg-6">
                        <label class="font-weight-bold">@Model._l("نوع الاجازة")</label>
                        <p class="mb-0">@(Model.TleaveAplTx.TleaveCode?.LeaveDespA ?? "خصم من الاعتيادية")</p>
                    </div>
                    <div class="col-lg-6">
                        <label class="font-weight-bold">@Model._l("تاريخ الطلب")</label>
                        <p class="mb-0">@Model._d(Model.TleaveAplTx.TimeStamp ?? DateTime.Now)</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-lg-4">
                        <label class="font-weight-bold">@Model._l("من تاريخ")</label>
                        <p class="mb-0">@Model._d(Model.TleaveAplTx.LeaveStartDate)</p>
                    </div>
                    <div class="col-lg-4">
                        <label class="font-weight-bold">@Model._l("إلى تاريخ")</label>
                        <p class="mb-0">@Model._d(Model.TleaveAplTx.LeaveEndDate)</p>
                    </div>
                    <div class="col-lg-4">
                        <label class="font-weight-bold">@Model._l("عدد الأيام")</label>
                        <p class="mb-0">@((int)(Model.TleaveAplTx.LeaveEndDate.Date -
                            Model.TleaveAplTx.LeaveStartDate.Date).TotalDays + 1)</ p >
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <label class="font-weight-bold">@Model._l("المسؤول المباشر")</label>
                        <p class="mb-0">@Model.TleaveAplTx.SignAuthCode -
                            @Model._h.StaffData(Model.TleaveAplTx.SignAuthCode).EmpNameA</p>
                    </div>
                </div>

                @if (Model.TleaveAplTx.FileGuid != null)
                {
                    <div class="row">
                        <div class="col-lg-12 mb-3">
                            <label class="font-weight-bold">@Model._l("الوثيقة المرفقة")</label>
                            <a href="@Model._h.GetFile(Model.TleaveAplTx.FileGuid)" class="btn btn-primary" target="_blank">
                                <i class="fas fa-paperclip"></i> @Model._l("تحميل ")</a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>


</div>
<!-- Logs Section -->
<h5 class="mb-0">@Model._("سجل الأنشطة")</h5>
<div class="card shadow-sm mb-4">

    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">

                <tbody>
                    @foreach (var log in Model._h.GetLogs(Model.TleaveAplTx.InYear + "-" + Model.TleaveAplTx.InMailNo +
                                        "-" + Model.TleaveAplTx.InDocSlNo, "Leaves"))
                    {
                        <tr>
                            <td class="text-center"><i class="far fa-clock text-info"></i></td>
                            <td>@log.Rem</td>
                            <td dir="ltr">@Model._dt(log.TimeStamp)</td>
                            <td>@Model._h.StaffData(log.UserId).EmpNameA</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@* Leave Return Logs Section *@
@if (Model.TleaveReturnTx != null)
{
    <h5 class="mb-0">@Model._("سجل أنشطة طلب قطع الإجازة")</h5>
    <div class="card shadow-sm mb-4">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <tbody>
                        @foreach (var log in Model._h.GetLogs(Model.TleaveReturnTx.InYear + "-" + Model.TleaveReturnTx.InMailNo +
                                            "-" + Model.TleaveReturnTx.InDocSlNo, "LeaveReturn"))
                        {
                            <tr>
                                <td class="text-center"><i class="far fa-clock text-warning"></i></td>
                                <td>@log.Rem</td>
                                <td dir="ltr">@Model._dt(log.TimeStamp)</td>
                                <td>@Model._h.StaffData(log.UserId).EmpNameA</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
}

<!-- Actions -->
<div class="mt-3">

    @if (Model.TleaveAplTx.ReqStat == 0)
    {

        <a href="~/Leaves/My/Cancel/@<EMAIL><EMAIL>"
            class="btn btn-danger" onclick="return confirm('@Model._l("هل أنت متأكد أنك تريد إلغاء هذا الطلب؟")')"><i
                class="fas fa-times"></i> @Model._l("إلغاء الطلب")</a>
    }
</div>