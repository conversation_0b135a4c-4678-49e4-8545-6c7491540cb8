﻿@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel

<div class="d-flex justify-content-between py-2">

    <h3>@Model._l("الاجازات")</h3>

    <div>
        <button data-toggle="modal" data-target="#new-request-modal" class="btn btn-primary "><i class="fa fa-plus"></i> @Model._l("طلب جديد")</button>
    </div>
</div>

<div class="card shadow">

    <div class="card-body">
        <div class="row">
            <div class="col-3 border rounded p-3 text-dark m-1">
                <div class="d-flex">
                    <h3 class="font-weight-semibold ">@ViewBag.LeaveBal يوم</h3>
                </div>
                <div>
                    <h5 class="font-weight-semibold mb-0"><i class="far fa-umbrella-beach fa-1x"></i> @Model._l("الرصيد الحالي") </h5>
                </div>
            </div>


        </div>
    </div>
    <table class="table datatable">
        <thead class="bg-primary">
            <tr>

                <th></th>
                <th>@Model._l("نوع الاجازة")</th>
                <th>@Model._l("من تاريخ")</th>
                <th>@Model._l("إلى تاريخ") </th>
                <th>@Model._l("عدد الأيام")</th>
                <th>@Model._l("الحالة")</th>

            </tr>
        </thead>
        <tbody>

            @foreach (var item in Model.TleaveAplTxs)
            {

                <tr>
                    <td data-sort="@Model._dt(item.LeaveStartDate)"> <a href="~/Leaves/My/Update/@<EMAIL><EMAIL>">@<EMAIL><EMAIL></a></td>
                    <td>
                        @item.TleaveCode.LeaveDespA
                    </td>
                    <td>
                        @Model._d(item.LeaveStartDate)
                    </td>
                    <td>
                        @Model._d(@item.LeaveEndDate)
                    </td>
                    <td>
                        @{
                            DateTime end = (DateTime)item.LeaveEndDate.AddDays(1);
                            DateTime start = (DateTime)item.LeaveStartDate;
                            TimeSpan diff = end.Subtract(start);

                            @diff.ToString("dd")
                            ;
                        }
                    </td>
                    <td>

                        @Html.Raw(Model.renderLeaveSatus(item.ReqStat))

                    </td>

                </tr>

            }
        </tbody>

    </table>
</div>




<form action="~/Leaves/My/Create" method="post" class="ajax" enctype="multipart/form-data">
    <div class="modal fade" id="new-request-modal" tabindex="-1" role="dialog" aria-labelledby="new-leave-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._l("طلب جديد")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="app">
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="LeaveCode">@Model._l("نوع الاجازة")</label>
                            <select class="form-control" name="LeaveCode" id="LeaveCode" v-model="selectedLeaveCode" v-on:change="updateLeaveTypeInfo">
                                
                                    <option value="1">  @Model._l("خصم من اعتيادية") (@Model._h.Leave().Balance(Model.Profile.EmpNo.Value, 1) @Model._l("يوم"))</option>

                                    <option value="21">  @Model._l("اجازة طارئة") (@Model._h.Leave().Balance(Model.Profile.EmpNo.Value, 21) @Model._l("مرة"))</option>

                                    <option value="3">  @Model._l("اجازة مرضية")</option>
                                    <option value="31">  @Model._l("اجازة مرافقة مريض")</option>
                                   
                            </select>
                            <small class="form-text text-muted">@Model._l("الرصيد الحالي معروض في الأقواس")</small>
                            <br>
                        </div>
                        <div class="col-lg-6" v-if="leaveTypeInfo">
                            <div class="alert bg-primary">
                                <h5><i class="fa fa-info-circle"></i> @Model._l("تعليمات الاجازة") </h5>
                                <p v-html="leaveTypeInfo"></p>
  
                                
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="LeaveStartDate">@Model._l("من تاريخ")</label>
                                <input 
                                type="date" 
                                id="LeaveStartDate" 
                                name="LeaveStartDate" 
                                v-model="FromDate"  
                                class="form-control" 
                                :min='(selectedLeaveCode == 1) ? "@DateTime.Now.AddDays(1).ToString("yyyy-MM-dd")" : ""'
                                required 
                                v-on:change="calculateDays" 
                                /><br>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="LeaveEndDate">@Model._l("إلى تاريخ")</label>
                                <input type="date" id="LeaveEndDate" :min='FromDate' name="LeaveEndDate" v-model="ToDate" class="form-control" required v-on:change="calculateDays" /><br>
                            </div>
                        </div>
                    </div>

                    <div class="row" v-if="calculatedDays > 0">
                        <div class="col-lg-12">
                            <div class="alert bg-primary">
                                
                                <span >
                                    @Model._l("اجمالي الاجازة: ") <strong>{{ calculatedDays }} @Model._l("يوم")</strong>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row" v-if="isWeekend">
                        <div class="col-lg-12">
                            <div class="alert bg-danger">
                                @Model._l("لا يمكن بدء أو إنهاء الإجازة في عطلة نهاية الأسبوع (الجمعة أو السبت)")
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label for="SignAuthCode">@Model._l("المسؤول المباشر")</label>
                                <select name="SignAuthCode" id="SignAuthCode" class="form-control" placeholder="Select Manager" required>
                                    <option value="" selected disabled hidden>@Model._l("Select Staff")</option>
                                    @foreach (var staff in Model._h.Managers(Model.Auth.EmpNo))
                                    {
                                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label for="LeaveReason">@Model._l("سبب الاجازة")</label>
                                <textarea id="LeaveReason" name="LeaveReason" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="form-group">
                                <label for="SupportingDocument">@Model._l("الوثائق ")</label>
                                <div class="custom-file">
                                    <input type="file" class="custom-file-input" id="SupportingDocument" name="SupportingDocument">
                                    <label class="custom-file-label" for="SupportingDocument">@Model._l("اختر الملف")</label>
                                </div>
                                <small class="form-text text-muted">@Model._l("يمكن رفع الوثائق المصاحبة (PDF, صورة)")</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("اغلاق")</button>
                    <button type="submit" class="btn btn-primary" :disabled="isWeekend"><i class="fa fa-save"></i> @Model._l("حفظ")</button>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
    // Leave request form handling
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('app')) {
            new Vue({
                el: '#app',
                data: {
                    FromDate: '',
                    ToDate: '',
                    selectedLeaveCode: 1,
                    calculatedDays: 0,
                    hasWeekends: false,
                    hasHolidays: false,
                    leaveTypeInfo: '',
                    isWeekend: false
                },
                methods: {
                    updateLeaveTypeInfo: function() {
                        // Get leave type information based on selectedLeaveCode
                        const leaveTypeMap = {
                            1: 'الاجازة السنوية هي الوقت المدفوع المتاح للموظفين. يتم تحديد عدد الأيام المتاحة للاجازة بناءً على مدة الخدمة المتراكمة.',
                            21: 'الاجازة الطارئة متاحة للموظفين في حالة الحاجة الشخصية أو الأسرية المؤقتة. الحد الأقصى 3',
                            3: ' الاجازة المرضية متاحة للموظفين في حالة حدوث حالة طارئة طبية أو أخرى يتطلب التوثيق.',
                            31: 'الاجازة المرافقة متاحة للموظفين في حالة مرافقة مريض من افراد الاسرة. يجب ارفاق الوثيقة المصاحبة',
                   
                        };
                        
                        this.leaveTypeInfo = leaveTypeMap[this.selectedLeaveCode] || '';
                    },
                    calculateDays: function() {
                        if (!this.FromDate || !this.ToDate) return;
                        
                        // This is a simplified calculation - in a real implementation, you would make an AJAX call
                        // to the server to calculate this properly, accounting for holidays
                        const start = new Date(this.FromDate);
                        const end = new Date(this.ToDate);
                        
                        // Check if start or end date is weekend
                        const startDay = start.getDay();
                        const endDay = end.getDay();
                        
                        this.isWeekend = (startDay === 5 || startDay === 6 || endDay === 5 || endDay === 6);
                        
                        // Basic calculation (inclusive of start and end dates) - including weekends
                        const diffTime = Math.abs(end - start);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                        
                        // Count weekends for display purposes but include them in calculation
                        let weekendCount = 0;
                        for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                            const day = d.getDay();
                            if (day === 5 || day === 6) { // Friday or Saturday
                                weekendCount++;
                            }
                        }
                        
                        this.hasWeekends = weekendCount > 0;
                        this.hasHolidays = false; // Would need server-side calculation
                        // Include weekends in the total calculation
                        this.calculatedDays = diffDays;
                    }
                },
                mounted: function() {
                    this.updateLeaveTypeInfo();
                    
                    // Initialize file input
                    $('.custom-file-input').on('change', function() {
                        let fileName = $(this).val().split('\\').pop();
                        $(this).next('.custom-file-label').addClass("selected").html(fileName);
                    });
                }
            });
        }
    });
</script>


