﻿@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel
<div class="d-flex justify-content-between py-2">

    <h3>الاجازات</h3>

    <div>
        <button data-toggle="modal" data-target="#new-request-modal" class="btn btn-primary "><i class="fa fa-plus"></i> اجازة جديدة</button>
    </div>
</div>

<div class="card shadow mb-4">

    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="leaveCodeFilter">نوع الاجازة</label>
                    <select id="leaveCodeFilter" class="form-control">
                        <option value="">الكل</option>
                        <option value="1">اجازة اعتيادية</option>
                        <option value="2">اجازة مرضية</option>
                        <option value="3">اجازة اخرى</option>
         
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="fromDate">بداية الاجازة</label>
                    <input type="date" id="fromDate" class="form-control" value="@DateTime.Today.AddMonths(-6).ToString("yyyy-MM-dd")" />
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="toDate">نهاية الاجازة</label>
                    <input type="date" id="toDate" class="form-control" value="@DateTime.Today.AddMonths(6).ToString("yyyy-MM-dd")" />
                </div>
            </div>
            <div class="col-md-2 d-flex align-items-center">
             
               <div>
                 <button id="applyFilters" class="btn btn-primary">تطبيق </button>
               </div>
            </div>
        </div>
    </div>
</div>

<div class="card shadow">
    
        <table id="leavesDataTable" class="table table-sm table-striped table-hover w-100">
            <thead class="bg-primary text-white">
                <tr>
                    <th>الرقم</th>
                    <th>رقم الموظف</th>
                    <th>الاسم</th>
                    <th>نوع الاجازة</th>
                    <th>بداية الاجازة</th>
                    <th>نهاية الاجازة</th>
                    <th>عدد الايام</th>
                    <th>الحالة</th>
                </tr>
            </thead>
        </table>
    
</div>

<form action="~/Leaves/Department/Create" method="post" class="ajax" id="app">
    <div class="modal fade" id="new-request-modal"  role="dialog" aria-labelledby="new-leave-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._l("New leave")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                
                

                <div class="modal-body" >

                    <div class="row">
                        <div class="col-lg-6">

                            <div class="form-group">
                                <label for="">الموظف</label>
                                <select name="EmpNo" class="select2" required onchange="app.get_balance()">
                                    <option value="0" selected disabled hidden>اختر الموظف</option>
                                    @foreach (var staff in Model.VempDtls)
                                    {
                                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                    }
                                </select>
                            </div>
                        </div>

                        <div class="col-lg-6">

                            <div class="form-group">
                                <label for=""> المسؤول المباشر</label>
                                <select name="SignAuthCode" class="select2" placeholder="اختر المسؤول المباشر" required>
                                    <option value="" selected disabled hidden>اختر المسؤول المباشر</option>
                                    @foreach (var staff in Model.VempDtls)
                                    {
                                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                    }

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="">نوع الاجازة</label>
                      

                            <select name="LeaveCode" class="form-control" >
                                <option v-for="bal in balances" :value="bal.leaveCode">{{bal.leaveName}} ({{bal.balance}})</option>
                            </select>
                            <br>
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="">بداية الاجازة</label>
                                <input type="date" name="LeaveStartDate" min='@DateTime.Now.ToString("yyyy-MM-dd")' v-model="FromDate" :max="ToDate" class="form-control" required /><br>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label for="">نهاية الاجازة</label>
                                <input type="date" :min='FromDate' name="LeaveEndDate" v-model="ToDate" class="form-control" required /><br>
                            </div>
                        </div>
                    </div>
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">اغلاق</button>
                    <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> ارسال</button>
                </div>
            </div>
        </div>
    </div>

</form>



<script>
    let app = new Vue({
        el: "#app",
        data: {
            balances: [],
            FromDate: '',
            ToDate: '',
        },
        methods: {
            get_balance() {

                let job = this;

                var empno = $("#new-request-modal select[name=EmpNo]").val();

                if (empno > 1) {

                    $.get("/Leaves/GetEmpBalance?empno=" + empno  , function (data) {

                        job.balances = data.data;

                    }, 'json');
                }
            },
        },

    });

$(document).ready(function () {
    
    // Initialize filter variables
    var leaveCode = $("#leaveCodeFilter").val() || '';
    var fromDate = $("#fromDate").val() || '';
    var toDate = $("#toDate").val() || '';
    
    // Initial datatable creation
    initDatatable();
    
    // Apply filters when button is clicked
    $("#applyFilters").click(function (e) {
        e.preventDefault();
        leaveCode = $("#leaveCodeFilter").val() || '';
        fromDate = $("#fromDate").val() || '';
        toDate = $("#toDate").val() || '';
        
        // Destroy existing datatable before creating a new one
        if ($.fn.DataTable.isDataTable('#leavesDataTable')) {
            $('#leavesDataTable').DataTable().destroy();
        }
        
        // Refresh the datatable with new filter values
        createDatatable('#leavesDataTable', true, {
            ajax: "/Leaves/Department/Datatable?leaveCode=" + leaveCode + "&fromDate=" + fromDate + "&toDate=" + toDate,
            columns: [
                { "data": "id", "title": "الرقم" },
                { "data": "empNo", "title": "رقم الموظف" },
                { "data": "empName", "title": "الاسم" },
                { "data": "leaveType", "title": "نوع الاجازة" },
                { "data": "startDate", "title": "بداية الاجازة" },
                { "data": "endDate", "title": "نهاية الاجازة" },
                { "data": "days", "title": "عدد الايام" },
                { "data": "status", "title": "الحالة" },
            ]
        });
    });
    
    // Function to initialize datatable with current filter values
    function initDatatable() {
        // Destroy existing datatable if it exists
        if ($.fn.DataTable.isDataTable('#leavesDataTable')) {
            $('#leavesDataTable').DataTable().destroy();
        }
        
        createDatatable('#leavesDataTable', true, {
            ajax: "/Leaves/Department/Datatable?leaveCode=" + leaveCode + "&fromDate=" + fromDate + "&toDate=" + toDate,
            columns: [
                { "data": "id", "title": "الرقم" },
                { "data": "empNo", "title": "رقم الموظف" },
                { "data": "empName", "title": "الاسم" },
                { "data": "leaveType", "title": "نوع الاجازة" },
                { "data": "startDate", "title": "بداية الاجازة" },
                { "data": "endDate", "title": "نهاية الاجازة" },
                { "data": "days", "title": "عدد الايام" },
                { "data": "status", "title": "الحالة" },
            ]
        });
    }
});
</script>