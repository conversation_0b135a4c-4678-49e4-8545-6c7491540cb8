﻿@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel

<div class="card shadow">
    <table class="table datatable">
        <thead>
            <tr>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </thead>
        <tbody>
            @foreach(var emp in Model.WorkingHours){
                if (emp.EmpNo < 1200 && emp.EmpNo > 1 && Model._h.StaffData(emp.EmpNo) != null)
                {
                    <tr class="bg-primary">
                    <td colspan="3">
                            @emp.EmpNo  
                            <br>
                            @Model._h.StaffData(emp.EmpNo).EmpNameA
                    </td>
                </tr>
                foreach(var days in emp.Days){
                        if(days.Hours<6){
                            <tr>
                                <td>@days.Day</td>
                                <td>@days.TotalTime</td>
                                <td>

                                    @foreach(var leave in Model.GetLeavesByDate(emp.EmpNo, days.Day)){
                                        <span>@Model._(@leave.Type)</span>
                                    }
                                </td>
                            </tr>
                        }
                    
                    }
                }
                
            }  
        </tbody>
    </table>
</div>