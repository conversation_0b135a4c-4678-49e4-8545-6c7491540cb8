﻿@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel

<div class="container-fluid mb-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="mb-0">@Model._("اجازة") - @Model._h.StaffData(Model.TleaveAplTx.EmpNo).EmpNameA</h3>
                
                    @Html.Raw(Model.renderLeaveSatus(Model.TleaveAplTx.ReqStat))
                
            </div>
        </div>
    </div>

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">@Model._("تفاصيل الاجازة")</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-lg-6">
                        <label class="font-weight-bold">@Model._l("نوع الاجازة")</label>
                        <p class="mb-0">@(Model.TleaveAplTx.TleaveCode?.LeaveDespA ?? "خصم من الاعتيادية")</p>
                    </div>
                    <div class="col-lg-6">
                        <label class="font-weight-bold">@Model._l("تاريخ الطلب")</label>
                        <p class="mb-0">@Model._d(Model.TleaveAplTx.TimeStamp ?? DateTime.Now)</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-lg-4">
                        <label class="font-weight-bold">@Model._l("من تاريخ")</label>
                        <p class="mb-0">@Model._d(Model.TleaveAplTx.LeaveStartDate)</p>
                    </div>
                    <div class="col-lg-4">
                        <label class="font-weight-bold">@Model._l("إلى تاريخ")</label>
                        <p class="mb-0">@Model._d(Model.TleaveAplTx.LeaveEndDate)</p>
                    </div>
                    <div class="col-lg-4">
                        <label class="font-weight-bold">@Model._l("عدد الأيام")</label>
                        <p class="mb-0">@((int)(Model.TleaveAplTx.LeaveEndDate.Date - Model.TleaveAplTx.LeaveStartDate.Date).TotalDays + 1)</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <label class="font-weight-bold">@Model._l("المسؤول المباشر")</label>
                        <p class="mb-0">@Model.TleaveAplTx.SignAuthCode - @Model._h.StaffData(Model.TleaveAplTx.SignAuthCode).EmpNameA</p>
                    </div>
                </div>

                @if (Model.TleaveAplTx.FileGuid != null)
                {
                    <div class="row">
                        <div class="col-lg-12 mb-3">
                            <label class="font-weight-bold">@Model._l("الوثيقة المرفقة")</label>
                            <a href="@Model._h.GetFile(Model.TleaveAplTx.FileGuid)" class="btn btn-primary" target="_blank"> <i class="fas fa-paperclip"></i> @Model._l("تحميل ")</a>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Logs Section -->
    <h5 class="mb-0">@Model._("سجل الأنشطة")</h5>
    <div class="card shadow-sm mb-4">

        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
             
                    <tbody>
                        @foreach (var log in Model._h.GetLogs(Model.TleaveAplTx.InYear + "-" + Model.TleaveAplTx.InMailNo + "-" + Model.TleaveAplTx.InDocSlNo, "Leaves"))
                        {
                            <tr>
                                <td class="text-center"><i class="far fa-clock text-info"></i></td>
                                <td>@log.Rem</td>
                                <td dir="ltr">@Model._dt(log.TimeStamp)</td>
                                <td>@Model._h.StaffData(log.UserId).EmpNameA</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    @if (Model.TleaveAplTx.ReqStat == 0 )
    {
        <div class="btn-group">
            <a href="~/Leaves/Manager/Send/@<EMAIL><EMAIL>" 
               class="after-confirm btn btn-success px-4">
                <i class="fa fa-check me-2"></i> @Model._("موافقة")
            </a>
            <a href="~/Leaves/Manager/Cancel/@<EMAIL><EMAIL>" 
               class="after-confirm btn btn-danger px-4">
                <i class="fa fa-times me-2"></i> @Model._("إلغاء")
            </a>
        </div>
    }
</div>