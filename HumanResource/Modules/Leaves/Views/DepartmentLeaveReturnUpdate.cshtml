@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel
@{
	ViewData["Title"] = Model.Page.Title;
	var leaveReturn = Model.TleaveReturnTx;
	var originalLeave = Model.TleaveAplTx;
	var daysToCredit = leaveReturn.LateOrEarlyDays*-1;
	var fingerprintData = Model.GetLeaveReturnFingerprints(Model.TleaveReturnTx);
}

<h3>@Model.Page.Title</h3>
<div class="card">
	<div class="card-header">

		<div class="card-tools my-3">
			@if (leaveReturn.ReqStat == 0)
            {
                <a href="~/Leaves/Department/Return/Approve/@($"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}")" 
                   class="btn btn-success btn-sm after-confirm">
                    <i class="fas fa-check"></i> موافقة
                </a>
                <a href="~/Leaves/Department/Return/Reject/@($"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}")" 
                   class="btn btn-danger btn-sm after-confirm">
                    <i class="fas fa-times"></i> رفض
                </a>
            }
            else if (leaveReturn.ReqStat == 1 && Model.Can("audit"))
            {
                <a href="~/Leaves/Audit/Return/Approve/@($"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}")" 
                   class="btn btn-success btn-sm after-confirm">
                    <i class="fas fa-clipboard-check"></i> مراجعة واعتماد نهائي
                </a>
            }
		</div>
	</div>
	<div class="card-body">
		<div class="row">
			<!-- Leave Return Information -->
			<div class="col-md-6">
				<h5 class="">
					<i class="fas fa-undo"></i> معلومات طلب قطع الإجازة
				</h5>
				<table class="table table-bordered">
					<tr>
						<th width="40%">رقم الطلب</th>
						<td>@($"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}")</td>
					</tr>
					<tr>
						<th>رقم الموظف</th>
						<td>@leaveReturn.EmpNo</td>
					</tr>
					<tr>
						<th>اسم الموظف</th>
						<td>@Model._h.StaffData(leaveReturn.EmpNo).EmpNameA</td>
					</tr>
					<tr>
						<th>نوع الإجازة</th>
						<td>@leaveReturn.TleaveCode?.LeaveDespA</td>
					</tr>
					<tr>
						<th>تاريخ العودة الفعلي</th>
						<td>
							<span class="badge badge-secondary">@Model._d(leaveReturn.LeaveEndDate)</span>
						</td>
					</tr>
					<tr>
						<th>الأيام المسترجعة</th>
						<td>
							<span class="badge badge-success">@daysToCredit أيام</span>
						</td>
					</tr>
					<tr>
						<th>الحالة</th>
						<td>@Html.Raw(Model.renderLeaveSatus(leaveReturn.ReqStat))</td>
					</tr>
					<tr>
						<th>تاريخ الطلب</th>
						<td>@Model._d(leaveReturn.TimeStamp)</td>
					</tr>
					@if (leaveReturn.ApprovalDate.HasValue)
					{
						<tr>
							<th>تاريخ الموافقة/الرفض</th>
							<td>@Model._d(leaveReturn.ApprovalDate)</td>
						</tr>
					}
					@if (!string.IsNullOrEmpty(leaveReturn.ApprovalRem))
					{
						<tr>
							<th>ملاحظات</th>
							<td>@leaveReturn.ApprovalRem</td>
						</tr>
					}
				</table>
			</div>

			<!-- Original Leave Information -->
			<div class="col-md-6">
				<h5 class="">
					<i class="fas fa-calendar-alt"></i> معلومات الإجازة الأصلية
				</h5>
				@if (originalLeave != null)
				{
					<table class="table table-bordered">
						<tr>
							<th width="40%">رقم الإجازة</th>
							<td>
								<a href="~/Leaves/Department/Update/@($"{originalLeave.InYear}-{originalLeave.InMailNo}-{originalLeave.InDocSlNo}")">
									@($"{originalLeave.InYear}-{originalLeave.InMailNo}-{originalLeave.InDocSlNo}")
								</a>
							</td>
						</tr>
						<tr>
							<th>تاريخ بداية الإجازة</th>
							<td>@Model._d(leaveReturn.PLeaveStartDate)</td>
						</tr>
						<tr>
							<th>تاريخ نهاية الإجازة الأصلية</th>
							<td>@Model._d(leaveReturn.PLeaveEndDate)</td>
						</tr>
						<tr>
							<th>إجمالي أيام الإجازة الأصلية</th>
							<td>
								@if (leaveReturn.PLeaveStartDate.HasValue && leaveReturn.PLeaveEndDate.HasValue)
								{
									var totalDays = (leaveReturn.PLeaveEndDate.Value - leaveReturn.PLeaveStartDate.Value).Days + 1;
									<span class="badge badge-primary">@totalDays أيام</span>
								}
							</td>
						</tr>
						<tr>
							<th>الأيام المستخدمة فعلياً</th>
							<td>
								@if (leaveReturn.LeaveStartDate != default && leaveReturn.LeaveEndDate.HasValue)
								{
									var usedDays = (leaveReturn.NoLeaveDays);
									<span class="badge badge-warning">@usedDays أيام</span>
								}
							</td>
						</tr>
						<tr>
							<th>حالة الإجازة الأصلية</th>
							<td>@Html.Raw(Model.renderLeaveSatus(originalLeave.ReqStat))</td>
						</tr>
					</table>
				}
				else
				{
					<div class="alert alert-warning">
						<i class="fas fa-exclamation-triangle"></i>
						لا يمكن العثور على معلومات الإجازة الأصلية
					</div>
				}
			</div>
		</div>

		<!-- Balance Information -->
		<div class="row mt-4">
			<div class="col-12">
				<h5 class="">
					معلومات الرصيد
				</h5>
				<div class="row">
					<div class="col-md-4">
						<div class="info-box bg-primary p-2 rounded">
							<span class="info-box-icon"><i class="fas fa-calendar-check"></i></span>
							<div class="info-box-content">
								<span class="info-box-text">الرصيد الحالي</span>
								<span class="info-box-number">
									@Model._h.Leave().Balance(leaveReturn.EmpNo, leaveReturn.LeaveCode) أيام
								</span>
							</div>
						</div>
					</div>
					@if (leaveReturn.ReqStat == 6)
					{
						<div class="col-md-4">
							<div class="info-box bg-success p-2 rounded">
								<span class="info-box-icon"><i class="fas fa-plus"></i></span>
								<div class="info-box-content">
									<span class="info-box-text">تم إضافة للرصيد</span>
									<span class="info-box-number">@daysToCredit أيام</span>
								</div>
							</div>
						</div>
					}
					else if (daysToCredit > 0)
					{
						<div class="col-md-4">
							<div class="info-box bg-secondary p-2 rounded">
								<span class="info-box-icon"><i class="fas fa-clock"></i></span>
								<div class="info-box-content">
									<span class="info-box-text">سيتم إضافة للرصيد</span>
									<span class="info-box-number">@daysToCredit أيام</span>
								</div>
							</div>
						</div>
					}
				</div>
			</div>
		</div>

		<!-- Fingerprint Data Section -->
		<div class="row mt-4">
			<div class="col-12">
				<h5 class="">
					<i class="fas fa-fingerprint"></i> بيانات البصمة خلال فترة الإجازة
				</h5>
				
				<!-- Debug Information -->
				<div class="alert alert-info mb-3">
					<small>
						<strong>فترة البحث:</strong>
						من: @(leaveReturn.PLeaveStartDate?.ToString("yyyy-MM-dd") ?? "غير محدد")
						إلى: @(leaveReturn.PLeaveEndDate?.ToString("yyyy-MM-dd") ?? "غير محدد")
						| رقم الموظف: @leaveReturn.EmpNo
						| عدد السجلات الموجودة: @fingerprintData.Count
					</small>
				</div>

				
			</div>
		</div>

	</div>
</div>
<!-- Action Buttons -->
<div class="row mt-4">
	<div class="col-12">
		<div class="">
			@if (leaveReturn.ReqStat == 0)
            {
                <a href="~/Leaves/Department/Return/Approve/@($"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}")" 
                    class="btn btn-success after-confirm">
                    <i class="fas fa-check"></i> موافقة على قطع الإجازة
                </a>
                <a href="~/Leaves/Department/Return/Reject/@($"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}")" 
                    class="btn btn-danger after-confirm">
                    <i class="fas fa-times"></i> رفض الطلب
                </a>
            }
            else if (leaveReturn.ReqStat == 1 && Model.Can("audit"))
            {
                <a href="~/Leaves/Audit/Return/Approve/@($"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}")" 
                    class="btn btn-primary after-confirm">
                    <i class="fas fa-clipboard-check"></i> مراجعة واعتماد نهائي
                </a>
            }

		</div>
	</div>
</div>
