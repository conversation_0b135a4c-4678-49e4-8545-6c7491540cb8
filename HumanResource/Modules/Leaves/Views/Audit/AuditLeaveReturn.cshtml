@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel


@await Html.PartialAsync("_AuditAppTabs")

<div class="card">


    <table class="table table-striped datatable">
        <thead class="bg-primary">
            <tr>
                <th>رقم الطلب</th>
                <th>رقم الموظف</th>
                <th>اسم الموظف</th>
                <th>نوع الإجازة</th>
                <th>تاريخ بداية الإجازة الأصلية</th>
                <th>تاريخ نهاية الإجازة الأصلية</th>
                <th>تاريخ العودة الفعلي</th>
                <th>الأيام المسترجعة</th>
                <th>الحالة</th>
                <th>تاريخ الطلب</th>
        
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.TleaveReturnTxs)
            {
                var daysToCredit = item.LateOrEarlyDays*-1;

                <tr>
                    <td>
                        <a href="~/Leaves/Audit/Return/@($"{item.InYear}-{item.InMailNo}-{item.InDocSlNo}")">
                            @($"{item.InYear}-{item.InMailNo}-{item.InDocSlNo}")
                        </a>
                    </td>
                    <td>@item.EmpNo</td>
                    <td>@Model._h.StaffData(item.EmpNo).EmpNameA</td>
                    <td>@item.TleaveCode?.LeaveDespA</td>
                    <td>@Model._d(item.PLeaveStartDate)</td>
                    <td>@Model._d(item.PLeaveEndDate)</td>
                    <td>@Model._d(item.LeaveEndDate)</td>
                    <td>
                        <span class="badge badge-secondary">@daysToCredit أيام</span>
                    </td>
                    <td>@Html.Raw(Model.renderLeaveSatus(item.ReqStat))</td>
                    <td>@Model._d(item.TimeStamp)</td>
              
                </tr>
            }
        </tbody>
    </table>

</div>

@if (Model.TleaveReturnTxs?.Count == 0)
{
    <div class="alert alert-info text-center">
        <i class="fas fa-info-circle"></i>
        لا توجد طلبات قطع إجازة في الفترة المحددة
    </div>
}