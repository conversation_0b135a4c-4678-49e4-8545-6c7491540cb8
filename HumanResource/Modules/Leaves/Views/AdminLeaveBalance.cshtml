@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel


@{
    ViewData["Title"] = Model.Page.Title;
}


    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@Model.Page.Title</h1>
    </div>

    <!-- Quick Balance Update Card -->
    <div class="row mb-4">
        <div class="col-lg-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">تحديث رصيد إجازة موظف</h6>
                </div>
                <div class="card-body">
                    <form id="balance-update-form" class="ajax" action="~/Leaves/Admin/Balance/Update" method="post">
                        <div class="row">
                            <div class="col-lg-3">
                                <label for="empNo">الموظف <span class="text-danger">*</span></label>
                                <select class="form-control" name="empNo" id="empNo" required>
                                    <option value="">اختر الموظف</option>
                                    @foreach (var emp in Model.VempDtls)
                                    {
                                        <option value="@emp.EmpNo">@emp.EmpNo - @emp.EmpNameA</option>
                                    }
                                </select>
                            </div>
                            <div class="col-lg-3">
                                <label for="leaveCode">نوع الإجازة <span class="text-danger">*</span></label>
                                <select class="form-control" name="leaveCode" id="leaveCode" required>
                                    <option value="">اختر نوع الإجازة</option>
                                    @foreach (var leave in Model.LeaveCodes)
                                    {
                                        <option value="@leave.LeaveCode">@leave.LeaveDespA</option>
                                    }
                                </select>
                            </div>
                            <div class="col-lg-2">
                                <label for="currentBalance">الرصيد الحالي</label>
                                <input type="text" class="form-control" id="currentBalance" readonly placeholder="--">
                            </div>
                            <div class="col-lg-2">
                                <label for="newBalance">الرصيد الجديد <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" class="form-control" name="newBalance" id="newBalance" required>
                            </div>
                            <div class="col-lg-2">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block" id="updateBtn" disabled>
                                    <i class="fas fa-save"></i> تحديث الرصيد
                                </button>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-lg-12">
                                <label for="remarks">ملاحظات</label>
                                <textarea class="form-control" name="remarks" id="remarks" rows="2" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Balances Table -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">أرصدة الإجازات الحالية</h6>
                    <div class="dropdown no-arrow">
                        <select class="form-control" id="filterLeaveCode">
                            <option value="">جميع أنواع الإجازات</option>
                            @foreach (var leave in Model.LeaveCodes)
                            {
                                <option value="@leave.LeaveCode">@leave.LeaveDespA</option>
                            }
                        </select>
                    </div>
                </div>
                
                    <div class="table-responsive">
                        <table class="table table-striped" id="balanceTable"  cellspacing="0">
                            <thead class="bg-primary ">
                                <tr>
                                    <th>رقم الموظف</th>
                                    <th>اسم الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>الرصيد</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                
            </div>
        </div>
    </div>


<!-- Edit Balance Modal -->
<div class="modal fade" id="editBalanceModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل رصيد الإجازة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="edit-balance-form" class="ajax" action="~/Leaves/Admin/Balance/Update" method="post">
                <div class="modal-body">
                    <div class="form-group">
                        <label>الموظف</label>
                        <input type="text" class="form-control" id="editEmpName" readonly>
                        <input type="hidden" name="empNo" id="editEmpNo">
                        <input type="hidden" name="leaveCode" id="editLeaveCode">
                    </div>
                    <div class="form-group">
                        <label>نوع الإجازة</label>
                        <input type="text" class="form-control" id="editLeaveCodeName" readonly>
                    </div>
                    <div class="form-group">
                        <label>الرصيد الحالي</label>
                        <input type="text" class="form-control" id="editCurrentBalance" readonly>
                    </div>
                    <div class="form-group">
                        <label for="editNewBalance">الرصيد الجديد <span class="text-danger">*</span></label>
                        <input type="number" step="0.01" class="form-control" name="newBalance" id="editNewBalance" required>
                    </div>
                    <div class="form-group">
                        <label for="editRemarks">ملاحظات</label>
                        <textarea class="form-control" name="remarks" id="editRemarks" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = createDatatable('#balanceTable',true,{

        ajax: {
            url: '/Leaves/Admin/Balance/Datatable',
            type: 'POST',
            data: function(d) {
                d.leaveCode = $('#filterLeaveCode').val();
            }
        },
        columns: [
            { data: 'empNo', name: 'empNo' },
            { data: 'empName', name: 'empName' },
            { data: 'leaveType', name: 'leaveType' },
            { data: 'balance', name: 'balance' },
            { data: 'lastUpdated', name: 'lastUpdated' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
  
    
    });

    // Filter by leave code
    $('#filterLeaveCode').change(function() {
        table.ajax.reload();
    });

    // Load current balance when employee and leave code are selected
    $('#empNo, #leaveCode').change(function() {
        var empNo = $('#empNo').val();
        var leaveCode = $('#leaveCode').val();
        
        if (empNo && leaveCode) {
            $.get('/Leaves/GetEmpBalance', {
                empNo: empNo
            }, function(response) {
                if (response.success && response.data) {
                    // Find the specific leave code balance from the returned array
                    var leaveBalance = response.data.find(function(item) {
                        return item.leaveCode == leaveCode;
                    });
                    
                    if (leaveBalance) {
                        $('#currentBalance').val(leaveBalance.balance);
                        $('#updateBtn').prop('disabled', false);
                    } else {
                        $('#currentBalance').val('0');
                        $('#updateBtn').prop('disabled', false);
                    }
                } else {
                    $('#currentBalance').val('خطأ');
                    $('#updateBtn').prop('disabled', true);
                }
            });
        } else {
            $('#currentBalance').val('--');
            $('#updateBtn').prop('disabled', true);
        }
    });

    // Handle edit balance button click
    $(document).on('click', '.edit-balance', function() {
        var empNo = $(this).data('empno');
        var leaveCode = $(this).data('leavecode');
        
        // Load balance data using existing endpoint
        $.get('/Leaves/GetEmpBalance', {
            empNo: empNo
        }, function(response) {
            if (response.success && response.data) {
                // Find the specific leave code balance from the returned array
                var leaveBalance = response.data.find(function(item) {
                    return item.leaveCode == leaveCode;
                });
                
                if (leaveBalance) {
                    $('#editEmpNo').val(empNo);
                    $('#editLeaveCode').val(leaveCode);
                    $('#editEmpName').val(empNo + ' - ' + $('option[value="' + empNo + '"]', '#empNo').text().split(' - ')[1]);
                    $('#editLeaveCodeName').val(leaveBalance.leaveName);
                    $('#editCurrentBalance').val(leaveBalance.balance);
                    $('#editNewBalance').val(leaveBalance.balance);
                    $('#editRemarks').val('');
                    
                    $('#editBalanceModal').modal('show');
                } else {
                    Swal.fire('خطأ', 'لم يتم العثور على رصيد لهذا النوع من الإجازة', 'error');
                }
            } else {
                Swal.fire('خطأ', 'فشل في تحميل بيانات الرصيد', 'error');
            }
        });
    });

    // Handle successful form submission
    $(document).on('ajaxSuccess', function(event, xhr, settings) {
        if (settings.url.includes('/Leaves/Admin/Balance/Update')) {
            // Reload the table
            table.ajax.reload();
            
            // Reset the quick update form
            $('#balance-update-form')[0].reset();
            $('#currentBalance').val('--');
            $('#updateBtn').prop('disabled', true);
            
            // Close modal if open
            $('#editBalanceModal').modal('hide');
        }
    });

    // Reset form when modal is closed
    $('#editBalanceModal').on('hidden.bs.modal', function() {
        $('#edit-balance-form')[0].reset();
    });
});
</script>
