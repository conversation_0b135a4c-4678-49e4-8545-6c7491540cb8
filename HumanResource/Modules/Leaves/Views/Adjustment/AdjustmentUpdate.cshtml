@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel

@{
    var adjustment = Model.LeaveBalAdjustment;
    var reqStat = adjustment.ReqStat ?? 0;

}


<!-- Content Row -->
<div class="row">
    <div class="col-xl-12 col-md-12 mb-4">
        <h3 class="">بيانات تعديل رصيد الإجازة</h3>
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">


            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-right-primary">
                            <div class="card-body">
                                <h5 class="card-title ">معلومات الطلب</h5>
                                <hr>
                                <div class="mb-3">
                                    <strong>رقم الطلب:</strong>
                                    @<EMAIL><EMAIL>
                                </div>
                                <div class="mb-3">
                                    <strong>حالة الطلب:</strong>
                                    <span
                                        class="@(reqStat == 6 ? "text-success" : reqStat == 3 ? "text-danger" : "text-warning")">
                                        @Html.Raw(Model.renderLeaveSatus(reqStat))
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <strong>تاريخ الإنشاء:</strong>
                                    @(adjustment.TimeStamp.HasValue ? adjustment.TimeStamp.Value.ToString("yyyy-MM-dd")
                                                                        : "")
                                </div>
                                <div class="mb-3">
                                    <strong>تاريخ الموافقة:</strong>
                                    @(adjustment.ApprovalDate.HasValue ?
                                                                        adjustment.ApprovalDate.Value.ToString("yyyy-MM-dd") : "-")
                                </div>
                                <div class="mb-3">
                                    <strong>ملاحظات:</strong>
                                    @(!string.IsNullOrEmpty(adjustment.ApprovalRem) ? adjustment.ApprovalRem : "-")
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card " style="background-color: #f8f9fa !important;">
                            <div class="card-body">
                                <h5 class="card-title ">معلومات التعديل</h5>
                                <hr>
                                <div class="mb-3">
                                    <strong>الموظف:</strong> @Model.Helper.StaffData(adjustment.EmpNo).EmpNameA
                                </div>
                                <div class="mb-3">
                                    <strong>نوع الإجازة:</strong> @adjustment.TleaveCode.LeaveDespA
                                </div>

                                <div class="mb-3">
                                    <strong>عدد أيام التعديل:</strong>
                                    <span class="@(adjustment.NoDaysAdjust > 0 ? "text-success" : "text-danger")">
                                        @adjustment.NoDaysAdjust
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="border-2 border-primary p-2 rounded">
                            <div class="">
                                <h5 class=" text-primary">معلومات الرصيد</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <strong>الرصيد الحالي:</strong>
                                        @ViewBag.CurrentBalance
                                    </div>
                                    @if (adjustment.NoDaysAdjust.HasValue)
                                    {
                                        <div class="col-md-4 mb-3">
                                            <strong>التعديل:</strong>
                                            <span class=" @(adjustment.NoDaysAdjust > 0 ? "text-success" : "text-danger")">
                                                @(adjustment.NoDaysAdjust > 0 ? "+" : "")@adjustment.NoDaysAdjust

                                            </span>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <strong>الرصيد الجديد بعد التعديل:</strong>
                                            @ViewBag.NewBalance
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="">


            @{

                if (Model.Can("leaves-department") && reqStat == 0)
                {
                    <a href="/Leaves/Adjustment/Approve/@<EMAIL><EMAIL>"
                        class="btn btn-success  mr-1"><i class="fas fa-check"></i> موافقة القسم</a>
                    <a href="/Leaves/Adjustment/Reject/@<EMAIL><EMAIL>"
                        class="btn btn-danger " onclick="return confirm('هل أنت متأكد من رفض التعديل؟');"><i
                            class="fas fa-times"></i> رفض</a>
                }
                else if (Model.Can("leaves-department-manager") && reqStat == 4)
                {
                    <a href="/Leaves/Adjustment/ApproveManager/@<EMAIL><EMAIL>"
                        class="btn btn-success  mr-1"><i class="fas fa-check"></i> موافقة رئيس قسم</a>
                    <a href="/Leaves/Adjustment/Reject/@<EMAIL><EMAIL>"
                        class="btn btn-danger " onclick="return confirm('هل أنت متأكد من رفض التعديل؟');"><i
                            class="fas fa-times"></i> رفض</a>
                }
                else if (Model.Can("leaves-hr-manager") && reqStat == 5)
                {
                    <a href="/Leaves/Adjustment/Complete/@<EMAIL><EMAIL>"
                        class="btn btn-success  mr-1"><i class="fas fa-check"></i> موافقة نهائية</a>
                    <a href="/Leaves/Adjustment/Reject/@<EMAIL><EMAIL>"
                        class="btn btn-danger " onclick="return confirm('هل أنت متأكد من رفض التعديل؟');"><i
                            class="fas fa-times"></i> رفض</a>
                }
                else if (reqStat == 0 && Model.Can("leaves-department"))
                {
                    <a href="/Leaves/Adjustment/Delete/@<EMAIL><EMAIL>"
                        class="btn btn-danger " onclick="return confirm('هل أنت متأكد من حذف هذا التعديل؟');"><i
                            class="fas fa-trash"></i> حذف</a>
                }
            }
        </div>


    </div>
    <div class="col-md-6">
        <h5 class="mb-0">@Model._("سجل الأنشطة")</h5>
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">

                <tbody>
                    @foreach (var log in Model._h.GetLogs(adjustment.InYear + "-" + adjustment.InMailNo + "-" +
                                        adjustment.InDocSlNo, "LeaveBalAdjustment"))
                    {
                        <tr>
                            <td class="text-center"><i class="far fa-clock text-info"></i></td>
                            <td>@log.Rem</td>
                            <td dir="ltr">@Model._dt(log.TimeStamp)</td>
                            <td>@Model._h.StaffData(log.UserId).EmpNameA</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
