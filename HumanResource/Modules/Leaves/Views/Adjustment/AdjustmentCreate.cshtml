@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel




<h3 class="">بيانات تعديل رصيد الإجازة</h3>
    <!-- Content Row -->
    <div class="row">
        <div class="col-xl-12 col-md-12 mb-4">
            <div class="card shadow mb-4">
           
                <div class="card-body">
                    <form id="adjustmentForm" action="/Leaves/Adjustment/Create" method="post" class="ajax">
                        <div class="row">
                            <div class="col-md-6 form-group">
                                <label for="EmpNo">الموظف <span class="text-danger">*</span></label>
                                <select id="EmpNo" name="EmpNo" class="form-control select2" required>
                                    <option value="">-- اختر الموظف --</option>
                                    @foreach (var emp in Model.VempDtls)
                                    {
                                        <option value="@emp.EmpNo">@emp.EmpNameA (@emp.EmpNo)</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-6 form-group">
                                <label for="LeaveCode">نوع الإجازة <span class="text-danger">*</span></label>
                                <select id="LeaveCode" name="LeaveCode" class="form-control select2" required>
                                    <option value="">-- اختر نوع الإجازة --</option>
                                    <option value="1">إجازة اعتيادية</option>
                                </select>
                            </div>
                        </div>
                        
        
                        
                        <div class="row">
                            <div class="col-md-6 form-group">
                                <label for="NoDaysAdjust">عدد أيام التعديل <span class="text-danger">*</span></label>
                                <input type="number" step="0.5" id="NoDaysAdjust" name="NoDaysAdjust" class="form-control" required>
                                <small class="form-text text-muted">أدخل قيمة موجبة للإضافة، وقيمة سالبة للخصم</small>
                            </div>
                            <div class="col-md-6 form-group">
                                <label for="currentBalance">الرصيد الحالي</label>
                                <input type="text" id="currentBalance" class="form-control" readonly>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 form-group">
                                <label for="ApprovalRem">ملاحظات</label>
                                <textarea id="ApprovalRem" name="ApprovalRem" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                        
                        <div class="">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                      
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <script>
        $(document).ready(function() {
      
            // When employee or leave type changes, fetch the current balance
            $('#EmpNo, #LeaveCode').change(function() {
                var empNo = $('#EmpNo').val();
                var leaveCode = $('#LeaveCode').val();
                
                if (empNo && leaveCode) {
                    // Call API to get balance
                    $.get('/Leaves/GetEmpBalance?empNo=' + empNo, function(response) {
                        if (response.success) {
                            // Find the balance for the selected leave code
                            var balances = response.data;
                            for (var i = 0; i < balances.length; i++) {
                                if (balances[i].leaveCode == leaveCode) {
                                    $('#currentBalance').val(balances[i].balance);
                                    break;
                                }
                            }
                        }
                    });
                } else {
                    $('#currentBalance').val('');
                }
            });
            
     
        });
    </script>
