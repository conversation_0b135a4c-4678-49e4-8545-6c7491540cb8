    @model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel



    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-1">
        <h3 >تعديل رصيد الإجازات</h3>
        <div>
            <a href="/Leaves/Adjustment/Create" class=" btn btn-primary ">
            <i class="fas fa-plus  "></i> إضافة تعديل جديد
        </a>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <div class="col-xl-12 col-md-12 mb-4">
            <div class="card shadow mb-4">
                
               
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover datatable"  width="100%" cellspacing="0">
                            <thead class="bg-primary text-white">
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>أيام التعديل</th>
            
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var adjustment in Model.LeaveBalAdjustments)
                                {
                                    <tr>
                                        <td>
                                            <a href="/Leaves/Adjustment/Update/@<EMAIL><EMAIL>">
                                                @<EMAIL><EMAIL>
                                            </a>
                                        </td>
                                        <td>@Model.Helper.StaffData(adjustment.EmpNo).EmpNameA</td>
                                        <td>@adjustment.TleaveCode.LeaveDespA</td>
                                        <td class="@(adjustment.NoDaysAdjust > 0 ? "text-success" : "text-danger")">
                                            <strong class="text-monospace">@adjustment.NoDaysAdjust</strong>
                                        </td>
                  
                                        <td>@Html.Raw(Model.renderLeaveSatus(adjustment.ReqStat ?? 0))</td>
                                        <td>@(adjustment.TimeStamp.HasValue ? adjustment.TimeStamp.Value.ToString("yyyy-MM-dd") : "")</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="/Leaves/Adjustment/Update/@<EMAIL><EMAIL>" 
                                               class="btn  btn-primary">
                                                عرض
                                            </a>
                                            @if (adjustment.ReqStat == 0)
                                            {
                                                <a href="/Leaves/Adjustment/Delete/@<EMAIL><EMAIL>" 
                                                   class="btn  btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا التعديل؟');">
                                                    حذف
                                                </a>
                                            }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                
            </div>
        </div>
    </div>


