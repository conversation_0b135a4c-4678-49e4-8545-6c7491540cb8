﻿@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel

@Html.Partial("_DGAppTabs")

<div class="card shadow">

    <table class="table table-sm datatable table-striped table-hover">
        <thead class="bg-primary">

            <tr>
                <th>@Model._l("الرقم")</th>
                <th>@Model._l("الموظف")</th>
                <th>@Model._l("نوع الاجازة")</th>
                <th>@Model._l("من تاريخ")</th>
                <th>@Model._l("إلى تاريخ") </th>
                <th>@Model._l("عدد الأيام")</th>
                
            </tr>
        </thead>
        <tbody>
            @foreach (var leave in Model.TleaveAplTxs)
            {
                <tr>
                    <td data-sort="@Model._dt(leave.LeaveStartDate)"> <a href="~/Leaves/Dg/Update/@<EMAIL><EMAIL>">@<EMAIL><EMAIL></a></td>
                  <td>(@leave.EmpNo)  @Model._h.StaffData(leave.EmpNo).EmpNameA </td>
                    <td> @leave.TleaveCode.LeaveDespA</td>
                    <td>@Model._d(@leave.LeaveStartDate)</td>
                    <td>@Model._d(@leave.LeaveEndDate)</td>
                    <td>
                        @{
                            DateTime end = (DateTime)leave.LeaveEndDate.AddDays(1);
                            DateTime start = (DateTime)leave.LeaveStartDate;
                            TimeSpan diff = end.Subtract(start);

                            @diff.ToString("dd")

                        }
                    </td>

                 
                </tr>
            }
        </tbody>
    </table>

</div>
