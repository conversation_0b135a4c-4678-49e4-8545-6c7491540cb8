@{
    ViewData["DisableTagHelpers"] = true;
}
@removeTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model HumanResource.Modules.Leaves.ViewModels.LeaveViewModel

@{
    Layout = "_Layout";
    var v = Model;
    ViewData["Title"] = "تقرير الإجازات";
}

<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">تقرير الإجازات</h6>
    </div>
    <div class="card-body">
        <!-- Filter Form -->
        <form method="get" action="/Leaves/Report" class="mb-4">
            <div class="row">
                <div class="col-md-2 mb-3">
                    <label for="year">السنة</label>
                    <select name="year" id="year" class="form-control">
                        @{
                            int currentYear = DateTime.Now.Year;
                            for (int year = currentYear - 5; year <= currentYear + 1; year++)
                            {
                                <option value="@year" @(ViewBag.Year == year ? "selected" : "")>@year</option>
                            }
                        }
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="month">الشهر</label>
                    <select name="month" id="month" class="form-control">
                        <option value="0" @(ViewBag.Month == 0 ? "selected" : "")>جميع الأشهر</option>
                        <option value="1" @(ViewBag.Month == 1 ? "selected" : "")>يناير</option>
                        <option value="2" @(ViewBag.Month == 2 ? "selected" : "")>فبراير</option>
                        <option value="3" @(ViewBag.Month == 3 ? "selected" : "")>مارس</option>
                        <option value="4" @(ViewBag.Month == 4 ? "selected" : "")>أبريل</option>
                        <option value="5" @(ViewBag.Month == 5 ? "selected" : "")>مايو</option>
                        <option value="6" @(ViewBag.Month == 6 ? "selected" : "")>يونيو</option>
                        <option value="7" @(ViewBag.Month == 7 ? "selected" : "")>يوليو</option>
                        <option value="8" @(ViewBag.Month == 8 ? "selected" : "")>أغسطس</option>
                        <option value="9" @(ViewBag.Month == 9 ? "selected" : "")>سبتمبر</option>
                        <option value="10" @(ViewBag.Month == 10 ? "selected" : "")">أكتوبر</option>
                        <option value="11" @(ViewBag.Month == 11 ? "selected" : "")>نوفمبر</option>
                        <option value="12" @(ViewBag.Month == 12 ? "selected" : "")>ديسمبر</option>
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="leaveCode">نوع الإجازة</label>
                    <select name="leaveCode" id="leaveCode" class="form-control">
                        <option value="0">جميع الأنواع</option>
                        @foreach (var leaveCode in ViewBag.LeaveCodes)
                        {
                            <option value="@leaveCode.LeaveCode" @(ViewBag.LeaveCode == leaveCode.LeaveCode ? "selected" : "")>@leaveCode.LeaveDespA</option>
                        }
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="Dg">المديرية </label>
                    <select name="Dg" id="Dg" class="form-control" onchange="updateDepartments()">
                        <option value="0">جميع المديريات</option>
                        @foreach (var dg in ViewBag.DGs)
                        {
                            <option value="@dg.DgCode" @(ViewBag.Dg == dg.DgCode ? "selected" : "")>@dg.DgDespA</option>
                        }
                    </select>
                </div>
                <div class="col-md-2 mb-3">
                    <label for="Dept">الدائرة</label>
                    <select name="Dept" id="Dept" class="form-control">
                        <option value="0">جميع الدوائر</option>
                        @foreach (var dept in ViewBag.Departments)
                        {
                            <option value="@dept.DeptCode" @(ViewBag.Dept == dept.DeptCode ? "selected" : "")>@dept.DeptDespA</option>
                        }
                    </select>
                </div>
                <div class="col-md-2 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary btn-block">تطبيق الفلتر</button>
                </div>
            </div>
        </form>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">إجمالي الإجازات</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.TotalLeaves</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">عدد الموظفين المستفيدين</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.UniqueEmployees</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">متوسط مدة الإجازة</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.AverageDuration يوم</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">أطول إجازة</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.LongestLeave يوم</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-hourglass fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row">
            <!-- Leave Type Distribution -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">توزيع الإجازات حسب النوع</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="leaveTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Distribution -->
            <div class="col-lg-6 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">توزيع الإجازات حسب الحالة</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Monthly Trend -->
            @if (ViewBag.Month == 0)
            {
                <div class="col-lg-12 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">التوزيع الشهري للإجازات</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-bar">
                                <canvas id="monthlyTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Department Distribution -->
            <div class="col-lg-12 mb-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">توزيع الإجازات حسب الدوائر</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-bar">
                            <canvas id="departmentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Department Table -->
<div class="card shadow mb-4 mt-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">تفاصيل الإجازات حسب الدوائر</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-striped" id="departmentsTable" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>الدائرة</th>
                        <th>عدد الإجازات</th>
                        <th>عدد الأيام</th>
                    </tr>
                </thead>
                <tbody>
                    @if(ViewBag.AllDepartments != null)
                    {
                        foreach(var dept in ViewBag.AllDepartments)
                        {
                            <tr>
                                <td>@dept.DeptName</td>
                                <td>@dept.Count</td>
                                <td>@dept.Days</td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/assets/js/chart.js"></script>
    <script src="~/assets/js/chartjs-plugin-datalabels.js"></script>

    <script>
        // Register the plugin to all charts
        Chart.register(ChartDataLabels);

        // Common options for all charts
        const commonOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                datalabels: {
                    color: '#fff',
                    font: {
                        weight: 'bold'
                    },
                    formatter: (value, ctx) => {
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map(data => {
                            sum += data;
                        });
                        let percentage = (value * 100 / sum).toFixed(1) + "%";
                        return percentage;
                    }
                },
                legend: {
                    position: 'right',
                    labels: {
                        boxWidth: 15,
                        padding: 15
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            let value = context.raw || 0;
                            return label + ': ' + value;
                        }
                    }
                }
            }
        };

        // Leave Type Distribution Chart
        const leaveTypeChart = new Chart(
            document.getElementById('leaveTypeChart'),
            {
                type: 'bar',
                data: {
                    labels: [@Html.Raw(ViewBag.LeaveTypeLabels)],
                    datasets: [
                        {
                            label: 'عدد الإجازات',
                            data: [@ViewBag.LeaveTypeCounts],
                            backgroundColor: '#4e73df',
                            borderWidth: 1
                        },
                        {
                            label: 'عدد الأيام',
                            data: [@ViewBag.LeaveTypeDays],
                            backgroundColor: '#1cc88a',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        datalabels: {
                            display: false
                        }
                    }
                }
            }
        );

        // Status Distribution Chart
        const statusChart = new Chart(
            document.getElementById('statusChart'),
            {
                type: 'bar',
                data: {
                    labels: [@Html.Raw(ViewBag.StatusLabels)],
                    datasets: [
                        {
                            label: 'عدد الإجازات',
                            data: [@ViewBag.StatusCounts],
                            backgroundColor: '#4e73df',
                            borderWidth: 1
                        },
                        {
                            label: 'عدد الأيام',
                            data: [@ViewBag.StatusDays],
                            backgroundColor: '#1cc88a',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        datalabels: {
                            display: false
                        }
                    }
                }
            }
        );

        @if (ViewBag.Month == 0)
        {
            <text>
            // Monthly Trend Chart
            const monthlyTrendChart = new Chart(
                document.getElementById('monthlyTrendChart'),
                {
                    type: 'bar',
                    data: {
                        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                        datasets: [{
                            label: 'عدد الإجازات',
                            data: [@ViewBag.MonthlyData],
                            backgroundColor: '#4e73df',
                            borderColor: '#4e73df',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            datalabels: {
                                color: '#fff',
                                anchor: 'end',
                                align: 'top',
                                formatter: function(value) {
                                    if (value === 0) return '';
                                    return value;
                                }
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0
                                }
                            }
                        }
                    }
                }
            );
            </text>
        }

        // Department Distribution Chart
        const departmentChart = new Chart(
            document.getElementById('departmentChart'),
            {
                type: 'bar',
                data: {
                    labels: [@Html.Raw(ViewBag.DepartmentLabels)],
                    datasets: [
                        {
                            label: 'عدد الإجازات',
                            data: [@ViewBag.DepartmentCounts],
                            backgroundColor: '#4e73df',
                            borderWidth: 1
                        },
                        {
                            label: 'عدد الأيام',
                            data: [@ViewBag.DepartmentDays],
                            backgroundColor: '#1cc88a',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        datalabels: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            }
        );

        // Initialize DataTable for departments
        $(document).ready(function() {
            if ($.fn.DataTable) {
                $('#departmentsTable').DataTable({
                    order: [[2, 'desc']],
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json'
                    }
                });
            }
        });

        // Cascading Dropdown Functions
        function updateDepartments() {
            var dgCode = document.getElementById('Dg').value;
            
            // Clear current department options
            var deptSelect = document.getElementById('Dept');
            deptSelect.innerHTML = '<option value="0">جميع الدوائر</option>';
            
            if (dgCode > 0) {
                // Fetch departments for the selected DG
                fetch(`/Leaves/departments?dgCode=${dgCode}`)
                    .then(response => response.json())
                    .then(data => {
                        data.forEach(dept => {
                            let option = document.createElement('option');
                            option.value = dept.deptCode;
                            option.textContent = dept.deptDespA;
                            deptSelect.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error fetching departments:', error);
                    });
            }
        }

        // Auto-submit form when filters change (optional)
        function autoSubmitForm() {
            document.querySelector('form').submit();
        }

        // Add event listeners for auto-submit (uncomment if desired)
        // document.getElementById('year').addEventListener('change', autoSubmitForm);
        // document.getElementById('month').addEventListener('change', autoSubmitForm);
        // document.getElementById('leaveCode').addEventListener('change', autoSubmitForm);
        // document.getElementById('Dg').addEventListener('change', autoSubmitForm);
        // document.getElementById('Dept').addEventListener('change', autoSubmitForm);
    </script>
} 