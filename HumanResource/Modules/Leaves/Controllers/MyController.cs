﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;


namespace HumanResource.Modules.Leaves.Controllers;

[Area("Leaves")]
[Route("Leaves/My")]
public class MyController : LeavesController
{

    public MyController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

    }

    [HttpGet("")]
    public IActionResult MyLeaves(DateTime? from, DateTime? to)
    {
        _v.Page.Active = "";
        _v.Page.Reload = true;

        _v.Page.Title = "الاجازات";


        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        if (from != null)
        {
            _v.Page.Filter.DateFrom = from;
        }

        if (to != null)
        {
            to = to.Value.Date.Add(new TimeSpan(23, 59, 59));
            _v.Page.Filter.DateTo = to;
        }

        
        var leaveAplTxs = _db.TleaveAplTxs
            .Include(a => a.TleaveCode)
            .Where(a => a.EmpNo == _v.Profile.EmpNo && a.TimeStamp >= _v.Page.Filter.DateFrom && a.TimeStamp <= _v.Page.Filter.DateTo && a.CancelFlag != 1)
            .OrderByDescending(x => x.TimeStamp).ToList();


        _v.TleaveAplTxs = leaveAplTxs;


        ViewBag.LeaveBal = _h.Leave().Balance(_v.Profile.EmpNo.Value, 1);

        return View(_v);

    }


    [HttpGet("Update/{Id}")]
    public IActionResult MyLeaveUpdate(string Id)
    {
        _v.Page.Active = "";
        _v.Page.Reload = true;
        _v.Page.Back = "/Leaves/My";

        _v.Page.Title = "الاجازات";

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);

        var empLeave = _db.TleaveAplTxs
            .Include(a => a.TleaveCode)
            .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.EmpNo == _v.Profile.EmpNo
            && ro.CancelFlag != 1
            )
            .FirstOrDefault();

        if (empLeave == null)
            return StatusCode(404);

        _v.TleaveAplTx = empLeave;

        ViewBag.IsReturnAllowed = _db.TleaveReturnTxs.Where(a => a.EmpNo == _v.Profile.EmpNo && a.LeaveCode == 1 && a.OrderYear == InYear && a.OrderDeptInd == 1 && a.OrderSlNo == InDocSlNo).ToList().Count == 0 ? true : false;

        // Load leave return details if they exist
        var leaveReturn = _db.TleaveReturnTxs
            .Include(r => r.TleaveCode)
            .Where(r =>
                r.EmpNo == _v.Profile.EmpNo &&
                r.OrderYear == InYear &&
                r.OrderDeptInd == 1 &&
                r.OrderSlNo == InDocSlNo &&
                r.CancelFlag != 1)
            .FirstOrDefault();

        _v.TleaveReturnTx = leaveReturn;

        return View(_v);
    }

    [HttpGet("Send/{Id}")]
    public IActionResult Send(string Id)
    {
        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);

        var empLeave = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.EmpNo == _v.Profile.EmpNo
            && ro.ApprovalDate == null
            && ro.CancelFlag != 1
            )
            .FirstOrDefault();

        if (empLeave == null)
            return StatusCode(404);

        empLeave.ReqStat = 0;

        _db.Update(empLeave);
        _db.SaveChanges();

        var balance = _h.Leave().Balance(empLeave.EmpNo, empLeave.LeaveCode);

        Log(
            empLeave.InYear + "-" + empLeave.InMailNo + "-" + empLeave.InDocSlNo,
            "Leaves",
            "ارسال الى المسؤول المباشر" + _h.StaffData(empLeave.SignAuthCode).EmpNameA
        );

        Log(
            empLeave.InYear + "-" + empLeave.InMailNo + "-" + empLeave.InDocSlNo,
            "LeaveBalLog",
            "EmpNo: " + empLeave.EmpNo + " - LeaveCode: " + empLeave.LeaveCode,
            ("الرصيد الحالي", balance)
        );

        return Content("<script>window.location.href='/Leaves/My/?success=Action completed';</script>", "text/html");
    }


    [HttpPost("Create")]
    public async Task<IActionResult> MyLeaves(TleaveAplTx post, IFormFile SupportingDocument)
    {
        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        post.EmpNo = _v.Profile.EmpNo.Value;

        int[] leaveCodes = new int[] { 3, 31, 1, 21 };

        if (!leaveCodes.Contains(post.LeaveCode))
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "الرجاء اختيار نوع الاجازة" });
        }

        if (SupportingDocument != null)
        {
            post.FileGuid = await _h.UploadAsync(SupportingDocument, _v.Profile.EmpNo.Value, "Leaves");
        }

        return CreateLeaveRequest(post);
    }

    [Route("Cancel/{Id}")]
    public IActionResult MyRequestCancel(string Id)
    {

        string[] rawcode = Id.Split("-");


        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);

        decimal beforeBalance = _h.Leave().Balance(_v.Profile.EmpNo.Value, 1);

        var empleave = _db.TleaveAplTxs
            .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.EmpNo == _v.Profile.EmpNo
            && ro.ApprovalDate == null
            && ro.CancelFlag == 0
            )
            .FirstOrDefault();

        if (empleave == null)
            return StatusCode(404);

        empleave.CancelFlag = 1;

        _db.SaveChanges();

        decimal balance = _h.Leave().Balance(_v.Profile.EmpNo.Value, 1);

        Log(
            empleave.InYear + "-" + empleave.InMailNo + "-" + empleave.InDocSlNo,
            "Leaves",
            "الغاء اجازة"
        );

        Log(
            empleave.InYear + "-" + empleave.InMailNo + "-" + empleave.InDocSlNo,
            "LeaveBalLog",
            "EmpNo: " + empleave.EmpNo + " - LeaveCode: " + empleave.LeaveCode,
            ("الرصيد السابق", beforeBalance),
            ("الرصيد الحالي", balance)
        );

        return Content("<script>window.location.href='/Leaves/My/?success=Action completed';</script>", "text/html");
    }
    
    [HttpGet("Calculate")]
    public IActionResult CalculateLeaveBalance()
    {
        _h.Leave().CalculateLeaveBalance(_v.Profile.EmpNo.Value, 1);
        return ResponseHelper.Json(data: _v.GetEmpLeaveCodeBals(_v.Profile.EmpNo.Value), success:true);
    }

    [HttpPost("Return/{Id}")]
    public async Task<IActionResult> MyLeaveReturn(string Id, TleaveReturnTx post)
    {
        if (!IsValid(ModelState))
        {
            return Json(new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = ""
            });
        }

        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);
        
        var leave = _db.TleaveAplTxs
            .Where(l => 
                l.InYear == InYear && 
                l.InMailNo == InMailNo && 
                l.InDocSlNo == InDocSlNo &&
                l.EmpNo == _v.Profile.EmpNo &&
                l.ReqStat == 6 &&
                l.CancelFlag != 1)
            .FirstOrDefault();

        if (leave == null)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "طلب الإجازة غير موجود" });
        }

        var leaveReturnValid = _db.TleaveReturnTxs
            .Where(l => 
                l.EmpNo == _v.Profile.EmpNo &&
                l.LeaveCode == leave.LeaveCode &&
                l.PLeaveStartDate <= leave.LeaveStartDate &&
                l.PLeaveEndDate >= leave.LeaveEndDate &&
                l.CancelFlag != 1)
            .FirstOrDefault();

        if (leaveReturnValid != null)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "يوجد طلب قطع إجازة سابق لهذه الإجازة" });
        }

        if (post.LeaveEndDate == default(DateTime))
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "يجب تحديد تاريخ العودة للعمل" });
        }

        if (post.LeaveEndDate < leave.LeaveStartDate || post.LeaveEndDate > leave.LeaveEndDate)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "تاريخ العودة للعمل يجب أن يكون ضمن فترة الإجازة" });
        }

        if (post.LeaveEndDate > leave.LeaveEndDate)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "تاريخ العودة للعمل يجب أن يكون قبل تاريخ اكمال الاجازة" });
        }

        int numberOfHolidays = _db.Tholidays.Where(h => h.Holiday >= leave.LeaveStartDate && h.Holiday <= post.LeaveEndDate).Count();

        var mail = _h.Mail().Create(266);

        // Create new leave return transaction record
        var leaveReturn = new TleaveReturnTx
        {
            // Mail reference details
            InYear = mail.InYear,
            InMailNo = mail.InMailNo,
            InDocSlNo = (int)mail.LastDocSlNo,

            // Employee and leave type info
            EmpNo = _v.Profile.EmpNo.Value,
            LeaveCode = leave.LeaveCode,
            UnitCode = 1,

            // Current leave dates
            LeaveStartDate = leave.LeaveStartDate,
            LeaveEndDate = post.LeaveEndDate,

            // Original leave dates for reference
            PLeaveStartDate = leave.LeaveStartDate,
            PLeaveEndDate = leave.LeaveEndDate,

            // Calculate leave duration
            NoLeaveDays = (int)(post.LeaveEndDate.Value - leave.LeaveStartDate).TotalDays,
            LateOrEarlyDays = (int)(post.LeaveEndDate.Value - leave.LeaveEndDate).TotalDays -1,
            NoHolidays = numberOfHolidays,

            // Status and metadata
            ReasonCode = 41, // Code for leave return
            CancelFlag = 0,  // Not cancelled
            ReqStat = 0,     // Initial request status
            
            // Approval tracking
            ApprovalDate = null,
            ApprovalRem = null,

            // Original order reference
            OrderYear = leave.InYear,
            OrderDeptInd = leave.InDeptInd,
            OrderSlNo = leave.InDocSlNo,

            // Return to duty date
            DutyResumeDate = post.LeaveEndDate,

            // Audit fields
            UserId = _v.Profile.EmpNo.Value.ToString(),
            TimeStamp = DateTime.Now,
            TxnDate = DateTime.Now
        };

        _db.TleaveReturnTxs.Add(leaveReturn);
        _db.SaveChanges();
        
        Log(
            $"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}",
            "LeaveReturn",
            $"تم إرسال طلب قطع إجازة جديد بتاريخ {post.LeaveEndDate.Value.ToShortDateString()}"
        );

        Log(
            $"{leave.InYear}-{leave.InMailNo}-{leave.InDocSlNo}",
            "Leaves",
            $" طلب قطع إجازة جديد بتاريخ {post.LeaveEndDate.Value.ToShortDateString()}"
        );

        await _h.Notify().Create(
            leave.SignAuthCode.Value,
            "قطع إجازة",
            $"قام {_h.StaffData(_v.Profile.EmpNo.Value).EmpNameA} بإرسال طلب قطع إجازة جديد بتاريخ {post.LeaveEndDate.Value.ToShortDateString()}"
        );

        await _h.Notify().Create(
            _v.Profile.EmpNo.Value,
            "قطع إجازة",
            $" تم تقديم طلب قطع إجازة جديد "
        );

        return ResponseHelper.Json(
            success: true,
            message: new List<string> { "تم إرسال طلب قطع إجازة جديد بنجاح" },
            action: "location.href='/Leaves/My/Update/" + Id + "'"
        );
    }


}

