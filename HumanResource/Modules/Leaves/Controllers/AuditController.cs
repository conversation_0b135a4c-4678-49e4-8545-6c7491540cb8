﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Core.Data;


namespace HumanResource.Modules.Leaves.Controllers;

[Can(Right.Audit)]
[Area("Leaves")]
[Route("Leaves/Audit")]
public class AuditController : LeavesController
{

    public AuditController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

    }
    
    [HttpGet("Return")]
    public IActionResult AuditLeaveReturn(DateTime? from, DateTime? to)
    {
        _v.Page.Title = " طلبات قطع الإجازات";
        _v.Page.Active = "Audit";
        _v.Page.Reload = true;

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        if (from.HasValue)
        {
            _v.Page.Filter.DateFrom = from.Value;
        }
        if (to.HasValue)
        {
            _v.Page.Filter.DateTo = to.Value;
        }

        // Get all leave return requests
        var leaveReturns = _db.TleaveReturnTxs
            .Include(r => r.TleaveCode)
            .Where(r => 
                r.CancelFlag != 1 && 
                (r.ReqStat == 1 || r.ReqStat == 6) &&
                r.TimeStamp >= _v.Page.Filter.DateFrom && 
                r.TimeStamp <= _v.Page.Filter.DateTo)
            .OrderByDescending(r => r.TimeStamp)
            .ToList();

        _v.TleaveReturnTxs = leaveReturns;

        // Get all leave codes for the dropdown
        _v.LeaveCodes = _db.TleaveCodes.OrderBy(l => l.LeaveDespA).ToList();

        return View(_v);
    }

    [HttpGet("Return/{Id}")]
    public IActionResult AuditLeaveReturnUpdate(string Id)
    {

        _v.Page.Title = "تفاصيل طلب قطع الإجازة";
        _v.Page.Active = "Audit";
        _v.Page.Back = "/Leaves/Audit/Return";
        _v.Page.Reload = true;

        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the leave return details
        var leaveReturn = _db.TleaveReturnTxs
            .Include(r => r.TleaveCode)
            .FirstOrDefault(r => 
                r.InYear == InYear && 
                r.InMailNo == InMailNo && 
                r.InDocSlNo == InDocSlNo &&
                (r.ReqStat == 1 || r.ReqStat == 6) &&
                r.CancelFlag != 1);

        if (leaveReturn == null)
            return StatusCode(404);

        _v.TleaveReturnTx = leaveReturn;

        // Get the original leave request details
        var originalLeave = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .FirstOrDefault(l => 
                l.InYear == leaveReturn.OrderYear && 
                l.InDeptInd == leaveReturn.OrderDeptInd && 
                l.InDocSlNo == leaveReturn.OrderSlNo);

        _v.TleaveAplTx = originalLeave;

        return View(_v);
    }

    [HttpGet("Return/Approve/{Id}")]
    public IActionResult AuditLeaveReturnApprove(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the leave return to audit
        var leaveReturn = _db.TleaveReturnTxs
            .FirstOrDefault(r => 
                r.InYear == InYear && 
                r.InMailNo == InMailNo && 
                r.InDocSlNo == InDocSlNo &&
                r.ReqStat == 1 &&  
                r.CancelFlag != 1);

        if (leaveReturn == null)
            return StatusCode(404);

        // Get the original leave request
        var originalLeave = _db.TleaveAplTxs
            .FirstOrDefault(l => 
                l.InYear == leaveReturn.OrderYear && 
                l.InDeptInd == leaveReturn.OrderDeptInd && 
                l.InDocSlNo == leaveReturn.OrderSlNo);

        if (originalLeave == null)
            return StatusCode(404);

        // Calculate days to be credited back
        var daysToCredit = leaveReturn.LateOrEarlyDays*-1;

        if (daysToCredit <= 0)
        {
            return Content($"<script>window.location.href='/Leaves/Department/Return/{Id}?error={Uri.EscapeDataString("لا توجد أيام لإضافتها للرصيد")}';</script>", "text/html");
        }

        // Update leave return status to audited and finalized (6)
        leaveReturn.ReqStat = 6;  // Audited and finalized
        leaveReturn.AuditStat = 1;  // Audited
        leaveReturn.AuditDate = DateTime.Now;
        leaveReturn.AuditRem = "تمت المراجعة والاعتماد النهائي";
        leaveReturn.AuditUserId = _v.Profile.EmpNo.Value.ToString();
        leaveReturn.AuditTimeStamp = DateTime.Now;

        // Update the original leave's end date to match the actual return date
        originalLeave.LeaveEndDate = leaveReturn.LeaveEndDate.Value;
        _db.Update(originalLeave);

        // Create mail reference for the balance adjustment
        var mail = _h.Mail().Create(269);

        // Create leave balance adjustment record
        var balanceAdjustment = new TleaveBalAdjustmentTx
        {
            InYear = leaveReturn.InYear,
            InDeptInd = leaveReturn.InDeptInd,
            InMailNo = leaveReturn.InMailNo,
            InDocSlNo = leaveReturn.InDocSlNo,
            UnitCode = leaveReturn.UnitCode,
            EmpNo = leaveReturn.EmpNo,
            LeaveCode = leaveReturn.LeaveCode,
            NoDaysAdjust = daysToCredit,
            ApprovalRem = "Return from leave: credit unused days",
            TxnDate = DateTime.Now,
            UserId = _v.Profile.EmpNo.Value.ToString(),
            TimeStamp = DateTime.Now,
            TransType = null,
            OrderType = 72,
            ReqStat = 6, // Automatically approved
            CancelFlag = 0
        };

        _db.TleaveBalAdjustmentTxs.Add(balanceAdjustment);

        // Update employee leave balance
        var leaveBal = _db.TleaveBals
            .FirstOrDefault(l => 
                l.UnitCode == (byte)leaveReturn.UnitCode &&
                l.EmpNo == leaveReturn.EmpNo && 
                l.LeaveCode == (byte)leaveReturn.LeaveCode);

        if (leaveBal != null)
        {
            // Log current balance before changes
            _h.Leave().LogLeaveBalance(leaveBal);
            
            // Add the credited days
            leaveBal.NoLeaveBalDays += (decimal)daysToCredit;
            leaveBal.RefDate = DateTime.Now;
            leaveBal.UserId = _v.Profile.EmpNo.Value.ToString();
            leaveBal.TimeStamp = DateTime.Now;
            
            // Log the updated balance
            _h.Leave().LogLeaveBalance(leaveBal);
        }
        else
        {
            _h.Leave().CalculateLeaveBalance(leaveReturn.EmpNo, leaveReturn.LeaveCode);
            
            // Create new balance record if none exists
            leaveBal = new TleaveBal
            {
                UnitCode = (byte)leaveReturn.UnitCode,
                EmpNo = (short)leaveReturn.EmpNo,
                LeaveCode = (byte)leaveReturn.LeaveCode,
                RefDate = DateTime.Now,
                NoLeaveBalDays = (decimal)daysToCredit,
                UserId = _v.Profile.EmpNo.Value.ToString(),
                TimeStamp = DateTime.Now
            };
            
            _db.TleaveBals.Add(leaveBal);
            
            // Log the new balance
            _h.Leave().LogLeaveBalance(leaveBal);
        }

        // Insert record identifying the return from leave transaction
        var leaveDetail = new TdispTrnLeaveDtl
        {
            InYear = leaveReturn.InYear,
            InDeptInd = leaveReturn.InDeptInd,
            InMailNo = leaveReturn.InMailNo,
            InDocSlNo = leaveReturn.InDocSlNo,
            LeaveCode = leaveReturn.LeaveCode,
            FromDate = leaveReturn.LeaveEndDate.Value,
            ToDate = leaveReturn.LeaveEndDate.Value,
            OrderType = 266 // Leave return order type
        };

        _db.TdispTrnLeaveDtls.Add(leaveDetail);
        _db.SaveChanges();

        // Log the audit action
        Log(
            $"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}",
            "LeaveReturn",
            "مراجعة واعتماد نهائي لقطع الإجازة"
        );

        // Log the balance change
        Log(
            $"Balance-{leaveReturn.EmpNo}-{leaveReturn.LeaveCode}",
            "LeaveBalLog",
            $"EmpNo: {leaveReturn.EmpNo} - LeaveCode: {leaveReturn.LeaveCode}",
            ("أيام مضافة للرصيد", daysToCredit),
            ("الرصيد الجديد", _h.Leave().Balance(leaveReturn.EmpNo, leaveReturn.LeaveCode))
        );

        // Notify the employee
        _h.Notify().Create(
            empNo: leaveReturn.EmpNo, 
            title: $"تم الاعتماد النهائي لطلب قطع الإجازة وإضافة {daysToCredit} أيام لرصيدك"
        );

        return Content($"<script>window.location.href='/Leaves/Department/Return/{Id}?success={Uri.EscapeDataString("تم الاعتماد النهائي بنجاح وتم تحديث رصيد الإجازة")}';</script>", "text/html");
    }
}

