﻿using Microsoft.AspNetCore.Mvc;
using System.Data;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Leaves.Controllers;

[Area("Leaves")]
[Route("Leaves/Holiday")]
[Can("leaves-department")]
public class HolidayController : LeavesController
{

    public HolidayController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
    }

    [HttpGet("")]
    public IActionResult Holiday()
    {
        _v.Page.Title = "الاجازات";
        _v.Page.Active = "hr_leaves";

        _v.Holidays = _db.Tholidays.OrderByDescending(h => h.Holiday).Take(100).ToList();

        return View(_v);
    }

    [HttpGet("Create")]
    public IActionResult HolidayCreate()
    {
        _v.Holiday = new Tholidays();



        _v.Page.Title = "الاجازات";
        _v.Page.Active = "hr_leaves";

        _v.Page.Back = "/Leaves/Holiday";


        return View(_v);
    }

    [HttpPost("Create")]
    public IActionResult HolidayCreate(List<Tholidays> holidays)
    {
        if (!IsValid(ModelState))
        {
            return Json(new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            });
        }

        foreach (var holiday in holidays)
        {
            if (holiday.Holiday.HasValue)
            {
                var existingHoliday = _db.Tholidays.Where(h => h.Holiday.HasValue && h.Holiday.Value.Date == holiday.Holiday.Value.Date).FirstOrDefault();
                if (existingHoliday == null)
                {
                    _db.Tholidays.Add(holiday);

                    Log(
                        holiday.Holiday.Value.Date.ToString("yyyy-MM-dd"),
                        "HolidayLog",
                        $"Holiday: {holiday.Holiday.Value.Date} - Description: {holiday.DespA} Created"
                    );
                }
            }
        }

        _db.SaveChanges();

        return Json(new {
            success = true,
            message = "تم إنشاء الاجازة الرسمية بنجاح",
            action = "location.href='/Leaves/Holiday'",
        });
    }


    [HttpGet("Delete/{Id}")]
    public IActionResult HolidayDelete(DateTime Id)
    {
        var holiday = _db.Tholidays.FirstOrDefault(h => h.Holiday.HasValue && h.Holiday.Value.Date == Id.Date);
        
        if (holiday == null)
            return StatusCode(404);
        
        _db.Tholidays.Remove(holiday);
        _db.SaveChanges();

        Log(
            holiday.Holiday.Value.Date.ToString("yyyy-MM-dd"),
            "HolidayLog",
            $"Holiday: {holiday.Holiday.Value.Date} - Description: {holiday.DespA} Deleted"
        );
        
        return Redirect("/Leaves/Holiday?success=true");
    }

}

