﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using HumanResource.Modules.Leaves.ViewModels;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;


namespace HumanResource.Modules.Leaves.Controllers;


    

[Area("Leaves")]
[Route("Leaves")]
public class LeavesController : BaseController
{

    public LeaveViewModel _v;

    public LeavesController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _v = new LeaveViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "hr";
        _v.Helper = helper;

    }
    
    // Shared helper methods to reduce code duplication
    #region Helper Methods
    
    /// <summary>
    /// Parses leave ID in format "Year-MailNo-DocSlNo" to individual components
    /// </summary>
    protected (int InYear, int InMailNo, int InDocSlNo) ParseLeaveId(string id)
    {
        string[] rawcode = id.Split("-");
        return (
            InYear: int.Parse(rawcode[0]),
            InMailNo: int.Parse(rawcode[1]),
            InDocSlNo: int.Parse(rawcode[2])
        );
    }
    
    /// <summary>
    /// Find a leave request by its composite ID components
    /// </summary>
    protected IQueryable<TleaveAplTx> FindLeaveRequest(int inYear, int inMailNo, int inDocSlNo)
    {
        return _db.TleaveAplTxs.Where(l => 
            l.InYear == inYear && 
            l.InMailNo == inMailNo && 
            l.InDocSlNo == inDocSlNo);
    }
    
    /// <summary>
    /// Creates standardized log entries for leave actions
    /// </summary>
    protected void LogLeaveAction(TleaveAplTx leave, string action, decimal? oldBalance = null)
    {
        string leaveId = $"{leave.InYear}-{leave.InMailNo}-{leave.InDocSlNo}";
        decimal currentBalance = _h.Leave().Balance(leave.EmpNo, leave.LeaveCode);
        
        // Log the action
        Log(
            leaveId,
            "Leaves",
            action + " " + _h.StaffData(_v.Profile.EmpNo).EmpNameA
        );
        
        // Log the balance change
        if (oldBalance.HasValue)
        {
            Log(
                "Balance-" + leave.EmpNo + "-" + leave.LeaveCode,
                "LeaveBalLog",
                $"EmpNo: {leave.EmpNo} - LeaveCode: {leave.LeaveCode}",
                ("الرصيد السابق", oldBalance.Value),
                ("الرصيد الحالي", currentBalance)
            );
        }
        else
        {
            Log(
                "Balance-" + leave.EmpNo + "-" + leave.LeaveCode,
                "LeaveBalLog",
                $"EmpNo: {leave.EmpNo} - LeaveCode: {leave.LeaveCode}",
                ("الرصيد الحالي", currentBalance)
            );
        }
    }
    
    /// <summary>
    /// Validates a leave request with common validation logic
    /// </summary>
    protected (bool IsValid, List<string> Errors) ValidateLeaveRequest(TleaveAplTx leaveRequest)
    {
        var errors = new List<string>();
        
        // Validate manager is valid for this employee
        var managers = _h.Managers(leaveRequest.EmpNo);
        bool validManager = managers.Any(item => item.EmpNo == leaveRequest.SignAuthCode);
        
        if (!validManager)
        {
            errors.Add(_("Invalid department manager"));
        }
        
        // Validate leave code exists
        var leaveCode = _db.TleaveCodes.FirstOrDefault(l => l.LeaveCode == leaveRequest.LeaveCode);
        if (leaveCode == null)
        {
            errors.Add(_("Invalid leave type"));
        }

        if(leaveRequest.LeaveCode != 1 && leaveRequest.LeaveCode != 21){
            if(leaveRequest.FileGuid == null){
                errors.Add(_("يجب إرفاق الوثائق "));
            }
        }
        
        // Additional validations can be added here
        
        return (errors.Count == 0, errors);
    }
    
    /// <summary>
    /// Processes leave approval for any approval level
    /// </summary>
    protected IActionResult ApproveLeave(TleaveAplTx leave, string approverType, int newStatus, string redirectUrl)
    {
        decimal oldBalance = _h.Leave().Balance(leave.EmpNo, leave.LeaveCode);
        
        // Update leave status based on approval level
        leave.ReqStat = newStatus;
        leave.SignDate = DateTime.Now;
        
        if (approverType == "Manager")
        {
            leave.ManagerApproval = 1;
            leave.SignRem = "-";
        }
        else if (approverType == "DG")
        {
            leave.ApprovalDate = DateTime.Now;
            leave.ApprovalRem = "-";
            leave.SignByCode = _v.Profile.EmpNo.Value;
            leave.AuditDate = DateTime.Now;
            leave.AuditRem = "-";
            leave.AuditStat = 1;
            
            // Apply leave deduction for final approval
            _h.Leave().LeaveDeduction(leave);
        }
        else if (approverType == "Department")
        {
            leave.SignByCode = _v.Profile.EmpNo.Value;
            leave.AuditDate = DateTime.Now;
            leave.AuditStat = 1;
            leave.AuditRem = "-";
            leave.AuditUserId = _v.Profile.EmpNo.Value.ToString();
            leave.AuditTimeStamp = DateTime.Now;
            leave.ApprovalDate = DateTime.Now;
            leave.ApprovalRem = "-";
            leave.UserId = _v.Profile.EmpNo.Value.ToString();
            
            // Apply leave deduction for final approval
            _h.Leave().LeaveDeduction(leave);
        }
        
        _db.Update(leave);
        _db.SaveChanges();
        
        // Log the approval action
        string actionMessage = $"اعتماد من {(approverType == "Manager" ? "المسؤول المباشر" : approverType == "DG" ? "المدير العام" : "القسم")}";
        LogLeaveAction(leave, actionMessage, newStatus >= 6 ? oldBalance : null);
        
        // Notify the employee
        string notificationMessage = $"تم اعتماد اجازتك من قبل {(approverType == "Manager" ? "المسؤول المباشر" : approverType == "DG" ? "المدير العام" : "القسم")}";
        _h.Notify().Create(empNo: leave.EmpNo, title: notificationMessage);
        
        return Redirect(redirectUrl);
    }
    
    /// <summary>
    /// Processes leave rejection for any approval level
    /// </summary>
    protected IActionResult RejectLeave(TleaveAplTx leave, string rejecterType, string redirectUrl)
    {
        // Set rejection status
        leave.ReqStat = 3;  // Rejection status
        
        if (rejecterType == "Manager")
        {
            leave.ManagerApproval = 2;  // Rejected
            leave.SignRem = "مرفوض من المسؤول المباشر";
        }
        else if (rejecterType == "DG")
        {
            leave.ApprovalRem = "مرفوض من المدير العام";
        }
        else if (rejecterType == "Department")
        {
            leave.ApprovalRem = "مرفوض من القسم";
        }
        
        leave.SignByCode = _v.Profile.EmpNo.Value;
        leave.SignDate = DateTime.Now;
        
        _db.Update(leave);
        _db.SaveChanges();
        
        // Log the rejection action
        string actionMessage = $"رفض من {(rejecterType == "Manager" ? "المسؤول المباشر" : rejecterType == "DG" ? "المدير العام" : "القسم")}";
        LogLeaveAction(leave, actionMessage);
        
        // Notify the employee
        string notificationMessage = $"تم رفض اجازتك من قبل {(rejecterType == "Manager" ? "المسؤول المباشر" : rejecterType == "DG" ? "المدير العام" : "القسم")}";
        _h.Notify().Create(empNo: leave.EmpNo, title: notificationMessage);
        
        return Redirect(redirectUrl);
    }
    
    /// <summary>
    /// Creates a leave request with standardized validation and processing
    /// </summary>
    protected ActionResult CreateLeaveRequest(TleaveAplTx leaveRequest)
    {
        // Validate the leave request
        var validation = ValidateLeaveRequest(leaveRequest);
        if (!validation.IsValid)
        {
            return Json(new
            {
                success = false,
                message = validation.Errors,
                action = "",
            });
        }
        
        // Validate leave dates using the helper
        var isValid = _h.Leave().ValidateLeave(
            leaveRequest.EmpNo, 
            leaveRequest.LeaveCode, 
            leaveRequest.LeaveStartDate, 
            leaveRequest.LeaveEndDate
        );
        
        if (!isValid.Success)
        {
            return ResponseHelper.Json(success: isValid.Success, message: isValid.Message);
        }
        
        // Get leave code description
        var leaveCode = _db.TleaveCodes.FirstOrDefault(l => l.LeaveCode == leaveRequest.LeaveCode);
        
        // Create the leave based on type
        if (leaveRequest.LeaveCode == 1 || leaveRequest.LeaveCode == 21) // Annual or Emergency leave
        {
            var leaveResult = _h.Leave().Create(
                leaveRequest.EmpNo, 
                leaveRequest.LeaveStartDate, 
                leaveRequest.LeaveEndDate, 
                leaveRequest.SignAuthCode.Value, 
                leaveRequest.LeaveCode
            );
            
            if (!leaveResult.Success)
            {
                return ResponseHelper.Json(success: leaveResult.Success, message: leaveResult.Message);
            }
            
            // Log creation and notify
            Log(
                leaveResult.Data.InYear + "-" + leaveResult.Data.InMailNo + "-" + leaveResult.Data.InDocSlNo,
                "Leaves",
                "اجازة جديدة"
            );
            
            Log(
                leaveResult.Data.InYear + "-" + leaveResult.Data.InMailNo + "-" + leaveResult.Data.InDocSlNo,
                "LeaveBalLog",
                "EmpNo: " + leaveResult.Data.EmpNo + " - LeaveCode: " + leaveResult.Data.LeaveCode,
                ("الرصيد الحالي", _h.Leave().Balance(leaveRequest.EmpNo, leaveRequest.LeaveCode))
            );
            
            _h.Notify().Create(
                empNo: leaveRequest.EmpNo, 
                title: "اجازة جديدة"
            );
            
            return ResponseHelper.Json(success: leaveResult.Success, message: leaveResult.Message, action: "reload");
        }
        else // Other leave types
        {
            var leave = _h.Leave().Create(
                leaveRequest.EmpNo, 
                leaveRequest.LeaveStartDate, 
                leaveRequest.LeaveEndDate, 
                leaveRequest.SignAuthCode.Value, 
                leaveRequest.LeaveCode,
                leaveRequest.FileGuid
            );
            
            if (!leave.Success)
            {
                return ResponseHelper.Json(success: leave.Success, message: leave.Message);
            }
            
            // Update leave code and set initial status for HR workflow
            var createdLeave = _db.TleaveAplTxs.FirstOrDefault(l => 
                l.InYear == leave.Data.InYear && 
                l.InMailNo == leave.Data.InMailNo && 
                l.InDocSlNo == leave.Data.InDocSlNo);
                
            if (createdLeave != null)
            {
                createdLeave.LeaveCode = leaveRequest.LeaveCode;
                createdLeave.ReqStat = 4; // New status code for HR Department workflow
                _db.SaveChanges();
            }
            
            // Log creation and notify
            Log(
                leave.Data.InYear + "-" + leave.Data.InMailNo + "-" + leave.Data.InDocSlNo,
                "Leaves",
                $"اجازة جديدة - {leaveCode.LeaveDespA}"
            );
            
            Log(
                leave.Data.InYear + "-" + leave.Data.InMailNo + "-" + leave.Data.InDocSlNo,
                "LeaveBalLog",
                "EmpNo: " + leave.Data.EmpNo + " - LeaveCode: " + leave.Data.LeaveCode,
                ("الرصيد الحالي", _h.Leave().Balance(leaveRequest.EmpNo, leaveRequest.LeaveCode))
            );
            
            _h.Notify().Create(
                empNo: leaveRequest.EmpNo, 
                title: $"اجازة جديدة - {leaveCode.LeaveDespA}"
            );
            
            return ResponseHelper.Json(success: leave.Success, message: leave.Message, action: "reload");
        }
    }
    
    #endregion
    
    public IActionResult Index()
    {
        return View(_v);
    }


    [HttpGet("Department")]
    [Can("leaves-department")]
    public IActionResult DepartmentLeaves()
    {

        _v.Page.Title = "الاجازات";
        _v.Page.Active = "hr_leaves";


        _v.Page.Filter.DateTo = DateTime.Today.AddMonths(6);
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        _v.VempDtls = _db.VempDtls.ToList();
        
        // Get all leave codes for the filter dropdown
        _v.LeaveCodes = _db.TleaveCodes.OrderBy(l => l.LeaveDespA).ToList();

        return View(_v);

    }

    [HttpPost("Department/Datatable")]
    
    public IActionResult DepartmentLeavesDatatable([FromForm] DataTableHelper datatable, int? leaveCode = null, DateTime? fromDate = null, DateTime? toDate = null)
    {
        // We'll validate permissions in the body of the method instead of using 403 response
        if (!Can("leaves-department"))
        {
            // Return empty dataset instead of 403 to avoid authentication scheme error
            return Json(new {
                draw = datatable.Draw,
                recordsTotal = 0,
                recordsFiltered = 0,
                data = new List<object>()
            });
        }

        // Set default date range if not provided
        _v.Page.Filter.DateTo = toDate ?? DateTime.Today.AddMonths(6);
        _v.Page.Filter.DateFrom = fromDate ?? DateTime.Today.AddMonths(-6);

        // Build query with base filters
        var query = _db.TleaveAplTxs
            .Include(a => a.TleaveCode)
            .Where(m =>
                m.LeaveStartDate >= _v.Page.Filter.DateFrom
                && m.LeaveStartDate <= _v.Page.Filter.DateTo
                && m.CancelFlag != 1
                && (m.ReqStat == 6 || m.ReqStat == 4 || m.ReqStat == 1)
            );

        // Apply leave code filter if provided
        if (leaveCode.HasValue && leaveCode > 0)
        {
            if (leaveCode == 1)
            {
                query = query.Where(m => m.LeaveCode == 1);
            }
            if(leaveCode == 2)
            {
                query = query.Where(m => m.LeaveCode == 3 || m.LeaveCode == 4 || m.LeaveCode == 5 || m.LeaveCode == 53 || m.LeaveCode == 31 || m.LeaveCode == 32 || m.LeaveCode == 37);
            }
        }

        // Apply search filter from DataTables
        if (!string.IsNullOrEmpty(datatable.Search.Value))
        {
            query = query.Where(f =>
                f.EmpNo.ToString().Contains(datatable.Search.Value) ||
             
                f.TleaveCode.LeaveDespA.Contains(datatable.Search.Value) ||
                f.LeaveStartDate.ToString().Contains(datatable.Search.Value) ||
                f.LeaveEndDate.ToString().Contains(datatable.Search.Value)
            );
        }

        // Get total count before any paging
        var total = query.Count();

        // Apply sorting
        if (datatable.Order != null && datatable.Order.Count > 0)
        {
            switch (datatable.Order[0].Column)
            {
                case 0: // No (ID)
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(r => r.InYear).ThenBy(r => r.InMailNo).ThenBy(r => r.InDocSlNo)
                        : query.OrderByDescending(r => r.InYear).ThenByDescending(r => r.InMailNo).ThenByDescending(r => r.InDocSlNo);
                    break;
                case 1: // Staff
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(r => r.EmpNo) 
                        : query.OrderByDescending(r => r.EmpNo);
                    break;
                case 3: // Leave Type
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(r => r.TleaveCode.LeaveDespA) 
                        : query.OrderByDescending(r => r.TleaveCode.LeaveDespA);
                    break;
                case 4: // Start Date
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(r => r.LeaveStartDate) 
                        : query.OrderByDescending(r => r.LeaveStartDate);
                    break;
                case 5: // End Date
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(r => r.LeaveEndDate) 
                        : query.OrderByDescending(r => r.LeaveEndDate);
                    break;
                case 6: // No of Days (calculated)
                    // Skip database sorting for date calculation in Oracle
                    query = query.OrderByDescending(r => r.LeaveStartDate); // Default ordering
                    break;
                case 7: // Status
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(r => r.ReqStat) 
                        : query.OrderByDescending(r => r.ReqStat);
                    break;
                default:
                    query = query.OrderByDescending(r => r.LeaveStartDate);
                    break;
            }
        }
        else
        {
            // Default sort by start date (newest first)
            query = query.OrderByDescending(r => r.LeaveStartDate);
        }

        // Apply paging
        var data = query.Skip(datatable.Start).Take(datatable.Length).ToList();

        // Load employee department info once
        var empDepartments = _db.VempDtls
            .Where(e => e.DeptCode.HasValue)
            .Select(e => new { e.EmpNo, e.DeptCode })
            .ToDictionary(e => e.EmpNo, e => e.DeptCode.Value);

        // Get department name data first
        var deptNames = _db.TDeptCode
            .GroupBy(d => d.DeptCode)  // Group by department code to handle duplicates
            .ToDictionary(
                g => g.Key,            // Use the department code as key
                g => g.First().DeptDespA  // Take the first department name for any duplicates
            );

        // Join and group in a single operation - with error checking
        var allDepartments = new List<DepartmentLeaveData>();
        
        try 
        {
            // Group leaves by department
            var deptGroups = data
                .Where(l => empDepartments.ContainsKey(l.EmpNo))
                .GroupBy(l => empDepartments[l.EmpNo])
                .Select(g => new { 
                    DeptCode = g.Key,
                    Count = g.Count(),
                    Days = g.Sum(l => (l.LeaveEndDate - l.LeaveStartDate).Days + 1)
                })
                .ToList();
                
            // Create department data with proper property names
            foreach (var dept in deptGroups)
            {
                if (deptNames.ContainsKey(dept.DeptCode))
                {
                    allDepartments.Add(new DepartmentLeaveData {
                        DeptName = deptNames[dept.DeptCode],
                        Count = dept.Count,
                        Days = dept.Days
                    });
                }
            }
            
            // Sort by days descending
            allDepartments = allDepartments
                .OrderByDescending(d => d.Days)
                .ToList();
        }
        catch (Exception ex)
        {
            // Log error but continue with empty list
            System.Diagnostics.Debug.WriteLine("Error processing department data: " + ex.Message);
        }
        
        // Get top 10 departments for the chart
        var topDepartments = allDepartments.Take(10).ToList();
        
        ViewBag.DepartmentLabels = string.Join(",", topDepartments.Select(x => $"'{x.DeptName}'"));
        ViewBag.DepartmentCounts = string.Join(",", topDepartments.Select(x => x.Count));
        ViewBag.DepartmentDays = string.Join(",", topDepartments.Select(x => x.Days));
        ViewBag.DepartmentColors = GenerateColors(topDepartments.Count);
        ViewBag.AllDepartments = allDepartments;
        
        // Transform data for DataTable
        var table = data.Select(leave => new
        {
            id = "<a href='/Leaves/Department/Update/" + $"{leave.InYear}-{leave.InMailNo}-{leave.InDocSlNo}" + "'>" + $"{leave.InYear}-{leave.InMailNo}-{leave.InDocSlNo}" + "</a>",
            empNo = leave.EmpNo,
            empName = _h.StaffData(leave.EmpNo).EmpNameA,
            leaveType = leave.TleaveCode.LeaveDespA,
            startDate = _v._d(leave.LeaveStartDate),
            endDate = _v._d(leave.LeaveEndDate),
            days = (leave.LeaveEndDate.AddDays(1) - leave.LeaveStartDate).TotalDays,
            status = _v.renderLeaveSatus(leave.ReqStat),
            sortStartDate = leave.LeaveStartDate.ToString("yyyyMMdd"), // For proper date sorting
            sortEndDate = leave.LeaveEndDate.ToString("yyyyMMdd"),      // For proper date sorting
            sortDays = (leave.LeaveEndDate.AddDays(1) - leave.LeaveStartDate).TotalDays // For client-side sorting
        }).ToList();

        // Prepare DataTables response format
        var response = new
        {
            datatable.Draw,
            recordsTotal = total,
            recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
            data = table
        };

        return Json(response);
    }

    [HttpPost("Department/Create")]
    [Can("leaves-depaardepartment|leaves-admin")]
    public IActionResult DepartmentLeaves(TleaveAplTx post)
    {
        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }
        
        return CreateLeaveRequest(post);
    }

    [HttpGet("Department/Update/{Id}")]
    public IActionResult DepartmentLeaveUpdate(string Id)
    {
        _v.Page.Active = "hr_leaves";
        _v.Page.Reload = true;
        _v.Page.Back = "/Leaves/Department";

        _v.Page.Title = "الاجازات";

        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);


        var empLeave = _db.TleaveAplTxs
            .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.CancelFlag == 0
            )
            .Include(l => l.TleaveCode)
            .FirstOrDefault();

        if (empLeave == null)
            return StatusCode(404);


        _v.TleaveAplTx = empLeave;

        return View(_v);
    }

    [HttpGet("Department/Send/{Id}")]
    public IActionResult DepartmentLeaveSend(string Id)
    {

        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        var leave = _db.TleaveAplTxs.FirstOrDefault(l => 
        l.InYear == InYear && 
        l.InMailNo == InMailNo && 
        l.InDocSlNo == InDocSlNo &&
        l.ReqStat == 4
        );
        
        if (leave == null)
            return StatusCode(404);

        return ApproveLeave(leave, "Department", 6, "/Leaves/Department/Update/" + Id + "?success=true");

    }

    [HttpGet("Department/Cancel/{Id}")]
    [Can("leaves-depaardepartment|leaves-admin")]
    public IActionResult DepartmentLeaveCancel(string Id)
    {
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Find leave request in manager-approved state
        var req = _db.TleaveAplTxs
            .Where(b => 
                b.CancelFlag == 0 && 
                b.ReqStat == 4 &&
                b.InYear == InYear && 
                b.InMailNo == InMailNo && 
                b.InDocSlNo == InDocSlNo
            )
            .FirstOrDefault();

        if (req == null)
            return StatusCode(404);

        return RejectLeave(req, "Department", "/Leaves/Department/Update/" + Id + "?success=true");

    }



    [HttpGet("GetEmpBalance")]
    public IActionResult GetEmpBalance(int empNo)
    {

        if (empNo == 0)
            return StatusCode(404);

        return ResponseHelper.Json(data: _v.GetEmpLeaveCodeBals(empNo), success:true);
    }

    

    [HttpGet("Report")]
    [Can("leaves-department|leaves-admin")]
    public IActionResult Report(int? year = null, int? month = null, int? leaveCode = null, int Dg = 0, int Dept = 0)
    {
        _v.Page.Title = "تقرير الإجازات";
        _v.Page.Active = "hr_leaves";
        _v.Page.Reload = true;
        
        // Set default year and month if not provided
        int currentYear = DateTime.Now.Year;
        int currentMonth = DateTime.Now.Month;
        
        year = year ?? currentYear;
        month = month ?? 0; // 0 means all months
        
        // Prepare date ranges for filtering
        DateTime startDate = month > 0 
            ? new DateTime(year.Value, month.Value, 1) 
            : new DateTime(year.Value, 1, 1);
        
        DateTime endDate = month > 0
            ? new DateTime(year.Value, month.Value, DateTime.DaysInMonth(year.Value, month.Value))
            : new DateTime(year.Value, 12, 31);
        
        // Store filter values in ViewBag for form persistence
        ViewBag.Year = year;
        ViewBag.Month = month;
        ViewBag.LeaveCode = leaveCode;
        ViewBag.Dg = Dg;
        ViewBag.Dept = Dept;
        
        // Get all leave codes for filter dropdown
        ViewBag.LeaveCodes = _db.TleaveCodes.OrderBy(l => l.LeaveDespA).ToList();
        
        // Get DGs for filter dropdown
        ViewBag.DGs = _db.TdgCodes.OrderBy(d => d.DgDespA).ToList();
        
        // Get departments for filter dropdown (filtered by DG if selected)
        ViewBag.Departments = Dg > 0 
            ? _db.TDeptCode.Where(d => d.DgCode == Dg).OrderBy(d => d.DeptDespA).ToList()
            : _db.TDeptCode.OrderBy(d => d.DeptDespA).ToList();
        
        // Base query for leave requests
        var query = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .Where(l => 
                l.LeaveStartDate >= startDate && 
                l.LeaveStartDate <= endDate &&
                l.CancelFlag != 1);
        
        // Apply additional filters if provided
        if (leaveCode.HasValue && leaveCode > 0)
        {
            query = query.Where(l => l.LeaveCode == leaveCode.Value);
        }
        
        // Apply hierarchical organizational filtering following the established pattern
        if (Dept > 0)
        {
            // Filter by specific department
            var empInDept = _db.VempDtls.Where(e => e.DeptCode == Dept && e.EmpNo > 1).Select(e => e.EmpNo).ToList();
            query = query.Where(l => empInDept.Contains(l.EmpNo));
        }
        else if (Dg > 0)
        {
            // Filter by DG (all departments within the DG)
            var empInDg = _db.VempDtls.Where(e => e.DgCode == Dg && e.EmpNo > 1).Select(e => e.EmpNo).ToList();
            query = query.Where(l => empInDg.Contains(l.EmpNo));
        }
        
        // Execute query
        var leaves = query.ToList();
        
        // Prepare data for charts
        
        // 1. Leave type distribution
        var leaveTypeData = leaves
            .GroupBy(l => l.LeaveCode)
            .Select(g => new {
                LeaveCode = g.Key,
                LeaveName = g.First().TleaveCode.LeaveDespA,
                Count = g.Count(),
                Days = g.Sum(l => (l.LeaveEndDate - l.LeaveStartDate).Days + 1)
            })
            .OrderByDescending(x => x.Days) // Order by days instead of count
            .ToList();
        
        ViewBag.LeaveTypeLabels = string.Join(",", leaveTypeData.Select(x => $"'{x.LeaveName}'"));
        ViewBag.LeaveTypeCounts = string.Join(",", leaveTypeData.Select(x => x.Count));
        ViewBag.LeaveTypeDays = string.Join(",", leaveTypeData.Select(x => x.Days));
        ViewBag.LeaveTypeColors = GenerateColors(leaveTypeData.Count);
        
        // 2. Leave status distribution
        var statusData = leaves
            .GroupBy(l => l.ReqStat)
            .Select(g => new {
                Status = g.Key,
                StatusName = GetStatusName(g.Key ?? 0),
                Count = g.Count(),
                Days = g.Sum(l => (l.LeaveEndDate - l.LeaveStartDate).Days + 1)
            })
            .OrderByDescending(x => x.Count)
            .ToList();
        
        ViewBag.StatusLabels = string.Join(",", statusData.Select(x => $"'{x.StatusName}'"));
        ViewBag.StatusCounts = string.Join(",", statusData.Select(x => x.Count));
        ViewBag.StatusDays = string.Join(",", statusData.Select(x => x.Days));
        ViewBag.StatusColors = string.Join(",", new[] { 
            "'#ffc107'", // Warning - Pending
            "'#28a745'", // Success - Approved
            "'#dc3545'", // Danger - Rejected
            "'#17a2b8'", // Info - Generated
            "'#007bff'"  // Primary - Finalized
        });
        
        // 3. Monthly trend data (if annual view)
        if (month == 0)
        {
            var monthlyData = new List<int>();
            for (int i = 1; i <= 12; i++)
            {
                int count = leaves.Count(l => l.LeaveStartDate.Month == i);
                monthlyData.Add(count);
            }
            ViewBag.MonthlyData = string.Join(",", monthlyData);
        }
        
        // 4. Department distribution
        // Load employee department info once
        var empDepartments = _db.VempDtls
            .Where(e => e.DeptCode.HasValue)
            .Select(e => new { e.EmpNo, e.DeptCode })
            .ToDictionary(e => e.EmpNo, e => e.DeptCode.Value);

        // Get department name data first
        var deptNames = _db.TDeptCode
            .GroupBy(d => d.DeptCode)  // Group by department code to handle duplicates
            .ToDictionary(
                g => g.Key,            // Use the department code as key
                g => g.First().DeptDespA  // Take the first department name for any duplicates
            );

        // Join and group in a single operation - with error checking
        var allDepartments = new List<DepartmentLeaveData>();
        
        try 
        {
            // Group leaves by department
            var deptGroups = leaves
                .Where(l => empDepartments.ContainsKey(l.EmpNo))
                .GroupBy(l => empDepartments[l.EmpNo])
                .Select(g => new { 
                    DeptCode = g.Key,
                    Count = g.Count(),
                    Days = g.Sum(l => (l.LeaveEndDate - l.LeaveStartDate).Days + 1)
                })
                .ToList();
                
            // Create department data with proper property names
            foreach (var dept in deptGroups)
            {
                if (deptNames.ContainsKey(dept.DeptCode))
                {
                    allDepartments.Add(new DepartmentLeaveData {
                        DeptName = deptNames[dept.DeptCode],
                        Count = dept.Count,
                        Days = dept.Days
                    });
                }
            }
            
            // Sort by days descending
            allDepartments = allDepartments
                .OrderByDescending(d => d.Days)
                .ToList();
        }
        catch (Exception ex)
        {
            // Log error but continue with empty list
            System.Diagnostics.Debug.WriteLine("Error processing department data: " + ex.Message);
        }
        
        // Get top 10 departments for the chart
        var topDepartments = allDepartments.Take(10).ToList();
        
        ViewBag.DepartmentLabels = string.Join(",", topDepartments.Select(x => $"'{x.DeptName}'"));
        ViewBag.DepartmentCounts = string.Join(",", topDepartments.Select(x => x.Count));
        ViewBag.DepartmentDays = string.Join(",", topDepartments.Select(x => x.Days));
        ViewBag.DepartmentColors = GenerateColors(topDepartments.Count);
        ViewBag.AllDepartments = allDepartments;
        
        // 5. Summary statistics
        ViewBag.TotalLeaves = leaves.Count;
        ViewBag.UniqueEmployees = leaves.Select(l => l.EmpNo).Distinct().Count();
        ViewBag.AverageDuration = leaves.Count > 0 
            ? Math.Round(leaves.Average(l => (l.LeaveEndDate - l.LeaveStartDate).Days + 1), 1) 
            : 0;
        ViewBag.LongestLeave = leaves.Count > 0 
            ? leaves.Max(l => (l.LeaveEndDate - l.LeaveStartDate).Days + 1) 
            : 0;
        
        return View(_v);
    }

    // Helper method to get status name
    private string GetStatusName(int statusCode)
    {
        switch (statusCode)
        {
            case 0: return "معلق";
            case 1: return "موافق عليه";
            case 3: return "مرفوض";
            case 4: return "جاهز";
            case 6: return "مكتمل";
            default: return "غير معروف";
        }
    }

    // Helper method to generate random chart colors
    private string GenerateColors(int count)
    {
        // Predefined color palette
        var palette = new List<string>
        {
            "#4e73df", "#1cc88a", "#36b9cc", "#f6c23e", "#e74a3b", 
            "#5a5c69", "#858796", "#6610f2", "#6f42c1", "#e83e8c", 
            "#fd7e14", "#20c9a6", "#27a844", "#ff6b6b", "#6f42c1"
        };
        
        var colors = new List<string>();
        
        // Use palette colors first, then generate random colors if needed
        for (int i = 0; i < count; i++)
        {
            if (i < palette.Count)
            {
                colors.Add($"'{palette[i]}'");
            }
            else
            {
                var r = new Random();
                string randomColor = $"'rgb({r.Next(0, 255)}, {r.Next(0, 255)}, {r.Next(0, 255)})'";
                colors.Add(randomColor);
            }
        }
        
        return string.Join(",", colors);
    }

  
    #region Department Leave Return Management
    
    [HttpGet("Department/Return")]
    [Can("leaves-department")]
    public IActionResult DepartmentLeaveReturn(DateTime? from, DateTime? to)
    {
        _v.Page.Title = "إدارة طلبات قطع الإجازات";
        _v.Page.Active = "hr_leaves";
        _v.Page.Reload = true;

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        if (from.HasValue)
        {
            _v.Page.Filter.DateFrom = from.Value;
        }
        if (to.HasValue)
        {
            _v.Page.Filter.DateTo = to.Value;
        }

        // Get all leave return requests
        var leaveReturns = _db.TleaveReturnTxs
            .Include(r => r.TleaveCode)
            .Where(r => 
                r.CancelFlag != 1 && 
                r.TimeStamp >= _v.Page.Filter.DateFrom && 
                r.TimeStamp <= _v.Page.Filter.DateTo)
            .OrderByDescending(r => r.TimeStamp)
            .ToList();

        _v.TleaveReturnTxs = leaveReturns;

        // Get all leave codes for the dropdown
        _v.LeaveCodes = _db.TleaveCodes.OrderBy(l => l.LeaveDespA).ToList();

        return View(_v);
    }

    [HttpGet("Department/Return/{Id}")]
    [Can("leaves-department")]
    public IActionResult DepartmentLeaveReturnUpdate(string Id)
    {
        _v.Page.Title = "تفاصيل طلب قطع الإجازة";
        _v.Page.Active = "hr_leaves";
        _v.Page.Back = "/Leaves/Department/Return";
        _v.Page.Reload = true;

        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the leave return details
        var leaveReturn = _db.TleaveReturnTxs
            .Include(r => r.TleaveCode)
            .FirstOrDefault(r => 
                r.InYear == InYear && 
                r.InMailNo == InMailNo && 
                r.InDocSlNo == InDocSlNo &&
                r.CancelFlag != 1);

        if (leaveReturn == null)
            return StatusCode(404);

        _v.TleaveReturnTx = leaveReturn;

        // Get the original leave request details
        var originalLeave = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .FirstOrDefault(l => 
                l.InYear == leaveReturn.OrderYear && 
                l.InDeptInd == leaveReturn.OrderDeptInd && 
                l.InDocSlNo == leaveReturn.OrderSlNo);

        _v.TleaveAplTx = originalLeave;

        return View(_v);
    }

  

    [HttpGet("Department/Return/Approve/{Id}")]
    [Can("leaves-department")]
    public IActionResult DepartmentLeaveReturnApprove(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the leave return to approve
        var leaveReturn = _db.TleaveReturnTxs
            .FirstOrDefault(r => 
                r.InYear == InYear && 
                r.InMailNo == InMailNo && 
                r.InDocSlNo == InDocSlNo &&
                r.ReqStat == 0 &&  // Must be in pending status
                r.CancelFlag != 1);

        if (leaveReturn == null)
            return StatusCode(404);

        // Calculate days to be credited back for validation
        var daysToCredit = leaveReturn.LateOrEarlyDays*-1;

        if (daysToCredit <= 0)
        {
            return Content($"<script>window.location.href='/Leaves/Department/Return/{Id}?error={Uri.EscapeDataString("لا توجد أيام لإضافتها للرصيد")}';</script>", "text/html");
        }

        // Update leave return status to approved by department (1)
        leaveReturn.ReqStat = 1;  // Department approved, awaiting audit
        leaveReturn.ApprovalDate = DateTime.Now;
        leaveReturn.ApprovalRem = "تمت الموافقة من قبل قسم ملفات الخدمة";
        _db.SaveChanges();

        // Log the approval action
        Log(
            $"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}",
            "LeaveReturn",
            "موافقة قسم ملفات الخدمة على قطع الإجازة - في انتظار المراجعة"
        );

        // Notify the employee
        _h.Notify().Create(
            empNo: leaveReturn.EmpNo, 
            title: "تمت الموافقة على طلب قطع الإجازة من قبل قسم ملفات الخدمة - في انتظار المراجعة"
        );

        return Content($"<script>window.location.href='/Leaves/Department/Return/{Id}?success={Uri.EscapeDataString("تمت الموافقة بنجاح - في انتظار المراجعة")}';</script>", "text/html");
    }

    [HttpGet("Department/Return/Reject/{Id}")]
    [Can("leaves-department")]
    public IActionResult DepartmentLeaveReturnReject(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the leave return to reject
        var leaveReturn = _db.TleaveReturnTxs
            .FirstOrDefault(r => 
                r.InYear == InYear && 
                r.InMailNo == InMailNo && 
                r.InDocSlNo == InDocSlNo &&
                r.ReqStat == 0 &&  // Must be in pending status
                r.CancelFlag != 1);

        if (leaveReturn == null)
            return StatusCode(404);

        // Update leave return status to rejected (3)
        leaveReturn.ReqStat = 3;
        leaveReturn.ApprovalDate = DateTime.Now;
        leaveReturn.ApprovalRem = "تم رفض الطلب من قبل قسم ملفات الخدمة";
        _db.SaveChanges();

        // Log the rejection action
        Log(
            $"{leaveReturn.InYear}-{leaveReturn.InMailNo}-{leaveReturn.InDocSlNo}",
            "LeaveReturn",
            "رفض قسم ملفات الخدمة لطلب قطع الإجازة"
        );

        // Notify the employee
        _h.Notify().Create(
            empNo: leaveReturn.EmpNo, 
            title: "تم رفض طلب قطع الإجازة من قبل قسم ملفات الخدمة"
        );

        return Content($"<script>window.location.href='/Leaves/Department/Return/{Id}?success={Uri.EscapeDataString("تم رفض الطلب")}';</script>", "text/html");
    }


    #endregion

    #region Leave Balance Administration
    
    [HttpGet("Admin/Balance")]
    [Can("leaves-admin")]
    public IActionResult AdminLeaveBalance()
    {
        _v.Page.Title = "إدارة أرصدة الإجازات";
        _v.Page.Active = "hr_leaves";
        _v.Page.Reload = true;
        
        // Get all employees for the dropdown
        _v.VempDtls = _db.VempDtls.OrderBy(e => e.EmpNameA).ToList();
        
        // Get all leave codes for the dropdown
        _v.LeaveCodes = _db.TleaveCodes.OrderBy(l => l.LeaveDespA).ToList();
        
        return View(_v);
    }

    [HttpPost("Admin/Balance/Update")]
    [Can("leaves-admin")]
    public IActionResult UpdateEmployeeLeaveBalance(int empNo, int leaveCode, decimal newBalance, string remarks = "")
    {
        if (empNo == 0 || leaveCode == 0)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "معاملات غير صحيحة" });
        }

        try
        {
            // Get current balance for logging
            decimal oldBalance = _h.Leave().Balance(empNo, leaveCode);
            
            // Find or create leave balance record
            var leaveBal = _db.TleaveBals
                .FirstOrDefault(l => 
                    l.UnitCode == 1 &&
                    l.EmpNo == empNo && 
                    l.LeaveCode == leaveCode);

            if (leaveBal != null)
            {
                // Log current balance before changes
                _h.Leave().LogLeaveBalance(leaveBal);
                
                // Update existing balance
                leaveBal.NoLeaveBalDays = newBalance;
                leaveBal.RefDate = DateTime.Now;
                leaveBal.UserId = _v.Profile.EmpNo.Value.ToString();
                leaveBal.TimeStamp = DateTime.Now;
                
                _db.Update(leaveBal);
            }
            else
            {
                // Create new balance record
                leaveBal = new TleaveBal
                {
                    UnitCode = 1,
                    EmpNo = (short)empNo,
                    LeaveCode = (byte)leaveCode,
                    RefDate = DateTime.Now,
                    NoLeaveBalDays = newBalance,
                    UserId = _v.Profile.EmpNo.Value.ToString(),
                    TimeStamp = DateTime.Now
                };
                
                _db.TleaveBals.Add(leaveBal);
            }

            _db.SaveChanges();
            
            // Log the updated balance
            _h.Leave().LogLeaveBalance(leaveBal);
            
            // Log the admin action
            Log(
                $"ADMIN-{empNo}-{leaveCode}",
                "LeaveBalAdmin",
                $"تحديث رصيد إجازة مباشر - الموظف: {empNo} - نوع الإجازة: {leaveCode}"
            );
            
            // Log the balance change
            Log(
                $"Balance-{empNo}-{leaveCode}",
                "LeaveBalLog",
                $"EmpNo: {empNo} - LeaveCode: {leaveCode} - Admin: {_v.Profile.EmpNo}",
                ("الرصيد السابق", oldBalance),
                ("الرصيد الجديد", newBalance),
                ("ملاحظات", string.IsNullOrEmpty(remarks) ? "تحديث مباشر من مدير النظام" : remarks)
            );
            
            return ResponseHelper.Json(
                success: true, 
                message: new List<string> { "تم تحديث رصيد الإجازة بنجاح" },
                data: new { newBalance = newBalance }
            );
        }
        catch (Exception ex)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { ex.Message });
        }
    }

    [HttpPost("Admin/Balance/Datatable")]
    [Can("leaves-admin")]
    public IActionResult AdminLeaveBalanceDatatable([FromForm] DataTableHelper datatable, int? leaveCode = null)
    {
        // Build query for leave balances
        var query = _db.TleaveBals
            .Where(b => b.UnitCode == 1);

        // Apply leave code filter if provided
        if (leaveCode.HasValue && leaveCode > 0)
        {
            query = query.Where(b => b.LeaveCode == leaveCode.Value);
        }

        // Apply search filter from DataTables
        if (!string.IsNullOrEmpty(datatable.Search.Value))
        {
            query = query.Where(b =>
                b.EmpNo.ToString().Contains(datatable.Search.Value) ||
                b.NoLeaveBalDays.ToString().Contains(datatable.Search.Value)
            );
        }

        // Get total count before any paging
        var total = query.Count();

        // Apply sorting
        if (datatable.Order != null && datatable.Order.Count > 0)
        {
            switch (datatable.Order[0].Column)
            {
                case 0: // Employee No
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(b => b.EmpNo) 
                        : query.OrderByDescending(b => b.EmpNo);
                    break;
                case 2: // Leave Type
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(b => b.LeaveCode) 
                        : query.OrderByDescending(b => b.LeaveCode);
                    break;
                case 3: // Balance
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(b => b.NoLeaveBalDays) 
                        : query.OrderByDescending(b => b.NoLeaveBalDays);
                    break;
                case 4: // Last Updated
                    query = datatable.Order[0].Dir == "asc" 
                        ? query.OrderBy(b => b.TimeStamp) 
                        : query.OrderByDescending(b => b.TimeStamp);
                    break;
                default:
                    query = query.OrderBy(b => b.EmpNo).ThenBy(b => b.LeaveCode);
                    break;
            }
        }
        else
        {
            // Default sort by employee number and leave code
            query = query.OrderBy(b => b.EmpNo).ThenBy(b => b.LeaveCode);
        }

        // Apply paging
        var data = query.Skip(datatable.Start).Take(datatable.Length).ToList();

        // Transform data for DataTable
        var table = data.Select(balance => new
        {
            empNo = balance.EmpNo,
            empName = _h.StaffData(balance.EmpNo).EmpNameA,
            leaveType = _db.TleaveCodes.FirstOrDefault(l => l.LeaveCode == balance.LeaveCode)?.LeaveDespA ?? "غير معروف",
            balance = balance.NoLeaveBalDays,
            lastUpdated = balance.TimeStamp?.ToString("yyyy-MM-dd") ?? "غير محدد",
            actions = $"<button class='btn btn-sm btn-primary edit-balance' data-empno='{balance.EmpNo}' data-leavecode='{balance.LeaveCode}'>تعديل</button>"
        }).ToList();

        // Prepare DataTables response format
        var response = new
        {
            datatable.Draw,
            recordsTotal = total,
            recordsFiltered = string.IsNullOrEmpty(datatable.Search.Value) ? total : table.Count,
            data = table
        };

        return Json(response);
    }
    
    #endregion

    #region API Endpoints for Cascading Dropdowns
    
    /// <summary>
    /// Get departments by DG code for cascading dropdown
    /// </summary>
    [HttpGet("departments")]
    public IActionResult GetDepartmentsByDg(int dgCode)
    {
        if (dgCode <= 0)
        {
            return Json(new List<object>());
        }

        var departments = _db.TDeptCode
            .Where(d => d.DgCode == dgCode && d.DeptCode >= 0) // Include department 0
            .Select(d => new { d.DeptCode, d.DeptDespA })
            .OrderBy(d => d.DeptDespA)
            .ToList();
        
        return Json(departments);
    }
    
    /// <summary>
    /// Get employees by department code for cascading dropdown
    /// </summary>
    [HttpGet("employees/by-department")]
    public IActionResult GetEmployeesByDepartment(int deptCode)
    {
        if (deptCode < 0)
        {
            return Json(new List<object>());
        }

        var employees = _db.VempDtls
            .Where(e => e.DeptCode == deptCode && e.EmpNo > 1 && e.EmpNo < 1000)
            .Select(e => new { e.EmpNo, e.EmpNameA })
            .OrderBy(e => e.EmpNameA)
            .ToList();
        
        return Json(employees);
    }
    
    /// <summary>
    /// Get employees by DG code for cascading dropdown
    /// </summary>
    [HttpGet("employees/by-dg")]
    public IActionResult GetEmployeesByDg(int dgCode)
    {
        if (dgCode <= 0)
        {
            return Json(new List<object>());
        }

        var employees = _db.VempDtls
            .Where(e => e.DgCode == dgCode && e.EmpNo > 1 && e.EmpNo < 1000)
            .Select(e => new { e.EmpNo, e.EmpNameA })
            .OrderBy(e => e.EmpNameA)
            .ToList();
        
        return Json(employees);
    }
    
    #endregion

}

// Add this class at the end of the controller file before the closing brace
public class DepartmentLeaveData
{
    public string DeptName { get; set; }
    public int Count { get; set; }
    public int Days { get; set; }
}
