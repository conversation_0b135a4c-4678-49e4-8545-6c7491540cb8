﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Leaves.Controllers;

[Area("Leaves")]
[Route("Leaves/Dg")]

[Can(Right.LeavesDG)]
public class DgController : LeavesController
{
    public DgController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

    }
    [HttpGet("")]

    public IActionResult DgLeaves(DateTime? from, DateTime? to)
    {
        _v.Page.Filter.DateTo = DateTime.Today.AddMonths(6);
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        if (from != null)
        {
            _v.Page.Filter.DateFrom = from;
        }

        if (to != null)
        {
            to = to.Value.Date.AddMonths(6);
            _v.Page.Filter.DateTo = to;
        }

        _v.Page.Reload = true;
        _v.Page.Title = "الاجازات";
        _v.Page.Active = "DgApprovals";

        var ReqList = _context.TleaveAplTxs
            .Include(a => a.TleaveCode)
            .Where(m =>
                m.LeaveStartDate >= _v.Page.Filter.DateFrom
                && m.LeaveStartDate <= _v.Page.Filter.DateTo
                && m.CancelFlag == 0
                && m.ReqStat == 1  // Manager approved, awaiting DG
            )
            .OrderByDescending(m => m.LeaveStartDate)
            .ToList();

        _v.TleaveAplTxs = ReqList;

        return View(_v);
    }

    [HttpGet("Update/{Id}")]
    public IActionResult DgLeaveUpdate(string Id)
    {
        _v.Page.Active = "DgApprovals";
        _v.Page.Reload = true;
        _v.Page.Back = "/Leaves/Dg";

        _v.Page.Title = "الاجازات";

        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        var empLeave = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && ro.CancelFlag == 0
            && ro.ReqStat == 1
            )
            .FirstOrDefault();

        if (empLeave == null)
            return StatusCode(404);


        _v.TleaveAplTx = empLeave;

        return View(_v);
    }


    [HttpGet("Send/{Id}")]
    public async Task<IActionResult> DGLeaveApprove(string Id)
    {
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Load the leave request with its leave code information
        var req = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .Where(b => b.CancelFlag == 0 && b.ReqStat == 1 &&
                   b.InYear == InYear && b.InMailNo == InMailNo && b.InDocSlNo == InDocSlNo)
            .FirstOrDefault();

        if (req == null)
            return StatusCode(404);

        return ApproveLeave(req, "DG", 6, "/Leaves/Dg?success=true");
    }



    [HttpGet("Cancel/{Id}")]
    public IActionResult DGLeaveCancel(string Id)
    {
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Find leave request in manager-approved state
        var req = _db.TleaveAplTxs
            .Where(b =>
                b.CancelFlag == 0 &&
                b.ReqStat == 1 &&
                b.InYear == InYear &&
                b.InMailNo == InMailNo &&
                b.InDocSlNo == InDocSlNo
            )
            .FirstOrDefault();

        if (req == null)
            return StatusCode(404);

        return RejectLeave(req, "DG", "/Leaves/Dg?success=true");
    }


}
