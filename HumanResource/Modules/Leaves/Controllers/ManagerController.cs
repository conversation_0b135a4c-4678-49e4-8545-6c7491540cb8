﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using HumanResource.Modules.Leaves.ViewModels;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Leaves.Controllers;

[Area("Leaves")]
[Route("Leaves/Manager")]
[Can(Right.DepartmentManager)]
public class ManagerController : LeavesController
{
    public ManagerController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

    }
    
    // First level approval
    [HttpGet("")]
    public IActionResult ManagerLeaves(DateTime? from, DateTime? to)
    {

        _v.Page.Filter.DateTo = DateTime.Today.AddMonths(6);
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        if (from != null)
        {
            _v.Page.Filter.DateFrom = from;
        }

        if (to != null)
        {
            to = to.Value.Date.AddMonths(6);
            _v.Page.Filter.DateTo = to;
        }


        _v.Page.Reload = true;
        _v.Page.Title = "الاجازات";
        _v.Page.Active = "ManagerApprovals";


        var ReqList = _context.TleaveAplTxs
            .Include(a => a.TleaveCode)
            .Where(m =>
            (m.SignAuthCode == _v.Profile.EmpNo || Can("leaves-admin"))
            && m.LeaveStartDate >= _v.Page.Filter.DateFrom
            && m.LeaveStartDate <= _v.Page.Filter.DateTo
            && m.ReqStat == 0
            && m.CancelFlag == 0)
            .OrderByDescending(m => m.LeaveStartDate)
            .ToList();
        _v.TleaveAplTxs = ReqList.ToList();

        //_v.tleaveAplTxes = ReqList;

        return View(_v);


    }


    [HttpGet("Update/{Id}")]
    public IActionResult ManagerLeaveUpdate(string Id)
    {
        _v.Page.Active = "ManagerApprovals";
        _v.Page.Reload = true;
        _v.Page.Back = "/Leaves/Manager";

        _v.Page.Title = "الاجازات";

        string[] rawcode = Id.Split("-");

        int InYear = int.Parse(rawcode[0]);
        int InMailNo = int.Parse(rawcode[1]);
        int InDocSlNo = int.Parse(rawcode[2]);


        var empLeave = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .Where(ro =>
            ro.InYear == InYear
            && ro.InMailNo == InMailNo
            && ro.InDocSlNo == InDocSlNo
            && (ro.SignAuthCode == _v.Profile.EmpNo || Can("leaves-admin"))
            && ro.ApprovalDate == null
            && ro.CancelFlag == 0
            && ro.ReqStat == 0
            )
            .FirstOrDefault();

        if (empLeave == null)
            return StatusCode(404);


        _v.TleaveAplTx = empLeave;

        return View(_v);
    }


    // Decline leave request
    [HttpGet("Cancel/{Id}")]
    public IActionResult ManagerLeaveCancel(string Id)
    {
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);
        
        // Find the pending leave request
        var request = _db.TleaveAplTxs
            .FirstOrDefault(a =>
            a.InYear == InYear
            && a.InMailNo == InMailNo
            && a.InDocSlNo == InDocSlNo
            && a.ReqStat == 0  // Must be in pending status
            && a.CancelFlag == 0
            && (a.SignAuthCode == _v.Profile.EmpNo || Can("leaves-admin")));

        if (request == null)
            return StatusCode(404);
            
        return RejectLeave(request, "Manager", "/Leaves/Manager/?success=Action completed");
    }

    [HttpGet("Send/{Id}")]
    public IActionResult ManagerApprove(string Id)
    {
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);
        
        // Find the pending leave request
        var requestToUpdate = _context.TleaveAplTxs
            .FirstOrDefault(a =>
            (a.SignAuthCode == _v.Profile.EmpNo || Can("leaves-admin"))
            && a.InYear == InYear
            && a.InMailNo == InMailNo
            && a.InDocSlNo == InDocSlNo
            && a.ReqStat == 0  // Must be in pending status
            && a.CancelFlag == 0);

        if (requestToUpdate == null)
            return StatusCode(404);
            
        return ApproveLeave(requestToUpdate, "Manager", 1, "/Leaves/Manager/?success=true");
    }


}

