﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using HumanResource.Modules.Leaves.ViewModels;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Leaves.Controllers;

namespace HumanResource.Modules.Leaves.Controllers;



/// <summary>
/// Adjustment controller
/// 
/// request 
/// - AdjustmentList
/// - AdjustmentCreate
/// - AdjustmentUpdate
/// - AdjustmentApprove
/// - AdjustmentApproveManager
/// - AdjustmentComplete
/// 

/// </summary>


[Area("Leaves")]
[Route("Leaves/Adjustment")]
public class AdjustmentController : LeavesController
{


    /// <summary>
    /// Adjustment controller
    /// </summary>
    /// <param name="context">The database context</param>
    /// <param name="httpContextAccessor">The HTTP context accessor</param>
    /// <param name="helper">The application helper</param>
    public AdjustmentController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {


    }




    [HttpGet("")]
    [Can("leaves-department")]
    public IActionResult AdjustmentList(DateTime? from, DateTime? to)
    {
        _v.Page.Title = "تعديل رصيد الإجازات";
        _v.Page.Active = "hr_leaves";
        _v.Page.Reload = true;


        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        if (from.HasValue)
        {
            _v.Page.Filter.DateFrom = from.Value;

        }
        if (to.HasValue)
        {
            _v.Page.Filter.DateTo = to.Value;
        }
        // Get all active leave balance adjustment requests
        var adjustments = _db.TleaveBalAdjustmentTxs
            .Include(a => a.TleaveCode)
            .Where(a => a.CancelFlag != 1 && a.TimeStamp >= _v.Page.Filter.DateFrom && a.TimeStamp <= _v.Page.Filter.DateTo && a.TransType == 269)
            .OrderByDescending(a => a.TimeStamp)
            .ToList();

        _v.LeaveBalAdjustments = adjustments;

        // Get all leave codes for the dropdown
        _v.LeaveCodes = _db.TleaveCodes.OrderBy(l => l.LeaveDespA).ToList();

        return View(_v);
    }

    [HttpGet("Create")]
    [Can("leaves-department")]
    public IActionResult AdjustmentCreate()
    {
        _v.Page.Title = "إضافة تعديل رصيد إجازة";
        _v.Page.Active = "hr_leaves";
        _v.Page.Back = "/Leaves/Adjustment";
        _v.Page.Reload = true;

        // Get all employees for the dropdown
        _v.VempDtls = _db.VempDtls.OrderBy(e => e.EmpNameA).ToList();

        // Get all leave codes for the dropdown
        _v.LeaveCodes = _db.TleaveCodes.OrderBy(l => l.LeaveDespA).ToList();

        return View(_v);
    }

    [HttpPost("Create")]
    [Can("leaves-department")]
    public IActionResult AdjustmentCreate(TleaveBalAdjustmentTx adjustment)
    {
        if (!IsValid(ModelState))
        {
            return Json(new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = ""
            });
        }

        // Create mail reference for the adjustment transaction
        var mail = _h.Mail().Create(269);

        // Set initial values for the adjustment
        adjustment.InYear = mail.InYear;
        adjustment.InMailNo = mail.InMailNo;
        adjustment.InDeptInd = 1;
        adjustment.InDocSlNo = (int)mail.LastDocSlNo;
        adjustment.UnitCode = 1;
        adjustment.ReqStat = 0; // Pending
        adjustment.CancelFlag = 0;
        adjustment.TransType = 269;
        adjustment.UserId = _v.Profile.EmpNo.Value.ToString();
        adjustment.TimeStamp = DateTime.Now;
        adjustment.TxnDate = DateTime.Now;

        // Save the adjustment
        _db.TleaveBalAdjustmentTxs.Add(adjustment);
        _db.SaveChanges();

        // Log the action
        Log(
            $"{adjustment.InYear}-{adjustment.InMailNo}-{adjustment.InDocSlNo}",
            "LeaveBalAdjustment",
            "إنشاء تعديل رصيد إجازة"
        );

        // Get current balance for reference
        decimal currentBalance = _h.Leave().Balance(adjustment.EmpNo, adjustment.LeaveCode);

        // Log the balance information
        Log(
            $"{adjustment.InYear}-{adjustment.InMailNo}-{adjustment.InDocSlNo}",
            "LeaveBalLog",
            $"EmpNo: {adjustment.EmpNo} - LeaveCode: {adjustment.LeaveCode}",
            ("الرصيد الحالي", currentBalance)
        );

        return Json(new
        {
            success = true,
            message = new List<string> { "تم إنشاء طلب تعديل رصيد الإجازة بنجاح" },
            action = "location.href='/Leaves/Adjustment'"
        });
    }

    [HttpGet("Update/{Id}")]
    [Can("leaves-department")]
    public IActionResult AdjustmentUpdate(string Id)
    {
        _v.Page.Title = "تفاصيل تعديل رصيد الإجازة";
        _v.Page.Active = "hr_leaves";
        _v.Page.Back = "/Leaves/Adjustment";
        _v.Page.Reload = true;

        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the adjustment details
        var adjustment = _db.TleaveBalAdjustmentTxs
            .Include(a => a.TleaveCode)
            .FirstOrDefault(a =>
                a.InYear == InYear &&
                a.InMailNo == InMailNo &&
                a.InDocSlNo == InDocSlNo &&
                a.OrderType == null &&
                a.TransType == 269 &&
                a.CancelFlag != 1);

        if (adjustment == null)
            return StatusCode(404);

        _v.LeaveBalAdjustment = adjustment;

        // Get current leave balance
        ViewBag.CurrentBalance = _h.Leave().Balance(adjustment.EmpNo, adjustment.LeaveCode);

        // Calculate the new balance after adjustment
        if (adjustment.NoDaysAdjust.HasValue)
        {
            ViewBag.NewBalance = ViewBag.CurrentBalance + adjustment.NoDaysAdjust.Value;
        }

        return View(_v);
    }

    [HttpGet("Approve/{Id}")]
    [Can("leaves-department")]
    public IActionResult AdjustmentApprove(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the adjustment to approve
        var adjustment = _db.TleaveBalAdjustmentTxs
            .FirstOrDefault(a =>
                a.InYear == InYear &&
                a.InMailNo == InMailNo &&
                a.InDocSlNo == InDocSlNo &&
                a.OrderType == null &&
                a.TransType == 269 &&
                a.ReqStat == 0 &&  // Must be in pending status
                a.CancelFlag != 1);

        if (adjustment == null)
            return StatusCode(404);

        // Update adjustment status to Ready (4)
        adjustment.ReqStat = 4;
        adjustment.ApprovalDate = DateTime.Now;
        adjustment.ApprovalRem = "تمت الموافقة من قبل قسم ملفات الخدمة";
        _db.SaveChanges();

        // Log the approval action
        Log(
            $"{adjustment.InYear}-{adjustment.InMailNo}-{adjustment.InDocSlNo}",
            "LeaveBalAdjustment",
            "موافقة قسم ملفات الخدمة"
        );

        return Content($"<script>window.location.href='/Leaves/Adjustment/Update/{Id}?success={Uri.EscapeDataString("تمت الموافقة بنجاح")}';</script>", "text/html");
    }

    [HttpGet("ApproveManager/{Id}")]
    [Can("leaves-department-manager")]
    public IActionResult AdjustmentApproveManager(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the adjustment to approve
        var adjustment = _db.TleaveBalAdjustmentTxs
            .FirstOrDefault(a =>
                a.InYear == InYear &&
                a.InMailNo == InMailNo &&
                a.InDocSlNo == InDocSlNo &&
                a.OrderType == null &&
                a.TransType == 269 &&
                a.ReqStat == 4 &&  // Must be in Ready status
                a.CancelFlag != 1);

        if (adjustment == null)
            return StatusCode(404);

        // Update adjustment status to HR Manager (5)
        adjustment.ReqStat = 5;
        _db.SaveChanges();

        // Log the approval action
        Log(
            $"{adjustment.InYear}-{adjustment.InMailNo}-{adjustment.InDocSlNo}",
            "LeaveBalAdjustment",
            "موافقة رئيس قسم ملفات الخدمة"
        );

        return Content($"<script>window.location.href='/Leaves/Adjustment/Update/{Id}?success={Uri.EscapeDataString("تمت الموافقة بنجاح")}';</script>", "text/html");
    }



    [HttpGet("Complete/{Id}")]
    [Can("leaves-hr-manager")]
    public IActionResult AdjustmentComplete(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the adjustment to complete
        var adjustment = _db.TleaveBalAdjustmentTxs
            .FirstOrDefault(a =>
                a.InYear == InYear &&
                a.InMailNo == InMailNo &&
                a.InDocSlNo == InDocSlNo &&
                a.OrderType == null &&
                a.TransType == 269 &&
                a.ReqStat == 5 &&  // Must be approved by HR Manager
                a.CancelFlag != 1);

        if (adjustment == null)
            return StatusCode(404);

        // Update adjustment status to Completed (6)
        adjustment.ReqStat = 6;
        _db.SaveChanges();

        _h.Leave().CalculateLeaveBalance(adjustment.EmpNo, adjustment.LeaveCode);

        // Get current balance before adjustment
        decimal currentBalance = _h.Leave().Balance(adjustment.EmpNo, adjustment.LeaveCode);

        // Apply the adjustment to the leave balance
        var leaveBal = _db.TleaveBals
            .FirstOrDefault(l =>
                l.EmpNo == adjustment.EmpNo &&
                l.LeaveCode == adjustment.LeaveCode);

        if (leaveBal != null)
        {


            // Log current balance before changes
            _h.Leave().LogLeaveBalance(leaveBal);

            // Apply the adjustment (add or subtract days)
            leaveBal.NoLeaveBalDays += adjustment.NoDaysAdjust ?? 0;

            // Ensure balance doesn't go below zero
            if (leaveBal.NoLeaveBalDays < 0)
                leaveBal.NoLeaveBalDays = 0;

            leaveBal.RefDate = DateTime.Now;

            // Save the updated balance
            _db.SaveChanges();

            // Log the updated balance
            _h.Leave().LogLeaveBalance(leaveBal);
        }
        else
        {
            // If no balance record exists, create one
            leaveBal = new TleaveBal
            {
                UnitCode = 1,
                EmpNo = (short)adjustment.EmpNo,
                LeaveCode = (byte)adjustment.LeaveCode,
                RefDate = DateTime.Now,
                NoLeaveBalDays = adjustment.NoDaysAdjust ?? 0,
                UserId = _v.Profile.EmpNo.Value.ToString(),
                TimeStamp = DateTime.Now
            };

            _db.TleaveBals.Add(leaveBal);
            _db.SaveChanges();

            // Log the new balance
            _h.Leave().LogLeaveBalance(leaveBal);
        }

        // Log the completion action
        Log(
            $"{adjustment.InYear}-{adjustment.InMailNo}-{adjustment.InDocSlNo}",
            "LeaveBalAdjustment",
            "اكتمال التعديل"
        );

        // Log the balance change
        Log(
            $"Balance-{adjustment.EmpNo}-{adjustment.LeaveCode}",
            "LeaveBalLog",
            $"EmpNo: {adjustment.EmpNo} - LeaveCode: {adjustment.LeaveCode}",
            ("الرصيد السابق", currentBalance),
            ("الرصيد الجديد", _h.Leave().Balance(adjustment.EmpNo, adjustment.LeaveCode))
        );

        // Notify the employee
        _h.Notify().Create(
            empNo: adjustment.EmpNo,
            title: "تم تعديل رصيد الإجازة الخاص بك ( " + adjustment.NoDaysAdjust.Value + " )"
        );

        return Content($"<script>window.location.href='/Leaves/Adjustment/Update/{Id}?success={Uri.EscapeDataString("تم اكتمال التعديل بنجاح")}';</script>", "text/html");
    }

    [HttpGet("Reject/{Id}")]
    [Can("leaves-department|leaves-department-manager|leaves-hr-manager")]
    public IActionResult AdjustmentReject(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the adjustment to reject
        var adjustment = _db.TleaveBalAdjustmentTxs
            .FirstOrDefault(a =>
                a.InYear == InYear &&
                a.InMailNo == InMailNo &&
                a.InDocSlNo == InDocSlNo &&
                a.OrderType == null &&
                a.TransType == 269 &&
                a.ReqStat != 6 &&  // Cannot reject completed adjustments
                a.CancelFlag != 1);

        if (adjustment == null)
            return StatusCode(404);

        // Update adjustment status to Rejected (3)
        adjustment.ReqStat = 3;
        adjustment.ApprovalRem = "تم رفض التعديل";
        _db.SaveChanges();

        // Log the rejection action
        Log(
            $"{adjustment.InYear}-{adjustment.InMailNo}-{adjustment.InDocSlNo}",
            "LeaveBalAdjustment",
            "رفض التعديل"
        );

        return Content($"<script>window.location.href='/Leaves/Adjustment/Update/{Id}?success={Uri.EscapeDataString("تم رفض التعديل")}';</script>", "text/html");
    }

    [HttpGet("Delete/{Id}")]
    [Can("leaves-department")]
    public IActionResult AdjustmentDelete(string Id)
    {
        // Parse the composite ID
        var (InYear, InMailNo, InDocSlNo) = ParseLeaveId(Id);

        // Get the adjustment to mark as deleted
        var adjustment = _db.TleaveBalAdjustmentTxs
            .FirstOrDefault(a =>
                a.InYear == InYear &&
                a.InMailNo == InMailNo &&
                a.InDocSlNo == InDocSlNo &&
                a.OrderType == null &&
                a.TransType == 269 &&
                a.ReqStat != 6 &&  // Cannot delete completed adjustments
                a.CancelFlag != 1);

        if (adjustment == null)
            return StatusCode(404);

        // Set cancel flag instead of actual deletion
        adjustment.CancelFlag = 1;
        _db.SaveChanges();

        // Log the deletion action
        Log(
            $"{adjustment.InYear}-{adjustment.InMailNo}-{adjustment.InDocSlNo}",
            "LeaveBalAdjustment",
            "حذف التعديل"
        );

        return Content("<script>window.location.href='/Leaves/Adjustment?success=تم حذف التعديل بنجاح';</script>", "text/html");
    }


}

