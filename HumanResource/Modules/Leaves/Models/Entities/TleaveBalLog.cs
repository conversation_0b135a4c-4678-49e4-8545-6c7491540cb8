using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[PrimaryKey("UnitCode", "EmpNo", "LeaveCode", "RefDate")]
[Table("TLEAVE_BAL_LOG")]
public partial class TleaveBalLog
{
    /// <summary>
    /// Unit code of the employee.
    /// </summary>
    [Key]
    [Column("UNIT_CODE")]
    [Precision(2)]
    public byte UnitCode { get; set; }

    /// <summary>
    /// Employee number whose leave balance is logged.
    /// </summary>
    [Key]
    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    /// <summary>
    /// Code representing the type of leave (e.g., annual, sick).
    /// </summary>
    [Key]
    [Column("LEAVE_CODE")]
    [Precision(2)]
    public byte LeaveCode { get; set; }

    /// <summary>
    /// Reference date when the leave balance was recorded.
    /// </summary>
    [Key]
    [Column("REF_DATE", TypeName = "DATE")]
    public DateTime RefDate { get; set; }

    /// <summary>
    /// Current number of leave days available (6,2).
    /// </summary>
    [Column("NO_LEAVE_BAL_DAYS", TypeName = "NUMBER(6,2)")]
    public decimal? NoLeaveBalDays { get; set; }

    /// <summary>
    /// Leave days carried over from the previous year (6,2).
    /// </summary>
    [Column("PREV_YR_AL_BAL_DAYS", TypeName = "NUMBER(6,2)")]
    public decimal? PrevYrAlBalDays { get; set; }

    /// <summary>
    /// User ID of the record creator/updater.
    /// </summary>
    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; }

    /// <summary>
    /// Timestamp when the record was created/updated.
    /// </summary>
    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; }
} 