﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Table("TLEAVE_E_APP")]
public partial class TleaveEApp
{
    [Key]
    [Column("APP_NO")]
    [Precision(9)]
    public int AppNo { get; set; }

    [Column("EMP_NO")]
    [Precision(4)]
    public byte? EmpNo { get; set; }

    [Column("APP_DATE", TypeName = "DATE")]
    public DateTime? AppDate { get; set; }

    [Column("LEAVE_TYPE")]
    [Precision(2)]
    public byte? LeaveType { get; set; }

    [Column("START_DATE", TypeName = "DATE")]
    public DateTime? StartDate { get; set; }

    [Column("END_DATE", TypeName = "DATE")]
    public DateTime EndDate { get; set; }

    [Column("MANGER_ID")]
    [Precision(4)]
    public byte MangerId { get; set; }

    [Column("FIRST_SIGN")]
    [Precision(3)]
    public byte? FirstSign { get; set; }

    [Column("SECOND_SIGN")]
    [Precision(3)]
    public byte? SecondSign { get; set; }

    [Column("SIGN_STAT")]
    [Precision(2)]
    public byte? SignStat { get; set; }

    [Column("FIRST_SIGN_DATE", TypeName = "DATE")]
    public DateTime? FirstSignDate { get; set; }

    [Column("SECOND_SIGN_DATE", TypeName = "DATE")]
    public DateTime? SecondSignDate { get; set; }

    [Column("LEAVE_T")]
    [Precision(2)]
    public byte? LeaveT { get; set; }

    [Column("CANCEL_FLAG")]
    [Precision(2)]
    public byte? CancelFlag { get; set; }

    [Column("REJECT_REASON")]
    [StringLength(100)]
    [Unicode(false)]
    public string RejectReason { get; set; }
}
