using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Table("TLEAVE_BAL_ADJUSTMENT_TXS")]
public partial class TleaveBalAdjustmentTx
{
    [Key]
    [Column("IN_YEAR")]
    [Precision(2)]
    public int InYear { get; set; }

    [Key]
    [Column("IN_DEPT_IND")]
    [Precision(2)]
    public int InDeptInd { get; set; }

    [Key]
    [Column("IN_MAIL_NO")]
    [Precision(5)]
    public int InMailNo { get; set; }

    [Key]
    [Column("IN_DOC_SL_NO")]
    [Precision(4)]
    public int InDocSlNo { get; set; }

    [Column("UNIT_CODE")]
    [Precision(2)]
    public int UnitCode { get; set; }
    
    [Column("EMP_NO")]
    [Precision(5)]
    public int EmpNo { get; set; }

    [Column("LEAVE_CODE")]
    [Precision(2)]
    public int LeaveCode { get; set; }
    [ForeignKey("LeaveCode")]
    public virtual TleaveCode TleaveCode { get; set; }

    [Column("LEAVE_TYPE")]
    [Precision(2)]
    public int? LeaveType { get; set; }

    [Column("CANCEL_FLAG")]
    [Precision(1)]
    public int? CancelFlag { get; set; } = 0;

    [Column("DATE_FROM", TypeName = "DATE")]
    public DateTime? DateFrom { get; set; }

    [Column("DATE_UPTO", TypeName = "DATE")]
    public DateTime? DateUpto { get; set; }

    [Column("NO_DAYS_ADJUST", TypeName = "NUMBER(6,2)")]
    public decimal? NoDaysAdjust { get; set; }

    [Column("REQ_STAT")]
    [Precision(2)]
    public int? ReqStat { get; set; } = 1;

    [Column("APPROVAL_DATE", TypeName = "DATE")]
    public DateTime? ApprovalDate { get; set; }

    [Column("APPROVAL_REM")]
    [StringLength(255)]
    [Unicode(false)]
    public string ApprovalRem { get; set; }

    [Column("TXN_DATE", TypeName = "DATE")]
    public DateTime? TxnDate { get; set; } = DateTime.Now;

    [Column("ORDER_TYPE")]
    [Precision(3)]
    public int? OrderType { get; set; }

    [Column("TRANS_TYPE")]
    [Precision(3)]
    public int? TransType { get; set; }

    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; }

    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; } = DateTime.Now;

    [Column("ORDER_YEAR")]
    [Precision(2)]
    public int? OrderYear { get; set; }

    [Column("ORDER_DEPT_IND")]
    [Precision(2)]
    public int? OrderDeptInd { get; set; }

    [Column("ORDER_SL_NO")]
    [Precision(5)]
    public int? OrderSlNo { get; set; }
} 