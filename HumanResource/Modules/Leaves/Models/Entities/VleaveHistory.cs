using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities
{
    [Keyless]
    [Table("VLEAVE_HISTORY")]
    public class VleaveHistory
    {
        [Column("UNIT_CODE")]
        public short UnitCode { get; set; }

        [Column("EMP_NO")]
        public int EmpNo { get; set; }

        [Column("LEAVE_TYPE")]
        public int LeaveType { get; set; }

        [Column("LEAVE_CODE")]
        public int LeaveCode { get; set; }

        [Column("ORDER_YEAR")]
        public short OrderYear { get; set; }

        [Column("ORDER_DEPT_IND")]
        public short OrderDeptInd { get; set; }

        [Column("ORDER_SL_NO")]
        public int OrderSlNo { get; set; }

        [Column("LEAVE_START_DATE")]
        public DateTime LeaveStartDate { get; set; }

        [Column("LEAVE_END_DATE")]
        public DateTime LeaveEndDate { get; set; }

        [Column("LEAVE_PLND_FLAG")]
        public int LeavePlndFlag { get; set; }

        [Column("LV_DYS")]
        public int LvDys { get; set; }

        [Column("LEAVE_DESP_A")]
        public string LeaveDesp { get; set; }
    }
}
