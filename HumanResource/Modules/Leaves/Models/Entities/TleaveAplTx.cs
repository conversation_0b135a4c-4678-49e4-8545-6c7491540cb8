﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;


[Table("TLEAVE_APL_TXS")]
public partial class TleaveAplTx
{
    [Key]
    [Column("IN_YEAR")]
    public int InYear { get; set; } //s

    [Key]
    [Column("IN_DEPT_IND")]
    public int InDeptInd { get; set; }//s

    [Key]
    [Column("IN_MAIL_NO")]
    public int InMailNo { get; set; } //s

    [Key]
    [Column("IN_DOC_SL_NO")]
    public int InDocSlNo { get; set; } //s

    [Column("UNIT_CODE")]
    public int UnitCode { get; set; } //s

    [Column("EMP_NO")]
    public int EmpNo { get; set; } //s

    [Column("LEAVE_CODE")]
    public int LeaveCode { get; set; } //s
    [ForeignKey("LeaveCode")]
    public virtual TleaveCode TleaveCode { get; set; }

    [Column("LEAVE_TYPE")]
    public int? LeaveType { get; set; }
    [ForeignKey("LeaveType")]
    public virtual TleaveType TleaveType { get; set; }

    [Column("LEAVE_APPLN_DATE")]
    public DateTime? LeaveApplnDate { get; set; } = DateTime.Now;

    [Column("TRAVEL_DAYS")]

    public int? TravelDays { get; set; }

    [Column("LEAVE_START_DATE", TypeName = "DATE")]
    public DateTime LeaveStartDate { get; set; }

    [Column("LEAVE_END_DATE", TypeName = "DATE")]
    public DateTime LeaveEndDate { get; set; }

    [Column("LEAVE_PLND_FLAG")]

    public int? LeavePlndFlag { get; set; }

    [Column("MED_CERT_SUBMITTED")]

    public int? MedCertSubmitted { get; set; } = 0;

    [Column("TRAINING_FLAG")]

    public int? TrainingFlag { get; set; } = 0;

    [Column("RES_PER_FLAG")]

    public int? ResPerFlag { get; set; } = 0;

    [Column("TICKET_REQD_FLAG")]

    public int? TicketReqdFlag { get; set; } = 0;

    [Column("ENCASH_FLAG")]
    public int? EncashFlag { get; set; } = 0;

    [Column("DUTY_JOINING_TXN_NEEDED_FLAG")]

    public int? DutyJoiningTxnNeededFlag { get; set; } = 0;

    [Column("LEAVE_EXT_TYPE")]

    public int? LeaveExtType { get; set; } = 0;

    [Column("LEAVE_BAL_ON_START_DATE", TypeName = "NUMBER(6,2)")]
    public decimal? LeaveBalOnStartDate { get; set; }

    [Column("SAL_ADVANCE_REQD_FLAG")]

    public int? SalAdvanceReqdFlag { get; set; } = 0;

    [Column("SAL_ADVANCE_UPTO_DATE", TypeName = "DATE")]
    public DateTime? SalAdvanceUptoDate { get; set; }

    [Column("SAL_ADVANCE_PAY_DATE", TypeName = "DATE")]
    public DateTime? SalAdvancePayDate { get; set; }



    [Column("PAY_STAT")]

    public int? PayStat { get; set; } = 0;

    [Column("DUTY_RESUME_DATE", TypeName = "DATE")]
    public DateTime? DutyResumeDate { get; set; }

    [Column("CANCEL_FLAG")]

    public int CancelFlag { get; set; } = 0;

    [Column("VIOLATION_FLAG")]

    public int? ViolationFlag { get; set; } = 0;

    [Column("VALIDATION_FLAG")]

    public int? ValidationFlag { get; set; } = 0;

    [Column("LEAVE_CANCEL_MOD_REASON_CODE")]

    public int? LeaveCancelModReasonCode { get; set; }

    [Column("MANAGER_APPROVAL")]
    public int? ManagerApproval { get; set; }

    [Column("APPROVAL_DATE", TypeName = "DATE")]
    public DateTime? ApprovalDate { get; set; }

    [Column("APPROVAL_REM")]
    [StringLength(255)]
    [Unicode(false)]
    public string ApprovalRem { get; set; }

    [Column("ORDER_YEAR")]

    public int? OrderYear { get; set; }

    [Column("ORDER_DEPT_IND")]

    public int? OrderDeptInd { get; set; }

    [Column("ORDER_SL_NO")]

    public int? OrderSlNo { get; set; }

    [Column("ORDER_DATE", TypeName = "DATE")]
    public DateTime? OrderDate { get; set; } = DateTime.Now;

    [Column("SIGN_AUTH_CODE")]

    public int? SignAuthCode { get; set; } = 0;

    [Column("SIGN_REM")]
    [StringLength(60)]
    [Unicode(false)]
    public string SignRem { get; set; }

    [Column("SIGN_DATE", TypeName = "DATE")]
    public DateTime? SignDate { get; set; }

    [Column("SIGN_BY_CODE")]

    public int? SignByCode { get; set; }

    [Column("AUDIT_STAT")]

    public int? AuditStat { get; set; } = 0;

    [Column("AUDIT_DATE", TypeName = "DATE")]
    public DateTime? AuditDate { get; set; }

    [Column("AUDIT_REM")]
    [StringLength(150)]
    [Unicode(false)]
    public string AuditRem { get; set; }

    [Column("AUDIT_USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string AuditUserId { get; set; }

    [Column("AUDIT_TIME_STAMP", TypeName = "DATE")]
    public DateTime? AuditTimeStamp { get; set; }

    [Column("OLD_IN_YEAR")]

    public int? OldInYear { get; set; }

    [Column("OLD_IN_DEPT_IND")]

    public int? OldInDeptInd { get; set; }

    [Column("OLD_IN_MAIL_NO")]

    public int? OldInMailNo { get; set; }

    [Column("OLD_IN_DOC_SL_NO")]

    public int? OldInDocSlNo { get; set; }

    [Column("NO_LATE_EARLY_DAYS")]

    public int? NoLateEarlyDays { get; set; }

    [Column("TXN_DATE", TypeName = "DATE")]
    public DateTime? TxnDate { get; set; } = DateTime.Now;

    [Column("ORDER_TYPE")]

    public int? OrderType { get; set; }

    [Column("TRANS_TYPE")]

    public int? TransType { get; set; }

    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; } = null;

    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; } = DateTime.Now;

    [Column("PAY_YEAR")]

    public int? PayYear { get; set; }

    [Column("PAY_MONTH")]

    public int? PayMonth { get; set; }

    [Column("RUN_NO")]

    public int? RunNo { get; set; }

    [Column("STUDY_ALLOW_REQD_FLAG")]

    public int? StudyAllowReqdFlag { get; set; }

    [Column("WORK_ALLOW_CUT_FLAG")]

    public int? WorkAllowCutFlag { get; set; }

    [Column("REQ_STAT")]
    public int? ReqStat { get; set; } = 1;


    [Column("FILE_GUID")]
    [StringLength(100)]
    [Unicode(false)]
    public string FileGuid { get; set; }

}
