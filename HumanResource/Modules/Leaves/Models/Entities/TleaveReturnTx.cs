using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Leaves.Models.Entities
{
    /// <summary>
    /// Records the details of employees returning from leave, including leave adjustments, approvals, and related transactions.
    /// </summary>
    [Table("TLEAVE_RETURN_TXS", Schema = "DRCH")]
    public class TleaveReturnTx
    {
        /// <summary>
        /// Year of the input or record creation.
        /// </summary>
        [Key]
        [Column("IN_YEAR")]
        public int InYear { get; set; }

        /// <summary>
        /// Indicator for the department responsible for the transaction.
        /// </summary>
        [Key]
        [Column("IN_DEPT_IND")]
        public int InDeptInd { get; set; }

        /// <summary>
        /// Reference mail number for the leave transaction.
        /// </summary>
        [Key]
        [Column("IN_MAIL_NO")]
        public int InMailNo { get; set; }

        /// <summary>
        /// Serial number of the associated document.
        /// </summary>
        [Key]
        [Column("IN_DOC_SL_NO")]
        public int InDocSlNo { get; set; }

        /// <summary>
        /// Code for the unit or department associated with the transaction.
        /// </summary>
        [Column("UNIT_CODE")]
        public int UnitCode { get; set; }

        /// <summary>
        /// Employee number linked to the leave transaction.
        /// </summary>
        [Column("EMP_NO")]
        public int EmpNo { get; set; }

        /// <summary>
        /// Type of leave (e.g., annual, sick, casual).
        /// </summary>
        [Column("LEAVE_TYPE")]
        public int LeaveType { get; set; }

        /// <summary>
        /// Code representing the specific leave category.
        /// </summary>
        [Column("LEAVE_CODE")]
        public int LeaveCode { get; set; }

        /// <summary>
        /// Start date of the approved leave.
        /// </summary>
        [Column("LEAVE_START_DATE")]
        public DateTime LeaveStartDate { get; set; }

        /// <summary>
        /// End date of the approved leave.
        /// </summary>
        [Column("LEAVE_END_DATE")]
        public DateTime? LeaveEndDate { get; set; }

        /// <summary>
        /// Start date of the previously approved leave, if applicable.
        /// </summary>
        [Column("P_LEAVE_START_DATE")]
        public DateTime? PLeaveStartDate { get; set; }

        /// <summary>
        /// End date of the previously approved leave, if applicable.
        /// </summary>
        [Column("P_LEAVE_END_DATE")]
        public DateTime? PLeaveEndDate { get; set; }

        /// <summary>
        /// Number of leave days used.
        /// </summary>
        [Column("NO_LEAVE_DAYS")]
        public int? NoLeaveDays { get; set; }

        /// <summary>
        /// Number of days the employee returned late or early.
        /// </summary>
        [Column("LATE_OR_EARLY_DAYS")]
        public int? LateOrEarlyDays { get; set; }

        /// <summary>
        /// Code representing the reason for leave adjustments.
        /// </summary>
        [Column("REASON_CODE")]
        public int? ReasonCode { get; set; }

        /// <summary>
        /// Date the employee resumed duty.
        /// </summary>
        [Column("DUTY_RESUME_DATE")]
        public DateTime? DutyResumeDate { get; set; }

        /// <summary>
        /// Indicator for disciplinary actions taken, if any.
        /// </summary>
        [Column("DISC_ACT_STAT")]
        public int? DiscActStat { get; set; }

        /// <summary>
        /// Flag indicating if a medical certificate was submitted.
        /// </summary>
        [Column("MED_CERT_SUBMITTED")]
        public int? MedCertSubmitted { get; set; }

        /// <summary>
        /// Request status (e.g., approved, pending, rejected).
        /// </summary>
        [Column("REQ_STAT")]
        public int? ReqStat { get; set; }

        /// <summary>
        /// Flag indicating if the leave or return request was canceled.
        /// </summary>
        [Column("CANCEL_FLAG")]
        public int? CancelFlag { get; set; }

        /// <summary>
        /// Flag indicating if a violation occurred.
        /// </summary>
        [Column("VIOLATION_FLAG")]
        public int? ViolationFlag { get; set; }

        /// <summary>
        /// Date the leave return transaction was approved.
        /// </summary>
        [Column("APPROVAL_DATE")]
        public DateTime? ApprovalDate { get; set; }

        /// <summary>
        /// Remarks related to the approval.
        /// </summary>
        [Column("APPROVAL_REM")]
        [StringLength(255)]
        public string ApprovalRem { get; set; }

        /// <summary>
        /// Year of the official order related to the transaction.
        /// </summary>
        [Column("ORDER_YEAR")]
        public int? OrderYear { get; set; }

        /// <summary>
        /// Indicator for the department issuing the official order.
        /// </summary>
        [Column("ORDER_DEPT_IND")]
        public int? OrderDeptInd { get; set; }

        /// <summary>
        /// Serial number of the official order.
        /// </summary>
        [Column("ORDER_SL_NO")]
        public int? OrderSlNo { get; set; }

        /// <summary>
        /// Date of the official order.
        /// </summary>
        [Column("ORDER_DATE")]
        public DateTime? OrderDate { get; set; }

        /// <summary>
        /// Code representing the signing authority.
        /// </summary>
        [Column("SIGN_AUTH_CODE")]
        public int? SignAuthCode { get; set; }

        /// <summary>
        /// Remarks related to the signing authority.
        /// </summary>
        [Column("SIGN_REM")]
        [StringLength(60)]
        public string SignRem { get; set; }

        /// <summary>
        /// Date the transaction was signed off.
        /// </summary>
        [Column("SIGN_DATE")]
        public DateTime? SignDate { get; set; }

        /// <summary>
        /// Code for the person who signed off on the transaction.
        /// </summary>
        [Column("SIGN_BY_CODE")]
        public int? SignByCode { get; set; }

        /// <summary>
        /// Audit status of the transaction.
        /// </summary>
        [Column("AUDIT_STAT")]
        public int? AuditStat { get; set; }

        /// <summary>
        /// Date the transaction was audited.
        /// </summary>
        [Column("AUDIT_DATE")]
        public DateTime? AuditDate { get; set; }

        /// <summary>
        /// Remarks related to the audit process.
        /// </summary>
        [Column("AUDIT_REM")]
        [StringLength(150)]
        public string AuditRem { get; set; }

        /// <summary>
        /// User ID of the auditor.
        /// </summary>
        [Column("AUDIT_USER_ID")]
        [StringLength(20)]
        public string AuditUserId { get; set; }

        /// <summary>
        /// Timestamp when the audit was completed.
        /// </summary>
        [Column("AUDIT_TIME_STAMP")]
        public DateTime? AuditTimeStamp { get; set; }

        /// <summary>
        /// Transaction date.
        /// </summary>
        [Column("TXN_DATE")]
        public DateTime? TxnDate { get; set; }

        /// <summary>
        /// Type of official order related to the transaction.
        /// </summary>
        [Column("ORDER_TYPE")]
        public int? OrderType { get; set; }

        /// <summary>
        /// Type of transaction (e.g., adjustment, extension).
        /// </summary>
        [Column("TRANS_TYPE")]
        public int? TransType { get; set; }

        /// <summary>
        /// User ID of the person who created or updated the record.
        /// </summary>
        [Column("USER_ID")]
        [StringLength(20)]
        public string UserId { get; set; }

        /// <summary>
        /// Timestamp when the record was last updated.
        /// </summary>
        [Column("TIME_STAMP")]
        public DateTime? TimeStamp { get; set; }

        /// <summary>
        /// Number of holidays included in the leave period.
        /// </summary>
        [Column("NO_HOLIDAYS")]
        public int? NoHolidays { get; set; }

        // Navigation properties
        /// <summary>
        /// Navigation property for the leave code details.
        /// </summary>
        [ForeignKey("LeaveCode")]
        public virtual TleaveCode TleaveCode { get; set; }
    }
} 