﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
namespace HumanResource.Modules.Leaves.Models.Entities
{

    [Table("THOLIDAYS")]
    public class Tholidays
    {

        [Key]
        [Required]
        [Column("HOLIDAY")]
        public DateTime? Holiday { get; set; }

        [Column("HOLIDAY_DESP_A")]
        public string DespA { get; set; }

        [Column("HOLIDAY_DESP_E")]
        public string DespE { get; set; }

    }
}
