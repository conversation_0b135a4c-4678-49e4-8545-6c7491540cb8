﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[PrimaryKey("InYear", "InDeptInd", "InMailNo", "InDocSlNo", "SlNo")]
[Table("TLEAVE_APL_DTLS")]
public partial class TleaveAplDtl
{
    [Key]
    [Column("IN_YEAR")]
    [Precision(2)]
    public byte InYear { get; set; }

    [Key]
    [Column("IN_DEPT_IND")]
    [Precision(2)]
    public byte InDeptInd { get; set; }

    [Key]
    [Column("IN_MAIL_NO")]
    [Precision(5)]
    public short InMailNo { get; set; }

    [Key]
    [Column("IN_DOC_SL_NO")]
    [Precision(4)]
    public byte InDocSlNo { get; set; }

    [Column("UNIT_CODE")]
    [Precision(2)]
    public byte UnitCode { get; set; }

    [Key]
    [Column("SL_NO")]
    [Precision(2)]
    public byte SlNo { get; set; }

    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    [Column("LEAVE_CODE")]
    [Precision(2)]
    public byte LeaveCode { get; set; }

    [Column("LEAVE_TYPE")]
    [Precision(2)]
    public byte? LeaveType { get; set; }

    [Column("TRAVEL_DAYS")]
    [Precision(2)]
    public byte? TravelDays { get; set; }

    [Column("LEAVE_START_DATE", TypeName = "DATE")]
    public DateTime LeaveStartDate { get; set; }

    [Column("LEAVE_END_DATE", TypeName = "DATE")]
    public DateTime? LeaveEndDate { get; set; }

    [Column("CANCEL_FLAG")]
    [Precision(1)]
    public bool CancelFlag { get; set; }

    [Column("VIOLATION_FLAG")]
    [Precision(1)]
    public bool? ViolationFlag { get; set; }

    [Column("OLD_IN_YEAR")]
    [Precision(2)]
    public byte? OldInYear { get; set; }

    [Column("OLD_IN_DEPT_IND")]
    [Precision(2)]
    public byte? OldInDeptInd { get; set; }

    [Column("OLD_IN_MAIL_NO")]
    [Precision(5)]
    public short? OldInMailNo { get; set; }

    [Column("OLD_IN_DOC_SL_NO")]
    [Precision(4)]
    public byte? OldInDocSlNo { get; set; }

    [Column("TXN_DATE", TypeName = "DATE")]
    public DateTime? TxnDate { get; set; }

    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; }

    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; }
}
