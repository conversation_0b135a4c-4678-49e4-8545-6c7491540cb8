using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Leaves.Models.Entities
{
    /// <summary>
    /// Stores details of disciplinary action leave transactions, including document references, leave codes, duration, and order types related to the disciplinary process.
    /// </summary>
    [Table("TDISP_TRN_LEAVE_DTLS", Schema = "DRCH")]
    public class TdispTrnLeaveDtl
    {
        /// <summary>
        /// Year of the disciplinary action leave transaction, represented in 2-digit format.
        /// </summary>
        [Key]
        [Column("IN_YEAR")]
        public int InYear { get; set; }

        /// <summary>
        /// Indicator for the department associated with the disciplinary action leave transaction.
        /// </summary>
        [Key]
        [Column("IN_DEPT_IND")]
        public int InDeptInd { get; set; }

        /// <summary>
        /// Mail number associated with the disciplinary action leave transaction, used for tracking.
        /// </summary>
        [Key]
        [Column("IN_MAIL_NO")]
        public int InMailNo { get; set; }

        /// <summary>
        /// Serial number of the document associated with the disciplinary action leave transaction.
        /// </summary>
        [Key]
        [Column("IN_DOC_SL_NO")]
        public int InDocSlNo { get; set; }

        /// <summary>
        /// Code representing the type of leave, such as disciplinary leave, suspension, or other leave types related to disciplinary actions.
        /// </summary>
        [Key]
        [Column("LEAVE_CODE")]
        public int LeaveCode { get; set; }

        /// <summary>
        /// Start date of the leave period requested or enforced as part of the disciplinary action process.
        /// </summary>
        [Key]
        [Column("FROM_DATE")]
        public DateTime FromDate { get; set; }

        /// <summary>
        /// End date of the leave period requested or enforced as part of the disciplinary action process.
        /// </summary>
        [Column("TO_DATE")]
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Code representing the type of order associated with the disciplinary action leave transaction, such as approval, suspension, or cancellation.
        /// </summary>
        [Column("ORDER_TYPE")]
        public int OrderType { get; set; }

        // Navigation properties
        /// <summary>
        /// Navigation property for the leave code details.
        /// </summary>
        [ForeignKey("LeaveCode")]
        public virtual TleaveCode TleaveCode { get; set; }
    }
} 