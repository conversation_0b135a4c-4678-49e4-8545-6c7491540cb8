﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[PrimaryKey("UnitCode", "EmpNo", "LeaveCode")]
[Table("TLEAVE_BAL")]
[Index("EmpNo", Name = "I1TLEAVE_BAL")]
public partial class TleaveBal
{
    [Key]
    [Column("UNIT_CODE")]
    [Precision(2)]
    public byte UnitCode { get; set; }

    [Key]
    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    [Key]
    [Column("LEAVE_CODE")]
    [Precision(2)]
    public byte LeaveCode { get; set; }

    [Column("REF_DATE", TypeName = "DATE")]
    public DateTime RefDate { get; set; }

    [Column("NO_LEAVE_BAL_DAYS", TypeName = "NUMBER(7,2)")]
    public decimal NoLeaveBalDays { get; set; }

    [Column("PREV_YR_AL_BAL_DAYS", TypeName = "NUMBER(7,2)")]
    public decimal? PrevYrAlBalDays { get; set; }

    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; }

    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; }

    [Column("OLD_NO_LEAVE_BAL_DAYS", TypeName = "NUMBER")]
    public decimal? OldNoLeaveBalDays { get; set; }

    [Column("OLD_REF_DATE", TypeName = "DATE")]
    public DateTime? OldRefDate { get; set; }
}
