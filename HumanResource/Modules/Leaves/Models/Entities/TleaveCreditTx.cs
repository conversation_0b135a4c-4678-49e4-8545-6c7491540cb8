using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Leaves.Models.Entities
{
    [Table("TLEAVE_CREDIT_TXS")]
    public class TleaveCreditTx
    {
        [Column("IN_YEAR")]
        public short InYear { get; set; }

        [Column("IN_MAIL_NO")]
        public int InMailNo { get; set; }

        [Column("IN_DOC_SL_NO")]
        public int InDocSlNo { get; set; }

        [Column("UNIT_CODE")]
        public short UnitCode { get; set; }

        [Column("EMP_NO")]
        public int EmpNo { get; set; }

        [Column("LEAVE_CODE")]
        public int LeaveCode { get; set; }

        [Column("NO_DAYS")]
        public decimal NoDays { get; set; }

        [Column("REQ_STAT")]
        public byte ReqStat { get; set; }

        [Column("CANCEL_FLAG")]
        public byte CancelFlag { get; set; }

        [Column("TXN_DATE")]
        public DateTime TxnDate { get; set; }

        [Column("TIME_STAMP")]
        public DateTime TimeStamp { get; set; }
    }
} 