﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Table("TLEAVE_TYPE")]
public partial class TleaveType
{
    [Key]
    [Column("LEAVE_TYPE")]

    public int LeaveType { get; set; }

    [Required]
    [Column("LEAVE_TYPE_DESP_A")]
    [StringLength(30)]
    [Unicode(false)]
    public string LeaveTypeDespA { get; set; }

    [Required]
    [Column("LEAVE_TYPE_DESP_E")]
    [StringLength(30)]
    [Unicode(false)]
    public string LeaveTypeDespE { get; set; }

    [InverseProperty("TleaveType")]
    public virtual ICollection<TleaveAplTx> TleaveAplTxes { get; set; } = new List<TleaveAplTx>();

    [InverseProperty("TleaveType")]
    public virtual ICollection<ConLeaveAplTx> ConLeaveAplTxs { get; set; } = new List<ConLeaveAplTx>();

}
