﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Keyless]
[Table("CON_LEAVE_BAL")]
public partial class ConLeaveBal
{
    [Column("CENTRE_CODE")]
    [Precision(2)]
    public byte? CentreCode { get; set; }

    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    [Required]
    [Column("CONTRACT_NO")]
    [StringLength(20)]
    [Unicode(false)]
    public string ContractNo { get; set; }

    [Column("LEAVE_CODE")]
    [Precision(2)]
    public int LeaveCode { get; set; }

    [Column("REF_DATE", TypeName = "DATE")]
    public DateTime RefDate { get; set; }

    [Column("NO_LEAVE_BAL_DAYS", TypeName = "NUMBER(7,2)")]
    public decimal NoLeaveBalDays { get; set; }
}
