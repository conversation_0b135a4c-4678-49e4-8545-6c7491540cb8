using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Table("TLEAVE_ELIGIBILITY")]
public partial class TleaveEligibility
{
    [Key]
    [Column("LEAVE_CODE")]
    public int LeaveCode { get; set; }

    [Column("LEAVE_CONDITION")]
    [StringLength(2000)]
    [Unicode(false)]
    public string LeaveCondition { get; set; }

    [Column("LEAVE_RULE_DESP_A")]
    [StringLength(200)]
    [Unicode(false)]
    public string LeaveRuleDespA { get; set; }

    [Column("LEAVE_RULE_DESP_E")]
    [StringLength(200)]
    [Unicode(false)]
    public string LeaveRuleDespE { get; set; }

    // Navigation property
    public virtual TleaveCode TleaveCode { get; set; }
} 