﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Table("TLEAVE_CODE")]
public partial class TleaveCode
{
    [Key]
    [Column("LEAVE_CODE")]

    public int LeaveCode { get; set; }

    [Required]
    [Column("LEAVE_DESP_A")]
    [StringLength(30)]
    [Unicode(false)]
    public string LeaveDespA { get; set; }

    [Required]
    [Column("LEAVE_DESP_E")]
    [StringLength(30)]
    [Unicode(false)]
    public string LeaveDespE { get; set; }

    [Column("ADJUST_AL_FLAG")]
    [Precision(1)]
    public bool? AdjustAlFlag { get; set; }

    [Column("LEAVE_TYPE")]

    public int? LeaveType { get; set; }

    //[InverseProperty("LeaveCodeNavigation")]
    //public virtual ICollection<TleaveAplDtl> TleaveAplDtls { get; } = new List<TleaveAplDtl>();

    //[InverseProperty("LeaveCodeNavigation")]
    //public virtual ICollection<TleaveBal> TleaveBals { get; } = new List<TleaveBal>();

    [InverseProperty("TleaveCode")]
    public virtual ICollection<TleaveAplTx> TleaveAplTxs { get; set; } = new List<TleaveAplTx>();

    [InverseProperty("TleaveCode")]
    public virtual ICollection<ConLeaveAplTx> ConLeaveAplTxs { get; set; } = new List<ConLeaveAplTx>();
  
}
