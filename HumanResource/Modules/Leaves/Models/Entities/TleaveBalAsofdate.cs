﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Keyless]
[Table("TLEAVE_BAL_ASOFDATE")]
[Index("EmpNo", Name = "I1TLEAVE_BAL_ASOFDATE")]
public partial class TleaveBalAsofdate
{
    [Column("SL_NO")]
    [Precision(5)]
    public short SlNo { get; set; }

    [Column("UNIT_CODE")]
    [Precision(2)]
    public byte UnitCode { get; set; }

    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    [Column("REF_DATE", TypeName = "DATE")]
    public DateTime RefDate { get; set; }

    [Column("NO_LEAVE_BAL_DAYS", TypeName = "NUMBER(6,2)")]
    public decimal? NoLeaveBalDays { get; set; }

    [Column("GRADE_RANK_CODE")]
    [Precision(3)]
    public byte? GradeRankCode { get; set; }

    [Column("TEACHER_FLAG")]
    [Precision(1)]
    public bool? TeacherFlag { get; set; }

    [Column("ENTITLE_DAYS")]
    [Precision(3)]
    public byte? EntitleDays { get; set; }

    [Column("PREV_TXN_DAYS", TypeName = "NUMBER(6,2)")]
    public decimal? PrevTxnDays { get; set; }

    [Column("LAST_TXN_DATE", TypeName = "DATE")]
    public DateTime? LastTxnDate { get; set; }

    [Column("TOTAL_TXN_DAYS", TypeName = "NUMBER(6,2)")]
    public decimal? TotalTxnDays { get; set; }

    [Column("BAL_DAYS_ASOF_CUTOFF", TypeName = "NUMBER(6,2)")]
    public decimal? BalDaysAsofCutoff { get; set; }

    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; }

    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; }

    [Column("AVAIL_TYPE")]
    [Precision(2)]
    public byte? AvailType { get; set; }
}
