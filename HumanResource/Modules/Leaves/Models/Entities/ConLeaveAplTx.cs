﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Models.Entities;

[Table("CON_LEAVE_APL_TXS")]
public partial class ConLeaveAplTx
{
    [Column("CONTRACT_NO")]
    [StringLength(20)]
    [Unicode(false)]
    public string ContractNo { get; set; }

    [Column("EMP_NO")]
    public int EmpNo { get; set; } //s

    [Column("LEAVE_CODE")]
    [ForeignKey("TleaveCode")]
    public int LeaveCode { get; set; }
    public virtual TleaveCode TleaveCode { get; set; }


    [Column("LEAVE_TYPE")]
    [ForeignKey("TleaveType")]
    public int? LeaveType { get; set; } //s
    public virtual TleaveType TleaveType { get; set; }

    [Key]
    [Column("IN_YEAR")]
    public int InYear { get; set; } //s

    [Key]
    [Column("IN_DEPT_IND")]
    public int InDeptInd { get; set; } //s

    [Key]
    [Column("IN_MAIL_NO")]
    public int InMailNo { get; set; } //s

    [Key]
    [Column("IN_DOC_SL_NO")]
    public int InDocSlNo { get; set; } //s

    [Column("LEAVE_APPLN_DATE", TypeName = "DATE")]
    public DateTime? LeaveApplnDate { get; set; } = DateTime.Now;

    [Column("LEAVE_START_DATE", TypeName = "DATE")]
    public DateTime LeaveStartDate { get; set; }

    [Column("LEAVE_END_DATE", TypeName = "DATE")]
    public DateTime LeaveEndDate { get; set; }

    [Column("LEAVE_PLND_FLAG")]

    public int? LeavePlndFlag { get; set; }

    [Column("MED_CERT_SUBMITTED")]

    public int? MedCertSubmitted { get; set; }

    [Column("LEAVE_BAL_ON_START_DATE", TypeName = "NUMBER(6,2)")]
    public decimal? LeaveBalOnStartDate { get; set; }

    [Column("PAY_STAT")]

    public int? PayStat { get; set; }

    [Column("CANCEL_FLAG")]

    public int CancelFlag { get; set; } = 0; //s

    [Column("MANAGER_APPROVAL")]
    public int? ManagerApproval { get; set; }

    [Column("APPROVAL_DATE", TypeName = "DATE")]
    public DateTime? ApprovalDate { get; set; }

    [Column("APPROVAL_REM")]
    [StringLength(255)]
    [Unicode(false)]
    public string ApprovalRem { get; set; }

    [Column("SIGN_AUTH_CODE")]

    public int? SignAuthCode { get; set; } = 0; //s

    [Column("SIGN_REM")]
    [StringLength(60)]
    [Unicode(false)]
    public string SignRem { get; set; } //s

    [Column("SIGN_DATE", TypeName = "DATE")]
    public DateTime? SignDate { get; set; } //s

    [Column("SIGN_BY_CODE")]

    public int? SignByCode { get; set; } //s

    [Column("TXN_DATE", TypeName = "DATE")]
    public DateTime? TxnDate { get; set; } = DateTime.Now;

    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; } = null; //s

    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; } = DateTime.Now; //s

    [Column("PAY_YEAR")]

    public int? PayYear { get; set; }

    [Column("PAY_MONTH")]

    public int? PayMonth { get; set; }

    [Column("RUN_NO")]

    public int? RunNo { get; set; }

    [Column("REMARKS")]
    [StringLength(100)]
    [Unicode(false)]
    public string Remarks { get; set; }

    [Column("CENTRE_CODE")]

    public int? CentreCode { get; set; }

    [Column("SALARY_CUT_FLAG")]

    public int? SalaryCutFlag { get; set; }

    [Column("HOLIDAY_CREDITED", TypeName = "NUMBER")]
    public decimal? HolidayCredited { get; set; }

    [Column("ORDER_YEAR")]

    public int? OrderYear { get; set; } //s

    [Column("ORDER_DEPT_IND")]

    public int? OrderDeptInd { get; set; }

    [Column("ORDER_SL_NO")]

    public int? OrderSlNo { get; set; }

    [Column("ORDER_DATE", TypeName = "DATE")]
    public DateTime? OrderDate { get; set; } = DateTime.Now;

    [Column("ORDER_TYPE")]

    public int? OrderType { get; set; }

    [Column("REQ_STAT")]

    public int? ReqStat { get; set; } = 1; //s

    [Column("AUDIT_DATE", TypeName = "DATE")]
    public DateTime? AuditDate { get; set; }

    [Column("AUDIT_REM")]
    [StringLength(150)]
    [Unicode(false)]
    public string AuditRem { get; set; } //s

    [Column("AUDIT_STAT")]

    public int? AuditStat { get; set; } //s

    [Column("AUDIT_TIME_STAMP", TypeName = "DATE")]
    public DateTime? AuditTimeStamp { get; set; }

    [Column("AUDIT_USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string AuditUserId { get; set; }

    [Column("TRANS_TYPE")]

    public int? TransType { get; set; }

    [Column("DUTY_RESUME_DATE", TypeName = "DATE")]
    public DateTime? DutyResumeDate { get; set; }

    [Column("REQ_STAT_CODE")]
    public int? ReqStatCode { get; set; } = 0; // see Tstat_code
}
