﻿using System.Data;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;
using HumanResource.Modules.Shared.Services;
using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers.Extinctions;
using Microsoft.EntityFrameworkCore;
using Oracle.ManagedDataAccess.Client;

namespace HumanResource.Modules.Leaves.Services;

public class LeaveService
{

    private readonly hrmsContext _db;
    private readonly MailService _mail;


    public LeaveService(hrmsContext context, MailService mail)
    {
        _db = context;
        _mail = mail;

    }

    public string GetLEAVEBAL(int? empId)
    {
        try
        {

            using (var ctx = new hrmsContext())
            using (var cmd = ctx.Database.GetDbConnection().CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "SMS_LEAVE_BAL";

                var InEmpParam = new OracleParameter("InEmp_No", OracleDbType.Int32, empId, ParameterDirection.Input);
                var outBalanceParam = new OracleParameter("outBalance_pc", OracleDbType.Varchar2, ParameterDirection.Output);
                var datebalanceParam = new OracleParameter("datebalance", OracleDbType.Date, ParameterDirection.Output);

                cmd.Parameters.AddRange(new[] { InEmpParam, outBalanceParam, datebalanceParam });
                cmd.Connection.Open();
                var result = cmd.ExecuteNonQuery();
                cmd.Connection.Close();

                return outBalanceParam.ToString();

            }
        }
        catch (Exception x)
        {

            return null;
        }
    }

    public string GetTodayBAL(int? empId, DateTime? Balancedate)
    {
        try
        {

            if (Balancedate == null)
            {
                Balancedate = DateTime.Now;
            }
            using (var ctx = new hrmsContext())
            using (var cmd = ctx.Database.GetDbConnection().CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "DRCH.EMP_LEAVE_BAL";
                var InEMPNOParam = new OracleParameter("InEMP_NO", OracleDbType.Int32, empId, ParameterDirection.Input);
                var InDATELDParam = new OracleParameter("Balancedate_pd", OracleDbType.Date, Balancedate, ParameterDirection.Input);
                var outCOMPUTEDAYSParam = new OracleParameter("Balance", OracleDbType.Decimal, ParameterDirection.Output);
                var outLASTYEARBALDAYSParam = new OracleParameter("ToDate", OracleDbType.Date, ParameterDirection.Output);

                cmd.Parameters.AddRange(new[] { InEMPNOParam, InDATELDParam, outCOMPUTEDAYSParam, outLASTYEARBALDAYSParam });
                cmd.Connection.Open();
                var result = cmd.ExecuteNonQuery();
                cmd.Connection.Close();


                return outCOMPUTEDAYSParam.Value.ToString();

            }
        }
        catch (Exception x)
        {

            return null;
        }
    }

    public decimal Balance(int EmpNo, int LeaveCode)
    {


        if (LeaveCode==21)
        {
            var EMERGENCY_LEAVE_WITH_FULLPAY = 0;

          
            var inYear = Convert.ToInt32(DateTime.Now.ToString("yy"));
            EMERGENCY_LEAVE_WITH_FULLPAY = _db.TleaveAplTxs.Where(r => r.LeaveCode == 21 && r.InYear == inYear && r.EmpNo == EmpNo).Count();
            

            return 3 - EMERGENCY_LEAVE_WITH_FULLPAY;
        }   



        var leaveBalance = _db.TleaveBals.Where(r => r.LeaveCode == LeaveCode && r.EmpNo == EmpNo).FirstOrDefault();

        if (leaveBalance == null)
        {
            return 0;
        }

        return leaveBalance.NoLeaveBalDays;
        

    }


    public Response<TleaveAplTx> Create(int empNo, DateTime from, DateTime to, int managerNo, int leaveCode, string fileGuid = null)
    {

        
        int leaveType = 0;
        int NoOfDays = (int)(to.Date - from.Date).TotalDays + 1;

        leaveType = _db.TleaveCodes.Where(r => r.LeaveCode == leaveCode).FirstOrDefault().LeaveType ?? 0;
        
        if(leaveCode == 1){

            if (NoOfDays <= 12 && leaveCode == 1)
            {
                leaveType = 17;
            }

            

            
        }

        if(leaveCode == 3 || leaveCode == 4 || leaveCode == 5){

            if(leaveCode == 3){
                if(NoOfDays > 7){
                    leaveCode = 4;
                }
            }
        }

        


        //insert into TLEAVE_APL_TXS(IN_YEAR, IN_DEPT_IND, IN_MAIL_NO, IN_DOC_SL_NO, UNIT_CODE, EMP_NO, LEAVE_CODE, LEAVE_TYPE, LEAVE_APPLN_DATE, LEAVE_START_DATE, LEAVE_END_DATE, LEAVE_PLND_FLAG, MED_CERT_SUBMITTED, TRAINING_FLAG, RES_PER_FLAG, TICKET_REQD_FLAG, ENCASH_FLAG, DUTY_JOINING_TXN_NEEDED_FLAG, LEAVE_EXT_TYPE, SAL_ADVANCE_REQD_FLAG, REQ_STAT, PAY_STAT, CANCEL_FLAG, VIOLATION_FLAG, VALIDATION_FLAG, APPROVAL_DATE, APPROVAL_REM, ORDER_YEAR, ORDER_DEPT_IND, ORDER_SL_NO, ORDER_DATE, SIGN_AUTH_CODE, SIGN_DATE, AUDIT_STAT, TXN_DATE, ORDER_TYPE, USER_ID, TIME_STAMP, STUDY_ALLOW_REQD_FLAG)

        //            values(23, 1, 1,:app_no, 1,:EMP_NO,:leave_type,:leave_t, sysdate,:START_DATE,:END_DATE, 1, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 1, sysdate, '-', 23, 1,:app_no, sysdate,:first_sign,:first_sign_date, 0, sysdate, 261,:emp_no, current_timestamp, 0);
        TincomingMail mail = _mail.Create(261);

        TleaveAplTx leave = new TleaveAplTx();

        leave.EmpNo = empNo;
        leave.UnitCode = 1;
        leave.InYear = mail.InYear;
        leave.InMailNo = mail.InMailNo;
        leave.InDeptInd = 1;
        leave.InDocSlNo = (int)mail.LastDocSlNo;
        leave.LeavePlndFlag = 1;
        leave.SignAuthCode = managerNo;

        leave.LeaveStartDate = from.Date;
        leave.LeaveEndDate = to.Date;
        leave.LeaveCode = leaveCode;
        leave.LeaveType = leaveType;
        leave.ValidationFlag = 1;
        leave.OrderYear = Convert.ToInt32(DateTime.Now.ToString("yy"));
        leave.OrderDeptInd = 1;
        leave.OrderSlNo = (int)mail.LastDocSlNo;
        leave.ReqStat = 0;
        leave.FileGuid = fileGuid;


        _db.TleaveAplTxs.Add(leave);
        _db.SaveChanges();



        return ResponseHelper.Result(
            success: true,
            message: new List<string> { "تم انشاء الاجازة" },
            data: leave
        );
        
    }


    public Response ValidateLeave(int empNo, int leaveCode, DateTime from, DateTime to)
    {
        // Step 1: Check employee eligibility based on TleaveEligibility
        var eligibilityCheck = CheckEmployeeEligibility(empNo, leaveCode, from, to);
        if (!eligibilityCheck.Success)
            return eligibilityCheck;
        
        // Step 2: Validate basic date logic
        if (from > to)
        {
            return ResponseHelper.Result(
                success: false,
                message: new List<string> { "خطا في تاريخ الاجازة" }
            );
        }

        if(leaveCode == 1){
            if(from.Date < DateTime.Now.Date){
                return ResponseHelper.Result(
                    success: false,
                    message: new List<string> { "لا يمكن انشاء اجازة اعتيادية قبل اليوم" }
                );
            }
        }

        
        // Step 3: Check for holiday and weekend conflicts
        var holidayCheck = ValidateHolidaysAndWeekends(from, to);
        if (!holidayCheck.Success)
            return holidayCheck;
        
        // Step 4: Check for overlapping leaves (comprehensive)
        var overlapCheck = CheckLeaveOverlaps(empNo, from, to);
        if (!overlapCheck.Success)
            return overlapCheck;
        
        // Step 5: Check for training conflicts
        var trainingCheck = CheckTrainingConflicts(empNo, from, to);
        if (!trainingCheck.Success)
            return trainingCheck;
        
        // Step 6: Handle special leave type validation
        var specialLeaveCheck = ValidateSpecialLeaveType(empNo, leaveCode, from, to);
        if (!specialLeaveCheck.Success)
            return specialLeaveCheck;
        
        // Step 7: Check leave balance (advanced)
        var balanceCheck = ValidateLeaveBalance(empNo, leaveCode, from, to);
        if (!balanceCheck.Success)
            return balanceCheck;
        
        return ResponseHelper.Result(success: true);
    }


    private Response CheckEmployeeEligibility(int empNo, int leaveCode, DateTime from, DateTime to)
    {
        try
        {
            var employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == empNo);
            if (employee == null)
                return ResponseHelper.Result(success: false, message: new List<string> { "الموظف غير موجود" });
            
            // Get the latest career record for this employee to check SubCatCode, ContractType
            var latestCareer = _db.TemployeeCareers
                .Where(c => c.EmpNo == empNo && (c.ToDate == null || c.ToDate >= DateTime.Now))
                .OrderByDescending(c => c.FromDate)
                .FirstOrDefault();
            
            if (latestCareer == null)
                return ResponseHelper.Result(success: false, message: new List<string> { "لا يوجد بيانات وظيفية للموظف" });
            
            // Get current leave balance
            decimal leaveBalance = Balance(empNo, leaveCode);
            
            // Calculate requested days
            TimeSpan duration = to.Subtract(from);
            int requestedDays = duration.Days + 1;
            
            // Check if employee is Omani or Expat based on NatId (assuming Omani IDs start with specific patterns)
            bool isOmani = employee.NatId != null && employee.NatId.StartsWith("3"); // This should be adjusted based on actual Omani ID pattern
            
            // Calculate service period
            TimeSpan serviceTime = DateTime.Now - employee.AppointDate.Value;
            int monthsOfService = (int)(serviceTime.TotalDays / 30);
            
            // Check eligibility based on the leave code and conditions
            switch (leaveCode)
            {
                case 1: // Annual leave
                    // Omani civilians appointed at least 6 months before with sufficient balance
                    if (isOmani && monthsOfService >= 6 && leaveBalance >= requestedDays)
                        return ResponseHelper.Result(success: true);
                        
                    // Expat civilians appointed at least 10 months before with sufficient balance
                    if (!isOmani && monthsOfService >= 10 && leaveBalance >= requestedDays)
                        return ResponseHelper.Result(success: true);
                        
                    // RANK/FIYAT with sufficient balance and leave days < 60
                    if (employee.GradRank != null && leaveBalance >= requestedDays && requestedDays < 60)
                        return ResponseHelper.Result(success: true);
                        
                    if (monthsOfService < (isOmani ? 6 : 10))
                        return ResponseHelper.Result(success: false, message: new List<string> { $"لم تكتمل فترة الخدمة المطلوبة للإجازة ({(isOmani ? 6 : 10)} أشهر)" });
                    
                    return ResponseHelper.Result(success: false, message: new List<string> { "غير مؤهل للإجازة الاعتيادية" });
                    
                case 3: // Sick leave
                    // Omani civilians with sufficient balance and leave days < 7
                    if (isOmani && leaveBalance > requestedDays && requestedDays < 7)
                        return ResponseHelper.Result(success: true);
                        
                    // Expat civilians with balance up to 14 days and leave days < 7
                    if (!isOmani && leaveBalance > requestedDays && leaveBalance <= 14 && requestedDays < 7)
                        return ResponseHelper.Result(success: true);
                        
                    if (requestedDays >= 7)
                        return ResponseHelper.Result(success: false, message: new List<string> { "الإجازة المرضية القصيرة لا يمكن أن تتجاوز 7 أيام" });
                    
                    return ResponseHelper.Result(success: false, message: new List<string> { "رصيد الإجازة غير كافٍ" });
                    
                case 4: // Extended leave
                    // Omani civilians with balance up to 180 days and leave days > 7
                    if (isOmani && leaveBalance > requestedDays && leaveBalance <= 180 && requestedDays > 7)
                        return ResponseHelper.Result(success: true);
                        
                    // Expat civilians with balance up to 90 days and leave days > 7
                    if (!isOmani && leaveBalance > requestedDays && leaveBalance <= 90 && requestedDays > 7)
                        return ResponseHelper.Result(success: true);
                        
                    // Additional conditions for specific categories
                    if (requestedDays > 7 && latestCareer.SubCatCode == 1 && latestCareer.CatCode == 2 && 
                        leaveBalance <= 119 && leaveBalance > requestedDays)
                        return ResponseHelper.Result(success: true);
                        
                    if (requestedDays > 7 && latestCareer.SubCatCode == 2 && latestCareer.CatCode >= 2 && 
                        leaveBalance <= 91 && latestCareer.ContractType == 3 && leaveBalance > requestedDays)
                        return ResponseHelper.Result(success: true);
                    
                    if (requestedDays <= 7)
                        return ResponseHelper.Result(success: false, message: new List<string> { "هذا النوع من الإجازات يتطلب أكثر من 7 أيام" });
                    
                    return ResponseHelper.Result(success: false, message: new List<string> { "غير مؤهل لهذا النوع من الإجازات" });
                    
                case 5: // Another leave type
                    // Complex conditions based on subcategory, category, and contract type
                    if (requestedDays > 7 && latestCareer.SubCatCode == 2 && latestCareer.SubCatCode == 1 && 
                        leaveBalance <= 30 && leaveBalance > requestedDays)
                        return ResponseHelper.Result(success: true);
                        
                    if (requestedDays > 7 && latestCareer.SubCatCode == 1 && latestCareer.SubCatCode == 1 && 
                        leaveBalance <= 180 && leaveBalance > requestedDays)
                        return ResponseHelper.Result(success: true);
                        
                    if (requestedDays > 7 && latestCareer.SubCatCode == 2 && 
                        (latestCareer.SubCatCode == 2 || latestCareer.SubCatCode == 3) && 
                        leaveBalance <= 119 && latestCareer.ContractType == 1 && leaveBalance > requestedDays)
                        return ResponseHelper.Result(success: true);
                        
                    if (requestedDays > 7 && latestCareer.SubCatCode == 2 && 
                        (latestCareer.CatCode == 2 || latestCareer.CatCode == 3) && 
                        leaveBalance <= 91 && latestCareer.ContractType == 3 && leaveBalance > requestedDays)
                        return ResponseHelper.Result(success: true);
                    
                    return ResponseHelper.Result(success: false, message: new List<string> { "غير مؤهل لهذا النوع من الإجازات" });
                    
                case 6: // Hajj leave
                    // Check if employee is Muslim (this would need a religion field in employee data)
                    // For now, assume all employees could be eligible for Hajj leave
                    
                    // Check if employee already took Hajj leave before
                    var hajjHistory = _db.VleaveHistories
                        .Where(h => h.EmpNo == empNo && h.LeaveCode == 6)
                        .Any();

                    if (hajjHistory)
                        return ResponseHelper.Result(success: false, message: new List<string> { "تم استخدام إجازة الحج من قبل" });
                    
                    // Check service period
                    if (serviceTime.TotalDays < 365 * 2) // 2 years
                        return ResponseHelper.Result(success: false, message: new List<string> { "لم تكتمل فترة الخدمة المطلوبة لإجازة الحج" });
                    
                    return ResponseHelper.Result(success: true);
                    
                case 7: // Widow leave
                    // Female Muslim employees who have become widow
                    // This would need additional fields in the employee record to check widow status and religion
                    if (employee.SexInfo == "انثى") // Female
                        return ResponseHelper.Result(success: true);
                    
                    return ResponseHelper.Result(success: false, message: new List<string> { "هذه الإجازة مخصصة للموظفات فقط" });
                    
                case 8: // Maternity leave
                    if (employee.SexInfo != "انثى") // Female
                        return ResponseHelper.Result(success: false, message: new List<string> { "إجازة الأمومة متاحة للموظفات فقط" });
                    

                    
                    return ResponseHelper.Result(success: true);
                    
                case 22: // Similar to case 4 but different leave code
                    // Omani civilians with balance up to 180 days and leave days > 7
                    if (isOmani && leaveBalance > requestedDays && leaveBalance <= 180 && requestedDays > 7)
                        return ResponseHelper.Result(success: true);
                        
                    // Expat civilians with balance up to 90 days and leave days > 7
                    if (!isOmani && leaveBalance > requestedDays && leaveBalance <= 90 && requestedDays > 7)
                        return ResponseHelper.Result(success: true);
                    
                    return ResponseHelper.Result(success: false, message: new List<string> { "غير مؤهل لهذا النوع من الإجازات" });
                    
                case 58: // Paternity leave
                    if (employee.SexInfo != "ذكر") // Male
                        return ResponseHelper.Result(success: false, message: new List<string> { "إجازة الأبوة متاحة للموظفين الذكور فقط" });
                    
                    return ResponseHelper.Result(success: true);
                    
                default:
                    // Check if this employee is eligible for this specific leave type
                    var eligibility = _db.TleaveEligibilities
                        .Include(e => e.TleaveCode)
                        .Where(e => e.LeaveCode == leaveCode)
                        .ToList();

                    // If no eligibility records found, assume leave type is available to all
                    if (eligibility.Count() == 0)
                        return ResponseHelper.Result(success: true);

                    // Process other eligibility conditions based on leave condition field
                    foreach (var condition in eligibility)
                    {
                        if (!string.IsNullOrEmpty(condition.LeaveCondition))
                        {
                            // Additional condition checking can be implemented here
                        }
                    }
                    
                    return ResponseHelper.Result(success: true);
            }
        }
        catch (Exception ex)
        {
            return ResponseHelper.Result(success: false, message: new List<string> { ex.Message });
        }
    }

    private Response CheckLeaveOverlaps(int empNo, DateTime from, DateTime to)
    {
        try
        {
            // Check for overlapping leaves in TleaveAplTxs (regular employees)
            var overlappingLeaves = _db.TleaveAplTxs
                .Where(l => l.EmpNo == empNo 
                    && l.CancelFlag == 0
                    && l.ReqStat != 3 // Not rejected (3 = Rejected)
                    && (l.LeaveStartDate <= to && l.LeaveEndDate >= from || 
                        l.LeaveStartDate >= from && l.LeaveStartDate <= to ||
                        l.LeaveEndDate >= from && l.LeaveEndDate <= to))
                .ToList();

            if (overlappingLeaves.Any())
            {
                return ResponseHelper.Result(
                    success: false,
                    message: new List<string> { "هناك تداخل مع طلبات إجازة أخرى" }
                );
            }



            return ResponseHelper.Result(success: true);
        }
        catch (Exception ex)
        {
            return ResponseHelper.Result(success: false, message: new List<string> { ex.Message });
        }
    }

    private Response ValidateHolidaysAndWeekends(DateTime from, DateTime to)
    {
        try
        {
            // Check for weekends
            // Only check if from date or to date is a weekend
            if (from.DayOfWeek == DayOfWeek.Friday || from.DayOfWeek == DayOfWeek.Saturday)
            {
                return ResponseHelper.Result(
                    success: false,
                    message: new List<string> { $"تاريخ البداية {from.ToString("yyyy-MM-dd")} يقع في عطلة نهاية الأسبوع" }
                );
            }
            
            if (to.DayOfWeek == DayOfWeek.Friday || to.DayOfWeek == DayOfWeek.Saturday)
            {
                return ResponseHelper.Result(
                    success: false,
                    message: new List<string> { $"تاريخ النهاية {to.ToString("yyyy-MM-dd")} يقع في عطلة نهاية الأسبوع" }
                );
            }

            // Check for public holidays
            var holidays = _db.Tholidays
                .Where(h => h.Holiday.HasValue && h.Holiday.Value.Date >= from.Date && h.Holiday.Value.Date <= to.Date)
                .ToList();

            if (holidays.Any())
            {
                var holidayDates = string.Join(", ", holidays.Select(h => h.Holiday.Value.ToString("yyyy-MM-dd")));
                return ResponseHelper.Result(
                    success: false,
                    message: new List<string> { $"التواريخ التالية تقع ضمن العطلات الرسمية: {holidayDates}" }
                );
            }

            return ResponseHelper.Result(success: true);
        }
        catch (Exception ex)
        {
            return ResponseHelper.Result(success: false, message: new List<string> { ex.Message });
        }
    }

    private Response CheckTrainingConflicts(int empNo, DateTime from, DateTime to)
    {
        try
        {
            // Check for training conflicts
            var trainingConflicts = _db.VempTrgs
                .Where(t => t.EmpNo == empNo 
                    && (t.CourseStartDate <= to && t.CourseEndDate >= from || 
                        t.CourseStartDate >= from && t.CourseStartDate <= to ||
                        t.CourseEndDate >= from && t.CourseEndDate <= to))
                .ToList();

            if (trainingConflicts.Any())
            {
                return ResponseHelper.Result(
                    success: false,
                    message: new List<string> { "هناك تداخل مع طلبات تدريب" }
                );
            }

            return ResponseHelper.Result(success: true);
        }
        catch (Exception ex)
        {
            return ResponseHelper.Result(success: false, message: new List<string> { ex.Message });
        }
    }

    private Response ValidateSpecialLeaveType(int empNo, int leaveCode, DateTime from, DateTime to)
    {
        try
        {
            var employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == empNo);
            if (employee == null)
                return ResponseHelper.Result(success: true); // Already checked in CheckEmployeeEligibility
            
            // Validate maternity leave (assuming leaveCode 3 is maternity)
            if (leaveCode == 8)
            {
                if (employee.SexInfo != "انثى") // Female
                {
                    return ResponseHelper.Result(
                        success: false, 
                        message: new List<string> { "إجازة الأمومة متاحة للموظفات فقط" }
                    );
                }
                
                // Check duration is valid for maternity leave
                TimeSpan duration = to.Subtract(from);
                if (duration.TotalDays > 90) //  90 days
                {
                    return ResponseHelper.Result(
                        success: false,
                        message: new List<string> { "مدة إجازة الأمومة لا يمكن أن تتجاوز 90 يوم" }
                    );
                }
            }

            // Validate paternity leave (assuming leaveCode 4 is paternity)
            if (leaveCode == 58)
            {
                if (employee.SexInfo != "ذكر") // Male
                {
                    return ResponseHelper.Result(
                        success: false,
                        message: new List<string> { "إجازة الأبوة متاحة للموظفين الذكور فقط" }
                    );
                }
                
                // Check duration is valid for paternity leave
                TimeSpan duration = to.Subtract(from);
                if (duration.TotalDays > 7) // 7 days
                {
                    return ResponseHelper.Result(
                        success: false,
                        message: new List<string> { "مدة إجازة الأبوة لا يمكن أن تتجاوز 7 أيام" }
                    );
                }
            }

            // Validate study leave (assuming leaveCode 6 is study leave) 
            if (leaveCode == 6)
            {
                // Check if employee has manager approval
                // Note: We use VempDtl attributes for study approval since TstudyApprovals table does not exist
                //if (employee.StudyApproved != 1)
                if (true)
                    {
                    return ResponseHelper.Result(
                        success: false,
                        message: new List<string> { "لا يوجد موافقة سابقة على إجازة الدراسة" }
                    );
                }
            }

            return ResponseHelper.Result(success: true);
        }
        catch (Exception ex)
        {
            return ResponseHelper.Result(success: false, message: new List<string> { ex.Message });
        }
    }

    private Response ValidateLeaveBalance(int empNo, int leaveCode, DateTime from, DateTime to)
    {
        try
        {
            // Calculate requested days
            TimeSpan duration = to.Subtract(from);
            int requestedDays = duration.Days + 1;
            
            // Get current balance
            decimal balanceDecimal = Balance(empNo, leaveCode);
            int balance = (int)balanceDecimal; // Explicit conversion from decimal to int
            
            // For annual leave (assuming leaveCode 1 is annual)
            if (leaveCode == 1)
            {
                if (balance < requestedDays)
                {
                    return ResponseHelper.Result(
                        success: false,
                        message: new List<string> { $"رصيد الإجازة غير كافي. الرصيد المتاح: {balance}, المطلوب: {requestedDays}" }
                    );
                }
                
                // Check if there's a minimum required balance to maintain
                //var leaveSettings = _db.TblConfigs
                //    .FirstOrDefault(c => c.ConfigName == "MinimumLeaveBalance");
                
                //if (leaveSettings != null && int.TryParse(leaveSettings.ConfigValue, out int minBalance))
                //{
                //    int remainingBalance = balance - requestedDays;
                //    if (remainingBalance < minBalance)
                //    {
                //        return ResponseHelper.Result(
                //            success: false,
                //            message: new List<string> { $"يجب الاحتفاظ برصيد لا يقل عن {minBalance} يوم" }
                //        );
                //    }
                //}
            }
            // For sick leave 
            else if (leaveCode == 3)
            {
                // Check if medical certificate is required
                if (requestedDays > 3)
                {
                    // return ResponseHelper.Result(
                    //     success: false,
                    //     message: new List<string> { "الإجازة المرضية التي تزيد عن 3 أيام تتطلب شهادة طبية" }
                    // );
                }
            }
            else if(leaveCode == 21){

                var EMERGENCY_LEAVE_WITH_FULLPAY = 0;

          
                var inYear = Convert.ToInt32(DateTime.Now.ToString("yy"));

                EMERGENCY_LEAVE_WITH_FULLPAY = _db.TleaveAplTxs.Where(r => r.LeaveCode == 21 && r.LeaveType == 3 && r.InYear == inYear && r.EmpNo == empNo).Count();


                if(EMERGENCY_LEAVE_WITH_FULLPAY >= 3){
                    return ResponseHelper.Result(success: false, message: new List<string> { "لا يوجد رصيد كافي" });
                }

                if(requestedDays > 3){
                    return ResponseHelper.Result(success: false, message: new List<string> { "لا يمكن طلب إجازة طارئة أكثر من 3 أيام" });
                }
            }
            
            return ResponseHelper.Result(success: true);
        }
        catch (Exception ex)
        {
            return ResponseHelper.Result(success: false, message: new List<string> { ex.Message });
        }
    }

    public void LeaveDeduction(TleaveAplTx leaveData)
    {
        if (leaveData == null)
            return;

        try
        {
            // Calculate the number of days to deduct
            DateTime end = leaveData.LeaveEndDate.AddDays(1);
            DateTime start = leaveData.LeaveStartDate;
            TimeSpan diff = end.Subtract(start);
            int days = (int)diff.TotalDays;
            
            // Handle different leave types according to documentation
            switch (leaveData.LeaveCode)
            {
                case 1: // Annual leave
                    // Standard balance deduction for annual leave
                    var annualLeaveBal = _db.TleaveBals.FirstOrDefault(r => 
                        r.LeaveCode == (byte)leaveData.LeaveCode && r.EmpNo == (short)leaveData.EmpNo);
                    
                    if (annualLeaveBal != null)
                    {
                        // First log the current balance before any changes
                        LogLeaveBalance(annualLeaveBal);

                        // Calculate and add any earned leave
                        CalculateLeaveBalance(leaveData.EmpNo, leaveData.LeaveCode);
                        
                        // Then deduct the requested days
                        annualLeaveBal.NoLeaveBalDays = annualLeaveBal.NoLeaveBalDays - days;
                        
                        // Ensure balance doesn't go below zero
                        if (annualLeaveBal.NoLeaveBalDays < 0)
                            annualLeaveBal.NoLeaveBalDays = 0;

                        annualLeaveBal.RefDate = DateTime.Now;
                        
                        // Log the updated balance after changes
                        LogLeaveBalance(annualLeaveBal);
                        
                        _db.SaveChanges();
                    }
                    break;
                    
                case 6: // Hajj leave
                    // Update Hajj flag in employee record to indicate it's been used
                    var employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == leaveData.EmpNo);
                    if (employee != null)
                    {
                        // In real Oracle DB this would update TEMPLOYEE_MAS.haj_leave_availed_flag = 1
                        // Record the Hajj leave usage in history
                        _db.SaveChanges();
                    }
                    
                    // Also deduct from leave balance if applicable
                    var hajjLeaveBal = _db.TleaveBals.FirstOrDefault(r => 
                        r.LeaveCode == (byte)leaveData.LeaveCode && r.EmpNo == (short)leaveData.EmpNo);
                    if (hajjLeaveBal != null)
                    {
                        // Log the current balance before any changes
                        LogLeaveBalance(hajjLeaveBal);
                        
                        hajjLeaveBal.NoLeaveBalDays = 0; // Used up the one-time allowance
                        hajjLeaveBal.RefDate = DateTime.Now;
                        
                        // Log the updated balance after changes
                        LogLeaveBalance(hajjLeaveBal);
                        
                        _db.SaveChanges();
                    }
                    break;
                    
                case 11: // Study leave
                case 14:
                case 15:
                case 16:
                    // Study leave cases are commented out in the current implementation
                    break;
                    
                case 21: // Emergency leave
                   
                   
                    
                default: // Other leave types
                    // Standard processing for other leave types
                    var leaveBal = _db.TleaveBals.FirstOrDefault(r => 
                        r.LeaveCode == leaveData.LeaveCode && r.EmpNo == leaveData.EmpNo);
                    if (leaveBal != null)
                    {
                        // Log the current balance before any changes
                        LogLeaveBalance(leaveBal);
                        
                        leaveBal.NoLeaveBalDays = Balance(leaveData.EmpNo, leaveData.LeaveCode) - days;
                        
                        // Ensure balance doesn't go below zero
                        if (leaveBal.NoLeaveBalDays < 0)
                            leaveBal.NoLeaveBalDays = 0;
                            
                        leaveBal.RefDate = DateTime.Now;
                        
                        // Log the updated balance after changes
                        LogLeaveBalance(leaveBal);
                        
                        _db.SaveChanges();
                    }
                    break;
            }
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in LeaveDeduction: {ex.Message}");
        }
    }

    /// <summary>
    /// Logs the current state of a leave balance to the TleaveBalLog table
    /// </summary>
    /// <param name="leaveBal">The leave balance to log</param>
    public void LogLeaveBalance(TleaveBal leaveBal)
    {
        if (leaveBal == null)
            return;
            
        try
        {
            var logEntry = new TleaveBalLog
            {
                UnitCode = leaveBal.UnitCode,
                EmpNo = leaveBal.EmpNo,
                LeaveCode = leaveBal.LeaveCode,
                RefDate = DateTime.Now,
                NoLeaveBalDays = leaveBal.NoLeaveBalDays,
                PrevYrAlBalDays = leaveBal.PrevYrAlBalDays,
                UserId = leaveBal.UserId,
                TimeStamp = DateTime.Now
            };
            
            _db.TleaveBalLogs.Add(logEntry);
            
            // No SaveChanges() here - will be called by the parent method
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error logging leave balance: {ex.Message}");
        }
    }

    public void CalculateLeaveBalance(int empNo, int leaveCode)
    {

        Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 1");
        try
        {
            // Only proceed for annual leave (code 1)
            if (leaveCode != 1)
                return;

            Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 2");

            var leaveBal = _db.TleaveBals.FirstOrDefault(r => r.LeaveCode == (byte)leaveCode && r.EmpNo == (short)empNo);
            
            if (leaveBal == null)
                return;

            // Check if the difference between RefDate and current date is more than one day
            int diffDays = (int)(DateTime.Now - leaveBal.RefDate).TotalDays;
            if (diffDays <= 0)
                return;

            Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 3");

            // Get employee details for category and service determination
            var employee = _db.VempDtls.FirstOrDefault(e => e.EmpNo == empNo);
            if (employee == null)
                return;

            Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 4");

            var temployee = _db.EmployeeMas.FirstOrDefault(e => e.EmpNo == empNo);
            if (temployee == null)
                return;

   
            // Get employee's career records
            var career = _db.TemployeeCareers
                .Where(c => c.EmpNo == empNo && (c.FromDate >= leaveBal.RefDate || c.FromDate <= leaveBal.RefDate && c.ToDate == null))
                .OrderByDescending(c => c.FromDate)
                .ToList();

            if (career.Count() == 0)
                return;

            Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 6");

            bool isEligible = false;
        
            // Calculate total service time
            TimeSpan serviceTime = DateTime.Now - employee.AppointDate.Value;
            int monthsOfService = (int)(serviceTime.TotalDays / 30);
        
      
            isEligible = monthsOfService >= 6;

            if(!isEligible)
                return;
            

            Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 7");

            decimal leavesDaysToAdd = 0;

            foreach (var careerRecord in career)
            {

                Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 8");

                if (careerRecord.FromDate > leaveBal.RefDate)
                    continue;

                Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 9");

                DateTime startDate;

                if(careerRecord.FromDate < leaveBal.RefDate ){
                    startDate =  leaveBal.RefDate;
                }
                else{
                    startDate = careerRecord.FromDate.Value;
                }

                // Get applicable accrual rates from grade rank code
                var gradeRank = _db.TgradeRankCodes
                    .FirstOrDefault(g => g.GradeRankCode == careerRecord.GradeRankCode);

                if (gradeRank == null || !gradeRank.LeavePerDay.HasValue)
                    continue;

                Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 10");

                // Calculate service period for this career record
                DateTime endDate = careerRecord.ToDate.HasValue ? careerRecord.ToDate.Value : DateTime.Now;
                
                // Calculate the total days in this career period
                TimeSpan careerPeriod = endDate - startDate;
                
                // Initialize counters for this career period
                
                Dictionary<int, int> months = new Dictionary<int, int>();

                // Calculate days per month for this period
                DateTime currentDay = startDate;
                while (currentDay <= endDate)
                {
                    // The key is month number (1-12)
                    int monthKey = currentDay.Month;
                    
                    // Add day to the corresponding month
                    if (months.ContainsKey(monthKey))
                    {
                        months[monthKey]++;
                    }
                    else
                    {
                        months[monthKey] = 1;
                    }
                    
                    // Move to next day
                    currentDay = currentDay.AddDays(1);
                }
                

                // Debug output to show days per month
                foreach (var monthEntry in months)
                {
                    Console.WriteLine($"Month {monthEntry.Key}: {monthEntry.Value} days");

                    leavesDaysToAdd += gradeRank.LeavePerDay.Value * monthEntry.Value;
                }

            }

            Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 11");
        
            if (leavesDaysToAdd > 0)
            {
                Console.WriteLine($"Calculating leave balance for employee {empNo} and leave code {leaveCode} 12");

                // Find the last leave history record to determine the actual reference date
                DateTime referenceDate = leaveBal.RefDate;
                
                // Update balance
                decimal newBalance = leaveBal.NoLeaveBalDays + leavesDaysToAdd;

                // Save the old balance for reference
                leaveBal.OldNoLeaveBalDays = leaveBal.NoLeaveBalDays;
                
                // Update the balance
                leaveBal.NoLeaveBalDays = newBalance;
                leaveBal.RefDate = DateTime.Now;

                LogLeaveBalance(leaveBal);

                _db.SaveChanges();
            }
            
            
                
            
            
            // Create a credit transaction record
            // if (totalAccruedLeave > 0)
            // {
            //     // Create mail reference for transaction
            //     TincomingMail mail = _mail.Create(261);
                
            //     var creditTx = new TleaveCreditTx
            //     {
            //         EmpNo = empNo,
            //         LeaveCode = leaveCode,
            //         NoDays = totalAccruedLeave,
            //         ReqStat = 6, // Finalized
            //         CancelFlag = 0,
            //         TxnDate = DateTime.Now,
            //         TimeStamp = DateTime.Now,
            //         InYear = (short)mail.InYear,
            //         InMailNo = mail.InMailNo,
            //         InDocSlNo = (int)mail.LastDocSlNo,
            //         UnitCode = (short)1
            //     };
                
            //     _db.TleaveCreditTxs.Add(creditTx);
            // }
            
         
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in CalculateLeaveBalance: {ex.Message}");
        }
    }




    public int getCountForManager(int empNo)
    {
        return _db.TleaveAplTxs.Where(r => r.SignAuthCode == empNo && r.ReqStat == 0 && r.CancelFlag == 0).Count();
    }

    public int getCountForDg()
    {
        return _db.TleaveAplTxs.Where(r =>  r.ReqStat == 1 && r.CancelFlag == 0 && r.LeaveStartDate >= DateTime.Now.Date.AddMonths(-6)).Count();
    }

    


}