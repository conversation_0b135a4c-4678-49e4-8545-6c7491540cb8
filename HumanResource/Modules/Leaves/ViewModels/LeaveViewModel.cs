﻿using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Attendence.Models.Entities;
using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.DTOs;
using HumanResource.Modules.Shared.ViewModels;


namespace HumanResource.Modules.Leaves.ViewModels;

public class LeaveViewModel : BaseViewModel
{

    public enum LeaveStatus
    {
        Pending = 0,
        Approved = 1,
        Generated = 4,
        Prepared = 5,
        Rejected = 3,
        Finalized = 6
    }

    public LeaveViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {


    }


    public List<Tholidays> Holidays { get; set; }
    public Tholidays Holiday { get; set; }

    public List<FPRecords> FPRecords { get; set; }

    public string Month { get; set; } = DateTime.Now.ToString("yyyy-MM");



    public List<Workdays> WorkingHours { get; set; }


    public List<LeavesDtl> Leaves { get; set; }
    public List<Absent> Absents { get; set; }
    public List<VleaveHistory> VleaveHistories { get; set; }

    public List<ConLeaveAplTx> conLeaveApls { get; set; }
    public List<ConLeaveBal> conLeaveBals { get; set; }
    public List<TleaveAplTx> TleaveAplTxs { get; set; }
    public TleaveAplTx TleaveAplTx { get; set; }
    public List<TleaveAplDtl> TleaveAplDtls { get; set; }
    public List<TleaveBal> TleaveBals { get; set; }

    public List<TleaveEApp> tleaveEApps { get; set; }
    public List<TleaveCode> LeaveCodes { get; set; }

    public List<TleaveBalAdjustmentTx> LeaveBalAdjustments { get; set; }
    public TleaveBalAdjustmentTx LeaveBalAdjustment { get; set; }

    // Leave Return properties
    public List<TleaveReturnTx> TleaveReturnTxs { get; set; }
    public TleaveReturnTx TleaveReturnTx { get; set; }

    // Fingerprint data for leave period
    public List<EmpWorkHours> LeaveFingerprints { get; set; }

    public List<LeavesDtl> GetLeavesByDate(int EmpN, DateOnly Day)
    {

        TimeOnly time = TimeOnly.FromDateTime(DateTime.Now);
        DateTime date1 = Day.ToDateTime(TimeOnly.MinValue);
        DateTime date2 = Day.ToDateTime(TimeOnly.MinValue);

        string sdate1 = date1.ToString("yyyy-MM-dd");
        string sdate2 = date2.ToString("yyyy-MM-dd");
        string EmpN2 = EmpN.ToString();

        //string sql = $"SELECT * FROM VLEAVE_DTL " +
        //    $"WHERE (EMP_NO = {EmpN2} OR EMP_NO=0) " +
        //    $"AND FROM_TIME <= TO_DATE('{sdate1}','YYYY-MM-DD') " +
        //    $"AND TO_TIME >= TO_DATE('{sdate2}', 'YYYY-MM-DD')";




        //return _db.LeavesDtl.FromSqlRaw(sql).ToList();
        return _db.LeavesDtl.AsNoTracking().Where(r => (r.EmpNo == EmpN || r.EmpNo == 0) && r.FromTime.Date <= date1.Date && r.ToTime.Date >= date2.Date).ToList();
    }

    // req_stat NUMBER(2) - Request status (0=Pending, 1=Approved, 3=Rejected, 6=Finalized)
    public string renderLeaveSatus(int? req_stat)
    {
        if (req_stat == null)
            return "<span class=\"badge bg-secondary\">Unknown</span>";
            
        switch (req_stat)
        {
            case (int)LeaveStatus.Pending:
                return "<span class=\"badge bg-warning text-white\">معلق <i class=\"fas fa-clock\"></i></span>";
            case (int)LeaveStatus.Approved:
                return "<span class=\"badge bg-success\">موافق عليه <i class=\"fas fa-check\"></i></span>";
            case (int)LeaveStatus.Rejected:
                return "<span class=\"badge bg-danger\">مرفوض <i class=\"fas fa-times\"></i></span>";
            case (int)LeaveStatus.Generated:
                return "<span class=\"badge bg-info\">جاهز <i class=\" fas fa-exclamation \"></i></span>";
            case (int)LeaveStatus.Prepared:
                return "<span class=\"badge bg-info\">تم التحضير <i class=\" fas fa-exclamation \"></i></span>";
            case (int)LeaveStatus.Finalized:
                return "<span class=\"badge bg-primary\">مكتمل <i class=\"fas fa-check-double\"></i></span>";
            default:
                return "<span class=\"badge bg-secondary\">Unknown</span>";
        }
    }

    public List<EmpLeaveCodeBal> GetEmpLeaveCodeBals(int empNo)
    {
        List<EmpLeaveCodeBal> Balances = new List<EmpLeaveCodeBal>();

        // Get all leave codes from the database
        var leaveCodes = _db.TleaveCodes.ToList();

        // Process each leave code
        foreach (var leaveCode in leaveCodes)
        {
            // Get the balance for this leave type
            decimal balance;
            
            try
            {
                balance = _h.Leave().Balance(empNo, leaveCode.LeaveCode);
            }
            catch
            {
                // If there's an error getting the balance, set it to 0
                balance = 0;
            }

            Balances.Add(new EmpLeaveCodeBal
            {
                LeaveCode = leaveCode.LeaveCode,
                LeaveName = leaveCode.LeaveDespA,
                Balance = balance
            });
        }

        return Balances;
    }

    public List<EmpLeaveCodeBal> GetEmpLeaveCodeBals()
    {
        return GetEmpLeaveCodeBals(Profile.EmpNo.Value);
    }

    /// <summary>
    /// Get fingerprint data (work hours) for an employee during a specific date range
    /// </summary>
    /// <param name="empNo">Employee number</param>
    /// <param name="startDate">Start date of the period</param>
    /// <param name="endDate">End date of the period</param>
    /// <returns>List of EmpWorkHours records</returns>
    public List<EmpWorkHours> GetFingerprintData(int empNo, DateTime startDate, DateTime endDate)
    {
        try
        {
            return _db.EmpWorkHours
                .Where(w => w.EmpNo == empNo && 
                           w.Day.Date >= startDate.Date && 
                           w.Day.Date <= endDate.Date)
                .OrderBy(w => w.Day)
                .ToList();
        }
        catch
        {
            return new List<EmpWorkHours>();
        }
    }

    /// <summary>
    /// Get fingerprint data for the current leave return period
    /// </summary>
    /// <returns>List of EmpWorkHours records for the leave period</returns>
    public List<EmpWorkHours> GetLeaveReturnFingerprints(TleaveReturnTx TleaveReturnTx)
    {
        if (TleaveReturnTx == null)
            return new List<EmpWorkHours>();

        // Use PLeaveStartDate and PLeaveEndDate (Previous/Planned leave dates)
        // These represent the original leave period dates
        var startDate = TleaveReturnTx.PLeaveStartDate;
        var endDate = TleaveReturnTx.PLeaveEndDate;

        // If we don't have the planned dates, fall back to actual dates
        if (!startDate.HasValue)
        {
            startDate = TleaveReturnTx.LeaveStartDate == default ? (DateTime?)null : TleaveReturnTx.LeaveStartDate;
        }

        if (!endDate.HasValue)
        {
            endDate = TleaveReturnTx.LeaveEndDate;
        }

        // If we still don't have valid dates, return empty list
        if (!startDate.HasValue)
            return new List<EmpWorkHours>();

        // Use start date as end date if end date is not available
        var finalEndDate = endDate ?? startDate.Value;

        return GetFingerprintData(TleaveReturnTx.EmpNo, startDate.Value, finalEndDate);
    }
}


public class EmpLeaveCodeBal
{
    public int LeaveCode { get; set; }
    public string LeaveName { get; set; }
    public decimal Balance { get; set; }
}





