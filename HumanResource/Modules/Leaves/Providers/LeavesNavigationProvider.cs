using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Leaves.Providers
{
    /// <summary>
    /// Navigation provider for the Leaves module
    /// </summary>
    public class LeavesNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Leaves";
        public int Priority => 20;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // HR Department navigation items
            var hrNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "hr_leaves",
                    Label = "الاجازات",
                    Active = "hr_leaves",
                    Icon = "<i class=\"fa fa-calendar-alt\"></i>",
                    Rights = new List<Right> { Right.LeavesDepartment },
                    Priority = 10,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "Leaves",
                            Label = "الاجازات",
                            Rights = new List<Right> { Right.LeavesDepartment },
                            Url = "/Leaves/Department"
                        },
                        new NavLink
                        {
                            Name = "Holiday",
                            Label = "الاجازات الرسمية",
                            Rights = new List<Right> { Right.LeavesDepartment },
                            Url = "/Leaves/Holiday"
                        },
                        new NavLink
                        {
                            Name = "Adjustment",
                            Label = "تعديل الرصيد",
                            Rights = new List<Right> { Right.LeavesDepartment },
                            Url = "/Leaves/Adjustment"
                        },
                        new NavLink
                        {
                            Name = "Return",
                            Label = "قطع الاجازة",
                            Rights = new List<Right> { Right.LeavesDepartment },
                            Url = "/Leaves/Department/Return"
                        },
                        new NavLink
                        {
                            Name = "Report",
                            Label = "تقرير الاجازات",
                            Rights = new List<Right> { Right.LeavesDepartment },
                            Url = "/Leaves/Report"
                        }
                    }
                }
            };

        
            // Add navigation items to their respective groups
            navigation[NavigationGroup.HR] = hrNavItems;
            // navigation[NavigationGroup.Approvals] = approvalNavItems;
            // navigation[NavigationGroup.SelfService] = selfServiceNavItems;

            return navigation;
        }
    }
} 