using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.Data;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Modules.Shared.Models.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Leaves.Providers
{
    /// <summary>
    /// Task provider for the Leaves module
    /// </summary>
    public class LeavesTaskProvider : ITaskProvider
    {
        private readonly hrmsContext _context;
        private readonly AppHelper _appHelper;

        public string ModuleName => "Leaves";

        public LeavesTaskProvider(hrmsContext context, AppHelper appHelper)
        {
            _context = context;
            _appHelper = appHelper;
        }

        public async Task<List<UserTask>> GetTasksAsync()
        {
            var tasks = new List<UserTask>();

            try
            {
                // Get current user info
                var currentUser = _appHelper.Auth();
                if (currentUser?.EmpNo == null)
                {
                    return tasks;
                }

                // Department manager tasks
                if (_appHelper.Can("department-manager"))
                {
                    var leaves = _appHelper.Leave().getCountForManager(currentUser.EmpNo.Value);
                    if (leaves > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "رئيس القسم",
                            Text = $"اجازات ({leaves})",
                            Url = "/Leaves/Manager",
                            Priority = TaskPriority.Medium,
                        });
                    }
                }

                if (_appHelper.Can(Right.LeavesDG))
                {
                    var leaves = _context.TleaveAplTxs.Where(r => (r.ReqStat == 5 || r.ReqStat == 1) && r.CancelFlag == 0 && r.LeaveStartDate >= DateTime.Now.Date.AddMonths(-6)).Count();
                    if (leaves > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "المدير العام",
                            Text = $"اعتماد اجازات ({leaves})",
                            Url = "/Leaves/DG",
                            Priority = TaskPriority.Medium,
                        });
                    }
                }


                if (_appHelper.Can("leaves-department"))
                {
                    var leaves = _context.TleaveAplTxs.Where(r => (r.ReqStat == 4 || r.ReqStat == 1) && r.CancelFlag == 0 && r.LeaveStartDate >= DateTime.Now.Date.AddMonths(-6)).Count();
                    if (leaves > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "مهام القسم",
                            Text = $"اعتماد اجازات ({leaves})",
                            Url = "/Leaves/Department",
                            Priority = TaskPriority.Medium,
                        });
                    }


                    var leaveReturns = _context.TleaveReturnTxs.Where(r => (r.ReqStat == 4 || r.ReqStat == 1) && r.CancelFlag == 0 && r.LeaveStartDate >= DateTime.Now.Date.AddMonths(-6)).Count();
                    if (leaveReturns > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "مهام القسم",
                            Text = $"قطع اجازات ({leaveReturns})",
                            Url = "/Leaves/Department/Return",
                            Priority = TaskPriority.Medium,
                        });
                    }
                }

                // Audit tasks for leave returns
                if (_appHelper.Can("audit"))
                {
                    var leaveReturn = await _context.TleaveReturnTxs
                        .Where(ro => ro.EmpNo > 0 
                                  && ro.CancelFlag != 1 
                                  && ro.ReqStat == 1)
                        .CountAsync();

                    if (leaveReturn > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "التدقيق",
                            Text = $"قطع اجازات ({leaveReturn})",
                            Url = "/Leaves/Audit",
                            Priority = TaskPriority.Medium,
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break task loading
                Console.WriteLine($"Error loading leaves tasks: {ex.Message}");
            }

            return tasks;
        }
    }
} 