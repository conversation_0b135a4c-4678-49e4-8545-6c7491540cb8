using HumanResource.Modules.Leaves.Providers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Leaves.Services;

namespace HumanResource.Modules.Leaves.Configuration
{
    /// <summary>
    /// Configuration for the Leaves module
    /// </summary>
    public static class LeavesConfiguration
    {
        /// <summary>
        /// Registers all Leaves module services
        /// </summary>
        public static IServiceCollection AddLeavesServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, LeavesNavigationProvider>();
    
            // Register task providers
            services.AddScoped<ITaskProvider, LeavesTaskProvider>();



            // Register module services
            services.AddScoped<LeaveService>();

            return services;
        }
    }
} 