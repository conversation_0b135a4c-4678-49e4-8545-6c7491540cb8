using HumanResource.Modules.Shared.ViewModels;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Attendence.Models.Entities;
using HumanResource.Modules.Attendence.Models.Enums;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace HumanResource.Modules.Attendence.ViewModels;

public class NoticesViewModel : BaseViewModel
{
    public NoticesViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        // Initialize default filter values
        Dg = 0;
        Dept = 0;
        EmpNo = 0;
        Status = -1; // -1 means all statuses
        PageSize = 25;

        // Initialize dropdown lists
        DgList = new List<TdgCode>();
        DeptList = new List<TDeptCode>();
        EmpList = new List<VempDtl>();
        StatusList = new List<SelectListItem>();

        // Populate status dropdown
        PopulateStatusList();
    }

    // Original properties
    public List<EmployeeWarning> EmployeeWarnings { get; set; } = new List<EmployeeWarning>();
    public EmployeeWarning EmployeeWarning { get; set; }

    // Filtering Properties
    public int Dg { get; set; } = 0;                    // DG filter (0 = All)
    public int Dept { get; set; } = 0;                  // Department filter (0 = All)
    public int EmpNo { get; set; } = 0;                 // Employee filter (0 = All)
    public int Status { get; set; } = -1;               // Status filter (-1 = All)
    public DateTime? FromDate { get; set; }             // Start date filter
    public DateTime? ToDate { get; set; }               // End date filter

    // Search Properties
    public string SearchTerm { get; set; } = string.Empty;  // General search term
    public string EmployeeSearch { get; set; } = string.Empty;  // Employee name search
    public string TitleSearch { get; set; } = string.Empty;     // Notice title search
    public string ContentSearch { get; set; } = string.Empty;   // Notice content search

    // Pagination Properties
    public int PageSize { get; set; } = 25;             // Records per page
    public int CurrentPage { get; set; } = 1;           // Current page number
    public int TotalRecords { get; set; } = 0;          // Total record count
    public int TotalPages => (int)Math.Ceiling((double)TotalRecords / PageSize);

    // Dropdown Data Lists
    public List<TdgCode> DgList { get; set; }           // DG dropdown options
    public List<TDeptCode> DeptList { get; set; }       // Department dropdown options
    public List<VempDtl> EmpList { get; set; }          // Employee dropdown options
    public List<SelectListItem> StatusList { get; set; } // Status dropdown options

    // Helper method to populate status dropdown
    private void PopulateStatusList()
    {
        StatusList = new List<SelectListItem>
        {
            new SelectListItem { Value = "-1", Text = "-- جميع الحالات --", Selected = true },
            new SelectListItem { Value = "0", Text = "جديد" },
            new SelectListItem { Value = "1", Text = "قيد المعالجة" },
            new SelectListItem { Value = "2", Text = "تم الحل" },
            new SelectListItem { Value = "3", Text = "مؤجل" },
            new SelectListItem { Value = "4", Text = "ملغي" },
            new SelectListItem { Value = "5", Text = "محذوف" }
        };
    }

    // Helper method to get status display text
    public string GetStatusText(EmployeeWarningStatus status)
    {
        return status switch
        {
            EmployeeWarningStatus.New => "جديد",
            EmployeeWarningStatus.Processing => "قيد المعالجة",
            EmployeeWarningStatus.Solved => "تم الحل",
            EmployeeWarningStatus.Bunchment => "مؤجل",
            EmployeeWarningStatus.Cancelled => "ملغي",
            EmployeeWarningStatus.Deleted => "محذوف",
            _ => "غير محدد"
        };
    }

    // Helper method to get status CSS class for styling
    public string GetStatusCssClass(EmployeeWarningStatus status)
    {
        return status switch
        {
            EmployeeWarningStatus.New => "badge bg-primary",
            EmployeeWarningStatus.Processing => "badge bg-warning",
            EmployeeWarningStatus.Solved => "badge bg-success",
            EmployeeWarningStatus.Bunchment => "badge bg-secondary",
            EmployeeWarningStatus.Cancelled => "badge bg-danger",
            EmployeeWarningStatus.Deleted => "badge bg-dark",
            _ => "badge bg-light text-dark"
        };
    }
}