using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.UI.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Attendence.Providers
{
    /// <summary>
    /// Badge provider for the Attendance module
    /// </summary>
    public class AttendanceBadgeProvider : IBadgeProvider
    {
        private readonly hrmsContext _db;
        private readonly AppHelper _appHelper;

        public string ModuleName => "Attendance";

        public AttendanceBadgeProvider(hrmsContext db, AppHelper appHelper)
        {
            _db = db;
            _appHelper = appHelper;
        }

        public async Task<Dictionary<string, int>> GetBadgesAsync()
        {
            var badges = new Dictionary<string, int>();

            try
            {
                // Get current user info
                var currentUser = _appHelper.Auth();
                if (currentUser?.EmpNo == null)
                {
                    return badges;
                }

                // Attendance warnings - employees with attendance issues
                if (_appHelper.Can("attendence|hr"))
                {
                    var today = DateTime.Today;
                    var lastWeek = today.AddDays(-7);
                    
                    // Count employees with significant attendance issues in the last week
                    var attendanceIssues = await _db.EmpWorkHours
                        .Where(w => w.Day >= lastWeek 
                                 && w.Day <= today
                                 && w.Hours < 6) // Less than 6 hours worked
                        .Select(w => w.EmpNo)
                        .Distinct()
                        .CountAsync();
                    
                    badges["Attendance.AttendanceIssues"] = attendanceIssues;
                }

                // Late arrivals - employees who arrived late today
                if (_appHelper.Can("attendence|hr"))
                {
                    var today = DateTime.Today;
                    
                    // Count employees who arrived late today (assuming 8:00 AM is standard start time)
                    var lateArrivals = await _db.EmpWorkHours
                        .Where(w => w.Day == today 
                                 && w.FirstEntry > new DateTime(today.Year, today.Month, today.Day, 8, 0, 0)) // Has late time recorded
                        .CountAsync();
                    
                    badges["Attendance.LateArrivals"] = lateArrivals;
                }

                // Missing attendance records - employees without attendance records for today
                if (_appHelper.Can("attendence|hr"))
                {
                    var today = DateTime.Today;
                    
                    // Get all active employees
                    var activeEmployees = await _db.VempDtls
                        .Where(e => e.EmpNo > 1 && e.EmpNo < 1000) // Regular employees
                        .Select(e => e.EmpNo)
                        .ToListAsync();
                    
                    // Get employees with attendance records today
                    var presentEmployees = await _db.EmpWorkHours
                        .Where(w => w.Day == today)
                        .Select(w => w.EmpNo)
                        .ToListAsync();
                    
                    // Calculate missing attendance records
                    var missingAttendance = activeEmployees.Count - presentEmployees.Count;
                    
                    badges["Attendance.MissingRecords"] = Math.Max(0, missingAttendance);
                }

            }
            catch (Exception ex)
            {
                // Log error but don't break navigation
                // TODO: Add proper logging
                Console.WriteLine($"Error calculating attendance badges: {ex.Message}");
            }

            return badges;
        }
    }
} 