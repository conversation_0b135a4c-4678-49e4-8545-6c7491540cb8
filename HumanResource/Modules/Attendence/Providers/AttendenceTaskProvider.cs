using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Modules.Shared.Models.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Attendence.Providers
{
    /// <summary>
    /// Task provider for the Attendance module
    /// </summary>
    public class AttendenceTaskProvider : ITaskProvider
    {
        private readonly hrmsContext _context;
        private readonly AppHelper _appHelper;

        public string ModuleName => "Attendence";

        public AttendenceTaskProvider(hrmsContext context, AppHelper appHelper)
        {
            _context = context;
            _appHelper = appHelper;
        }

        public async Task<List<UserTask>> GetTasksAsync()
        {
            var tasks = new List<UserTask>();

            try
            {
                // Get current user info
                var currentUser = _appHelper.Auth();
                if (currentUser?.EmpNo == null)
                {
                    return tasks;
                }

                // Attendance time tracking for current user
                var attendTime = await _context.EmpWorkHours
                    .Where(r => r.EmpNo == currentUser.EmpNo.Value 
                             && r.Day.Date == DateTime.Today.Date)
                    .FirstOrDefaultAsync();

                if (attendTime != null)
                {
                    // Check if employee is late (after 8:30 AM)
                    if (attendTime.FirstEntry > DateTime.Today.Add(new TimeSpan(8, 30, 0)))
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "تاخير",
                            Text = "تقديم استمارة تاخير",
                            Url = "/Execuses/My",
                            Priority = TaskPriority.Critical
                        });
                    }
                }
                else
                {
                    // No attendance record found for today
                    tasks.Add(new UserTask
                    {
                        Title = "الحضور",
                        Text = "لا يوجد بصمة",
                        Url = "#",
                        Priority = TaskPriority.Critical
                    });
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break task loading
                Console.WriteLine($"Error loading attendance tasks: {ex.Message}");
            }

            return tasks;
        }
    }
} 