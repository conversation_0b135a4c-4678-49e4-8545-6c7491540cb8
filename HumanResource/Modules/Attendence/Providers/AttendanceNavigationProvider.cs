using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Attendence.Providers
{
    /// <summary>
    /// Navigation provider for the Attendance module
    /// </summary>
    public class AttendanceNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Attendance";
        public int Priority => 22;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // HR Department navigation items
            var hrNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "attendence",
                    Label = "جدول العمل والحضور",
                    Active = "attendence",
                    Icon = "<i class=\"far fa-business-time\"></i>",
                    Rights = new List<Right> { Right.Attendence },
                    Priority = 20,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "AttendanceReport",
                            Label = "قائمة الحضور",
                            Rights = new List<Right> { Right.Attendence },
                            Url = "/Attendence/AttendanceReport"
                        },
                        new NavLink
                        {
                            Name = "DailyReport",
                            Label = "اليومي",
                            Rights = new List<Right> { Right.Attendence },
                            Url = "/Attendence/DailyReport"
                        },
                        new NavLink
                        {
                            Name = "MonthlyReport",
                            Label = "الشهري",
                            Rights = new List<Right> { Right.Attendence },
                            Url = "/Attendence/MonthlyNonAttendanceReport"
                        },
                        new NavLink
                        {
                            Name = "Notices",
                            Label = "الاخطارات",
                            Rights = new List<Right> { Right.Attendence },
                            Url = "/Attendence/Notices/Department"
                        }
                    }
                }
            };

            // Self-service navigation items
          
            // Add navigation items to their respective groups
            navigation[NavigationGroup.HR] = hrNavItems;
            // navigation[NavigationGroup.SelfService] = selfServiceNavItems;

            return navigation;
        }
    }
} 