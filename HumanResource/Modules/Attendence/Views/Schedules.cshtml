﻿@using HumanResource.Modules.Attendence.ViewModels
@model AttendenceViewModel

<div class="row">
    <div class="col-lg-12">
        <div class="card ">
            <div class="card-body text-end">
                <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#new-shift-modal"><i class="fa fa-plus"></i> جدول عمل جديد</button>
            </div>
            <table class="table">
                <thead class="bg-primary ts">
                    <tr>
                        <th>من</th>
                        <th>الى</th>
                        <th>الحضور</th>
                        <th>المغادرة</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var shift in Model.Shifts)
                    {
                        <tr>
                            <td>@Model._d(shift.ShiftStartDate)</td>
                            <td>@Model._d(shift.ShiftEndDate)</td>
                            <td dir="ltr">@shift.ShiftStartH:@shift.ShiftStartI</td>
                            <td dir="ltr">@shift.ShiftEndH:@shift.ShiftEndI</td>

                            <td class="text-end">
                                <a href="~/Employees/Shift/Delete/@shift.Guid" class="btn btn-danger btn-sm after-confirm"><i class="far fa-trash-alt"></i></a>
                            </td>
                        </tr>
                    }
                    @if (Model.Shifts.Count == 0)
                    {
                        <tr>
                            <td colspan="5" class="text-center text-muted">لا يوجد جدول عمل</td>
                        </tr>
                    }
                </tbody>
            </table>
            <div class="card-footer">
                <p class="text-muted"> ** ساعات العمل الافتراضي من 07:30 الى 14:30</p>
            </div>
        </div>
    </div>
</div>


<form action="~/Employees/Shift/@Model.Employee.EmpNo" method="post" class="ajax">
    <div class="modal fade" id="new-shift-modal" tabindex="-1" role="dialog" aria-labelledby="new-shift-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-shift-modalLabel">@Model._l("New")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    <div class="row">
                        <div class="col-lg-6">
                            <label>من</label>
                            <input type="date" name="ShiftStartDate" class="form-control" /> <br />
                        </div>
                        <div class="col-lg-6">
                            <label>الى</label>
                            <input type="date" name="ShiftEndDate" class="form-control" /> <br />
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <span>الحضور</span>
                            <div class="d-flex align-items-center">
                                <div>
                                    <input type="number" name="ShiftStartI" max="60" min="0" step="1" class="form-control" placeholder="دقيقة" />
                                </div>
                                <strong>:</strong>
                                <div>
                                    <input type="number" name="ShiftStartH" max="24" step="1" min="0" class="form-control" placeholder="ساعة" />
                                </div>
                            </div>
                            <br />
                        </div>
                        <div class="col-lg-6">
                            <span>المغادرة</span>
                            <div class="d-flex align-items-center">
                                <div>
                                    <input type="number" name="ShiftEndI" max="60" step="1" min="0" class="form-control" placeholder="دقيقة" />
                                </div>
                                <strong>:</strong>
                                <div>
                                    <input type="number" name="ShiftEndH" max="24" step="1" min="0" class="form-control" placeholder="ساعة" />
                                </div>
                            </div>
                            <br />
                        </div>
                    </div>






                </div>
                <div class="modal-footer d-felx">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>
</form>
