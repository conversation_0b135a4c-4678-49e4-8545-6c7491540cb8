@using HumanResource.Modules.Attendence.ViewModels
@using HumanResource.Modules.Attendence.Models.Enums
@removeTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model NoticesViewModel

@{

    var employee = ViewBag.Employee as HumanResource.Modules.Employees.Models.Entities.VempDtl;
}

<!-- [doc-ref:department-notice-management.md] -->

<style>
    .notice-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .notice-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    }
    
    .notice-list-item {
        border-left: 4px solid #007bff;
        margin-bottom: 10px;
    }
    
    .notice-detail {
        border-left: 4px solid #28a745;
    }
    
    .notice-date {
        color: #6c757d;
        font-size: 0.9em;
    }
    
    .notice-title {
        color: #495057;
        font-weight: 600;
    }
    
    .notice-body {
        color: #6c757d;
        line-height: 1.6;
    }
    
    .employee-info {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .status-badge {
        font-size: 0.9em;
        padding: 0.5rem 1rem;
    }
    
    .action-buttons {
        position: sticky;
        top: 20px;
        z-index: 100;
    }
</style>


<div class="content">
    <div class="row">
        <!-- Employee Information Panel -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                @if (employee != null)
                    {
                        <div class="text-center mb-3">
                            @if (!string.IsNullOrEmpty(employee.ProfileImage))
                            {
                                <img src="@employee.ProfileImageUrl" alt="صورة الموظف" class="rounded-circle mb-2" width="80" height="80">
                            }
                            else
                            {
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 80px; height: 80px;">
                                    <i class="icon-user text-white" style="font-size: 2rem;"></i>
                                </div>
                            }
                        </div>
                        
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td class="font-weight-semibold">رقم الموظف:</td>
                                <td>@employee.EmpNo</td>
                            </tr>
                            <tr>
                                <td class="font-weight-semibold">الاسم:</td>
                                <td>@employee.EmpNameA</td>
                            </tr>
                            <tr>
                                <td class="font-weight-semibold">المديرية:</td>
                                <td>@employee.DgDespA</td>
                            </tr>
                            <tr>
                                <td class="font-weight-semibold">القسم:</td>
                                <td>@employee.DeptDespA</td>
                            </tr>
                            <tr>
                                <td class="font-weight-semibold">المنصب:</td>
                                <td>@employee.DesgCode</td>
                            </tr>
                            <tr>
                                <td class="font-weight-semibold">البريد الإلكتروني:</td>
                                <td>@employee.EmailId</td>
                            </tr>
                        </table>
                        }
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card action-buttons">
                <div class="card-body">
                    <h6 class="card-title">الإجراءات المتاحة</h6>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success btn-block" onclick="updateStatus()">
                            <i class="icon-pencil mr-1"></i>تحديث الحالة
                        </button>
                     
                    </div>
                </div>
            </div>
        </div>

        <!-- Notice Details Panel -->
        <div class="col-md-8">
            @if (Model.EmployeeWarning != null)
            {
                <div class="card shadow notice-detail">
                    <div class="card-header py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-exclamation-triangle mr-2 text-danger"></i>
                                تفاصيل الاخطار
                            </h6>
                            <span class="@Model.GetStatusCssClass(Model.EmployeeWarning.Status) status-badge">
                                @Model.GetStatusText(Model.EmployeeWarning.Status)
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="text-muted">عنوان الاخطار</h6>
                                <p class="notice-title">@(Model.EmployeeWarning.Title ?? "تنبيه عام")</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="text-muted">تاريخ الاخطار</h6>
                                <p class="notice-date">
                                    <i class="fas fa-calendar-alt mr-1"></i>
                                    @(Model.EmployeeWarning.WarningDate?.ToString("yyyy-MM-dd") ?? "غير محدد")
                                </p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="text-muted">تاريخ الاخطار</h6>
                                <p class="notice-date">
                                    <i class="fas fa-clock mr-1"></i>
                                    @(Model.EmployeeWarning.Timestamp?.ToString("yyyy-MM-dd HH:mm") ?? "غير محدد")
                                </p>
                            </div>
                      
                            <div class="col-md-12 mb-3">
                                <h6 class="text-muted">محتوى الاخطار</h6>
                                <div class="bg-light border rounded p-3">
                                    <div class="notice-body text-dark">
                                        @if (!string.IsNullOrEmpty(Model.EmployeeWarning.Body))
                                        {
                                            @Html.Raw(Model.EmployeeWarning.Body.Replace("\n", "<br/>"))
                                        }
                                        else
                                        {
                                            <span class="text-muted">لا يوجد محتوى للتنبيه</span>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td width="150"><strong>معرف الاخطار:</strong></td>
                                            <td><small class="text-muted">@Model.EmployeeWarning.Guid</small></td>
                                        </tr>
       
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-body text-center">
                        <i class="icon-warning text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3">الاخطار غير موجود</h5>
                        <p class="text-muted">لم يتم العثور على الاخطار المطلوب أو تم حذفه.</p>
                        <a href="@Url.Action("Department", "Notices")" class="btn btn-primary">
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الاخطار</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="statusUpdateForm">
                    <div class="form-group">
                        <label>الحالة الجديدة:</label>
                        <select class="form-control" id="newStatus" required>
                            <option value="0" @(Model.EmployeeWarning?.Status == EmployeeWarningStatus.New ? "selected" : "")>جديد</option>
                            <option value="1" @(Model.EmployeeWarning?.Status == EmployeeWarningStatus.Processing ? "selected" : "")>قيد المعالجة</option>
                            <option value="2" @(Model.EmployeeWarning?.Status == EmployeeWarningStatus.Solved ? "selected" : "")>تم الحل</option>
                            <option value="3" @(Model.EmployeeWarning?.Status == EmployeeWarningStatus.Bunchment ? "selected" : "")>مؤجل</option>
                            <option value="4" @(Model.EmployeeWarning?.Status == EmployeeWarningStatus.Cancelled ? "selected" : "")>ملغي</option>
                            <option value="5" @(Model.EmployeeWarning?.Status == EmployeeWarningStatus.Deleted ? "selected" : "")>محذوف</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات (اختياري):</label>
                        <textarea class="form-control" id="statusNotes" rows="3" 
                                  placeholder="أضف ملاحظات حول تغيير الحالة..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveStatusUpdate()">حفظ </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function updateStatus() {
            $('#statusUpdateModal').modal('show');
        }
        
        function saveStatusUpdate() {
            var guid = '@Model.EmployeeWarning?.Guid';
            var status = $('#newStatus').val();
            var notes = $('#statusNotes').val();
            
            if (!status) {
                showNotification('يرجى اختيار الحالة', 'error');
                return;
            }
            
            $.post('/Attendence/Notices/UpdateStatus', {
                guid: guid,
                status: status,
                notes: notes
            })
            .done(function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    $('#statusUpdateModal').modal('hide');
                    // Reload the page to show updated status
                    location.reload();
                } else {
                    showNotification(response.message, 'error');
                }
            })
            .fail(function() {
                showNotification('حدث خطأ أثناء تحديث الحالة', 'error');
            });
        }
        
        function printNotice() {
            window.print();
        }
        
        function sendReminder() {
            var guid = '@Model.EmployeeWarning?.Guid';
            var empNo = '@Model.EmployeeWarning?.EmpNo';
            
            // Implement reminder functionality
            showNotification('سيتم إرسال تذكير للموظف', 'info');
        }
        
        // Utility function for notifications
        function showNotification(message, type) {
            // Use the project's notification system
            if (typeof new_notification !== 'undefined') {
                new_notification({
                    message: message,
                    type: type
                });
            } else {
                doswal({
                    text: message,
                    icon: type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info'),
                    confirmButtonText: 'حسناً'
                });
            }
        }
    </script>
}
