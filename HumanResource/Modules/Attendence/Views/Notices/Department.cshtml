@using HumanResource.Modules.Attendence.ViewModels
@using HumanResource.Modules.Attendence.Models.Enums
@removeTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@model NoticesViewModel


<h3>اخطارات الموظفين</h3>
<div class="">
    <!-- Filter Panel -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">فلاتر البحث</h5>
        </div>
        <div class="card-body">
            <form id="filterForm">
                <div class="row">
                    <!-- First Row: Organizational Filters -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>المديرية العامة:</label>
                            <select class="form-control" id="Dg" name="Dg" onchange="updateDepartments()">
                                <option value="0">-- جميع المديريات --</option>
                                @foreach (var dg in Model.DgList)
                                {
                                    <option value="@dg.DgCode" @(Model.Dg == dg.DgCode ? "selected" : "")>
                                        @dg.DgDespA
                                    </option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>القسم:</label>
                            <select class="form-control" id="Dept" name="Dept" onchange="updateEmployees()">
                                <option value="0">-- جميع الأقسام --</option>
                                @foreach (var dept in Model.DeptList)
                                {
                                    <option value="@dept.DeptCode" @(Model.Dept == dept.DeptCode ? "selected" : "")>
                                        @dept.DeptDespA
                                    </option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>الموظف:</label>
                            <select class="form-control" id="EmpNo" name="EmpNo">
                                <option value="0">-- جميع الموظفين --</option>
                                @foreach (var emp in Model.EmpList)
                                {
                                    <option value="@emp.EmpNo" @(Model.EmpNo == emp.EmpNo ? "selected" : "")>
                                        @emp.EmpNameA
                                    </option>
                                }
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Second Row: Status and Date Filters -->
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>حالة الاخطار:</label>
                            <select class="form-control" id="Status" name="Status">
                                @foreach (var status in Model.StatusList)
                                {
                                    <option value="@status.Value" @(status.Selected ? "selected" : "")>
                                        @status.Text
                                    </option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>من تاريخ:</label>
                            <input type="date" class="form-control" id="FromDate" name="FromDate" 
                                   value="@(Model.FromDate?.ToString("yyyy-MM-dd"))" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>إلى تاريخ:</label>
                            <input type="date" class="form-control" id="ToDate" name="ToDate" 
                                   value="@(Model.ToDate?.ToString("yyyy-MM-dd"))" />
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>البحث:</label>
                            <input type="text" class="form-control" id="SearchTerm" name="SearchTerm" 
                                   placeholder="اسم الموظف، عنوان الاخطار..." value="@Model.SearchTerm" />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Action Buttons -->
                    <div class="col-md-12">
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">
                            <i class="icon-search4 mr-2"></i>تطبيق الفلاتر
                        </button>
                        <button type="button" class="btn btn-light" onclick="resetFilters()">
                            <i class="icon-reset mr-2"></i>إعادة تعيين
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">اخطارات الموظفين</h5>
            <div class="header-elements">
                <div class="list-icons">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="bulkStatusUpdate()">
                        <i class="icon-stack2 mr-1"></i>تحديث مجمع
                    </button>
                </div>
            </div>
        </div>
        <div class="">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="noticesTable">
                    <thead class="bg-primary text-white">
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" />
                            </th>
                            <th>رقم الموظف</th>
                            <th>اسم الموظف</th>
                            <th>القسم</th>
                            <th>عنوان </th>
                            <th>تاريخ </th>
                            <th>الحالة</th>
                            <th width="120">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusUpdateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث حالة الاخطار</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="statusUpdateForm">
                    <input type="hidden" id="updateGuid" />
                    <div class="form-group">
                        <label>الحالة الجديدة:</label>
                        <select class="form-control" id="newStatus" required>
                            <option value="0">جديد</option>
                            <option value="1">قيد المعالجة</option>
                            <option value="2">تم الحل</option>
                            <option value="3">مؤجل</option>
                            <option value="4">ملغي</option>
                            <option value="5">محذوف</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات (اختياري):</label>
                        <textarea class="form-control" id="statusNotes" rows="3" 
                                  placeholder="أضف ملاحظات حول تغيير الحالة..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveStatusUpdate()">حفظ التحديث</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Status Update Modal -->
<div class="modal fade" id="bulkStatusModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث مجمع للحالة</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="icon-info22 mr-2"></i>
                    سيتم تحديث <span id="selectedCount">0</span> اخطار
                </div>
                <form id="bulkStatusForm">
                    <div class="form-group">
                        <label>الحالة الجديدة:</label>
                        <select class="form-control" id="bulkNewStatus" required>
                            <option value="0">جديد</option>
                            <option value="1">قيد المعالجة</option>
                            <option value="2">تم الحل</option>
                            <option value="3">مؤجل</option>
                            <option value="4">ملغي</option>
                            <option value="5">محذوف</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>ملاحظات (اختياري):</label>
                        <textarea class="form-control" id="bulkStatusNotes" rows="3" 
                                  placeholder="أضف ملاحظات حول التحديث المجمع..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveBulkStatusUpdate()">تحديث الكل</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        var noticesTable;
        
        $(document).ready(function() {
        
        
            noticesTable = createDatatable('#noticesTable',true,{
                ajax: {
                    url: '/Attendence/Notices/Department/Datatable',
                    type: 'POST',
                    data: function(d) {
                        d.Dg = $('#Dg').val();
                        d.Dept = $('#Dept').val();
                        d.EmpNo = $('#EmpNo').val();
                        d.Status = $('#Status').val();
                        d.from = $('#FromDate').val();
                        d.to = $('#ToDate').val();
                        d.searchTerm = $('#SearchTerm').val();
                    }
                },
                columns: [
                    { 
                        data: null,
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            return '<input type="checkbox" class="notice-checkbox" value="' + row.guid + '" />';
                        }
                    },
                    { data: 'empNo' },
                    { data: 'empName' },
                    { data: 'department' },
                    { data: 'title' },
                    { 
                        data: 'warningDate',
                        render: function(data, type, row) {
                            return data || 'غير محدد';
                        }
                    },
                    { 
                        data: 'status',
                        render: function(data, type, row) {
                            return '<span class="' + row.statusClass + '">' + row.statusText + '</span>';
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            return '<div class="btn-group btn-group-sm">' +
                                   '<button class="btn btn-light" onclick="viewNotice(\'' + row.guid + '\')" title="عرض التفاصيل">' +
                                   '<i class="icon-eye"></i></button>' +
                                   '<button class="btn btn-light" onclick="updateStatus(\'' + row.guid + '\')" title="تحديث الحالة">' +
                                   '<i class="icon-pencil"></i></button>' +
                                   '</div>';
                        }
                    }
                ],
  

                order: [[4, 'desc']], // Sort by warning date descending

            });
        });


        // Cascading dropdown functions
        function updateDepartments() {
            var dgCode = $('#Dg').val();
            var deptSelect = $('#Dept');
            var empSelect = $('#EmpNo');

            // Clear current options
            deptSelect.html('<option value="0">-- جميع الأقسام --</option>');
            empSelect.html('<option value="0">-- جميع الموظفين --</option>');

            if (dgCode > 0) {
                $.get('/Attendence/Notices/GetDepartments', { dgCode: dgCode })
                    .done(function(data) {
                        $.each(data, function(index, dept) {
                            deptSelect.append('<option value="' + dept.deptCode + '">' + dept.deptDespA + '</option>');
                        });
                    })
                    .fail(function() {
                        showNotification('خطأ في تحميل الأقسام', 'error');
                    });
            }

            updateEmployees();
        }

        function updateEmployees() {
            var deptCode = $('#Dept').val();
            var dgCode = $('#Dg').val();
            var empSelect = $('#EmpNo');

            // Clear current options
            empSelect.html('<option value="0">-- جميع الموظفين --</option>');

            if (deptCode > 0 || dgCode > 0) {
                $.get('/Attendence/Notices/GetEmployees', {
                    dgCode: dgCode > 0 ? dgCode : null,
                    deptCode: deptCode > 0 ? deptCode : null
                })
                .done(function(data) {
                    $.each(data, function(index, emp) {
                        empSelect.append('<option value="' + emp.empNo + '">' + emp.empNameA + '</option>');
                    });
                })
                .fail(function() {
                    showNotification('خطأ في تحميل الموظفين', 'error');
                });
            }
        }

        // Filter functions
        function applyFilters() {
            noticesTable.ajax.reload();
        }

        function resetFilters() {
            $('#filterForm')[0].reset();
            $('#Dg').val('0');
            $('#Dept').val('0');
            $('#EmpNo').val('0');
            $('#Status').val('-1');
            updateDepartments();
            noticesTable.ajax.reload();
        }

        // Selection functions
        function toggleSelectAll() {
            var isChecked = $('#selectAll').is(':checked');
            $('.notice-checkbox').prop('checked', isChecked);
            updateSelectedCount();
        }

        function updateSelectedCount() {
            var count = $('.notice-checkbox:checked').length;
            $('#selectedCount').text(count);
        }

        // Status update functions
        function updateStatus(guid) {
            $('#updateGuid').val(guid);
            $('#statusUpdateModal').modal('show');
        }

        function saveStatusUpdate() {
            var guid = $('#updateGuid').val();
            var status = $('#newStatus').val();
            var notes = $('#statusNotes').val();

            if (!status) {
                showNotification('يرجى اختيار الحالة', 'error');
                return;
            }

            $.post('/Attendence/Notices/UpdateStatus', {
                guid: guid,
                status: status,
                notes: notes
            })
            .done(function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    $('#statusUpdateModal').modal('hide');
                    noticesTable.ajax.reload();
                } else {
                    showNotification(response.message, 'error');
                }
            })
            .fail(function() {
                showNotification('حدث خطأ أثناء تحديث الحالة', 'error');
            });
        }

        function bulkStatusUpdate() {
            var selectedGuids = $('.notice-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            if (selectedGuids.length === 0) {
                showNotification('يرجى اختيار اخطار واحد على الأقل', 'error');
                return;
            }

            updateSelectedCount();
            $('#bulkStatusModal').modal('show');
        }

        function saveBulkStatusUpdate() {
            var selectedGuids = $('.notice-checkbox:checked').map(function() {
                return $(this).val();
            }).get();

            var status = $('#bulkNewStatus').val();
            var notes = $('#bulkStatusNotes').val();

            if (!status) {
                showNotification('يرجى اختيار الحالة', 'error');
                return;
            }

            $.ajax({
                url: '/Attendence/Notices/BulkUpdateStatus',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    guids: selectedGuids,
                    status: parseInt(status),
                    notes: notes
                })
            })
            .done(function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    $('#bulkStatusModal').modal('hide');
                    $('#selectAll').prop('checked', false);
                    noticesTable.ajax.reload();
                } else {
                    showNotification(response.message, 'error');
                }
            })
            .fail(function() {
                showNotification('حدث خطأ أثناء التحديث المجمع', 'error');
            });
        }

        // View notice details
        function viewNotice(guid) {
            window.open('/Attendence/Notices/Department/' + guid, '_blank');
        }


        // Utility function for notifications
        function showNotification(message, type) {
            // Use the project's notification system
            if (typeof new_notification !== 'undefined') {
                new_notification({
                    message: message,
                    type: type
                });
            } else {
               

                doswal({
                    text: message,
                    icon: type === 'success' ? 'success' : (type === 'error' ? 'error' : 'info'),
                    confirmButtonText: 'حسناً'
                });
            }
        }

        // Update selected count when checkboxes change
        $(document).on('change', '.notice-checkbox', function() {
            updateSelectedCount();
        });
    </script>
}
