@using HumanResource.Modules.Attendence.ViewModels
@using HumanResource.Modules.Attendence.Models.Enums
@model NoticesViewModel

@{
    ViewData["Title"] = "الإخطارات";
}

<style>
    .notice-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .notice-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    }
    
    .notice-list-item {
        border-left: 4px solid #007bff;
        margin-bottom: 10px;
    }
    
    .notice-detail {
        border-left: 4px solid #28a745;
    }
    
    .notice-date {
        color: #6c757d;
        font-size: 0.9em;
    }
    
    .notice-title {
        color: #495057;
        font-weight: 600;
    }
    
    .notice-body {
        color: #6c757d;
        line-height: 1.6;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .status-badge-small {
        font-size: 0.7rem !important;
        padding: 0.25rem 0.5rem !important;
        @* border-radius: 0.25rem !important; *@

        margin-right: 0.5rem !important;
    }

    .notice-list-item a {
        text-decoration: none;
        display: block;
        padding: 0.5rem;
    }

    .notice-list-item a:hover {
        text-decoration: none;
    }

</style>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">@ViewData["Title"]</h1>
     
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card p-0">
                <div class="card-body p-0 m-0">
                    <ul class="list-group bg-transparent m-0 p-0 rounded">
                    @foreach (var warning in Model.EmployeeWarnings.OrderByDescending(w => w.WarningDate))
                    {
                        <li class="list-group-item border text-primary rounded @(Model.EmployeeWarning?.Guid == warning.Guid ? "bg-primary text-white" : "")" >
                            <a class=" @(Model.EmployeeWarning?.Guid == warning.Guid ? "text-white" : "text-primary")" href="@Url.Action("My", "Notices", new { guid = warning.Guid })">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong>@(warning.Title ?? "تنبيه عام")</strong><br>
                                        <small>@(warning.WarningDate?.ToString("yyyy-MM-dd") ?? "غير محدد")</small>
                                    </div>
                                    <div>
                                        <span class="@Model.GetStatusCssClass(warning.Status) status-badge-small">
                                            @Model.GetStatusText(warning.Status)
                                        </span>
                                    </div>
                                </div>
                            </a>
                        </li>
                    }
                </ul>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            @if (Model.EmployeeWarning != null)
            {
            <div class="card shadow notice-detail">
                    <div class="card-header py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold">
                                <i class="fas fa-exclamation-triangle mr-2 text-danger"></i>
                                تفاصيل الإخطار
                            </h6>
                            <span class="@Model.GetStatusCssClass(Model.EmployeeWarning.Status)">
                                @Model.GetStatusText(Model.EmployeeWarning.Status)
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <h6 class="text-muted">عنوان الإخطار</h6>
                                <p class="notice-title">@(Model.EmployeeWarning.Title ?? "تنبيه عام")</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <h6 class="text-muted">تاريخ الإخطار</h6>
                                <p class="notice-date">
                                    <i class="fas fa-calendar-alt mr-1"></i>
                                    @(Model.EmployeeWarning.WarningDate?.ToString("yyyy-MM-dd") ?? "غير محدد")
                                </p>
                            </div>
                         
                    
                            <div class="col-md-12 mb-3">
                                <h6 class="text-muted">محتوى الإخطار</h6>
                                <div class="bg-white border rounded p-3" >
                                    <div class="notice-body text-dark">
                                        @if (!string.IsNullOrEmpty(Model.EmployeeWarning.Body))
                                        {
                                            @Html.Raw(Model.EmployeeWarning.Body.Replace("\n", "<br/>"))
                                        }
                                        else
                                        {
                                            <span class="text-muted">لا يوجد محتوى للإخطار</span>
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-sm table-borderless">
                                        <tr>
                                            <td width="150"><strong>رقم الموظف:</strong></td>
                                            <td>@(Model.EmployeeWarning.EmpNo?.ToString() ?? "غير محدد")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>معرف الإخطار:</strong></td>
                                            <td><small class="text-muted">@Model.EmployeeWarning.Guid</small></td>
                                        </tr>
                                   
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>@(Model.EmployeeWarning.Timestamp?.ToString("yyyy-MM-dd HH:mm") ?? "غير محدد")</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
              
            }
        </div>
    </div>
