@using HumanResource.Modules.Attendence.ViewModels
@model AttendenceViewModel

<style>
    .progress {
        height: 25px;
        border:2px solid black !important;
    }
    
    .excuse-badge {
        position: absolute;
        top: -10px;
        right: 10px;
        z-index: 1;
    }
    
    .progress-container {
        position: relative;
    }
    
    .attendance-card {
        transition: all 0.3s ease;
    }
    
    .attendance-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Time markers */
    .time-marker {
        position: absolute;
        bottom: -18px;
        transform: translateX(-50%);
        font-size: 8px;
        color: #6c757d;
    }
    
    .time-container {
        position: relative;
        height: 25px;
        margin-bottom: 25px;
    }
    
    /* Leave card styling */
    .leave-card {
        border-left: 4px solid var(--secondary) !important;
    }
    
    .weekend-card {
        border-left: 4px solid var(--secondary) !important;
        background-color: #f8f9fc;
    }
    
    /* Print styles */
    @@media print {
        .no-print {
            display: none !important;
        }
        
        .content-wrapper, .container-fluid {
            width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .navbar, .sidebar, footer, .scroll-to-top, .sticky-footer {
            display: none !important;
        }
        
        body {
            background-color: white !important;
            margin: 0;
            padding: 15px;
            font-size: 12pt;
        }
        
        .card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
            margin-bottom: 20px !important;
        }
        
        .print-header {
            display: block !important;
            text-align: center;
            border-bottom: 2px solid black;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
    }
</style>

<div class="container-fluid">


    <div class="row ">
        <div class="col-md-6">
            <h1 class="h3 mb-2 text-gray-800">تقرير حضوري الشهري</h1>
            <p class="mb-4">عرض وتتبع سجل الحضور والانصراف وساعات العمل والاستئذان الخاصة بك</p>
        </div>

    </div>
    

       
            <form method="get" action="/Attendence/My" id="monthForm" >
                <div class="row">
                    
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label>الشهر</label>
                        <select name="month" class="form-control">
                            @for (int i = 1; i <= 12; i++)
                            {
                                if (i == Model.SelectedMonth)
                                {
                                    <option value="@i" selected>@(new DateTime(2023, i, 1).ToString("MMMM"))</option>
                                }
                                else
                                {
                                    <option value="@i">@(new DateTime(2023, i, 1).ToString("MMMM"))</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>السنة</label>
                        <select name="year" class="form-control">
                            @for (int i = DateTime.Now.Year - 3; i <= DateTime.Now.Year; i++)
                            {
                                if (i == Model.SelectedYear)
                                {
                                    <option value="@i" selected>@i</option>
                                }
                                else
                                {
                                    <option value="@i">@i</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-4 d-flex align-items-center">
                    <button type="submit" class="btn btn-primary">عرض</button>
                </div>
                </div>
            </form>
     
    
    <!-- Summary Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                ملخص الحضور لشهر @(new DateTime(Model.SelectedYear, Model.SelectedMonth, 1).ToString("MMMM yyyy"))
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4 border-left-primary">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        عدد أيام الحضور
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        @Model.DailyAttendance.Count() من @DateTime.DaysInMonth(Model.SelectedYear, Model.SelectedMonth) يوم
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4 border-left-warning">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        إجمالي ساعات الاستئذان
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        @Model.FormatExcuseDuration(Model.TotalExcuseDuration) ساعة
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-12">
                    <h3 class="small font-weight-bold mb-4">متوسط ساعات العمل اليومية 
                        <span class="float-left">
                            @{
                                float avgHours = Model.DailyAttendance.Any() ? 
                                    Model.DailyAttendance.Average(d => d.Hours) : 0;
                            }
                            @Model._h.FormatHour(avgHours) ساعة (@(Model.CalculateWorkPercentage(avgHours))%)
                        </span>
                    </h3>
                    <div class="progress mb-4 rounded-pill border-2 border-dark">
                        <div class="progress-bar rounded-pill @Model.GetProgressColorClass(avgHours)" role="progressbar" 
                             style="width: @Model.CalculateWorkPercentage(avgHours)%" 
                             aria-valuenow="@Model.CalculateWorkPercentage(avgHours)" aria-valuemin="0" aria-valuemax="100">
                             @(Model.CalculateWorkPercentage(avgHours))%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Daily Attendance Cards -->
    <div class="row">
        @{
            // Create a list of all days in the month
            var allDaysInMonth = new List<DateTime>();
            var startDate = new DateTime(Model.SelectedYear, Model.SelectedMonth, 1);
            var endDate = startDate.AddMonths(1).AddDays(-1);
            
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                allDaysInMonth.Add(date);
            }
            
            // Sort in descending order (newest first)
            allDaysInMonth = allDaysInMonth.OrderByDescending(d => d).ToList();
            
            bool hasData = false;
            
            foreach (var date in allDaysInMonth)
            {
                // Skip weekends (Friday and Saturday)
                bool isWeekend = Model.IsWeekend(date);
                if (isWeekend)
                {
                    continue;
                }
                
                // Check if the employee is on leave for this day
                var leave = Model.GetLeaveForDate(Model.Employee?.EmpNo ?? 0, date);
                bool isOnLeave = leave != null;
                
                // Get the attendance record for this day
                var attendance = Model.DailyAttendance.FirstOrDefault(d => d.Day.Date == date.Date);
                bool hasAttendance = attendance != null;
                
                if (!hasAttendance && !isOnLeave)
                {
                    continue; // Skip days with no data (no attendance and not on leave)
                }
                
                hasData = true;
                
                // Find excuses for this day
                var dayExcuses = Model.MonthlyExcuses
                    .Where(e => e.OrderDate?.Date == date.Date)
                    .ToList();
                
                var hasExcuse = dayExcuses.Any();
                
                string cardClass = isOnLeave ? "leave-card" : 
                                 hasExcuse ? "border-left-warning" : 
                                 "border-left-primary";
                
                // Get the leave description
                string leaveDescription = isOnLeave ? 
                    (leave.TleaveCode?.LeaveDespA ?? Model.GetLeaveDescription(leave.LeaveCode)) : "";
                
                <div class="col-md-12 mb-1">
                    <div class="card attendance-card shadow-sm @cardClass">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                @date.ToString("dddd, yyyy-MM-dd")
                            </h6>
                            @if (isOnLeave)
                            {
                                <span class="badge bg-primary">@leaveDescription</span>
                            }
                            else if (hasExcuse)
                            {
                                <span class="badge bg-secondary">يوجد استئذان</span>
                            }
                        </div>
                        <div class="card-body">
                            @if (isOnLeave)
                            {
                                <div class="row">
                                    <div class="col-md-12 text-center my-3">
                                        <i class="fas fa-umbrella-beach fa-3x text-primary mb-3"></i>
                                        <h5>إجازة: @leaveDescription</h5>
                                        <p>من @leave.LeaveStartDate.ToString("yyyy-MM-dd") إلى @leave.LeaveEndDate.ToString("yyyy-MM-dd")</p>
                       
                                        @if (leave.AuditStat == 1)
                                        {
                                            <span class="badge badge-success">تمت الموافقة</span>
                                        }
                                        else if (leave.ReqStat == 0)
                                        {
                                            <span class="badge badge-secondary">قيد الانتظار</span>
                                        }
                                        else if (leave.ReqStat == 1)
                                        {
                                            <span class="badge badge-success">تمت الموافقة</span>
                                        }
                                        else if (leave.ReqStat == 3)
                                        {
                                            <span class="badge badge-danger">مرفوضة</span>
                                        }
                                        else if (leave.ReqStat == 4 || leave.ReqStat == 6)
                                        {
                                            <span class="badge badge-success">معتمدة</span>
                                        }
                                    </div>
                                </div>
                            }
                            else if (hasAttendance)
                            {
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>وقت الحضور:</strong> @attendance.FirstEntry.ToString("HH:mm")</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>وقت الانصراف:</strong> @attendance.LastEntry.ToString("HH:mm")</p>
                                    </div>
                                    <div class="col-md-12 mt-3">
                                        <h4 class="small font-weight-bold">ساعات العمل 
                                            <span class="float-left">@Model._h.FormatHour(attendance.Hours) ساعة </span>
                                        </h4>
                                        <div class="progress-container mb-4">
                                            <div class="progress rounded-pill">
                                                <div class="progress-bar rounded-pill @Model.GetProgressColorClass(attendance.Hours)" role="progressbar" 
                                                     style="width: @Model.CalculateWorkPercentage(attendance.Hours)%" 
                                                     aria-valuenow="@Model.CalculateWorkPercentage(attendance.Hours)" aria-valuemin="0" aria-valuemax="100">
                                                     @Model.CalculateWorkPercentage(attendance.Hours)%
                                                </div>
                                            </div>
                 
                                        </div>
                                    </div>
                                    
                                    @if (attendance.Extra > 0)
                                    {
                                        <div class="col-md-12">
                                            <h4 class="small font-weight-bold">  العمل الإضافي 
                                                <span class="mx-1">(@Model._h.FormatHour(attendance.Extra) ساعة)</span>
                                            </h4>
                                            <div class="progress mb-4 rounded-pill">
                                                <div class="progress-bar bg-secondary rounded-pill" role="progressbar" 
                                                     style="width: @Math.Min(attendance.Extra / 3 * 100, 100)%" 
                                                     aria-valuenow="@Math.Min(attendance.Extra / 3 * 100, 100)" aria-valuemin="0" aria-valuemax="100">
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    
                                    @if (hasExcuse)
                                    {
                                        <div class="col-md-12">
                                            <div class="card bg-light mb-2 border-2" style="border: 2px solid var(--primary) !important;">
                                                <div class="card-header py-2">
                                                    <h6 class="m-0 font-weight-bold text-dark">تفاصيل الاستئذان</h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    @foreach (var excuse in dayExcuses)
                                                    {
                                                        string excuseType = excuse.ReqType == 1 ? "استئذان" : 
                                                                            excuse.ReqType == 2 ? "تأخير" : 
                                                                            excuse.ReqType == 3 ? "مهمة رسمية" : "غير معروف";
                                                        
                                                        string approvalStatus = excuse.Status == 0 ? "قيد الانتظار" :
                                                                                excuse.Status == 1 ? "تمت الموافقة" :
                                                                                excuse.Status == 3 ? "مرفوض" : "غير معروف";
                                                        
                                                        string approvalClass = excuse.Status == 0 ? "badge-secondary" :
                                                                              excuse.Status == 1 ? "badge-success" :
                                                                              "badge-danger";
                                                                              
                                                        string duration = "--:--";
                                                        if (excuse.TimeFrom.HasValue && excuse.TimeTo.HasValue)
                                                        {
                                                            var minutes = (int)(excuse.TimeTo.Value - excuse.TimeFrom.Value).TotalMinutes;
                                                            var hours = minutes / 60;
                                                            var mins = minutes % 60;
                                                            duration = $"{hours}:{mins:D2}";
                                                        }
                                                        
                                                        <div class="row mb-2">
                                                            <div class="col-md-3">
                                                                <span class="badge bg-primary">@excuseType</span>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <small>من: @excuse.TimeFrom?.ToString("HH:mm")</small>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <small>إلى: @excuse.TimeTo?.ToString("HH:mm")</small>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <small>المدة: @duration</small>
                                                            </div>
                                                            <div class="col-md-9">
                                                                <small class="text-muted">السبب: @excuse.Reason</small>
                                                            </div>
                                                            <div class="col-md-3 text-right">
                                                                <span class="badge @approvalClass">@approvalStatus</span>
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
            
            // Display weekend cards (optional, if we want to show weekends)
            foreach (var date in allDaysInMonth.Where(d => Model.IsWeekend(d)))
            {
                @* <div class="col-md-6 mb-4">
                    <div class="card attendance-card shadow-sm weekend-card">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-gray-600">
                                @date.ToString("dddd, yyyy-MM-dd")
                            </h6>
                            <span class="badge badge-secondary">عطلة نهاية الأسبوع</span>
                        </div>
                        <div class="card-body text-center py-4">
                            <i class="fas fa-mug-hot fa-3x text-gray-300 mb-3"></i>
                            <h5 class="text-gray-600">عطلة نهاية الأسبوع</h5>
                        </div>
                    </div>
                </div> *@
            }
            
            if (!hasData)
            {
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-calendar-times fa-4x text-gray-300 mb-3"></i>
                            <h5>لا توجد سجلات حضور لهذا الشهر</h5>
                            <p class="text-muted">جرب اختيار شهر آخر أو تواصل مع قسم الموارد البشرية للمساعدة.</p>
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Print button handler
            $('#printReport').on('click', function() {
                // Show print header
                $('.print-header').show();
                
                // Print the page
                window.print();
                
                // Hide print header after printing
                setTimeout(function() {
                    $('.print-header').hide();
                }, 500);
            });
        });
    </script>
} 