@using HumanResource.Modules.Attendence.ViewModels
@model AttendenceViewModel

<style>
    /* Print styles */
    @@media print {
        .no-print {
            display: none !important;
        }
        
        .content-wrapper, .container-fluid {
            width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .navbar, .sidebar, footer, .scroll-to-top, .sticky-footer {
            display: none !important;
        }
        
        body {
            background-color: white !important;
            margin: 0;
            padding: 15px;
            font-size: 12pt;
        }
        
        .card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
            margin-bottom: 20px !important;
            page-break-inside: avoid;
        }
        
        .card + .card {
            margin-top: 20px;
        }
        
        .table {
            width: 100% !important;
            border-collapse: collapse !important;
        }
        
        .table th, .table td {
            background-color: #fff !important;
            color: #000 !important;
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }
        
        .table thead th {
            background-color: #f8f9fc !important;
        }
        
        .badge {
            border: 1px solid #000 !important;
            color: #000 !important;
            display: inline-block !important;
            margin: 1px !important;
        }
        
        .badge-danger { background-color: #f8d7da !important; }
        .badge-secondary { background-color: #e2e3e5 !important; }
        
        .dataTables_wrapper .row:first-child,
        .dataTables_wrapper .row:last-child {
            display: none !important;
        }
        
        div[style*="max-height"] {
            max-height: none !important;
            overflow: visible !important;
        }
        
        .print-header {
            display: block !important;
            text-align: center;
            border-bottom: 2px solid black;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .print-footer {
            display: block !important;
            text-align: center;
            position: fixed;
            bottom: 0;
            width: 100%;
            border-top: 1px solid #ddd;
            padding-top: 5px;
            font-size: 9pt;
        }
        
        .page-break {
            page-break-before: always;
        }
    }
</style>

<div class="container-fluid">
    <!-- Print Header (only visible when printing) -->
    <div class="print-header" style="display: none;">
        <h2>نظام الموارد البشرية</h2>
        <h3>تقرير الغياب الشهري</h3>
        <p>شهر: @(new DateTime(Model.SelectedYear, Model.SelectedMonth, 1).ToString("MMMM yyyy"))</p>
    </div>

    <div class="d-flex justify-content-between mb-4 no-print">
        <h3>تقرير الغياب الشهري</h3>
        <div>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <a href="javascript:void(0)" class="dropdown-item" id="printReport">
                        <i class="fas fa-print"></i> طباعة التقرير
                    </a>
                    <a href="javascript:void(0)" class="dropdown-item" id="exportPdf">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </a>
                    <a href="javascript:void(0)" class="dropdown-item" id="exportExcel">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="javascript:void(0)" class="dropdown-item" id="printDailySummary">
                        <i class="fas fa-print"></i> طباعة ملخص الغياب اليومي
                    </a>
                    <a href="javascript:void(0)" class="dropdown-item" id="printEmployeeSummary">
                        <i class="fas fa-print"></i> طباعة ملخص الغياب للموظفين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4 no-print">
        <div class="card-header bg-primary text-white">
            <h5 class="m-0">اختر الشهر</h5>
        </div>
        <div class="card-body">
            <form method="get" action="/Attendence/MonthlyNonAttendanceReport" id="monthForm" class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>الشهر</label>
                        <select name="month" class="form-control">
                            @for (int i = 1; i <= 12; i++)
                            {
                                if (i == Model.SelectedMonth)
                                {
                                    <option value="@i" selected>@(new DateTime(2023, i, 1).ToString("MMMM"))</option>
                                }
                                else
                                {
                                    <option value="@i">@(new DateTime(2023, i, 1).ToString("MMMM"))</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>السنة</label>
                        <select name="year" class="form-control">
                            @for (int i = DateTime.Now.Year - 5; i <= DateTime.Now.Year; i++)
                            {
                                if (i == Model.SelectedYear)
                                {
                                    <option value="@i" selected>@i</option>
                                }
                                else
                                {
                                    <option value="@i">@i</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">عرض</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow" id="dailySummaryCard">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                تقرير الغياب لشهر @(new DateTime(Model.SelectedYear, Model.SelectedMonth, 1).ToString("MMMM yyyy"))
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="nonAttendanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الموظفين الغائبين</th>
                            <th>عدد الغائبين</th>
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            var monthStart = new DateTime(Model.SelectedYear, Model.SelectedMonth, 1);
                            var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                        }
                        
                        @for (var date = monthStart; date <= monthEnd; date = date.AddDays(1))
                        {
                            var presentEmployees = Model.AttendanceByDate.ContainsKey(date) ? Model.AttendanceByDate[date] : new List<int>();
                            var absentEmployees = Model.VempDtls
                                .Where(e => e.EmpNo.HasValue && !presentEmployees.Contains(e.EmpNo.Value))
                                .ToList();
                            
                            <tr>
                                <td>@date.ToString("yyyy-MM-dd")</td>
                                <td>
                                    <div style="max-height: 100px; overflow-y: auto;">
                                        @foreach (var emp in absentEmployees)
                                        {
                                            <span class="badge badge-secondary m-1">@emp.EmpNameA (@emp.EmpNo)</span>
                                        }
                                    </div>
                                </td>
                                <td>@absentEmployees.Count</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="card shadow mt-4" id="employeeSummaryCard">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                ملخص الغياب لكل موظف
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="employeeSummaryTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>رقم الموظف</th>
                            <th>اسم الموظف</th>
                            <th>عدد أيام الغياب</th>
                            <th>أيام الغياب</th>
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            var monthDays = Enumerable.Range(1, DateTime.DaysInMonth(Model.SelectedYear, Model.SelectedMonth))
                                .Select(day => new DateTime(Model.SelectedYear, Model.SelectedMonth, day))
                                .ToList();
                                
                            foreach (var employee in Model.VempDtls.Where(e => e.EmpNo.HasValue))
                            {
                                var absentDays = new List<DateTime>();
                                
                                foreach (var date in monthDays)
                                {
                                    var presentEmployees = Model.AttendanceByDate.ContainsKey(date) ? Model.AttendanceByDate[date] : new List<int>();
                                    if (!presentEmployees.Contains(employee.EmpNo.Value))
                                    {
                                        absentDays.Add(date);
                                    }
                                }
                                
                                if (absentDays.Any())
                                {
                                    <tr>
                                        <td>@employee.EmpNo</td>
                                        <td>@employee.EmpNameA</td>
                                        <td>@absentDays.Count</td>
                                        <td>
                                            <div style="max-height: 100px; overflow-y: auto;">
                                                @foreach (var day in absentDays)
                                                {
                                                    <span class="badge badge-danger m-1">@day.ToString("yyyy-MM-dd")</span>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            }
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Print footer (only visible when printing) -->
    <div class="print-footer" style="display: none;">
        <p>تم إنشاء هذا التقرير بواسطة نظام الموارد البشرية - @DateTime.Now.ToString("yyyy-MM-dd HH:mm")</p>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.datatables.net/buttons/2.2.3/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.3/js/buttons.print.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTables
            var nonAttendanceTable = $('#nonAttendanceTable').DataTable({
                "language": {
                    "url": "/js/datatables-ar.json"
                },
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                "order": [[ 0, "asc" ]]
            });
            
            var employeeSummaryTable = $('#employeeSummaryTable').DataTable({
                "language": {
                    "url": "/js/datatables-ar.json"
                },
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                "order": [[ 2, "desc" ]]
            });
            
            // Main print button handler
            $('#printReport').on('click', function() {
                // Show print-specific elements
                $('.print-header, .print-footer').show();
                
                // Print the page
                window.print();
                
                // Hide print-specific elements after printing
                setTimeout(function() {
                    $('.print-header, .print-footer').hide();
                }, 500);
            });
            
            // Print only daily summary
            $('#printDailySummary').on('click', function() {
                // Hide the employee summary card
                $('#employeeSummaryCard').hide();
                
                // Show print-specific elements
                $('.print-header, .print-footer').show();
                
                // Add special title for this report
                $('.print-header h3').text('تقرير الغياب اليومي الشهري');
                
                // Print the page
                window.print();
                
                // Restore original title
                $('.print-header h3').text('تقرير الغياب الشهري');
                
                // Hide print-specific elements and show all cards
                setTimeout(function() {
                    $('.print-header, .print-footer').hide();
                    $('#employeeSummaryCard').show();
                }, 500);
            });
            
            // Print only employee summary
            $('#printEmployeeSummary').on('click', function() {
                // Hide the daily summary card
                $('#dailySummaryCard').hide();
                
                // Show print-specific elements
                $('.print-header, .print-footer').show();
                
                // Add special title for this report
                $('.print-header h3').text('ملخص الغياب للموظفين');
                
                // Print the page
                window.print();
                
                // Restore original title
                $('.print-header h3').text('تقرير الغياب الشهري');
                
                // Hide print-specific elements and show all cards
                setTimeout(function() {
                    $('.print-header, .print-footer').hide();
                    $('#dailySummaryCard').show();
                }, 500);
            });
            
            // Export to PDF
            $('#exportPdf').on('click', function() {
                var docDefinition = {
                    pageSize: 'A4',
                    pageOrientation: 'landscape',
                    rtl: true,
                    content: [
                        { text: 'نظام الموارد البشرية', style: 'header' },
                        { text: 'تقرير الغياب الشهري', style: 'subheader' },
                        { text: 'شهر: ' + '@(new DateTime(Model.SelectedYear, Model.SelectedMonth, 1).ToString("MMMM yyyy"))', style: 'dateText' },
                        { text: ' ', margin: [0, 10] },
                        { text: 'ملخص الغياب اليومي', style: 'sectionHeader' },
                        createNonAttendanceTable(),
                        { text: ' ', margin: [0, 20] },
                        { text: 'ملخص الغياب للموظفين', style: 'sectionHeader', pageBreak: 'before' },
                        createEmployeeSummaryTable()
                    ],
                    styles: {
                        header: { fontSize: 22, bold: true, alignment: 'center', margin: [0, 0, 0, 10] },
                        subheader: { fontSize: 16, bold: true, alignment: 'center', margin: [0, 0, 0, 5] },
                        dateText: { fontSize: 12, alignment: 'center', margin: [0, 0, 0, 20] },
                        sectionHeader: { fontSize: 14, bold: true, margin: [0, 0, 0, 10] },
                        tableHeader: { bold: true, fontSize: 13, color: 'black', fillColor: '#f2f2f2' }
                    },
                    footer: function(currentPage, pageCount) {
                        return { 
                            text: 'صفحة ' + currentPage.toString() + ' من ' + pageCount, 
                            alignment: 'center',
                            fontSize: 8
                        };
                    }
                };
                
                pdfMake.createPdf(docDefinition).download('تقرير_الغياب_الشهري_' + '@(new DateTime(Model.SelectedYear, Model.SelectedMonth, 1).ToString("yyyy_MM"))' + '.pdf');
            });
            
            // Export to Excel
            $('#exportExcel').on('click', function() {
                // For simplicity, we'll only export the employee summary table
                var exportTable = $('#employeeSummaryTable').DataTable({
                    dom: 'Bfrtip',
                    buttons: [{
                        extend: 'excel',
                        text: 'تصدير Excel',
                        title: 'تقرير_الغياب_الشهري_' + '@(new DateTime(Model.SelectedYear, Model.SelectedMonth, 1).ToString("yyyy_MM"))',
                        filename: 'تقرير_الغياب_الشهري_' + '@(new DateTime(Model.SelectedYear, Model.SelectedMonth, 1).ToString("yyyy_MM"))',
                        exportOptions: {
                            columns: [0, 1, 2]  // Exclude the absence days column
                        }
                    }],
                    retrieve: true
                });
                
                exportTable.button('.buttons-excel').trigger();
            });
            
            // Helper function to create daily non-attendance table for PDF
            function createNonAttendanceTable() {
                var tableData = {
                    headerRows: 1,
                    widths: ['auto', '*', 'auto'],
                    body: [
                        [
                            { text: 'التاريخ', style: 'tableHeader' },
                            { text: 'عدد الغائبين', style: 'tableHeader' },
                            { text: 'النسبة المئوية', style: 'tableHeader' }
                        ]
                    ]
                };
                
                // Add data rows from the table
                @{
                    var totalEmployees = Model.VempDtls.Count(e => e.EmpNo.HasValue);
                    var monthStart = new DateTime(Model.SelectedYear, Model.SelectedMonth, 1);
                    var monthEnd = monthStart.AddMonths(1).AddDays(-1);
                }
                
                @for (var date = monthStart; date <= monthEnd; date = date.AddDays(1))
                {
                    var presentEmployees = Model.AttendanceByDate.ContainsKey(date) ? Model.AttendanceByDate[date] : new List<int>();
                    var absentCount = Model.VempDtls.Count(e => e.EmpNo.HasValue && !presentEmployees.Contains(e.EmpNo.Value));
                    var percentage = totalEmployees > 0 ? (absentCount * 100.0 / totalEmployees).ToString("F1") : "0.0";
                    
                    <text>
                    tableData.body.push([
                        '@date.ToString("yyyy-MM-dd")',
                        '@absentCount',
                        '@percentage%'
                    ]);
                    </text>
                }
                
                return { table: tableData, layout: 'lightHorizontalLines' };
            }
            
            // Helper function to create employee summary table for PDF
            function createEmployeeSummaryTable() {
                var tableData = {
                    headerRows: 1,
                    widths: ['auto', '*', 'auto', '*'],
                    body: [
                        [
                            { text: 'رقم الموظف', style: 'tableHeader' },
                            { text: 'اسم الموظف', style: 'tableHeader' },
                            { text: 'عدد أيام الغياب', style: 'tableHeader' },
                            { text: 'أيام الغياب', style: 'tableHeader' }
                        ]
                    ]
                };
                
                @{
                    var monthDays = Enumerable.Range(1, DateTime.DaysInMonth(Model.SelectedYear, Model.SelectedMonth))
                        .Select(day => new DateTime(Model.SelectedYear, Model.SelectedMonth, day))
                        .ToList();
                }
                
                @foreach (var employee in Model.VempDtls.Where(e => e.EmpNo.HasValue))
                {
                    var absentDays = new List<DateTime>();
                    
                    foreach (var date in monthDays)
                    {
                        var presentEmployees = Model.AttendanceByDate.ContainsKey(date) ? Model.AttendanceByDate[date] : new List<int>();
                        if (!presentEmployees.Contains(employee.EmpNo.Value))
                        {
                            absentDays.Add(date);
                        }
                    }
                    
                    if (absentDays.Any())
                    {
                        var absentDaysText = string.Join(", ", absentDays.Select(d => d.ToString("yyyy-MM-dd")));
                        
                        <text>
                        tableData.body.push([
                            '@employee.EmpNo',
                            '@employee.EmpNameA',
                            '@absentDays.Count',
                            '@absentDaysText'
                        ]);
                        </text>
                    }
                }
                
                return { table: tableData, layout: 'lightHorizontalLines' };
            }
        });
    </script>
} 