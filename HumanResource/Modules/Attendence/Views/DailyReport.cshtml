@using HumanResource.Modules.Attendence.ViewModels
@model AttendenceViewModel

<style>
    /* Print styles */
    @@media print {
        .no-print {
            display: none !important;
        }
        
        .content-wrapper, .container-fluid {
            width: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
        }
        
        .navbar, .sidebar, footer, .scroll-to-top, .sticky-footer {
            display: none !important;
        }
        
        body {
            background-color: white !important;
            margin: 0;
            padding: 15px;
            font-size: 12pt;
        }
        
        .card {
            box-shadow: none !important;
            border: 1px solid #ddd !important;
        }
        
        .table {
            width: 100% !important;
            border-collapse: collapse !important;
        }
        
        .table th, .table td {
            background-color: #fff !important;
            color: #000 !important;
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }
        
        .table thead th {
            background-color: #f8f9fc !important;
        }
        
        .badge {
            border: 1px solid #000 !important;
            color: #000 !important;
        }
        
        .badge-success { background-color: #d4edda !important; }
        .badge-warning { background-color: #fff3cd !important; }
        
        .dataTables_wrapper .row:first-child,
        .dataTables_wrapper .row:last-child {
            display: none !important;
        }
        
        .print-header {
            display: block !important;
            text-align: center;
            border-bottom: 2px solid black;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .print-footer {
            display: block !important;
            text-align: center;
            position: fixed;
            bottom: 0;
            width: 100%;
            border-top: 1px solid #ddd;
            padding-top: 5px;
            font-size: 9pt;
        }
    }
</style>

<div class="container-fluid">
    <!-- Print Header (only visible when printing) -->
    <div class="print-header" style="display: none;">
        <h2>نظام الموارد البشرية</h2>
        <h3>تقرير الحضور اليومي</h3>
        <p>التاريخ: @Model.SelectedDate.ToString("yyyy-MM-dd")</p>
    </div>

    <div class="d-flex justify-content-between mb-4 no-print">
        <h3>تقرير الحضور اليومي</h3>
        <div>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                    <a href="javascript:void(0)" class="dropdown-item" id="printReport">
                        <i class="fas fa-print"></i> طباعة التقرير
                    </a>
             
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4 no-print">
        <div class="card-header bg-primary text-white">
            <h5 class="m-0">اختر التاريخ</h5>
        </div>
        <div class="card-body">
            <form method="get" action="/Attendence/DailyReport" id="dateForm" class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>التاريخ</label>
                        <input type="date" name="date" class="form-control" value="@Model.SelectedDate.ToString("yyyy-MM-dd")" />
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">عرض</button>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                الموظفين الحاضرين بتاريخ @Model.SelectedDate.ToString("yyyy-MM-dd")
            </h6>
            <span class="badge badge-info p-2">
                عدد الحاضرين: @Model.DailyAttendance.Count
            </span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="attendanceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>رقم الموظف</th>
                            <th>اسم الموظف</th>
                            <th>وقت الحضور</th>
                            <th>وقت الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>ساعات العمل الإضافي</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var record in Model.DailyAttendance)
                        {
                            var employee = Model.VempDtls.FirstOrDefault(e => e.EmpNo == record.EmpNo);
                            
                            <tr>
                                <td>@record.EmpNo</td>
                                <td>
                                    @if (employee != null)
                                    {
                                        @employee.EmpNameA
                                    }
                                    else
                                    {
                                        <span class="text-muted">غير معروف</span>
                                    }
                                </td>
                                <td>@record.FirstEntry.ToString("HH:mm")</td>
                                <td>@record.LastEntry.ToString("HH:mm")</td>
                                <td>@record.Hours.ToString("F2")</td>
                                <td>@record.Extra.ToString("F2")</td>
                            
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Print footer (only visible when printing) -->
    <div class="print-footer" style="display: none;">
        <p>تم إنشاء هذا التقرير بواسطة نظام الموارد البشرية - @DateTime.Now.ToString("yyyy-MM-dd HH:mm")</p>
    </div>
</div>

@section Scripts {

    
    <script>
        $(document).ready(function() {
            // Initialize DataTable with export buttons
            var table = createDataTable("#attendanceTable");
            
            // Print button handler
            $('#printReport').on('click', function() {
                // Show print-specific elements
                $('.print-header, .print-footer').show();
                
                // Print the page
                window.print();
                
                // Hide print-specific elements after printing
                setTimeout(function() {
                    $('.print-header, .print-footer').hide();
                }, 500);
            });
        });
    </script>
} 