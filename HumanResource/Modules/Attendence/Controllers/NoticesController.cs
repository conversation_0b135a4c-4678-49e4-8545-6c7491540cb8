﻿using Microsoft.AspNetCore.Mvc;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using Microsoft.AspNetCore.Http;
using HumanResource.Core.Data;
using HumanResource.Modules.Attendence.Models.Entities;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Attendence.ViewModels;
using HumanResource.Modules.Attendence.Models.Enums;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Attendence.Controllers;


[Area("Attendence")]
[Route("Attendence/Notices")]
public class NoticesController : BaseController
{

    private readonly AppHelper _h;
    public NoticesViewModel _v;
    public NoticesController(hrmsContext context, IHttpContextAccessor httpContextAccessor, AppHelper helper) : base(context, httpContextAccessor, helper)
    {
        _h = helper;
        _v = new NoticesViewModel(context, httpContextAccessor, helper);
    }


    [HttpGet("My/{guid?}")]
    public IActionResult My(string? guid)
    {
        _v.EmployeeWarnings = _db.EmployeeWarnings.Where(x =>
        x.EmpNo == _v.Profile.EmpNo &&
        (x.Status == EmployeeWarningStatus.New || x.Status == EmployeeWarningStatus.Processing)
        ).ToList();

        if (guid != null)
        {
            _v.EmployeeWarning = _db.EmployeeWarnings.FirstOrDefault(x => x.Guid == guid && x.EmpNo == _v.Profile.EmpNo);
        }

        return View(_v);
    }


    [HttpGet("Department")]
    public IActionResult Department(int Dg = 0, int Dept = 0, int EmpNo = 0, int Status = -1,
        DateTime? from = null, DateTime? to = null, string searchTerm = "")
    {
        // Check authorization for department notice management
        if (!Can("attendence"))
            return StatusCode(403);

        // Apply organizational hierarchy restrictions based on user role
        ApplyOrganizationalRestrictions(ref Dg, ref Dept);

        // Set filter values in ViewModel
        _v.Dg = Dg;
        _v.Dept = Dept;
        _v.EmpNo = EmpNo;
        _v.Status = Status;
        _v.FromDate = from;
        _v.ToDate = to;
        _v.SearchTerm = searchTerm ?? string.Empty;

        // Load dropdown data
        LoadDropdownData(Dg, Dept);

        // Set default date range if not provided (last 6 months)
        if (!from.HasValue)
        {
            from = DateTime.Today.AddMonths(-6);
            _v.FromDate = from;
        }

        if (!to.HasValue)
        {
            to = DateTime.Today.Add(new TimeSpan(23, 59, 59));
            _v.ToDate = to;
        }
        else
        {
            to = to.Value.Date.Add(new TimeSpan(23, 59, 59));
            _v.ToDate = to;
        }

        // For initial page load, don't load all data - let DataTable handle it via AJAX
        _v.EmployeeWarnings = new List<EmployeeWarning>();

        // Store filter values in ViewBag for JavaScript access
        ViewBag.Dg = Dg;
        ViewBag.Dept = Dept;
        ViewBag.EmpNo = EmpNo;
        ViewBag.Status = Status;
        ViewBag.FromDate = from?.ToString("yyyy-MM-dd");
        ViewBag.ToDate = to?.ToString("yyyy-MM-dd");
        ViewBag.SearchTerm = searchTerm;

        return View(_v);
    }

    [HttpGet("Department/{guid}")]
    public IActionResult DepartmentView(string guid)
    {
        // Check authorization for department notice management
        if (!Can("notices-department|hr|admin"))
            return StatusCode(403);

        // Get the specific warning with employee details
        var warningWithEmployee = (from warning in _db.EmployeeWarnings
                                  join emp in _db.VempDtls on warning.EmpNo equals emp.EmpNo
                                  where warning.Guid == guid
                                  select new { Warning = warning, Employee = emp }).FirstOrDefault();

        if (warningWithEmployee == null)
        {
            TempData["Error"] = "التنبيه غير موجود";
            return RedirectToAction("Department");
        }

        // Apply organizational restrictions to ensure user can view this notice
        var userProfile = _v.Profile;
        if (!Can("admin|hr"))
        {
            // Check if user has access to this employee's notices based on organizational hierarchy
            bool hasAccess = false;

            if (Can("department-manager") && userProfile.DeptCode.HasValue)
            {
                hasAccess = warningWithEmployee.Employee.DeptCode == userProfile.DeptCode.Value;
            }
            else if (Can("dg-manager") && userProfile.DgCode.HasValue)
            {
                hasAccess = warningWithEmployee.Employee.DgCode == userProfile.DgCode.Value;
            }
            else if (userProfile.DeptCode.HasValue)
            {
                hasAccess = warningWithEmployee.Employee.DeptCode == userProfile.DeptCode.Value;
            }

            if (!hasAccess)
            {
                TempData["Error"] = "لا تتوفر لديك صلاحية لعرض هذا التنبيه";
                return RedirectToAction("Department");
            }
        }

        // Set the warning and employee details in the view model
        _v.EmployeeWarning = warningWithEmployee.Warning;
        _v.EmployeeWarnings = new List<EmployeeWarning> { warningWithEmployee.Warning };

        // Add employee details to ViewBag for display
        ViewBag.Employee = warningWithEmployee.Employee;

        return View(_v);
    }

    // Helper method to apply organizational restrictions based on user role
    private void ApplyOrganizationalRestrictions(ref int Dg, ref int Dept)
    {
        // If user is not admin or HR, restrict to their organizational scope
        if (!Can("attendence"))
        {
            var userProfile = _v.Profile;

            // If user has department manager role, restrict to their department
            if (Can("department-manager") && userProfile.DeptCode.HasValue)
            {
                Dept = userProfile.DeptCode.Value;
                Dg = userProfile.DgCode ?? 0;
            }
            // If user has DG role, restrict to their DG
            else if (Can("dg-manager") && userProfile.DgCode.HasValue)
            {
                Dg = userProfile.DgCode.Value;
                // Don't restrict department within the DG
            }
            // For other roles, restrict to their own department if available
            else if (userProfile.DeptCode.HasValue)
            {
                Dept = userProfile.DeptCode.Value;
                Dg = userProfile.DgCode ?? 0;
            }
        }
    }

    // Helper method to load dropdown data
    private void LoadDropdownData(int selectedDg = 0, int selectedDept = 0)
    {
        // Load DG dropdown
        _v.DgList = _db.TdgCodes.Where(d => d.DgCode > 0).OrderBy(d => d.DgDespA).ToList();

        // Load Department dropdown (filtered by DG if selected)
        if (selectedDg > 0)
        {
            _v.DeptList = _db.TDeptCode.Where(d => d.DgCode == selectedDg && d.DeptCode > 0)
                                      .OrderBy(d => d.DeptDespA).ToList();
        }
        else
        {
            _v.DeptList = _db.TDeptCode.Where(d => d.DeptCode > 0).OrderBy(d => d.DeptDespA).ToList();
        }

        // Load Employee dropdown (filtered by Department if selected)
        if (selectedDept > 0)
        {
            _v.EmpList = _db.VempDtls.Where(e => e.DeptCode == selectedDept && e.EmpNo > 1 && e.EmpNo < 1000)
                                     .OrderBy(e => e.EmpNameA).ToList();
        }
        else if (selectedDg > 0)
        {
            _v.EmpList = _db.VempDtls.Where(e => e.DgCode == selectedDg && e.EmpNo > 1 && e.EmpNo < 1000)
                                     .OrderBy(e => e.EmpNameA).ToList();
        }
        else
        {
            // Load limited set for performance
            _v.EmpList = new List<VempDtl>();
        }
    }

    // AJAX endpoint for DataTable server-side processing
    [HttpPost("Department/Datatable")]
    public IActionResult DepartmentDatatable([FromForm] DataTableHelper datatable,
        int Dg = 0, int Dept = 0, int EmpNo = 0, int Status = -1,
        DateTime? from = null, DateTime? to = null, string searchTerm = "")
    {
        // Check authorization
        if (!Can("attendence"))
            return StatusCode(403);

        try
        {
            // Apply organizational restrictions
            ApplyOrganizationalRestrictions(ref Dg, ref Dept);
            // Set default date range if not provided
            var dateFrom = from ?? DateTime.Today.AddMonths(-6);
            var dateTo = to?.Date.Add(new TimeSpan(23, 59, 59)) ?? DateTime.Today.Add(new TimeSpan(23, 59, 59));

            // Build base query with joins for employee information
            var query = from warning in _db.EmployeeWarnings
                       join emp in _db.VempDtls on warning.EmpNo equals emp.EmpNo
                       where warning.WarningDate >= dateFrom && warning.WarningDate <= dateTo
                       select new
                       {
                           Warning = warning,
                           Employee = emp
                       };

            // Apply organizational filtering
            if (Dg > 0)
            {
                query = query.Where(x => x.Employee.DgCode == Dg);
            }

            if (Dept > 0)
            {
                query = query.Where(x => x.Employee.DeptCode == Dept);
            }

            if (EmpNo > 0)
            {
                query = query.Where(x => x.Warning.EmpNo == EmpNo);
            }

            // Apply status filtering
            if (Status >= 0)
            {
                query = query.Where(x => (int)x.Warning.Status == Status);
            }

            // Apply search filtering
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(x =>
                    (x.Employee.EmpNameA != null && x.Employee.EmpNameA.Contains(searchTerm)) ||
                    (x.Warning.Title != null && x.Warning.Title.Contains(searchTerm)) ||
                    (x.Warning.Body != null && x.Warning.Body.Contains(searchTerm)) ||
                    x.Warning.EmpNo.ToString().Contains(searchTerm));
            }

            // Apply DataTable search
            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                var searchValue = datatable.Search.Value;
                query = query.Where(x =>
                    (x.Employee.EmpNameA != null && x.Employee.EmpNameA.Contains(searchValue)) ||
                    (x.Warning.Title != null && x.Warning.Title.Contains(searchValue)) ||
                    x.Warning.EmpNo.ToString().Contains(searchValue));
            }

            // Get total count before pagination
            var totalRecords = query.Count();

            // Apply sorting
            if (datatable.Order != null && datatable.Order.Any())
            {
                var orderColumn = datatable.Order[0];
                var columnIndex = orderColumn.Column;
                var sortDirection = orderColumn.Dir;

                // Map column index to sorting logic
                query = columnIndex switch
                {
                    0 => sortDirection == "asc"
                        ? query.OrderBy(x => x.Warning.EmpNo)
                        : query.OrderByDescending(x => x.Warning.EmpNo),
                    1 => sortDirection == "asc"
                        ? query.OrderBy(x => x.Employee.EmpNameA)
                        : query.OrderByDescending(x => x.Employee.EmpNameA),
                    2 => sortDirection == "asc"
                        ? query.OrderBy(x => x.Employee.DeptDespA)
                        : query.OrderByDescending(x => x.Employee.DeptDespA),
                    3 => sortDirection == "asc"
                        ? query.OrderBy(x => x.Warning.Title)
                        : query.OrderByDescending(x => x.Warning.Title),
                    4 => sortDirection == "asc"
                        ? query.OrderBy(x => x.Warning.WarningDate)
                        : query.OrderByDescending(x => x.Warning.WarningDate),
                    5 => sortDirection == "asc"
                        ? query.OrderBy(x => x.Warning.Status)
                        : query.OrderByDescending(x => x.Warning.Status),
                    _ => query.OrderByDescending(x => x.Warning.WarningDate)
                };
            }
            else
            {
                query = query.OrderByDescending(x => x.Warning.WarningDate);
            }

            // Apply pagination
            var pagedData = query.Skip(datatable.Start).Take(datatable.Length).ToList();

            // Format data for DataTable
            var formattedData = pagedData.Select(x => new
            {
                guid = x.Warning.Guid,
                empNo = x.Warning.EmpNo,
                empName = x.Employee.EmpNameA,
                department = x.Employee.DeptDespA,
                title = x.Warning.Title ?? "تنبيه عام",
                warningDate = x.Warning.WarningDate?.ToString("yyyy-MM-dd") ?? "",
                status = (int)x.Warning.Status,
                statusText = _v.GetStatusText(x.Warning.Status),
                statusClass = _v.GetStatusCssClass(x.Warning.Status),
            }).ToList();

            return Json(new
            {
                draw = datatable.Draw,
                recordsTotal = totalRecords,
                recordsFiltered = totalRecords,
                data = formattedData
            });
        }
        catch (Exception ex)
        {
            return Json(new
            {
                draw = datatable.Draw,
                recordsTotal = 0,
                recordsFiltered = 0,
                data = new List<object>(),
                error = ex.Message
            });
        }
    }

    // AJAX endpoint for cascading DG dropdown
    [HttpGet("GetDepartments")]
    public IActionResult GetDepartments(int dgCode)
    {
        try
        {
            var departments = _db.TDeptCode
                .Where(d => d.DgCode == dgCode && d.DeptCode > 0)
                .Select(d => new { d.DeptCode, d.DeptDespA })
                .OrderBy(d => d.DeptDespA)
                .ToList();

            return Json(departments);
        }
        catch (Exception ex)
        {
            return Json(new { error = ex.Message });
        }
    }

    // AJAX endpoint for cascading Employee dropdown
    [HttpGet("GetEmployees")]
    public IActionResult GetEmployees(int? dgCode = null, int? deptCode = null)
    {
        try
        {
            var query = _db.VempDtls.Where(e => e.EmpNo > 1 && e.EmpNo < 1000);

            if (deptCode.HasValue && deptCode > 0)
            {
                query = query.Where(e => e.DeptCode == deptCode);
            }
            else if (dgCode.HasValue && dgCode > 0)
            {
                query = query.Where(e => e.DgCode == dgCode);
            }

            var employees = query
                .Select(e => new { e.EmpNo, e.EmpNameA })
                .OrderBy(e => e.EmpNameA)
                .ToList();

            return Json(employees);
        }
        catch (Exception ex)
        {
            return Json(new { error = ex.Message });
        }
    }

    // Status update endpoint
    [HttpPost("UpdateStatus")]
    public async Task<IActionResult> UpdateStatus(string guid, int status, string notes = "")
    {
        try
        {
            // Check authorization for status updates
            if (!Can("attendence"))
                return Json(new { success = false, message = "لا تتوفر لديك صلاحية لتحديث حالة التنبيهات" });

            var warning = await _db.EmployeeWarnings.FirstOrDefaultAsync(w => w.Guid == guid);
            if (warning == null)
            {
                return Json(new { success = false, message = "التنبيه غير موجود" });
            }

            // Validate status
            if (!Enum.IsDefined(typeof(EmployeeWarningStatus), status))
            {
                return Json(new { success = false, message = "حالة غير صحيحة" });
            }

            // Update status
            warning.Status = (EmployeeWarningStatus)status;
            warning.Timestamp = DateTime.Now;
            warning.Note = notes;

            await _db.SaveChangesAsync();

            // Log the status change
            Log(
                warning.Guid,
                "Notice Status Update",
                $"Status changed to {warning.Status} by {_v.Profile.EmpNameA}"
            );

            // Send notification to employee if needed
            if (warning.EmpNo.HasValue)
            {
                await _h.Notify().Create(
                    empNo: warning.EmpNo.Value,
                    title: "تحديث حالة التنبيه",
                    text: $"تم تحديث حالة التنبيه إلى: {_v.GetStatusText(warning.Status)}",
                    url: $"/Attendence/Notices/My/{warning.Guid}"
                );
            }

            return Json(new
            {
                success = true,
                message = "تم تحديث الحالة بنجاح",
                newStatus = (int)warning.Status,
                statusText = _v.GetStatusText(warning.Status),
                statusClass = _v.GetStatusCssClass(warning.Status)
            });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "حدث خطأ أثناء تحديث الحالة: " + ex.Message });
        }
    }

    // Bulk status update endpoint
    [HttpPost("BulkUpdateStatus")]
    public async Task<IActionResult> BulkUpdateStatus([FromBody] BulkStatusUpdateRequest request)
    {
        try
        {
            // Check authorization
            // if (!Can("notices-status-update"))
            //     return StatusCode(403);

            if (request.Guids == null || !request.Guids.Any())
            {
                return Json(new { success = false, message = "لم يتم تحديد أي تنبيهات" });
            }

            // Validate status
            if (!Enum.IsDefined(typeof(EmployeeWarningStatus), request.Status))
            {
                return Json(new { success = false, message = "حالة غير صحيحة" });
            }

            var warnings = await _db.EmployeeWarnings
                .Where(w => request.Guids.Contains(w.Guid))
                .ToListAsync();

            if (!warnings.Any())
            {
                return Json(new { success = false, message = "لم يتم العثور على التنبيهات المحددة" });
            }

            var updatedCount = 0;
            foreach (var warning in warnings)
            {
                warning.Status = (EmployeeWarningStatus)request.Status;
                warning.Timestamp = DateTime.Now;
                warning.Note = request.Notes;
                updatedCount++;

                // Send notification to employee
                if (warning.EmpNo.HasValue)
                {
                    await _h.Notify().Create(
                        empNo: warning.EmpNo.Value,
                        title: "تحديث حالة التنبيه",
                        text: $"تم تحديث حالة التنبيه إلى: {_v.GetStatusText(warning.Status)}",
                        url: $"/Attendence/Notices/My/{warning.Guid}"
                    );
                }
            }

            await _db.SaveChangesAsync();

            // Log the bulk update
            Log(
                string.Join(",", request.Guids),
                "تم تحديث حالة التنبيهات بالمجموعة",
                $"تم تحديث حالة التنبيهات بالمجموعة إلى {(EmployeeWarningStatus)request.Status} لـ {updatedCount} تنبيهات بواسطة {_v.Profile.EmpNameA}"
            );

            return Json(new
            {
                success = true,
                message = $"تم تحديث {updatedCount} تنبيه بنجاح",
                updatedCount = updatedCount
            });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "حدث خطأ أثناء التحديث المجمع: " + ex.Message });
        }
    }


}

// DTO for bulk status update
public class BulkStatusUpdateRequest
{
    public List<string> Guids { get; set; } = new List<string>();
    public int Status { get; set; }
    public string Notes { get; set; } = string.Empty;
}

