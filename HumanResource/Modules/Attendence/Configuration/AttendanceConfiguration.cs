using HumanResource.Modules.Attendence.Providers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Attendence.Configuration
{
    /// <summary>
    /// Configuration for the Attendance module
    /// </summary>
    public static class AttendanceConfiguration
    {
        /// <summary>
        /// Registers all Attendance module services
        /// </summary>
        public static IServiceCollection AddAttendanceServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, AttendanceNavigationProvider>();

            // Register badge providers
            services.AddScoped<IBadgeProvider, AttendanceBadgeProvider>();

            // Register task providers
            services.AddScoped<ITaskProvider, AttendenceTaskProvider>();

     

            // Register module services
            // services.AddScoped<AttendanceService>();

            return services;
        }
    }
} 