﻿using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Attendence.Models.Entities;

[Keyless]
public class FPRecords
{

    
    [Column("EMP_NO")]
    public string _EmpNo { set;  get; }

    [NotMapped]
    public int EmpNo {
        
        get
        {
            return Convert.ToInt32(_EmpNo);
        }
      
    }

    [Column("TRANS_DATE")]
    public DateTime? Date { set;  get; }

    [Column("TRANS_HOURS")]
    public int? h { set; get; }

    [Column("TRANS_MINUTES")]
    public int? i { set; get; }

    [Column("TRANS_DATETIME")]
    public DateTime? CreatedAt { set; get; }



}

