﻿
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Transports.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Transports.ViewModels;

public class TransportsViewModel : BaseViewModel
{

   

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly hrmsContext _context;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly IHttpContextAccessor _http;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    private AppHelper _h;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword

    public  TransportsViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _context = context;
        _db = context;
        _http = httpContextAccessor;
        _h = helper;
    }


	public List<Cars> Cars { get; set; }
    public Cars Car { get; set; }
    public List<CarsGasDtl> CarsGasDtl { get; set; }

    public List<CarsViolations> CarsViolations { get; set; }
    public List<CarsServices> CarsServices { get; set; }
    public CarsServices ServiceRecord { get; set; } // one record
    public CarsViolations ViolationRecord { get; set; } // one record
    public CarsGasDtl GasRecord { get; set; } // one record

    public List<CarsRequests> CarsRequests { get; set; }
    public CarsRequests CarsRequest { get; set; }

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public List<VempDtl> StaffList { get; set; }
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword

    public List<string> RequestStatusList { get; set; } = new List<string>
    {
        "New",
        "Processing",
        "Approved",
        "Completed",
        "Rejected",
        "Approval Canceled",
		"Canceled"
	}; 

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public CarsRequests GetActiveRequestByCar(int CarId)
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    {

        var request = _db.CarsRequests.Where(r => r.CarId == CarId && r.Status=="Approved").FirstOrDefault();

        if (request!=null)
        {
            return request;
        }

        return null;
    }

}

