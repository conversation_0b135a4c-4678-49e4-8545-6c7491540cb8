﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._l("Service records")</h3>

    <div>
   
        <button data-toggle="modal" data-target="#new-record-modal" class="btn btn-primary rounded-pill"><i class="fa fa-plus"></i> @Model._l("New request")</button>
      
        
    </div>
</div>
<div class="card shadow">
    

    <table class="table datatable table-sm">
        <thead>
            <tr>
                <td>@Model._("No")</td>
                <td>@Model._("Vehicle")</td>
                <td>@Model._("Date")</td>
                <td>@Model._("Type")</td>
                <td>@Model._("Description")</td>

                <td>@Model._("Cost")</td>
                <td>@Model._("Status")</td>

            </tr>
        </thead>
        <tbody>
            @foreach (var service in Model.CarsServices)
            {
            <tr>
                <td> <a href="#" onclick="get_record_data(@service.Id)">#@service.Id</a></td>
                <td>@service.Car.Plate <br> <i><small class="text-muted">@service.Car.Name</small></i></td>
                <td>@Model._d(service.Date)</td>
                <td>@Model._(service.Type)</td>
                <td>@service.Description</td>

                <td>
                    @if(service.Cost>0){
                    @Model._amount(service.Cost)
                    }else{
                    @Model._("--")
                    }

                </td>

                <td>
                    @Model._(@service.Status)
                </td>

            </tr>
            }
        </tbody>
    </table>
</div>

<form action="~/Transports/CreateService/" method="POST" class="ajax">
    <div class="modal fade" id="new-record-modal" tabindex="-1" role="dialog" aria-labelledby="new-record-modalLabel" aria-hidden="true">

        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-record-modalLabel">@Model._("New record")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <div>
                                <label for="">@Model._("Vehicle")</label>
                                <select name="CarId" class="select2" required>
                                    <option selected disabled hidden>@Model._("Select a vehicle")</option>
                                    @foreach (var car in Model.Cars)
                                    {
                                    <option value="@car.CarId" data-CurrentOdo="@car.CurrentOdo"
                                        data-NextOdoService="@car.NextOdoService">@car.Name <br>
                                        @car.Name</option>
                                    }

                                </select>
                            </div>
                            <br>

                            <label for="">@Model._("Date")</label>
                            <input type="date" name="Date" class="form-control" value="@DateTime.Now.ToString("yyyy-MM-dd")" required><br>

                            <label for="">@Model._("Current ODO")</label>
                            <input type="text" name="CurrentOdo" class="form-control" required><br>

                            <label for="">@Model._l("Next Service ODO")</label>
                            <input type="number" name="NextOdoService" class="form-control" value="0" required>
                            <br>

                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._("Service Type")</label>
                            <select name="Type" class="form-control">
                                <option value="Regular Service">@Model._("Regular Service")</option>
                                <option value="Specialized Service">@Model._("Specialized Service")</option>
                            </select><br>

                            <label for="">@Model._("Description")</label>
                            <input type="text" name="Description" class="form-control" required><br>

                            <label for="">@Model._("Long Description")</label>
                            <textarea name="LongDescription" class="form-control" cols="30"></textarea><br>

                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._("Close")</button>
                    <button type="submit" class="btn btn-primary">@Model._("Submit")</button>
                </div>
            </div>
        </div>
    </div>
</form>


<div class="modal fade" id="edit-record-modal" tabindex="-1" role="dialog" aria-labelledby="edit-record-modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="edit-record-modalLabel">@Model._("View")</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="edit-modal-html">

            </div>
        </div>
    </div>
</div>

<script>
    function get_record_data(id) {
        $.get("/Transports/ViewServiceRecord/" + id, function (data) {
            $('#edit-modal-html').html(data);
            $('#edit-record-modal').modal().show();

        });
    }

    $(document).ready(function () {
        $("select[name='CarId']").change(function () {
            var selectedOption = $(this).find('option:selected');
            $("input[name='CurrentOdo']").val(selectedOption.data('currentodo'));
        });
    });
</script>