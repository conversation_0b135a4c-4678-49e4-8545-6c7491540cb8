﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

@{
    Layout = null;
}
<form action="~/Transports/UpdateViolation/@Model.ViolationRecord.Id" method="post" class="ajax">
    <div class="row">
        <div class="col-lg-6">
           
            <input type="hidden" name="Id" value="@Model.ViolationRecord.Id">

            <div>
                <label for="">@Model._("Vehicle")</label>
                <select name="CarId" class="select2 form-control" required>
                    <option selected disabled hidden>@Model._("Select a vehicle")</option>
                    @foreach (var car in Model.Cars)
                    {
                        if (Model.Car.CarId == car.CarId)
                        {
                            <option value="@car.CarId" selected>@car.Plate <br> @car.Name</option>
                        }
                        else
                        {
                            <option value="@car.CarId">@car.Plate <br> @car.Name</option>
                        }
                    }

                </select>
            </div><br>

            <label for="">@Model._l("Date")</label>
            <input type="datetime-local" name="Date" class="form-control" value="@Model.ViolationRecord.Date.ToString("yyyy-MM-ddTHH:mm")" required><br>


            <label for="">@Model._l("Description")</label>
            <input type="text" name="Description" value="@Model.ViolationRecord.Description" class="form-control"
                   required><br>

            <label for="">@Model._l("Location")</label>
            <input type="text" name="Location" class="form-control" value="@Model.ViolationRecord.Location" required><br>

        </div>
        <div class="col-lg-6">

            <div>
                <label for="">@Model._l("Staff")</label>
                <select name="StaffId" class="select2 form-control" placeholder="Select Vehicle" required>
                    <option value="" selected disabled hidden>@Model._l("Select Staff")</option>
                    @foreach (var staff in Model.StaffList)
                    {
                        if (@staff.EmpNo == Model.ViolationRecord.StaffId)
                        {
                            <option selected value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                        }
                        else
                        {
                            <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                        }


                    }
                </select>
            </div>

            <label for="">@Model._l("Total Cost")</label>
            <input type="number" name="Cost" class="form-control" step="0.001" min="0"
                   value="@Model.ViolationRecord.Cost" required><br>




            <label for="">@Model._l("Reference")</label>
            <input type="text" name="Reference" value="@Model.ViolationRecord.Reference" class="form-control"><br>

            <label for="">@Model._l("Receipt No")</label>
            <input type="text" name="ReceiptNo" class="form-control" value="@Model.ViolationRecord.ReceiptNo" required><br>

        </div>
    </div>
    <br>

    <div class="d-flex justify-content-between">
        <a href="/Transports/DeleteViolation2/@Model.ViolationRecord.Id" class=" btn btn-danger after-confirm">
            <i class="fas fa-trash-alt"></i> @Model._("Delete")</a>
        <div>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._("Close")</button>
            <button type="submit" class="btn btn-primary">@Model._("Save")</button>
        </div>
    </div>
</form>