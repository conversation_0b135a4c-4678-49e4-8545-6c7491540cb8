@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel
@{
    Layout = Model.Page.Layout;
}


<div class="d-flex">
<div class="btn-group ">
  @if (Model.CarsRequest.Status=="New")
  {
      <a href="~/Transports/My/DeleteRequest/@Model.CarsRequest.Id" class="btn btn-danger after-confirm "><i class="fas fa-trash-alt"></i> @Model._l("Delete")</a>
  }
</div>
</div>
<br>
<div class="card shadow table-responsive">
    <table class="table">
        <thead class="bg-primary">
            <tr>
                <th>@Model._l("رقم الطلب")</th>
                <th>@Model._l("تاريخ الطلب")</th>

                <th>@Model._l("الموظف")</th>
                <th>@Model._l("السبب")</th>
                <th>@Model._l("الحالة")</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>#@Model.CarsRequest.Id</td>
                <td>@Model.CarsRequest.FromDate <br> @Model.CarsRequest.ToDate</td>
        
                <td>@Model.CarsRequest.Staff.EmpNameA</td>
                <td>@Model.CarsRequest.Reason</td>
                <td>
               
                        @Model._l((Model.CarsRequest.Status!=null?Model.CarsRequest.Status:""))
                </td>
            </tr>
        </tbody>
    </table>
</div>
@if (@Model.Car!=null)
{
    
<div class="card shadow table-responsive">
    <div class="card-header bg-primary">
        @Model._l("تفاصيل السيارة")
    </div>
    <table class="table">
        <thead class="bg-primary">
            <th>@Model._l("اللوحة")</th>
            <th>@Model._l("السيارة")</th>
            <th>@Model._l("المسافة المقطوعة")</th>
            <th>@Model._l("تاريخ الخدمة التالية")</th>
        </thead>
        <tbody>
            <tr>
                <td>@Model.CarsRequest.Car.Plate</td>
                <td>@Model.CarsRequest.Car.Name</td>
                <td>@Model.CarsRequest.Car.CurrentOdo</td>
                <td>@Model.CarsRequest.Car.NextOdoService</td>
                
            </tr>
        </tbody>
    </table>
</div>

}


