﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

<div class="d-flex justify-content-between py-2">

    <h3>@Model._l("طلبات السيارات")</h3>

    <div>
        <button data-toggle="modal" data-target="#new-request-modal" class="btn btn-primary "> <i class="fa fa-plus"></i> @Model._l("طلب جديد")</button>
    </div>
</div>
<div class="card sahdow">
    <table class="table table-sm datatable">
        <thead class="bg-primary text-white">
            <tr>
                <td>@Model._l("رقم الطلب")</td>
                <td>@Model._l("تاريخ الطلب")</td>
                <td>@Model._l("من - إلى")</td>
                <td>@Model._l("الحالة")</td>

            </tr>
        </thead>
        <tbody>
          @foreach (var request in Model.CarsRequests)
          {
            <tr>
              <td>
              
                   <a href="~/Transports/My/View/@request.Id">#@request.Id</a>
             
            
              </td>

              <td>@request.CreatedAt</td>
              <td>@request.FromDate - @request.ToDate</td>
              <td>@request.Status</td>
          
            </tr>
          }
        </tbody>
    </table>
</div>

<form  method="post" class="ajax">
  <div class="modal fade" id="new-request-modal" tabindex="-1" role="dialog" aria-labelledby="new-vehicle-modalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._l("طلب جديد")</h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-6">

              <div>
                <label for="">@Model._l("السمؤول المباشر")</label>
                <select name="DepManagerId" class="select2" placeholder="Select Vehicle" required>
                  <option value="0" selected disabled hidden>@Model._l("اختر موظف")</option>
                  @foreach (var staff in Model._h.Managers(Model.Auth.EmpNo))
                  {
                  <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                  }

                </select>
              </div>
              <br>

              <label for="">@Model._l("من")</label>
              <input type="datetime-local" name="FromDate" class="form-control" required value="@DateTime.Now.ToString("yyyy-MM-ddTHH:mm")"><br>

              <label for="">@Model._l("إلى")</label>
              <input type="datetime-local" name="ToDate" class="form-control" required><br>


            </div>
            <div class="col-lg-6">

       

        
              <label for="">@Model._l("السبب")</label>
              <textarea name="Reason" class="form-control"></textarea>
              <br>

              

              
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("إغلاق")</button>
          <button type="submit" class="btn btn-primary">@Model._l("إرسال")</button>
        </div>
      </div>
    </div>
  </div>

</form>

@foreach (var request in Model.CarsRequests)
{

<form action="~/Transports/My/UpdateRequest/@request.Id" method="post" class="ajax">
  <div class="modal fade" id="<EMAIL>-request-modal" tabindex="-1" role="dialog" aria-labelledby="view-vehicle-modalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title" id="view-vehicle-modalLabel">@Model._l("عرض الطلب")</h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-6">

              <div>
                <label for="">@Model._l("السمؤول المباشر")</label>
                <select name="DepManagerId" class="select2" placeholder="Select Vehicle" required>
                  <option value="0" selected disabled hidden>@Model._l("اختر موظف")</option>
                  @foreach (var staff in Model._h.Managers(Model.Auth.EmpNo))
                  {
                    @if (staff.EmpNo == request.DepManagerId)
                    {
                        <option value="@staff.EmpNo" selected>@staff.EmpNo - @staff.EmpNameA</option>
                    }else{
                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    }
                  }
                </select>
              </div>
              <br>

              <label for="">@Model._l("من")</label>
              <input type="datetime-local" name="FromDate" class="form-control" required value="@request.FromDate.ToString("yyyy-MM-ddTHH:mm")"><br>

              <label for="">@Model._l("إلى")</label>
              <input type="datetime-local" name="ToDate" class="form-control" required value="@request.ToDate.ToString("yyyy-MM-ddTHH:mm")"><br>


            </div>
            <div class="col-lg-6">

       

        
              <label for="">@Model._l("السبب")</label>
              <textarea name="Reason" class="form-control">@request.Reason</textarea>
              <br>

              

              
            </div>
          </div>
        </div>
        @if (request.Status=="New")
        {
            <div class="modal-footer d-flex">
                <a href="/Transports/DeleteMyRequest/@request.Id" class="btn btn-danger btn-block rounded-pill after-confirm" >@Model._l("حذف")</a>
                <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("إغلاق")</button>
                <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("حفظ")</button>
            </div>
        }
        
      </div>
    </div>
  </div>

</form>

}