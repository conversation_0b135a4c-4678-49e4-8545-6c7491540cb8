﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel



<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-lg-4">
                <div class=" d-flex align-items-center justify-content-start">  
                    <i class="far fa-road fa-2x mr-3"></i>
                    <h5><strong>@Model._l("Odometer"):</strong> @Model.Car.CurrentOdo KM</h5>
                </div>
                <small class="text-muted">@Model._amount((Model.Car.TotalServiceCost+Model.Car.TotalGasCost)/Model.Car.CurrentOdo) OMR / KM</small>
            </div>
            <div class="col-lg-4">
                <div class=" d-flex align-items-center justify-content-start"> 
                <i class="fas fa-car-mechanic fa-2x mr-3"></i>
                <h5><strong>@Model._l("Maintenance"):</strong> @Model._amount(Model.Car.TotalServiceCost) OMR</h5>
                </div>
                
                <small class="text-muted">@Model._("Next Service"): @Model.Car.NextOdoService</small>
            </div>
            <div class="col-lg-4">
           
                <div class=" d-flex align-items-center justify-content-start"> 
                <i class="far fa-oil-can fa-2x mr-3"></i>
                <h5><strong>@Model._l("Gas"):</strong> @Model._amount(Model.Car.TotalGasCost) OMR</h5>
                </div>
                <small class="text-muted">@Model._amount(Model.Car.TotalGasCost/Model.Car.CurrentOdo) OMR / KM</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
<div class="col-lg-5">
    <div class="card shadow">
        <div class="card-header ">
            <div class="d-flex justify-content-between">
                <h3>@Model.Car.Plate 
                    <button class="btn btn-link text-dark" data-toggle="modal" data-target="#edit-vehicle-modal"><i class="far fa-edit"></i></button>
                </h3>

                <div>
                     @if(@Model.Car.Status=="Unavailable"){
                    <span class="badge badge-danger rounded-pill px-3" style="border: 3px white solid;">@Model._l(Model.Car.Status)</span>

                  }
                  else{
                    <span class="badge badge-success rounded-pill px-3" style="border: 3px white solid;">@Model._l("Available")</span>
             
                  }

                </div>
            </div>
        </div>

        <table class="table">
             <tr>
                <th>@Model._l("Vehicle")</th>
                <td>@Model.Car.Name</td>
            </tr>

            <tr>
                <th>@Model._l("Year")</th>
                <td>@Model.Car.YearModel</td>
            </tr>


            <tr>
                <th>@Model._l("Description")</th>
                <td>@Model.Car.Description</td>
            </tr>

            

            <tr>
                <th>@Model._l("VIN")</th>
                <td>@Model.Car.Vin</td>
            </tr>

             <tr>
                <th>@Model._l("Gas Card")</th>
                <td>@Model.Car.GasCardNo</td>
            </tr>

            <tr>
                <th>@Model._l("Expiry")</th>
                <td>@Model.Car.Expiry.ToString("yyyy-MM-dd")</td>
            </tr>
            <tr>
                <th>@Model._l("Status")</th>
                <td>  @if(@Model.Car.Status=="Unavailable"){
                    <span class="badge badge-danger rounded-pill px-3" style="border: 3px white solid;">@Model._l(Model.Car.Status)</span>

                  }
                  else{
                    <span class="badge badge-success rounded-pill px-3" style="border: 3px white solid;">@Model._l("Available")</span>
             
                  }</td>
            </tr>
            @if (@Model.Car.Status=="Unavailable")
            {
                <tr>
                    <th>@Model._l("Staff")</th>
                    <td></td>
                </tr>
            }
            
        </table>
    </div>

    <div class="card shadow">
        <div class="card-header  d-flex justify-content-between">
            <h3>@Model._l("Maintenance")</h3>
            <div>
                <button class="btn btn-primary rounded-pill" data-toggle="modal" data-target="#new-service-modal"><i class="fas fa-plus"></i> @Model._l("New")</button>
            </div>
        </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <td>@Model._l("Date")</td>
                    <td>@Model._l("ODO")</td>
                    <td>@Model._l("Type")</td>
                    <td>@Model._l("Cost")</td>
                </tr>
            </thead>
            <tbody>
                @foreach(var service in Model.Car.CarsServices)
                {
                    <tr>
                        <td class="actions">@service.Date.ToString("yyyy-MM-dd")
                    <a href="#" data-toggle="modal" data-target="#<EMAIL>-modal" class="action-link ">@Model._l("View")</a>
                </td>
                        <td>@service.CurrentOdo</td>
                        <td>@service.Type</td>
                        <td>@service.Cost.ToString("F3")</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>


    <div class="card shadow">
        <div class="card-header  d-flex justify-content-between">
            <h3>@Model._l("Gas")</h3>
            <div>
                <button class="btn btn-primary rounded-pill" data-toggle="modal" data-target="#new-gas-modal"><i class="fas fa-plus"></i> @Model._l("New")</button>
            </div>
        </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <td>@Model._l("Date")</td>
                    <td>@Model._l("ODO")</td>
                  
                    <td>@Model._l("Total Cost")</td>
                </tr>
            </thead>
            <tbody>
                @foreach(var gas in Model.Car.CarsGasDtl)
                {
                    <tr>
                        <td class="actions">@gas.Date.ToString("yyyy-MM-dd")
                    <a href="#" data-toggle="modal" data-target="#<EMAIL>-modal" class="action-link ">@Model._l("View")</a>
                </td>
                        <td>@gas.CurrentOdo</td>
                   
                        <td>@gas.Cost.ToString("F3")</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>


    <div class="card shadow">
        <div class="card-header  d-flex justify-content-between">
            <h3>@Model._l("Violations")</h3>
            <div>
                <button class="btn btn-primary rounded-pill" data-toggle="modal" data-target="#new-violation-modal"><i class="fas fa-plus"></i> @Model._l("New")</button>
            </div>
        </div>
        <table class="table table-sm">
            <thead>
                <tr>
                    <td>@Model._l("Date")</td>
                    <td>@Model._l("Staff")</td>
                    <td>@Model._l("Description")</td>
                </tr>
            </thead>
            <tbody>
                @foreach(var violation in Model.Car.CarsViolations)
                {
                    <tr>
                        <td class="actions">@violation.Date.ToString("yyyy-MM-dd")
                    <a href="#" data-toggle="modal" data-target="#<EMAIL>-modal" class="action-link ">View</a>
                </td>
                        <td>@violation.Date</td>
                        <td>@Model._h.StaffData(violation.StaffId).EmpNameA</td>
                        <td>@violation.Description</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

</div>

<div class="col-lg-7">
    @if (@Model.Car.Status=="Unavailable")
    {
       <div class="card shadow">
        <div class="card-body">
            <div class="row">
                <div class="col-lg-8 text-center">
                     <p class="m-0">@Model._l("Request No"):<a href="~/Transports/ViewRequest/@Model.CarsRequest.Id">#@Model.CarsRequest.Id</a></p>
                     <p class="m-0">@Model.Car.Staff.EmpNameA</p>
                     <p class="m-0">@Model.Car.Staff.EmpNo - @Model.Car.Staff.DeptDespA</p>
                     <br>

                     <div class="d-flex justify-content-center">
                        <a href="~/Transports/ViewRequest/@Model.CarsRequest.Id" class="btn btn-larg btn-primary rounded-pill  mx-1">@Model._l("View request")</a>
               
                       
                    </div>
                </div>
                <div class="col-lg-4 text-center d-flex align-items-center justify-content-center">
                    <div class="spinner-grow" style="width: 3rem; height: 3rem;" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
       </div>
    }
    <div class="card shadow">
        <div class="card-header ">
            <div class="d-flex">
                <h3>@Model._l("History")</h3>
            </div>
        </div>

        <table class="table datatable table-sm table-striped">
            <thead>
                <tr>
                    <td>@Model._l("Request Date")</td>
                    <td>@Model._l("From")</td>
                    <td>@Model._l("To")</td>
                    <td>@Model._l("Staff")</td>
                    <td>@Model._l("Status")</td>

                </tr>
            </thead>
            <tbody>
             
                @foreach (var request in Model.CarsRequests)
                {
                    <tr>
                        <td>
                            <p class="m-0"><a href="#">@Model._l("No."): @request.Id</a></p>
                            <p class="m-0"> @Model._dt(request.CreatedAt)</p>
                            <p class="m-0">@Model._l("ODO"): @request.CurrentOdo</p>
                        </td>
                        <td>@Model._dt(request.FromDate)</td>
                        <td>@Model._dt(request.ToDate)</td>
                        <td>
                            <p class="m-0"><small>@Model._h.StaffData(request.StaffId).EmpNameA</small></p>
                                <p class="m-0">@Model._h.StaffData(request.StaffId).EmpNo - @Model._h.StaffData(request.StaffId).DeptDespA</p>
                        </td>

                        <td>
                            <p class="m-0">@request.Status</p>
                        </td>
                    </tr>       
                }
                
            </tbody>
        </table>
    </div>
</div>
</div>


<!--action="/Transports/UpdateCar/@Model.Car.CarId"--> 
<form action="~/Transports/UpdateCar/@Model.Car.CarId"  method="POST" class="ajax">
<div class="modal fade" id="edit-vehicle-modal"  role="dialog" aria-labelledby="edit-vehicle-modalLabel" aria-hidden="true">
 
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="edit-vehicle-modalLabel">@Model.Car.Plate</h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
         <div class="row">
            <div class="col-lg-6">
                <label for="">@Model._l("Vehicle")</label>
                <input type="text" name="Name" class="form-control" value="@Model.Car.Name" required><br>
                <input type="hidden" name="CarId" value="@Model.Car.CarId">
                <label for="">@Model._l("Plate")</label>
                <input type="text" name="Plate" class="form-control" value="@Model.Car.Plate"  required><br>

                <label for="">@Model._l("VIN")</label>
                <input type="text" name="Vin" class="form-control" value="@Model.Car.Vin"  required><br>

                <label for="">@Model._l("Model Year")</label>
                <input type="text" name="YearModel" class="form-control" value="@Model.Car.YearModel"  required><br>

                <label for="">@Model._l("Gas Card")</label>
                <input type="text" name="GasCardNo" class="form-control" value="@Model.Car.GasCardNo"  required><br>

                

            </div>
            <div class="col-lg-6">

                <label for="">@Model._l("Current ODO")</label>
                <input type="text" name="CurrentOdo" class="form-control" value="@Model.Car.CurrentOdo"  required><br>

                <label for="">@Model._l("Next Service ODO")</label>
                <input type="number" required name="NextOdoService" min="@Model.Car.CurrentOdo" class="form-control" value="@Model.Car.NextOdoService"  required><br>

                

                

                <label for="">@Model._l("Expiry")</label>
                <input type="date" name="Expiry" class="form-control" value="@Model.Car.Expiry.ToString("yyyy-MM-dd")" required><br>


                <label for="">@Model._l("Descriotion")</label>
                <textarea name="Description" class="form-control" value="@Model.Car.Description"  cols="30" ></textarea><br>
            </div>
         </div>
        
            

     
      </div>
      <div class="modal-footer d-flex">
        <a href="~/Transports/DeleteCar/@Model.Car.CarId" class="btn btn-danger btn-block rounded-pill after-confirm" >@Model._l("Delete")</a>
        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
      </div>
    </div>
  </div>
</div>

</form>


<form action="~/Transports/CreateService/@Model.Car.CarId"  method="POST" class="ajax">
<div class="modal fade" id="new-service-modal"  role="dialog" aria-labelledby="new-servic-modalLabel" aria-hidden="true">
 
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="new-servic-modalLabel">@Model._l("New Maintenance")</h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-lg-6">
                 <input type="hidden" name="CarId" value="@Model.Car.CarId">
    
        <label for="">@Model._l("Vehicle")</label>
        <input disabled type="text" class="form-control" value="@Model.Car.Name" ><br>

         <label for="">@Model._l("Date")</label>
        <input type="date" name="Date" class="form-control" value="@DateTime.Now.ToString("yyyy-MM-dd")"  required><br>

                <label for="">@Model._l("Current ODO")</label>
      <input type="text" name="CurrentOdo" class="form-control" value="@Model.Car.CurrentOdo"  required><br>

        <label for="">@Model._l("Next Service ODO")</label>
        <input type="number" name="NextOdoService" class="form-control" min="@Model.Car.CurrentOdo" value="@Model.Car.NextOdoService"  required>
        <br>

        
            </div>
            <div class="col-lg-6">
                    <label for="">@Model._l("Service Type")</label>
        <select name="Type" class="form-control">
            <option value="Regular Service">@Model._l("Regular Service")</option>
            <option value="Specialized Service">@Model._l("Specialized Service")</option>
        </select><br>

                <label for="">@Model._l("Total Cost")</label>
        <input type="number" name="Cost" class="form-control" step="0.001" min="0"  required><br>

         <label for="">@Model._l("Reference")</label>
        <input type="text" name="Reference" class="form-control" ><br>
                
        <label for="">@Model._l("Description")</label>
        <input type="text" name="Description" class="form-control"  required><br>

        <label for="">@Model._l("Long Description")</label>
        <textarea name="LongDescription" class="form-control"  cols="30" ></textarea><br>
            </div>
        </div>
       
      </div>
      <div class="modal-footer d-flex">
        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
      </div>
    </div>
  </div>
</div>

</form>


@foreach(var service in Model.Car.CarsServices)
{
    <form action="~/Transports/UpdateService/@service.Id" method="POST" class="ajax">
        <div class="modal fade" id="<EMAIL>-modal"  role="dialog"
            aria-labelledby="<EMAIL>-modalLabel" aria-hidden="true">

            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="<EMAIL>-modalLabel">
                            @service.Date.ToString("yyyy-MM-dd")</h3>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <input type="hidden" name="CarId" value="@Model.Car.CarId">
                                <input type="hidden" name="Id" value="@service.Id">

                                <label for="">@Model._l("Vehicle")</label>
                                <input disabled type="text" class="form-control" value="@Model.Car.Name"><br>

                                <label for="">@Model._l("Date")</label>
                                <input type="date" name="Date" class="form-control" value="@service.Date.ToString("yyyy-MM-dd")" required><br>
                            </div>
                            <div class="col-lg-6">
                                <label for="">@Model._l("Service Type")</label>
                                <select name="Type" class="form-control">
                                    @if (service.Type == "Regular Service")
                                    {
                                        <option value="Regular Service" selected>@Model._l("Regular Service")</option>
                                    }else{
                                        <option value="Regular Service" >@Model._l("Regular Service")</option>
                                    }

                                    @if (service.Type == "Specialized Service")
                                    {
                                        <option value="Specialized Service" selected>@Model._l("Specialized Service")</option>
                                    }else{
                                        <option value="Specialized Service" >@Model._l("Specialized Service")</option>
                                    }
                                </select><br>


                                <label for="">@Model._l("Total Cost")</label>
                                <input type="number" name="Cost" class="form-control" step="0.001" min="0"
                                    value="@service.Cost" required><br>

                                <label for="">@Model._l("Reference")</label>
                                <input type="text" name="Reference" value="@service.Reference" class="form-control"><br>


                                <label for="">@Model._l("Description")</label>
                                <input type="text" name="Description" value="@service.Description" class="form-control"
                                    required><br>

                                <label for="">@Model._l("Long Description")</label>
                                <textarea name="LongDescription" class="form-control"
                                    cols="30">@service.LongDescription</textarea><br>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex">
                        <a href="/Transports/DeleteService/@service.Id" class=" btn btn-danger after-confirm btn-block rounded-pill">@Model._l("Delete")</a>
                        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                    </div>
                </div>
            </div>
        </div>

    </form>
}


<form action="~/Transports/CreateGasDtl/@Model.Car.CarId"  method="POST" class="ajax">
<div class="modal fade" id="new-gas-modal"  role="dialog" aria-labelledby="new-gas-modalLabel" aria-hidden="true">
 
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="new-gas-modalLabel">@Model._l("New Gas record")</h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-lg-6">
                 <input type="hidden" name="CarId" value="@Model.Car.CarId">
    
        <label for="">@Model._l("Vehicle")</label>
        <input disabled type="text" class="form-control" value="@Model.Car.Name" ><br>

         <label for="">@Model._l("Date")</label>
        <input type="date" name="Date" class="form-control" value="@DateTime.Now.ToString("yyyy-MM-dd")"  required><br>

                <label for="">@Model._l("Current ODO")</label>
      <input type="text" name="CurrentOdo" class="form-control" value="@Model.Car.CurrentOdo"  required><br>

           <label for="">@Model._l("Description")</label>
        <input type="text" name="Description" class="form-control"  required><br>

            </div>
            <div class="col-lg-6">

        <label for="">@Model._l("Total Cost")</label>
        <input type="number" name="Cost" class="form-control" min="0" value="0"  required>
        <br>

         <label for="">@Model._l("Total Tax")</label>
        <input type="number" name="Tax" class="form-control" min="0"  min="0" value="0"  required>
        <br>

         <label for="">@Model._l("Reference")</label>
        <input type="text" name="Reference" class="form-control" ><br>
                

            </div>
        </div>
       
      </div>
      <div class="modal-footer d-flex">
        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
      </div>
    </div>
  </div>
</div>

</form>


@foreach(var gas in Model.Car.CarsGasDtl)
{
    <form action="~/Transports/UpdateGas/@gas.Id" method="POST" class="ajax">
        <div class="modal fade" id="<EMAIL>-modal"  role="dialog"
            aria-labelledby="<EMAIL>-modalLabel" aria-hidden="true">

            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="<EMAIL>-modalLabel">
                            @gas.Date.ToString("yyyy-MM-dd")</h3>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <input type="hidden" name="CarId" value="@Model.Car.CarId">
                                <input type="hidden" name="Id" value="@gas.Id">

                                <label for="">@Model._l("Vehicle")</label>
                                <input disabled type="text" class="form-control" value="@Model.Car.Name"><br>

                                <label for="">@Model._l("Date")</label>
                                <input type="date" name="Date" class="form-control" value="@gas.Date.ToString("yyyy-MM-dd")" required><br>

                                
                                <label for="">@Model._l("Description")</label>
                                <input type="text" name="Description" value="@gas.Description" class="form-control"
                                    required><br>

                            </div>
                            <div class="col-lg-6">
                               


                                <label for="">@Model._l("Total Cost")</label>
                                <input type="number" name="Cost" class="form-control" step="0.001" min="0"
                                    value="@gas.Cost" required><br>

                                    <label for="">@Model._l("Total Tax")</label>
                                <input type="number" name="Cost" class="form-control" step="0.001" min="0"
                                    value="@gas.Cost" required><br>


                                <label for="">@Model._l("Reference")</label>
                                <input type="text" name="Reference" value="@gas.Reference" class="form-control"><br>

                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex">
                        <a href="/Transports/DeleteGas/@gas.Id" class=" btn btn-danger after-confirm btn-block rounded-pill">@Model._l("Delete")</a>
                        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                    </div>
                </div>
            </div>
        </div>

    </form>
}

@* ======================= violations ==================================  *@
<form action="~/Transports/CreateViolation/@Model.Car.CarId"  method="POST" class="ajax">
<div class="modal fade" id="new-violation-modal"  role="dialog" aria-labelledby="new-violation-modalLabel" aria-hidden="true">
 
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="new-violation-modalLabel">@Model._l("New traffic violations")</h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-lg-6">
                 <input type="hidden" name="CarId" value="@Model.Car.CarId">
    
        <label for="">@Model._l("Vehicle")</label>
        <input disabled type="text" class="form-control" value="@Model.Car.Name" ><br>

         <label for="">@Model._l("Date")</label>
       <input 
       type="datetime-local" 
       name="Date" class="form-control" 
       onchange="GetStaffByDate($(this).val(),'#new-violation-staff')"
        required><br>


                <label for="">@Model._l("Current ODO")</label>
      <input type="text" name="CurrentOdo" class="form-control" value="@Model.Car.CurrentOdo"  required><br>

           <label for="">@Model._l("Description")</label>
        <input type="text" name="Description" class="form-control"  required><br>

           <label for="">@Model._l("Location")</label>
        <input type="text" name="Location" class="form-control"  required><br>

            </div>
            <div class="col-lg-6">

                <div>
                <label for="">@Model._l("Staff") <i class="fa fa-search"></i></label>
                <select name="StaffId" class="select2" id="new-violation-staff" placeholder="Select Vehicle" required>
                  <option value="" selected disabled hidden>@Model._l("Select Staff")</option>
                  @foreach (var staff in Model.StaffList)
                  {
                
                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    
                  }
                </select>
              </div>
                <br>

        <label for="">@Model._l("Total Cost")</label>
        <input type="number" name="Cost" class="form-control" min="0" value="0"  required>
        <br>



         <label for="">@Model._l("Reference")</label>
        <input type="text" name="Reference" class="form-control" ><br>

         <label for="">@Model._l("Receipt No")</label>
        <input type="text" name="ReceiptNo" class="form-control" required><br>
                

            </div>
        </div>
       
      </div>
      <div class="modal-footer d-flex">
        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
      </div>
    </div>
  </div>
</div>

</form>


@foreach(var violation in Model.Car.CarsViolations)
{
    <form action="~/Transports/UpdateViolation/@violation.Id" method="POST" class="ajax">
        <div class="modal fade" id="<EMAIL>-modal" role="dialog"
            aria-labelledby="<EMAIL>-modalLabel" aria-hidden="true">

            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" id="<EMAIL>-modalLabel">
                            @violation.Reference</h3>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <input type="hidden" name="CarId" value="@Model.Car.CarId">
                                <input type="hidden" name="Id" value="@violation.Id">

                                <label for="">@Model._l("Vehicle")</label>
                                <input disabled type="text" class="form-control" value="@Model.Car.Name"><br>

                                <label for="">@Model._l("Date")</label>
                                <input type="datetime-local" name="Date" class="form-control" value="@violation.Date.ToString("yyyy-MM-ddTHH:mm")" required><br>

                                
                                <label for="">@Model._l("Description")</label>
                                <input type="text" name="Description" value="@violation.Description" class="form-control"
                                    required><br>

                                      <label for="">@Model._l("Location")</label>
                                <input type="text" name="Location" class="form-control" value="@violation.Location"  required><br>

                            </div>
                            <div class="col-lg-6">
                               
                                  <div>
                                    <label for="">@Model._l("Staff") <label for="">@Model._l("Staff") <a href="#" onclick="search_staf()"><i class="fa fa-search"></i> </a></label></label>
                                    <select name="StaffId" class="select2" placeholder="Select Vehicle" required>
                                    <option value="" selected disabled hidden>@Model._l("Select Staff")</option>
                                    @foreach (var staff in Model.StaffList)
                                    {
                                        if(@staff.EmpNo==violation.StaffId){
                                            <option selected value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                        }else{
                                            <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                                        }
                                            
                                        
                                    }
                                    </select>
                                </div>

                                <label for="">@Model._l("Total Cost")</label>
                                <input type="number" name="Cost" class="form-control" step="0.001" min="0"
                                    value="@violation.Cost" required><br>




                                <label for="">@Model._l("Reference")</label>
                                <input type="text" name="Reference" value="@violation.Reference" class="form-control"><br>

                                <label for="">@Model._l("Violation No")</label>
                                 <input type="text" name="ReceiptNo" class="form-control" value="@violation.ReceiptNo" required><br>

                            </div>
                        </div>
                    </div>
                    <div class="modal-footer d-flex">
                        <a href="/Transports/DeleteViolation/@violation.Id" class=" btn btn-danger after-confirm btn-block rounded-pill">@Model._("Delete")</a>
                        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                    </div>
                </div>
            </div>
        </div>

    </form>
}


<script>
    function search_staf(){
        var car_id = $("select[name=CarId").val();
        var date = $("input[name=Date").val();


        
        $.get("/Transports/GetStaffByDate",{car_id,date},function(data){
            if(data.data.length > 0){
                $("select[name=StaffId]").val(data.data[0].empNo).trigger("change");


            }
        },'json');


    }

    $(document).ready(function(){


    })

    function GetStaffByDate(date, append_to){
        $.get("/Transports/GetStaffByDate/"+date,function(data){
            console.log(data)
        });
    }
</script>