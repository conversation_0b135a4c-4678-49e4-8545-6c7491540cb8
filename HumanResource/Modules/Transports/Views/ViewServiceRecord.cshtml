﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

@{
    Layout = null;
}

    <div class="row">
        <div class="col-lg-6">
         
   

            
            <label for="">@Model._("Vehicle")</label>
            <input type="text" disabled class="form-control" value="@Model.Car.Name">
            <br>

            <label for="">@Model._("ODO")</label>
            <input type="text" disabled class="form-control" value="@Model.ServiceRecord.CurrentOdo"> <br>

            <label for="">@Model._("Date")</label>
            <input type="text" disabled class="form-control" value="@Model._d(Model.ServiceRecord.Date)">
        
            <br>
            <br>
            <br>

            <p>@Model._("Status")</p>
            <h3>@Model._(Model.ServiceRecord.Status)</h3>
        </div>
        <div class="col-lg-6">
            <label for="">@Model._("Service Type")</label>
            <input type="text" disabled class="form-control" value="@Model._(Model.ServiceRecord.Type )">

            <br>


            <label for="">@Model._("Total Cost")</label>
            <input type="text" disabled class="form-control" value="@Model._amount(Model.ServiceRecord.Cost )">
            <br>

            <label for="">@Model._("Reference")</label>
            <input type="text" disabled class="form-control" value="@Model.ServiceRecord.Reference">
            <br>


            <label for="">@Model._("Description")</label>
            <input type="text" name="Description" disabled value="@Model.ServiceRecord.Description" class="form-control"
                ><br>

            <label for="">@Model._("Long Description")</label>
            <textarea name="LongDescription" class="form-control" disabled
                cols="30">@Model.ServiceRecord.LongDescription</textarea><br>

        </div>
    </div>
    <br>

    <div class="d-flex justify-content-between">

        @if(Model.Can("transports-admin|transports-dgeneral") && Model.ServiceRecord.ApprovedBy==0){
            <a href="/Transports/DeleteService2/@Model.Ec(Model.ServiceRecord.Id)" class=" btn btn-danger after-confirm rounded-pill btn-block mx-1"><i
                class="fas fa-trash-alt"></i> @Model._("Delete")</a>
        }
        
        
           

            @if(Model.Can("transports-admin|transports-dgeneral") && Model.ServiceRecord.ApprovedBy==0){
                <a href="/Transports/ApproveService/@Model.Ec(Model.ServiceRecord.Id)" class=" btn btn-success rounded-pill btn-block after-confirm mx-1"><i
                    class="far fa-check-circle "></i> @Model._("Approve")</a>
            }
            
             <button type="button" class="btn btn-secondary rounded-pill btn-block mx-1" data-dismiss="modal">@Model._("Close")</button>
    </div>
