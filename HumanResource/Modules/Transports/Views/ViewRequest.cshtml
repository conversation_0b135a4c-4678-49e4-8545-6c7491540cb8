﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel
@{
    Layout = Model.Page.Layout;
}


<div class="d-flex">
<div class="btn-group ">
  @if (Model.Can("transports-admin"))
  {
      <a href="~/Transports/DeleteRequest/@Model.CarsRequest.Id" class="btn btn-danger after-confirm btn-sm "><i class="fas fa-trash-alt"></i> @Model._l("حذف")</a>
  }
</div>
</div>
<br>
<div class="card shadow table-responsive">
    <table class="table">
        <thead class="bg-primary">
            <tr>
                <th>@Model._l("رقم الطلب")</th>
                <th>@Model._l("تاريخ الطلب")</th>

                <th>@Model._l("الموظف")</th>
                <th>@Model._l("السبب")</th>
                <th>@Model._l("الحالة")</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>#@Model.CarsRequest.Id</td>
                <td>@Model.CarsRequest.FromDate <br> @Model.CarsRequest.ToDate</td>
        
                <td>@Model.CarsRequest.Staff.EmpNameA</td>
                <td>@Model.CarsRequest.Reason</td>
                <td>
               
                        @Model._l((Model.CarsRequest.Status!=null?Model.CarsRequest.Status:""))
                </td>
            </tr>
        </tbody>
    </table>
</div>
@if (@Model.Car!=null)
{
    
<div class="card shadow table-responsive">
    <div class="card-header bg-primary">
        @Model._l("تفاصيل السيارة")
    </div>
    <table class="table">
        <thead class="bg-primary">
            <th>@Model._l("اللوحة")</th>
            <th>@Model._l("السيارة")</th>
            <th>@Model._l("المسافة المقطوعة")</th>
            <th>@Model._l("تاريخ الخدمة التالية")</th>
        </thead>
        <tbody>
            <tr>
                <td>@Model.CarsRequest.Car.Plate</td>
                <td>@Model.CarsRequest.Car.Name</td>
                <td>@Model.CarsRequest.Car.CurrentOdo</td>
                <td>@Model.CarsRequest.Car.NextOdoService</td>
                
            </tr>
        </tbody>
    </table>
</div>

}
@if (@Model.CarsRequest.Status=="Approved" && Model.Can("transports-admin|transports-department"))
    {
       <div class="card shadow">
        <div class="card-body">
            <div class="row">
                <div class="col-lg-8 text-center">
                     
                     <p class="m-0">@Model.CarsRequest.Staff.EmpNameA</p>
                    <p class="m-0">@Model.CarsRequest.Staff.EmpNo - @Model.CarsRequest.Staff.DeptDespA</p>

                        <br>

                     <div class="d-flex justify-content-center">
                      
                        <a href="~/Transports/CancelApproval/@Model.CarsRequest.Id" class="btn btn-larg btn-danger after-confirm mx-1 ">@Model._l("إلغاء الموافقة")</a>
                        <a data-target="#complete-request-modal" data-toggle="modal" href="#" class="btn btn-larg btn-success mx-1 ">@Model._l("تم الانتهاء")</a>
                    </div>
                </div>
                <div class="col-lg-4 text-center d-flex align-items-center justify-content-center">
                    <div class="spinner-grow" style="width: 3rem; height: 3rem;" role="status">
                        <span class="sr-only">@Model._l("جاري التحميل")</span>
                    </div>
                </div>
            </div>
        </div>
       </div>

       <form action="~/Transports/RequestCompleted" class="ajax" method="post">
        <div class="modal fade" id="complete-request-modal"  role="dialog" aria-labelledby="complete-request-modalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header bg-success">
                    <h5 class="modal-title" id="complete-request-modalLabel">@Model._l("تم الانتهاء")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("تاريخ الاستلام")</label>
                    <input type="datetime-local" name="ToDate" class="form-control" required value="@Model.CarsRequest.ToDate.ToString("yyyy-MM-ddTHH:mm")"><br>

                    <label for="">@Model._l("المسافة المقطوعة")</label>
                    <input type="text" name="CurrentOdo" class="form-control" value="@Model.CarsRequest.Car.CurrentOdo" required><br>


                    <label for="">@Model._l("الملاحظات")</label>
                    <textarea name="Note" class="form-control">@Model.CarsRequest.Note</textarea><br>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("إرسال")</button>
                </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="Id" value="@Model.CarsRequest.Id">
    </form>
    }
@if (Model.Can("transports-admin|trasports-department") && Model.CarsRequest.Status!="Approved" && Model.CarsRequest.Status!="Completed" && Model.CarsRequest.Status!="Declined" &&  Model.CarsRequest.ManagerApprov!=2 && Model.CarsRequest.DGeneralApprov!=2)
              {
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header">

            </div> 
            <div class="card-body">
                <form action="~/Transports/UpdateRequest" method="post" class="ajax">
                <input type="hidden" name="Id" value="@Model.CarsRequest.Id">
                <div class="row">
            <div class="col-lg-6">

              
                <div>
                <label for="">@Model._l("الموظف")</label>
                <select name="StaffId" class="select2" placeholder="Select Vehicle" required>
                  <option value="" selected disabled hidden>@Model._l("اختر موظف")</option>
                  @foreach (var staff in Model.StaffList)
                  {
                    @if (staff.EmpNo == Model.CarsRequest.StaffId)
                    {
                        <option selected value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    }else{
                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    }
                  }
                </select>
              </div>
              
              <br>

              <div>
                <label for="">@Model._l("المسؤول المباشر")</label>
                <select name="DepManagerId" class="select2" placeholder="Select Vehicle" required>
                  <option value="" selected disabled hidden>@Model._l("اختر مدير مباشر")</option>
                  @foreach (var staff in Model.StaffList)
                  {
                    @if (staff.EmpNo == Model.CarsRequest.DepManagerId)
                    {
                        <option selected value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    }else{
                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    }
                  }
                </select>
              </div>
              <br>

              <label for="">@Model._l("من")</label>
              <input type="datetime-local" name="FromDate" class="form-control" required value="@Model.CarsRequest.FromDate.ToString("yyyy-MM-ddTHH:mm")"><br>

              <label for="">@Model._l("إلى")</label>
              <input type="datetime-local" name="ToDate" class="form-control" required value="@Model.CarsRequest.ToDate.ToString("yyyy-MM-ddTHH:mm")"><br>


            </div>
            <div class="col-lg-6">


              <label for="">@Model._l("الملاحظات")</label>
              <textarea name="Note" class="form-control">@Model.CarsRequest.Note</textarea><br>
              <label for="">@Model._l("السبب")</label>
              <textarea name="Reason" class="form-control">@Model.CarsRequest.Reason</textarea>
              <br>

              

              
         
            </div>
          </div>

          <button class="btn btn-primary px-3" type="submit">@Model._l("حفظ")</button>
          </form>
            </div>  
        </div>
    </div>
</div>

}


@if (Model.CarsRequest.ManagerApprov==0 && (Model.CarsRequest.DepManagerId==Model.Auth.EmpNo || Model.Can("transports-admin")))
{
    <form action="~/Transports/DepManagerDecline" class="ajax" method="post">
        <div class="modal fade" id="department-manager-decline-modal"  role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("تم الرفض بواسطة المسؤول المباشر")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظات الرفض")</label>
                    <textarea name="DeclineNote"  class="form-control" ></textarea>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("إرسال")</button>
                </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="Id" value="@Model.CarsRequest.Id">
    </form>

    <div class="d-flex justify-content-center">
        <a href="~/Transports/DepManagerApprove/@Model.CarsRequest.Id" class="btn btn-larg btn-success mx-1 after-confirm "> <i class="fas fa-check"></i> @Model._l("موافقة")</a>
        <a href="#" class="btn btn-larg btn-danger mx-1  "  data-toggle="modal" data-target="#department-manager-decline-modal"><i class="fas fa-times"></i> @Model._l("رفض")</a>
    </div>    
}

@if(Model.CarsRequest.ManagerApprov==1 && Model.CarsRequest.Status!="Approved" && Model.CarsRequest.Status!="Declined" && Model.CarsRequest.Status!="Completed"  && Model.Can("transports-admin|transports-department") && (Model.CarsRequest.DGeneralApprov==1 || Model.CarsRequest.Type!="1")){

    <form action="~/Transports/ApproveRequest" class="ajax" method="post">
        <div class="modal fade" id="approve-departemnt-modal"  role="dialog" aria-labelledby="department-manager-decline-modalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header bg-success">
                    <h5 class="modal-title" id="department-manager-decline-modalLabel">@Model._l("موافقة")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div>

                <label for="">@Model._l("السيارة")</label>
                <select name="CarId" class="select2" placeholder="Select Vehicle" required>
                  <option value="0" selected disabled hidden>@Model._l("اختر سيارة")</option>
                  @foreach (var car in Model.Cars)
                  {
                    if(Model.CarsRequest.CarId==car.CarId){
                        <option selected data-currentodo="@car.CurrentOdo" value="@car.CarId">@car.Plate - @car.Name</option>
                    }else{
                        <option data-currentodo="@car.CurrentOdo" value="@car.CarId">@car.Plate - @car.Name</option>
                    }
                  
                  }

                </select>
              </div>
              <br>

              <label for="">@Model._l("المسافة المقطوعة")</label>
              <input type="text" name="CurrentOdo" class="form-control" value="" required><br>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("إرسال")</button>
                </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="Id" value="@Model.CarsRequest.Id">
    </form>

    <form action="~/Transports/DeclineRequest" class="ajax" method="post">
        <div class="modal fade" id="request-decline-modal"  role="dialog" aria-labelledby="request-decline-modalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="request-decline-modalLabel">@Model._l("تم الرفض بواسطة القسم المروري")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظات الرفض")</label>
                    <textarea name="DeclineNote"  class="form-control" ></textarea>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("إرسال")</button>
                </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="Id" value="@Model.CarsRequest.Id">
    </form>

     <div class="d-flex justify-content-center">
        <a href="#" data-toggle="modal" data-target="#approve-departemnt-modal" class="btn btn-larg btn-success mx-1 "><i class="fas fa-check"></i> @Model._l("موافقة")</a>
        <a href="#" data-toggle="modal" data-target="#request-decline-modal " class="btn btn-larg btn-danger mx-1 "><i class="fas fa-times"></i> @Model._l("رفض")</a>
        
    </div>           
}else if(Model.CarsRequest.Status!="Approved" && Model.CarsRequest.Status!="Completed" && Model.CarsRequest.Status!="Declined" && Model.CarsRequest.ManagerApprov==1 && Model.CarsRequest.DGeneralApprov==0  && Model.Can("transports-admin|transports-dgeneral") &&  Model.CarsRequest.Type=="1"){
 <form action="~/Transports/DGDecline" class="ajax" method="post">
        <div class="modal fade" id="dgeneral-decline-modal"  role="dialog" aria-labelledby="dgeneral-decline-modalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header bg-danger">
                    <h5 class="modal-title" id="dgeneral-decline-modalLabel">@Model._l("تم الرفض بواسطة المدير العام")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <label for="">@Model._l("ملاحظات الرفض")</label>
                    <textarea name="DeclineNote"  class="form-control" ></textarea>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("إرسال")</button>
                </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="Id" value="@Model.CarsRequest.Id">
    </form>

    <form action="~/Transports/DGeneralApprove" class="ajax" method="post">
        <div class="modal fade" id="dgeneral-approve-modal"  role="dialog" aria-labelledby="dgeneral-approve-modalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header bg-success">
                    <h5 class="modal-title" id="dgeneral-approve-modalLabel">@Model._l("موافقة")</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div>

                <label for="">@Model._l("السيارة")</label>
                <select name="CarId" class="select2" placeholder="Select Vehicle" required>
                  <option value="0" selected disabled hidden>@Model._l("اختر سيارة")</option>
                  @foreach (var car in Model.Cars)
                  {
                    <option  value="@car.CarId">@car.Plate - @car.Name</option>
                  }

                </select>
              </div>
              <br>

           
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary " data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-primary ">@Model._l("إرسال")</button>
                </div>
                </div>
            </div>
        </div>

        <input type="hidden" name="Id" value="@Model.CarsRequest.Id">
    </form>

    <div class="d-flex justify-content-center">
        <a href="#" data-toggle="modal" data-target="#dgeneral-approve-modal" class="btn btn-larg btn-success mx-1  "><i class="fas fa-check"></i> @Model._l("موافقة")</a>
        <a href="#" class="btn btn-larg btn-danger mx-1 "  data-toggle="modal" data-target="#dgeneral-decline-modal"><i class="fas fa-times"></i> @Model._l("رفض")</a>
    </div>    
}
    

<script>
    $(document).ready(function() {


        $("select[name='CarId']").change(function() {
            var selectedOption = $(this).find('option:selected');
            $("input[name='CurrentOdo']").val(selectedOption.data('currentodo'));
        });
    });


</script>

