﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

<div class="d-flex justify-content-between py-2">



  <h3>@Model._l("المركبات")</h3>

  <div>
    <button data-toggle="modal" data-target="#new-vehicle-modal" class="btn btn-primary"><i class="fa fa-plus"></i>
      @Model._l("اضافة مركبة")</button>

  </div>
</div>
<div class="card shadow">


  <table class="table datatable table-hover ">
    <thead class="bg-primary text-white">
      <tr>
        <td>@Model._l("اللوحة")</td>
        <td>@Model._l("المركبة")</td>
        <td>@Model._l("سنة الموديل")</td>
        <td class=" text-center">@Model._l("المسافة المقطوعة")</td>
        <td></td>
      </tr>
    </thead>
    <tbody>
      @foreach (var car in Model.Cars)
      {
        <tr>
          <td><a href="/Transports/ViewCar/@car.CarId">@car.Plate</a></td>
          <td>@car.Name</td>
          <td>@car.YearModel</td>
          <td class="text-monospace text-center">

            @car.CurrentOdo


          </td>
          <td >

            <div class="bg-dark p-2 rounded">

                @if ((car.CurrentOdo + 300) >= car.NextOdoService)
                {
                    <span class="mx-1" style="color: #ffc107 !important;" title="تنبيه موعد الخدمة">
                      <i class="far fa-engine-warning"></i>
                    </span>
                }


                @if (car.Status != "Available")
              {

                var request = Model.GetActiveRequestByCar(car.CarId);

                if (request.Type == "0")
                {
                  <span class=" mx-1" style="color: #e77777 !important;"><i class="fas fa-ban"></i> غير متوفرة</span>
                }
                else
                {
                  <span class=" mx-1" style="color: #e77777 !important;"><i class="fas fa-ban"></i> غير متوفرة</span>
                }


                <p class="p-0 m-0 text-white text-monospace" dir="ltr">(@Model._dt(@request.ToDate))</p>

              }
              else
              {
                <span class="text-success mx-1" style="color: #77e791 !important;">
                  <i class="fa fa-check"></i>
                  @Model._l("متوفرة")</span>
              }


            </div>

            

            
          </td>


        </tr>
      }
    </tbody>
  </table>
</div>


<form action="/Transports/CreateCar" class="ajax" method="post">
  <div class="modal fade" id="new-vehicle-modal" tabindex="-1" role="dialog" aria-labelledby="new-vehicle-modalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._l("اضافة مركبة جديدة")</h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-6">
              <label for="">@Model._l("المركبة")</label>
              <input type="text" name="Name" class="form-control" required><br>

              <label for="">@Model._l("اللوحة")</label>
              <input type="text" name="Plate" class="form-control" required><br>

              <label for="">@Model._l("سنة الموديل")</label>
              <input type="text" name="YearModel" class="form-control" required><br>

              <label for="">@Model._l("رقم المحرك")</label>
              <input type="text" name="Vin" class="form-control" required><br>

              <label for="">@Model._l("رقم البطاقة البنزين")</label>
              <input type="text" name="GasCardNo" class="form-control" required><br>




            </div>

            <div class="col-lg-6">
              <label for="">@Model._l("المسافة المقطوعة")</label>
              <input type="text" name="CurrentOdo" class="form-control" required><br>

              <label for="">@Model._l("تاريخ الانتهاء")</label>
              <input type="date" name="Expiry" class="form-control" required><br>

              <label for="">@Model._l("الوصف")</label>
              <textarea name="Description" id="" class="form-control" cols="30"></textarea><br>
            </div>
          </div>
        </div>
        <div class="modal-footer d-flex">
          <button type="button" class="btn btn-secondary" data-dismiss="modal"><i class="fa fa-times"></i>
            @Model._l("إغلاق")</button>
          <button type="submit" class="btn btn-primary"><i class="fa fa-save"></i> @Model._l("إرسال")</button>
        </div>
      </div>
    </div>
  </div>

</form>