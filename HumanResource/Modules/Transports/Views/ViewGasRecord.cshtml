﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

@{
    Layout = null;
}
<form action="/Transports/UpdateGas/@Model.GasRecord.Id" method="post" class="ajax">

    <div class="row">
        <div class="col-lg-6">        
            <input type="hidden" name="Id" value="@Model.GasRecord.Id">
            <div>
                <label for="">@Model._l("Vehicle")</label>
                <select name="CarId" class="select2 form-control" required>
                    <option selected disabled hidden>@Model._("Select a vehicle")</option>
                    @foreach (var car in Model.Cars)
                    {
                        if (Model.Car.CarId == car.CarId)
                        {
                        <option value="@car.CarId" selected>@car.Name <br> @car.Name</option>
                        }
                        else
                        {
                        <option value="@car.CarId">@car.Name <br> @car.Name</option>
                        }
                    }

                </select>
            </div><br>

            <label for="">@Model._l("Date")</label>
            <input type="date" name="Date" class="form-control" value="@Model.GasRecord.Date.ToString("yyyy-MM-dd")"
                required><br>


            <label for="">@Model._l("Description")</label>
            <input type="text" name="Description" value="@Model.GasRecord.Description" class="form-control"
                required><br>

        </div>
        <div class="col-lg-6">
            <label for="">@Model._l("Total Cost")</label>
            <input type="number" name="Cost" class="form-control" step="0.001" min="0" value="@Model.GasRecord.Cost"
                required><br>

            <label for="">@Model._l("Total Tax")</label>
            <input type="number" name="Cost" class="form-control" step="0.001" min="0" value="@Model.GasRecord.Cost"
                required><br>

            <label for="">@Model._l("Reference")</label>
            <input type="text" name="Reference" value="@Model.GasRecord.Reference" class="form-control"><br>

        </div>
    </div>
    <br>

    <div class="d-flex justify-content-between">
        <a href="/Transports/DeleteGas2/@Model.GasRecord.Id" class=" btn btn-danger after-confirm"><i class="fas fa-trash-alt"></i> @Model._l("Delete")</a>
        <div>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("Close")</button>
            <button type="submit" class="btn btn-primary">@Model._l("Save")</button>
        </div>
    </div>
</form>