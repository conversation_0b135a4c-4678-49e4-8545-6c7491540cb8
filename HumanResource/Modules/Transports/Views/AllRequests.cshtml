﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._l("Vehicle requests")</h3>

    <div>
      @if(@Model.Can("transports-department|transports-admin")){
        <button data-toggle="modal" data-target="#new-request-modal" class="btn btn-primary"><i class="fa fa-plus"></i> @Model._l("New request")</button>
      }
        
    </div>
</div>
<div class="card shadow">
    

    <table class="table table-sm  table-striped table-hover" id="datatable">
        <thead class="bg-primary text-white">
            <tr>
                <td>@Model._l("No")</td>
                <td>@Model._l("Staff")</td>
                <td>@Model._l("Vehicle")</td>
                <td>@Model._l("Request date")</td>
                <td>@Model._l("From - To")</td>
                <td>@Model._l("Status")</td>

            </tr>
        </thead>
        <tbody>
          
        </tbody>
    </table>
</div>


@if(Model.Can("transports-department|transports-admin")){
<form action="~/Transports/CreateRequest" method="post" class="ajax">
  <div class="modal fade" id="new-request-modal"  role="dialog" aria-labelledby="new-vehicle-modalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._("New request")</h3>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-6">

              <div>
                <label for="">@Model._l("Staff")</label>
                <select name="StaffId" class="select2" placeholder="Select Vehicle" required>
                  <option value="0" selected disabled hidden>@Model._l("Select Staff")</option>
                  @foreach (var staff in Model.StaffList)
                  {
                  <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                  }

                </select>
              </div>
              <br>

              <div>
                <label for="">@Model._l("Department Manager")</label>
                <select name="DepManagerId" class="select2" placeholder="Select Vehicle" required>
                  <option value="0" selected disabled hidden>@Model._l("Select Staff")</option>
                  @foreach (var staff in Model.StaffList)
                  {
                    
                        <option data-department="@staff.DeptCode" value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    
                  }
                </select>
              </div>
              <br>

              <label for="">@Model._l("From")</label>
              <input type="datetime-local" name="FromDate" class="form-control" required value="@DateTime.Now.ToString("yyyy-MM-ddTHH:mm")"><br>

              <label for="">@Model._l("To")</label>
              <input type="datetime-local" name="ToDate" class="form-control" required><br>


            </div>
            <div class="col-lg-6">

              
              <label for="">@Model._l("Note")</label>
              <textarea name="Note" class="form-control"></textarea><br>
              <label for="">@Model._l("Reason")</label>
              <textarea name="Reason" class="form-control"></textarea>
              <br>

              

       
              
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("Close")</button>
          <button type="submit" class="btn btn-primary">@Model._l("Submit")</button>
        </div>
      </div>
    </div>
  </div>

</form>

}


<script>
    $(document).ready(function(){
        createDatatable('#datatable',true, {
            ajax:"/Transports/AllDatatable",
            columns:[
                {"data":"id","title":"#"},
                {"data":"emp","title":"@Model._("Staff")"},
                {"data":"car","title":"@Model._("Vehicle")"},
                {"data":"date","title":"@Model._("Request date")"},
                {"data":"fromTo","title":"@Model._("From - To")"},
               
                {"data":"status","title":"@Model._("Status")"},
            ]
        });
    })
</script>
<script>

function get_record_data(id){
  $.get("/Transports/ViewViolationRecord/"+id,function(data){
    $('#edit-modal-html').html(data);
    $('#edit-record-modal').modal().show();

  });
}

$(document).ready(function() {
  $("select[name='CarId']").change(function() {
    var selectedOption = $(this).find('option:selected');
    $("input[name='CurrentOdo']").val(selectedOption.data('currentodo'));
  });
});
</script>