@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

@Html.Partial("_DgAppTabs")

<div class="card shadow">
    

    <table class="table table-sm datatable table-striped table-hover">
        <thead>
            <tr>
                <td>@Model._l("No")</td>
                <td>@Model._l("Staff")</td>
                <td>@Model._l("Vehicle")</td>
                <td>@Model._l("Request date")</td>
                <td>@Model._l("From - To")</td>
                <td>@Model._l("Status")</td>

            </tr>
        </thead>
        <tbody>
          @foreach (var request in Model.CarsRequests)
          {
            <tr >
              <td data-order="@request.Id"> <a class="popup" data-size="1000x720" href="~/Transports/Dg/View/@Model.Ec(request.Id)">#@request.Id</a>  </td>
              <td>@Model._h.<PERSON>(request.StaffId).EmpNo  <br> @Model._h.StaffData(request.StaffId).EmpNameA</td>
              @if (@request.Car != null)
              {
                <td>@request.Car.Plate <br> @request.Car.Name</td>
              }else{
                <td>--</td>
              }
              <td>@Model._dt(@request.CreatedAt)</td>
              <td class="text-center">
                <span dir="ltr" class="badge badge-dark rounded-pill px-2 "><strong>@Model._dt(request.FromDate)</strong></span>  
                  <br>
                <i class="fas fa-arrow-down"></i>
                @if(request.ToDate > DateTime.Now ){

                if( request.Status != "Completed"){
                  <br>

                <span dir="ltr" class="badge badge-danger rounded-pill px-2 "><strong>@Model._dt(request.ToDate)</strong></span>
                }else{
                  <br>

                <span dir="ltr" class="badge badge-success rounded-pill px-2 "><strong>@Model._dt(request.ToDate)</strong></span>
                }
                

                }else{
                  <br>

                  <span dir="ltr" class="badge badge-@(request.Status=="Completed"?"success":"info") rounded-pill px-2 shadow"><strong>@Model._dt(request.ToDate)</strong></span>
                }
                
              </td>
              <td>
                <span class="badge  rounded-pill px-2  badge-@(request.Status=="Completed"?"success":"danger")"><strong class="">@Model._(request.Status)</strong></span>
                </td>
          
            </tr>
          }
        </tbody>
    </table>
</div>