﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._l("Violation records")</h3>

    <div>
   
        <button data-toggle="modal" data-target="#new-record-modal" class="btn btn-primary rounded-pill"><i class="fa fa-plus"></i> @Model._l("Add new record")</button>
      
        
    </div>
</div>
<div class="card shadow">
    

    <table class="table datatable table-sm">
        <thead>
            <tr>
                <td>@Model._("No")</td>
                <td>@Model._("Vehicle")</td>
                <td>@Model._("Staff")</td>
                <td>@Model._("Date")</td>
                <td>@Model._("Violation No")</td>
                <td>@Model._("Cost")</td>
                <td>@Model._("Description")</td>

                

            </tr>
        </thead>
        <tbody>
            @foreach (var violation in Model.CarsViolations)
            {
            <tr>
                <td><a href="#" onclick="get_record_data(@violation.Id)">#@violation.Id</a></td>
                <td>@violation.Car.Plate <br> <i><small class="text-muted">@violation.Car.Name</small></i></td>
                <td>@Model._h.StaffData(violation.StaffId).EmpNo - @Model._h.StaffData(violation.StaffId).EmpNameA</td>
                <td>@Model._dt(violation.Date)</td>
                
                <td>@violation.ReceiptNo</td>
                <td>@violation.Cost.ToString("F3")</td>
                <td>@violation.Description</td>

                

            </tr>
            }
        </tbody>
    </table>
</div>

<form action="~/Transports/CreateViolation/" method="POST" class="ajax">
    <div class="modal fade" id="new-record-modal"  role="dialog" aria-labelledby="new-record-modalLabel"
        aria-hidden="true">

        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-record-modalLabel">@Model._("New record")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <div>
                                <label for="">@Model._("Vehicle")</label>
                                <select name="CarId" class="select2" required id="carid-new">
                                    <option selected disabled hidden>@Model._("Select a vehicle")</option>
                                    @foreach (var car in Model.Cars)
                                    {
                                    <option value="@car.CarId" data-CurrentOdo="@car.CurrentOdo">@car.Plate <br>
                                        @car.Name</option>
                                    }

                                </select>
                            </div>
                            <br>


                           <label for="">@Model._("Date")</label>
       <input type="datetime-local" name="Date"  id="date-new" class="form-control" value="@DateTime.Now.ToString("yyyy-MM-ddTHH:mm")" required><br>


                <label for="">@Model._("Current ODO")</label>
      <input type="text" name="CurrentOdo" class="form-control" value=""  required><br>

           <label for="">@Model._("Description")</label>
        <input type="text" name="Description" class="form-control"  required><br>

           <label for="">@Model._("Location")</label>
        <input type="text" name="Location" class="form-control"  required><br>

            </div>
            <div class="col-lg-6">

                <div>
                <label for="">@Model._l("Staff") <a href="#" onclick="search_staf()"><i class="fa fa-search"></i> </a></label>
                <select name="StaffId" id="staffid-new" class="select2" placeholder="Select Vehicle" required>
                  <option value="" selected disabled hidden>@Model._l("Select Staff")</option>
                  @foreach (var staff in Model.StaffList)
                  {
                
                        <option value="@staff.EmpNo">@staff.EmpNo - @staff.EmpNameA</option>
                    
                  }
                </select>
              </div>

                <br>

        <label for="">@Model._("Total Cost")</label>
        <input type="number" name="Cost" class="form-control" min="0" value="0"  required>
        <br>



         <label for="">@Model._("Reference")</label>
        <input type="text" name="Reference" class="form-control" ><br>

         <label for="">@Model._("Violation No")</label>
        <input type="text" name="ReceiptNo" class="form-control" required><br>
                



                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._("Close")</button>
                    <button type="submit" class="btn btn-primary">@Model._("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>


<div class="modal fade" id="edit-record-modal"  role="dialog" aria-labelledby="edit-record-modalLabel"
    aria-hidden="true">

    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="edit-record-modalLabel">@Model._("Edit record")</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="edit-modal-html">
                
            </div>
        </div>
    </div>
</div>

<script>

function get_record_data(id){
  $.get("/Transports/ViewViolationRecord/"+id,function(data){
    $('#edit-modal-html').html(data);
    $('#edit-record-modal').modal().show();

  });
}

function search_staf(){
        var car_id = $("select[name=CarId").val();
        var date = $("input[name=Date").val();


        
        $.get("/Transports/GetStaffByDate",{car_id,date},function(data){
            if(data.data.length > 0){
                $("select[name=StaffId]").val(data.data[0].empNo).trigger("change");


            }
        },'json');


    }

$(document).ready(function() {
  $("select[name='CarId']").change(function() {
    var selectedOption = $(this).find('option:selected');
    $("input[name='CurrentOdo']").val(selectedOption.data('currentodo'));
  });



});
</script>