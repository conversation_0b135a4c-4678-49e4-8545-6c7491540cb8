﻿@using HumanResource.Modules.Transports.ViewModels
@model TransportsViewModel

<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._l("Gas records")</h3>

    <div>
     
      <button data-toggle="modal" data-target="#new-gas-modal" class="btn btn-primary rounded-pill"><i class="fa fa-plus"></i> @Model._l("Add new record")</button>
        
    </div>
</div>
<div class="card shadow">
 

    <table class="table datatable table-sm">
        <thead>
            <tr>
                <td>@Model._l("No")</td>
                <td>@Model._l("Vehicle")</td>
                <td>@Model._l("Date")</td>

                <td>@Model._l("Cost")</td>
       
               
            </tr>
        </thead>
        <tbody>
            @foreach (var gas in Model.CarsGasDtl)
            {
                <tr>
                  <td><a href="#" onclick="get_gas_data(@gas.Id)">#@gas.Id</a> </td>
                    <td>@gas.Car.Plate <br> <i><small class="text-muted">@gas.Car.Name</small></i></td>
                    <td>@gas.Date.ToString("yyyy-MM-dd")</td>
                  
                    <td>@gas.Cost.ToString("F3")</td>
                   
           

                </tr>
            }
        </tbody>
    </table>
</div>

<form action="~/Transports/CreateGasDtl/"  method="POST" class="ajax">
<div class="modal fade" id="new-gas-modal" tabindex="-1" role="dialog" aria-labelledby="new-gas-modalLabel" aria-hidden="true">
 
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="new-gas-modalLabel">@Model._l("New Gas record")</h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
            <div class="col-lg-6">
              <div>
                 <label for="">@Model._l("Vehicle")</label>
               <select name="CarId" class="select2" required >
                <option selected disabled hidden>Select a vehicle</option>
                @foreach (var car in Model.Cars)
                {
                     <option value="@car.CarId" data-CurrentOdo="@car.CurrentOdo">@car.Name <br> @car.Name</option>
                }
               
               </select>
              </div>
          <br>


         <label for="">@Model._l("Date")</label>
        <input type="date" name="Date" class="form-control" value="@DateTime.Now.ToString("yyyy-MM-dd")"  required><br>

                <label for="">@Model._l("Current ODO")</label>
      <input type="text" name="CurrentOdo" class="form-control"  required><br>

           <label for="">@Model._l("Description")</label>
        <input type="text" name="Description" class="form-control"  required><br>

            </div>
            <div class="col-lg-6">

        <label for="">@Model._l("Total Cost")</label>
        <input type="number" name="Cost" class="form-control" min="0" value="0"  required>
        <br>

         <label for="">@Model._l("Total Tax")</label>
        <input type="number" name="Tax" class="form-control" min="0" value="0"  required>
        <br>

         <label for="">@Model._l("Reference")</label>
        <input type="text" name="Reference" class="form-control" ><br>
                

            </div>
        </div>
       
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("Close")</button>
        <button type="submit" class="btn btn-primary">@Model._l("Submit")</button>
      </div>
    </div>
  </div>
</div>

</form>


<div class="modal fade" id="edit-gas-modal" role="dialog" aria-labelledby="edit-gas-modalLabel" aria-hidden="true">
 
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="edit-gas-modalLabel">@Model._l("Edit Gas record")</h3>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="edit-modal-html">
      </div>
    </div>
  </div>
</div>

<script>

function get_gas_data(id){
  $.get("/Transports/ViewGasRecord/"+id,function(data){
    $('#edit-modal-html').html(data);
    $('#edit-gas-modal').modal().show();

  });
}

$(document).ready(function() {
  $("select[name='CarId']").change(function() {
    var selectedOption = $(this).find('option:selected');
    $("input[name='CurrentOdo']").val(selectedOption.data('currentodo'));
  });
});
</script>