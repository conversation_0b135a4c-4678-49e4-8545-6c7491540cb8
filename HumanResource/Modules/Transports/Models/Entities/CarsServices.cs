﻿
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Transports.Models.Entities;

public partial class CarsServices
{
    public Cars Car { get; set; }

    [Key]
    public int Id { get; set; }

    [Required]
    public int CarId { get; set; }

    [StringLength(100)]
    public string Reference { get; set; }

    [StringLength(100)]
    public string Type { get; set; }

    public DateTime Date { get; set; }

    public int CurrentOdo { get; set; }
    public int NextOdoService { get; set; }

    public DateTime CreatedAt { get; set; }


    public int Deleted01 { get; set; } = 0;

    public int DeletedBy { get; set; } = 0;

    public float Cost { get; set; } = 0;

    public int CreatedBy { get; set; }

    public int? UpdatedBy { get; set; } = 0;



    [StringLength(200)]
    public string Description { get; set; }

    [StringLength(700)]
    public string LongDescription { get; set; }


    public string Status { get; set; } = "New";
    public int ApprovedBy { get; set; } = 0;
    public DateTime? ApprovedAt { get; set; } = null;







}
