using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Transports.Models.Enums;
using Microsoft.EntityFrameworkCore;


namespace HumanResource.Modules.Transports.Models.Entities
{
    public class CarsRequests
    {

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public virtual Cars Car { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [NotMapped]
        public virtual VempDtl Staff { get; set; }

        [NotMapped]
        public virtual VempDtl ApprovedByStaff { get; set; }


        [Key]
        public int Id { get; set; }
        public int? CarId { get; set; }
        public int? StaffId { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Department manager is required")]
        public int DepManagerId { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        [StringLength(500)]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string Note { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [StringLength(100)]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string Reason { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [StringLength(100)]
        public string Status { get; set; }

        /// <summary>
        /// New REQ_STAT field using RequestStatus enum
        /// </summary>
        public RequestStatus REQ_STAT { get; set; } = RequestStatus.New;

        [StringLength(100)]
        public string Type { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public int? ApprovedBy { get; set; }

        [StringLength(100)]
        public string CurrentOdo { get; set; }

        [StringLength(100)]
        public string CompleteOdo { get; set; }

        public int? CreatedBy { get; set; }
        public int Deleted01 { get; set; } = 0;
        public DateTime? CreatedAt { get; set; }

        public DateTime? UpdatedAt { get; set; }

        public int? UpdatedBy { get; set; }

        [StringLength(100)]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string DeclineNote { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        public int ManagerApprov { get; set; } = 0;
        public int DGeneralApprov { get; set; } = 0;



    }
}
