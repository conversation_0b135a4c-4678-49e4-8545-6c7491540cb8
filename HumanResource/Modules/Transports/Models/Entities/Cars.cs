﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HumanResource.Modules.Employees.Models.Entities;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Transports.Models.Entities;

public partial class Cars
{

    public ICollection<CarsServices> CarsServices { get; set; }
    public ICollection<CarsGasDtl> CarsGasDtl { get; set; }
    public ICollection<CarsViolations> CarsViolations { get; set; }
    public ICollection<CarsRequests> CarsRequests { get; set; }

    [Key]
    public int CarId { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; }


    [StringLength(100)]
    public string GasCardNo { get; set; }

    [Required]
    [StringLength(100)]
    public string Vin { get; set; }

    [Required]
    public int CurrentOdo { get; set; }


    public int NextOdoService { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime Expiry { get; set; }

    [Required]
    public int YearModel { get; set; }

    public int Deleted01 { get; set; } = 0;

    public int DeletedBy { get; set; } = 0;

    [Required]
    [StringLength(50)]
    public string Plate { get; set; }

    [StringLength(50)]
    public string Status { get; set; }

    public int? StaffId { get; set; }

    [StringLength(50)]
    public string Code { get; set; }

    [StringLength(500)]
    public string Description { get; set; }

    [NotMapped]
    public float TotalServiceCost { get; set; }

    [NotMapped]
    public float TotalGasCost { get; set; }

    [NotMapped]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public VempDtl Staff { get; set; } = null;
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.


}
