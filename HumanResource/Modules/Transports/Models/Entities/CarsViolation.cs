﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Transports.Models.Entities;

public partial class CarsViolations
{
    public Cars Car { get; set; }

    [Key]
    public int Id { get; set; }


    public int CarId { get; set; }

    [StringLength(100)]
    public string Reference { get; set; }

    [StringLength(100)]
    public string Location { get; set; }

    public DateTime PaidAt { get; set; }

    [StringLength(100)]
    public string ReceiptNo { get; set; }

    public int StaffId { get; set; }

    public DateTime Date { get; set; }

    public int CurrentOdo { get; set; }

    public DateTime CreatedAt { get; set; }

    public int Deleted01 { get; set; }

    public int? DeletedBy { get; set; } = 0;

    public float Cost { get; set; }


    public int CreatedBy { get; set; }

    public int? UpdatedBy { get; set; } = 0;



    [StringLength(200)]
    public string Description { get; set; }




}
