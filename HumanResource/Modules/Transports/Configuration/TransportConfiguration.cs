using HumanResource.Modules.Transports.Providers;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Transports.Configuration
{
    /// <summary>
    /// Configuration for the Transport module
    /// </summary>
    public static class TransportConfiguration
    {
        /// <summary>
        /// Registers all Transport module services
        /// </summary>
        public static IServiceCollection AddTransportServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, TransportNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, TransportBadgeProvider>();

            // Register module services
            // services.AddScoped<TransportService>();

            return services;
        }
    }
} 