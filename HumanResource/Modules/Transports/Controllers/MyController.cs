using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Transports.Models.Entities;
using HumanResource.Modules.Transports.Models.Enums;
using HumanResource.Modules.Transports.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;
using HumanResource.Core.UI.Models;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Purchases.Models.Entities;


namespace HumanResource.Modules.Transports.Controllers;

[Area("Transports")]
[Route("Transports/My")]
public class MyController : TransportsController
{
    public TransportsViewModel _v;

    public MyController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _v = new TransportsViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "";
        _v.Helper = helper;
        _v.Auth = Auth();

    }


    [HttpGet("")]
    public IActionResult Index()
    {

        var identity = Identity;

        _v.Page.Class = " sidebar-xs ";
        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="طلبات السيارات", Url=$"/Transports/Index"},
        };

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }
        }

        if (!string.IsNullOrEmpty(toQueryString))
        {
            if (DateTime.TryParse(toQueryString, out DateTime to))
            {
                to = to.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }
        }
        var data2 = Auth().DeptCode;

        _v.CarsRequests = _context.CarsRequests
            .Include(car => car.Car)
            //.Include(staff => staff.Staff)
            .Where(
                r => r.Deleted01 != 1
                && r.StaffId == Auth().EmpNo
                && r.CreatedAt >= _v.Page.Filter.DateFrom
                && r.CreatedAt <= _v.Page.Filter.DateTo
                && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
             )
            .ToList();

        _v.StaffList = _context.VempDtls.Where(s => s.DeptCode == Auth().DeptCode).ToList();

        return View(_v);
    }

    [HttpPost("")]
    public IActionResult CreateRequest(CarsRequests model)
    {

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        if ((model.FromDate < DateTime.Now) && model.ToDate < model.FromDate)
        {
            var response = new
            {
                success = false,
                message = new List<string> { "الوقت غير متاح" },
                action = "",
            };

            return Json(response);
        }

        var StaffId = Auth().EmpNo;
        int? CarId = null;
        var Status = "New";
        var reqStatus = RequestStatus.New;
        var CurrentOdo = "0";
        var Note = "";
        var Type = "0";
        var DepManagerId = model.DepManagerId;
        var ManagerApprov = 0;


        var Managers = _h.Managers(_v.Profile.EmpNo);


        DateTime FromDate = model.FromDate;
        DateTime ToDate = model.ToDate;
        DateTime SpTime = new DateTime(FromDate.Year, FromDate.Month, FromDate.Day, 14, 30, 0);

        int? ApprovedBy = null;
        DateTime? ApprovedAt = null;


        if (ToDate > SpTime)
            Type = "1";


        if (Can("transports-dgeneral"))
        {
            DepManagerId = Auth().EmpNo.Value;
            Status = "Approved by department manager";
            reqStatus = RequestStatus.ManagerApproved;
            ManagerApprov = 1;
        }

        var NewRequest = new CarsRequests
        {
            StaffId = StaffId,
            CarId = CarId,
            DepManagerId = DepManagerId,
            FromDate = model.FromDate,
            ToDate = model.ToDate,
            Note = Note,
            Reason = model.Reason,
            CurrentOdo = CurrentOdo,
            Status = Status,
            REQ_STAT = reqStatus,
            Type = Type,
            ApprovedBy = ApprovedBy,
            ApprovedAt = ApprovedAt,
            ManagerApprov = ManagerApprov,
            CreatedBy = Auth().EmpNo,
            CreatedAt = DateTime.UtcNow
        };



        try
        {

            _context.CarsRequests.Add(NewRequest);
            _context.SaveChanges();

            var response = new
            {
                success = true,
                message = new List<string> { "تم إنشاء الطلب بنجاح." },
                action = "reload",
            };

            if (Can("transports-dgeneral"))
            {
                response = new
                {
                    success = true,
                    message = new List<string> { "تم إنشاء الطلب بنجاح." },
                    action = $"location.href='/Transports/My/View/{NewRequest.Id}'",
                };
            }



            return Json(response);
        }
        catch (Exception ex)
        {

            var response = new
            {
                success = false,
                message = new List<string> { $"Error: {ex.InnerException}" },
                action = "",
            };

            return Json(response);
        }
    }



    [HttpGet("View/{id}")]
    public IActionResult View(int id)
    {

        _v.Page.Class = " sidebar-xs ";
        _v.Page.Reload = true;
        _v.Page.Back = $"/Transports/My";

        _v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="طلبات السيارات", Url=$"/Transports/My"},
             new Breadcrumb {Label="عرض", Url=$"/Transports/Index"},
        };

        _v.CarsRequest = _context.CarsRequests
            .Include(car => car.Car)
            // .Include(staff => staff.Staff)
            .Where(r => r.Id == id && r.StaffId == _v.Profile.EmpNo)
            .FirstOrDefault();
        _v.Cars = _context.Cars.Where(r => r.Deleted01 != 1 && r.Status != "Unavailable").ToList();
        _v.StaffList = _context.VempDtls.ToList();
        _v.CarsRequest.Staff = _context.VempDtls.Where(s => s.EmpNo == _v.CarsRequest.StaffId).FirstOrDefault();
        _v.Auth = Auth();

        if (_v.CarsRequest.Status == "Approved")
        {
            _v.CarsRequest.ApprovedByStaff = _context.VempDtls
                .FirstOrDefault(r => r.EmpNo == _v.CarsRequest.ApprovedBy);
        }

        return View(_v);
    }

    [HttpGet("DeleteRequest/{id}")]
    public IActionResult DeleteRequest(int id)
    {
        var request = _context.CarsRequests.Where(r => r.Id == id && r.StaffId == _v.Profile.EmpNo).FirstOrDefault();
        if (request != null)
        {
            request.Deleted01 = 1;
            _context.SaveChanges();
            return RedirectToAction("Index");
        }
        return RedirectToAction("Index");
    }
}

