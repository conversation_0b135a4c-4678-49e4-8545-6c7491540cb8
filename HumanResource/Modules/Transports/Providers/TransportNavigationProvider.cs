using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Transports.Providers
{
    /// <summary>
    /// Navigation provider for the Transport module
    /// </summary>
    public class TransportNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Transport";
        public int Priority => 41;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Inventory Group navigation items
            var inventoryNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "Transports",
                    Label = "النقليات",
                    Active = "transports",
                    Icon = "<i class=\"far fa-car\"></i>",
                    Rights = new List<Right> { Right.TransportsAdmin, Right.TransportsDepartment },
                    Priority = 5,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "Vehicles",
                            Label = "المركبات",
                            Rights = new List<Right> { Right.TransportsAdmin, Right.TransportsDepartment },
                            Url = "/Transports/Index"
                        },
                        new NavLink
                        {
                            Name = "Gas",
                            Label = "الوقود",
                            Rights = new List<Right> { Right.TransportsAdmin, Right.TransportsDepartment },
                            Url = "/Transports/Gas"
                        },
                        new NavLink
                        {
                            Name = "Maintenance",
                            Label = "الصيانة",
                            Rights = new List<Right> { Right.TransportsAdmin, Right.TransportsDepartment },
                            Url = "/Transports/Service"
                        },
                        new NavLink
                        {
                            Name = "Violations",
                            Label = "المخالفات",
                            Rights = new List<Right> { Right.TransportsAdmin, Right.TransportsDepartment },
                            Url = "/Transports/Violations"
                        },
                        new NavLink
                        {
                            Name = "AllRequests",
                            Label = "جميع الطلبات",
                            Rights = new List<Right> { Right.TransportsAdmin, Right.TransportsDepartment },
                            Url = "/Transports/AllRequests"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Inventory] = inventoryNavItems;

            return navigation;
        }
    }
} 