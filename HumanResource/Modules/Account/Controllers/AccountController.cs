﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using System.Data;
using Microsoft.CodeAnalysis;
using Microsoft.AspNetCore.Mvc.Rendering;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Contexts;
using HumanResource.Core.UI.Services;
using HumanResource.Modules.Account.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Attendence.Models.Enums;

namespace HumanResource.Modules.Account.Controllers;

[Area("Account")]
[Route("Account")]
public class AccountController : BaseController
{

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    private readonly hrmsContext _context;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly TaskService _taskService;
    public AccountViewModel _vmodel;

    public AccountController(hrmsContext context, IHttpContextAccessor httpContextAccessor, AppHelper helper, TaskService taskService) : base(context, httpContextAccessor, helper)
    {
        _vmodel = new AccountViewModel(context, httpContextAccessor, helper);
        _vmodel.Page.Active = "dashboard";
        _vmodel.Helper = helper;
        _vmodel.Auth = Auth();
        _context = context;
        _httpContextAccessor = httpContextAccessor;
        _taskService = taskService;
    }

    [HttpGet("Dashboard")]
    public async Task<IActionResult> Dashboard()
    {
        var ViewModel = _vmodel;

        ViewModel.vempQual = _context.VempQuals.Where(a => a.EmpNo == _vmodel.Profile.EmpNo).ToList();
        ViewModel.vempTrg = _context.VempTrgs.Where(a => a.EmpNo == _vmodel.Profile.EmpNo).ToList();
        int y = Convert.ToSByte(DateTime.Now.ToString("yy"));

        //ViewModel.Overtimes = _context.OvertimePays.Where(a => a.EmpNo == _vmodel.Profile.EmpNo && a.PayYear == DateTime.Now.Year).Select(x => new OvertimePay { EmpNo = x.EmpNo, PayMonth = x.PayMonth, Amt = x.Amt, PayYear = x.PayYear }).ToList();

        ViewBag.GetLEAVEBAL = _h.Leave().Balance(_vmodel.Profile.EmpNo.Value, 1);

        var timeIn = _context.VAttendTrans.Where(e => e.TransDate.Date >= DateTime.Now.Date && e.EmpNo == _vmodel.Profile.EmpNo.ToString()).FirstOrDefault();

        var fillForm = _context.Absents.Any(a => a.EmpNo == _vmodel.Profile.EmpNo && a.OrderDate.Value.Date == DateTime.Now.Date && a.ReqType == 2);
        ViewBag.fillForm = fillForm;

        var month = Convert.ToInt32(DateTime.Now.ToString("MM"));
        var year = Convert.ToInt32(DateTime.Now.ToString("yyyy"));



        var TotalExcs = _context.TotalExcs.FirstOrDefault(a => a.EmpNo == _vmodel.Profile.EmpNo && a.DateMonth == month && a.DateYear.Value == year);
        var TotalLate = _context.TotalLates.FirstOrDefault(a => a.EmpNo == _vmodel.Profile.EmpNo && a.DateMonth == month && a.DateYear.Value == year);


        if (TotalExcs != null)
            ViewBag.ExcuseHour = TotalExcs.Minutes + " : " + TotalExcs.Hours;
        else
            ViewBag.ExcuseHour = "00:00";

        if (TotalLate != null)
            ViewBag.LateHour = TotalLate.Minutes + " : " + TotalLate.Hours;
        else
            ViewBag.LateHour = "00:00";


        ViewBag.HasTimeEnter = false;

        if (timeIn != null)
        { 
            ViewBag.TimeEnter = Convert.ToDateTime(timeIn.TimeIn);
            ViewBag.HasTimeEnte = true;
        }
        else
        { 
            ViewBag.TimeEnter = Convert.ToDateTime("00:00");
        }


        ViewBag.DayList = _context.EmpWorkHours.Where(ro => ro.EmpNo == _vmodel.Profile.EmpNo && ro.Day.Month == DateTime.Now.Month).OrderBy(o => o.Day).ToList();

        // Load tasks from all task providers
        var allTasks = await _taskService.GetAllTasksAsync();
        ViewModel.UserTasks.AddRange(allTasks);

        ViewModel.EmployeeWarnings = _db.EmployeeWarnings.Where(a => a.EmpNo == _vmodel.Profile.EmpNo && (a.Status == EmployeeWarningStatus.New || a.Status == EmployeeWarningStatus.Processing)).ToList();
        return View(ViewModel);
    }

    [HttpGet("UserDashboard")]
    public async Task<IActionResult> UserDashboard()
    {

        return RedirectToAction("Dashboard");

    }

    public IActionResult EmployeeUpdate()
    {
        _vmodel.vempDtl = _context.VempDtls.FirstOrDefault(a => a.EmpNo == _vmodel.Profile.EmpNo);
        ViewBag.Marital = new List<SelectListItem>()
               {new SelectListItem { Value = "0", Text = "أختار الحالة" },
                new SelectListItem { Value = "1", Text = "أعزب" },
                new SelectListItem { Value = "2", Text = "متزوج" },
                new SelectListItem {Value = "3", Text = "متزوجة" },
                new SelectListItem {Value = "4", Text = "أرمل" },
                new SelectListItem {Value = "5", Text = "أرملة" },
                new SelectListItem {Value = "6", Text = "عزباء" },
                new SelectListItem {Value = "7", Text = "مطلق" },
                new SelectListItem {Value = "8", Text = "مطلقة" }
              };
        ViewBag.Section = _context.TsectionCodes
                              .Where(c => c.DgCode == _vmodel.vempDtl.DgCode && c.DeptCode == _vmodel.vempDtl.DeptCode)
                              .Select(c => new SelectListItem
                              {
                                  Value = c.SectionCode.ToString(),
                                  Text = c.SectionDespA.ToString()
                              }).ToList();
        return View(_vmodel);

    }


    [HttpGet("Dg")]
    public IActionResult Dg()
    {
        _vmodel.Page.Active = "DgApprovals";

        return View(_vmodel);

    }

    [HttpPost("EmployeeUpdate")]
    public IActionResult EmployeeUpdate(EmpDtlsUpdate dtlsUpdate)
    {

        try
        {

            dtlsUpdate.UpadteDate = DateTime.Now;

            _context.EmpDtlsUpdates.Add(dtlsUpdate);
            _context.SaveChanges();


            return RedirectToAction("UserDashboard", "Account");

        }
        catch (Exception ex)
        {

            return RedirectToAction("Errorpage404", "Account");
        }
#pragma warning restore CS0168 // Variable is declared but never used
    }

    //public string GetExcutionTimeForMonth(string month, string year)
    //{

    //    var totalTime = _context.Absents
    //        .Where(a => a.EmpNo == _vmodel.Profile.EmpNo && a.ReqType == 1 && a.OrderDate.Value.ToString("MM") == month && a.OrderDate.Value.ToString("yy") == year)
    //        .Select(a => a.TimeTo - a.TimeFrom).ToList()
    //        .Aggregate(TimeSpan.Zero, (sum, timeDiffreence) => sum.Value + timeDiffreence.Value);

    //    int totalHoure = (int)totalTime.TotalHours;
    //    int totalMinuts = (int)totalTime.TotalMinutes;

    //    Ensure minutes are less 60
    //    totalHoure += totalMinuts / 60;
    //    totalMinuts %= 60;

    //    var time = totalHoure + ':' + totalMinuts;

    //    return time.ToString();
    //}




    [HttpGet("RemoveSingleNotification/{guid}")]
    public IActionResult RemoveSingleNotification(string guid)
    {
        var notifi = _db.TUserNotifications.Where(r => r.Guid == guid).FirstOrDefault();

        if (notifi != null)
        {
            _db.Remove(notifi);
            _db.SaveChanges();
        }

        return ResponseHelper.Json(success: true);
    }

    [HttpGet("RemoveAllNotification")]
    public IActionResult RemoveAllNotification()
    {
        var notifi = _db.TUserNotifications.Where(r => r.EmpNo == _vmodel.Profile.EmpNo).ToList();

        _db.RemoveRange(notifi);
        _db.SaveChanges();

        return ResponseHelper.Json(success: true);
    }
}
