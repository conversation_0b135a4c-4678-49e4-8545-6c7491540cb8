﻿@using HumanResource.Modules.Account.ViewModels
@using HumanResource.Modules.Shared.Models.DTOs
@model AccountViewModel
@{
    Layout = "_Layout";
}




<div class="row">
    <div class="col-md-12">
        <div class="card shadow border-0 bg-primary mb-4 ts" style="border:0 !important">
            <div class="card-body py-4 text-white">
                <div class="row">
                    <div class="col-md-6 border-right align-items-center d-flex">
                        <div class="d-flex align-items-center">
                            @if (Model.Profile.ProfileImage != null)
                            {
                                <div class="profile-thumb shadow-sm"
                                     style="width: 90px; height: 90px; border-radius: 45px; background: url('@Model._h.GetFile(Model.Profile.ProfileImage)') no-repeat; background-position: center center; background-size: cover; position:relative; border: 3px solid rgba(255,255,255,0.2);">
                                </div>
                            }
                            else
                            {
                                <div class="profile-thumb shadow-sm"
                                     style="width: 90px; height: 90px; border-radius: 45px; background: url('/assets/images/user60x60.png') no-repeat; background-position: center center; background-size: cover; position:relative; border: 3px solid rgba(255,255,255,0.2);">
                                </div>
                            }

                            <div class="media-body text-white pl-4">
                                <h2 class="mb-1">@Model.Profile.EmpNameA <small class="badge badge-light text-primary">@Model.Profile.EmpNo</small></h2>
                                <h5 class="text-uppercase mb-2">
                                    <i class="far fa-id-badge mr-1"></i> @Model.Profile.DesgCode
                                </h5>
                                <p class="text-uppercase mb-0">
                                    <i class="far fa-building mr-1"></i> @Model.Profile.DeptDespA
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 ml-auto">
                        <div class="">
                            <div class="mb-3 d-flex align-items-center ">
                                <div class="text-white p-2 rounded-circle mr-3">
                                    <i class="far fa-clock fa-lg"></i>
                                </div>
                                <div>
                                    <span class="text-white-50">الحضور</span>
                                    <h5 class="mb-0">
                                        @if (ViewBag.HasTimeEnte==true)
                                        {
                                            @Model._dt(ViewBag.TimeEnter)
                                        }
                                        else
                                        {
                                            <span>--</span>
                                        }
                                    </h5>
                                </div>
                            </div>
                            
                            <div class="d-flex align-items-center ">
                                <div class="text-white p-2 rounded-circle mr-3">
                                    <i class="far fa-umbrella-beach fa-lg"></i>
                                </div>
                                <div>
                                    <span class="text-white-50">رصيد الاجازات <button class="btn btn-sm btn-link text-white" onclick="calculateLeaveBalance()"><i class="fas fa-redo-alt"></i></button></span>
                                    <h5 class="mb-0 leave-balance">@Model._h.Leave().Balance(Model.Profile.EmpNo.Value, 1).ToString("0.00") يوم</h5>
                                
                                </div>
                            </div>
                            
                        </div>
                        
                    </div>
                    <div class="col-md-3">
                        <div class="">
                            <div class="d-flex align-items-center ">
                                <div class="text-white p-2 rounded-circle mr-3">
                                    <i class="far fa-exclamation-triangle fa-lg"></i>
                                </div>
                                <div>
                                    <span class="text-white">الإخطارات<a class="btn btn-sm btn-link text-white" href="/Attendence/Notices/My"><i class="fas fa-external-link-alt"></i></a></span>
                                    <h5 class="mb-0">
                                        @Model.EmployeeWarnings.Count
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





<div class="row">
    <div class="col-md-6 ">

        <div class="card">
            <div class="card-header">
                <h4 class="card-title"><i class="far fa-bookmark fa-lg"></i> المهام الطلوبة  </h4>
            </div>

            <table class="table table-sm table-borderless">
                @foreach (var task in Model.UserTasks)
                {
                    <tr>
                        <th class="pb-0 pt-2">
                            <a href="@task.Url" class="text-dark">
                                @task.Title
                            </a>
                        </th>
                        <td rowspan="2" class="text-center">
                            @if (task.Priority == TaskPriority.Critical)
                            {
                                <span class="badge badge-danger px-3">عاجل</span>
                            }
                            @if (task.Priority == TaskPriority.High)
                            {
                                <span class="badge badge-warning px-3">مهم</span>
                            }
                            @if (task.Priority == TaskPriority.Medium)
                            {
                                <span class="text-primary"><i class="fas fa-exclamation-circle"></i></span>
                            }
                            @if (task.Priority == TaskPriority.Low)
                            {
                                <span class="text-muted"><i class="fas fa-info-circle"></i></span>
                            }
                            @if (task.Priority == TaskPriority.Info)
                            {
                             
                            }
                        </td>
                 
                    </tr>
                    <tr class="border-bottom">
                        <td  class="text-muted  pt-0 ">
                            @task.Text
                        </td>
                    </tr>
                }
                </table>


        </div>
    </div>

    <div class="col-md-6 ">

        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between">
                    <h4 class="card-title"><i class="far fa-bells fa-lg"></i>  الاشعارات </h4>

                    <div>
                        <button type="button"  class="btn btn-light btn-sm" onclick="return confirm('هل انت متاكد')?removeAllNotification():false"><i class="fas fa-trash-alt"></i> حذف الكل</button>
                    </div>
                </div>
            </div>

            <table class="table table-sm  table-borderless">
                @{
                    var notifications = Model._h.Notify().List(Model.Profile.EmpNo.Value);
                }
                @if (notifications.Count() > 0)
                {
                    foreach (var noifi in notifications)
                    {
                        <tr data-id="@noifi.Guid" class="notification-row">
                            <th class="pb-0 pt-2">
                                <a href="@noifi.Url" class="text-dark">
                                    <small>@noifi.Title</small>
                                </a>
                            </th>
                            <td rowspan="2" class="text-end">
                                <button type="button" class="btn btn-light btn-sm" onclick="removeSingleNotification('@noifi.Guid')"><i class="fas fa-trash-alt"></i> حذف </button>
                            </td>

                        </tr>
                        <tr data-id="@noifi.Guid" class="notification-row">
                            <td  class="text-muted border-bottom pt-0 ">
                                @noifi.Text
                                <small class="text-muted">@Model._d(noifi.Timestamp)</small>
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td colspan="2" class="text-muted text-center ">
                            لا يوجد اشعارات
                        </td>
                    </tr>
                }
            </table>
        </div>
    </div>
</div>



<script>

    function removeAllNotification(){

        $('.notification-row').each(function(el){
             $(this).remove();
        });

        $.get('/Account/RemoveAllNotification');
    }

    function removeSingleNotification(guid){

        $('[data-id='+guid+']').each(function(el){
             $(this).remove();
        });

        $.get('/Account/RemoveSingleNotification/'+guid);
    }
    function calculateLeaveBalance(){
        $.get('/Leaves/My/Calculate', function(response) {
            if(response.success) {
                // Find the annual leave (code 1) in the returned data
                var annualLeaveData = response.data.find(item => item.leaveCode === 1);
                if(annualLeaveData) {
                    // Update the displayed balance
                    $(".leave-balance").text(annualLeaveData.balance.toFixed(2) + " يوم");
                }
            }
        });
    }
</script>
    