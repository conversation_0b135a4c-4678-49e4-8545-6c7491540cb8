﻿@using HumanResource.Modules.Account.ViewModels
@model AccountViewModel
@{
    Layout = "_HomeLayout";
}
<form action="EmployeeUpdate" method="post">
<div class="row justify-content-center mt-5">
   
    <div class="col-lg-8 col-md-10 ">
            <div class="card">
                <div class="card-body">
                    <h2> <strong>عزيزي الموظف / عزيزتي الموظفة </strong></h2>
                    <h3>
                        إن وعيك و إلتزامك بأهمية تحديث بياناتك الشخصية يسهم في ضمان دقة المعلومات لدينا.
                    </h3>
                    <h3>
                    دائرة شؤون الموارد البشرية
                    <br />
                </h3>
                </div>
            </div>
        <div class="card">
            <div class="card-header header-elements-inline bg-primary">
                <h5 class="card-title"> @Model._l("Employee Details")</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("Employee No")</label>
                            <input type="text" class="form-control" value="@Model.vempDtl.EmpNo" disabled /><br>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("Name")</label>
                            <input type="text" class="form-control" value="@Model.vempDtl.EmpNameA" disabled /><br>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("Desgnation")</label>
                            <input type="text" class="form-control" value="@Model.vempDtl.DesgCode" disabled /><br>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("Grad Rank")</label>
                            <input type="text" class="form-control" value="@Model.vempDtl.GradRank" disabled /><br>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("DG")</label>
                            <input type="text" class="form-control" value="@Model.vempDtl.DgDespA" disabled /><br>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("Department")</label>
                            <input type="text" class="form-control" value="@Model.vempDtl.DeptDespA" disabled /><br>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("ID card")</label>
                            <input type="text" class="form-control" value="@Model.vempDtl.NatId" disabled /><br>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("Birth Date")</label>
                            <input type="text" class="form-control" value="@Model._d(@Model.vempDtl.BirthDate)" disabled /><br>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group">
                            <label for="">@Model._l("Appoint Date")</label>
                            <input type="text" class="form-control" value="@Model._d(@Model.vempDtl.AppointDate)" disabled /><br>
                        </div>
                    </div>
                </div>
                  <div class="row">
                    <label class="text-danger bold" for="">@Model._l("Note IF Mistake")</label>
                    <textarea name="DeclineNote" class="form-control" required></textarea>

            </div>
            
        
      
   <hr />
 <input type="hidden" name="EmpNo" value="@Model.vempDtl.EmpNo">
        <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="">@Model._l("ID card")</label>
                            <input type="number"  min="8" class="form-control" name="NationalID" pattern="\d{8}" required  /><br>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="">
                                @Model._l("ID card Epiry Date")</label>
                                <input type="date" class="form-control" min='@DateTime.Now.ToString("yyyy-MM-dd")'  name="IdEpiryDate" required /><br>
                        </div>
                    </div>
                </div>
        <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="">@Model._l("Passport No")</label>
                                <input type="text" class="form-control" name="Passport" required /><br>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="">@Model._l("Passport Expire Date")</label>
                                <input type="date" class="form-control" min='@DateTime.Now.ToString("yyyy-MM-dd")' name="PassportExpire" required /><br>
                        </div>
                    </div>
                </div>    
         <div class="row">                 
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="">@Model._l("Marital Status")</label>
                            <select class="form-control" name="MarStat" asp-items="@ViewBag.Marital" required></select>
                            
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label for="">@Model._l("Section")</label>
                            <select class="form-control" name="SectionNo" asp-items="@ViewBag.Section" required></select>
                        </div>
                    </div>
                </div>
         <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label>@Model._l("Perment Address")</label>
                            <input type="text" class="form-control" name="PerAddress" required /><br>

                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label>@Model._l("Curent Address")</label>
                            <input type="text" class="form-control" name="CurAddress" required /><br>
                        </div>
                    </div>
                </div>
         <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                                <label for="">@Model._l("Mobile No")</label>
                                <input type="number" min="=8" class="form-control" name="PhoneNo" pattern="\d{8}" required /><br>

                        </div>
                    </div>
           
                </div>
         <div class="row">
                    <div class="col-lg-12">
                        <div class="form-group">
                            <input type="submit" value='@Model._l("Submit")' class="btn btn-primary btn-block rounded-pill" />

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
</form>