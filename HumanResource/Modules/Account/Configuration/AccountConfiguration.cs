using HumanResource.Modules.Account.Providers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Account.Configuration
{
    /// <summary>
    /// Configuration for the Account module
    /// </summary>
    public static class AccountConfiguration
    {
        /// <summary>
        /// Registers all Account module services
        /// </summary>
        public static IServiceCollection AddAccountServices(this IServiceCollection services)
        {
            // Register task providers
            services.AddScoped<ITaskProvider, AccountTaskProvider>();

            // Register module services
            // services.AddScoped<AccountService>();

            return services;
        }
    }
} 