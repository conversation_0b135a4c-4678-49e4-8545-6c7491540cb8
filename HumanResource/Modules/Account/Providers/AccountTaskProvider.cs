using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Modules.Shared.Models.DTOs;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Account.Providers
{
    /// <summary>
    /// Task provider for the Account module
    /// </summary>
    public class AccountTaskProvider : ITaskProvider
    {
        private readonly hrmsContext _context;
        private readonly AppHelper _appHelper;

        public string ModuleName => "Account";

        public AccountTaskProvider(hrmsContext context, AppHelper appHelper)
        {
            _context = context;
            _appHelper = appHelper;
        }

        public async Task<List<UserTask>> GetTasksAsync()
        {
            var tasks = new List<UserTask>();

            try
            {
                // Get current user info
                var currentUser = _appHelper.Auth();
                if (currentUser?.EmpNo == null)
                {
                    return tasks;
                }

                // Check if employee details update is required
                var isUpdate = await _context.EmpDtlsUpdates
                    .Where(a => a.EmpNo == currentUser.EmpNo)
                    .FirstOrDefaultAsync();

                if (isUpdate == null)
                {
                    tasks.Add(new UserTask
                    {
                        Title = "Employee Details", // This could be localized
                        Text = "دائرة شؤون الموارد البشرية",
                        Url = "/Account/EmployeeUpdate/",
                    });
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break task loading
                Console.WriteLine($"Error loading account tasks: {ex.Message}");
            }

            return tasks;
        }
    }
} 