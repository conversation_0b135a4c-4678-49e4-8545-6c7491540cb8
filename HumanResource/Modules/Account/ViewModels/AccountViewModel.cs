﻿using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Overtime.Models.Entities;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Account.ViewModels
{
    public class AccountViewModel  : BaseViewModel
    {

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        public readonly hrmsContext _context;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        public readonly hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        public readonly IHttpContextAccessor _http;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
        private AppHelper _h;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword

        public AccountViewModel(
            hrmsContext context,
            IHttpContextAccessor httpContextAccessor, AppHelper helper)
            : base(context, httpContextAccessor, helper)
        {

            _context = context;
            _db = context;
            _http = httpContextAccessor;
            _h = helper;
        }


        public VempDtl vempDtl { get; set; }
        public List<VempQual> vempQual { get; set; }
        public List<VempTrg> vempTrg { get; set; }
        public List<VleaveHistory> VleaveHistories { get; set; }

        public EmpDtlsUpdate EmpDtls { get; set; }

        public List<OvertimePay> Overtimes  { get; set; }
        public List<EmployeeWarning> EmployeeWarnings { get; set; }
    }
}
