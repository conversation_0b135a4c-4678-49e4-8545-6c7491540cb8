using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Assets.Providers
{
    /// <summary>
    /// Navigation provider for the Assets module
    /// </summary>
    public class AssetsNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Assets";
        public int Priority => 42;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Inventory Group navigation items
            var inventoryNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "assets",
                    Label = "الأصول الثابتة",
                    Active = "assets",
                    Icon = "<i class=\"fas fa-building\"></i>",
                    Rights = new List<Right> { Right.InventoryDepartment },
                    Priority = 20,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "assets",
                            Label = "الأصول",
                            Rights = new List<Right> { Right.InventoryDepartment },
                            Url = "/Assets/Items"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Inventory] = inventoryNavItems;

            return navigation;
        }
    }
} 