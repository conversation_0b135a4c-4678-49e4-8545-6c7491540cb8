﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Assets.Models.Entities
{
    [Table("ASSET_LOCATION")]
    public class AssetLocation
    {
        [Key]
        [Column("LOC_ID")]
        public int LocId { get; set; }

        [Column("LOC_NAME")]
        public string LocName { get; set; }

        [Column("LOC_DESC")]
        public string LocDesc { get; set; }
        public virtual ICollection<AssetLocation> AssetLocations { get; set; }
    }
}
