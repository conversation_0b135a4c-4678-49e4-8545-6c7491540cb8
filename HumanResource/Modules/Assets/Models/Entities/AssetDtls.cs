﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Assets.Models.Entities;

[Table("ASSET_DTLS")]
public class AssetDtls
{
    [Key]
    [Column("ASSET_ID")]
    public int AssetId { get; set; }

    [Column("ASSET_NAME")]
    public string AssetName { get; set; }

    [Column("ASSET_DESC")]
    public string AssetDesc { get; set; }

    [Column("CAT_ID")]
    public int CatId { get; set; }

    [Column("BARCOAD")]
    public string Barcoad { get; set; }

    [Column("OWNED_BY")]
    public int OwnedBy { get; set; }

    [Column("OWN_DATE")]
    public DateTime OwnDate { get; set; }

    [Column("BRAND_NAME")]
    public string BrandName { get; set; }

    [Column("WARRANTY_YEARS")]
    public int WarrantyYears { get; set; }

}
