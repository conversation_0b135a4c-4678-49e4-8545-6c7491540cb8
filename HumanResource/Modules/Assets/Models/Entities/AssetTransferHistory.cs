﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Assets.Models.Entities
{
    [Table("ASSET_TRANSFER_HISTORY")]
    public class AssetTransferHistory
    {
        [Key]
        [Column("TH_ID")]
        public int ThId { get; set; }

        [Column("ASSET_ID")]
        public int AssetId { get; set; }

        [Column("LOC_ID")]
        public int LocId { get; set; }

        [Column("T_DATE")]
        public DateTime TDate { get; set; } = DateTime.Now;

    }
}
