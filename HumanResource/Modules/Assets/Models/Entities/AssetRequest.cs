﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using HumanResource.Modules.Employees.Models.Entities;

namespace HumanResource.Modules.Assets.Models.Entities;

[Table("ASSET_REQUEST")]
public class AssetRequest
{

    [Key]
    [Column("REQ_ID")]
    public int ReqId { get; set; }

    [Column("REQ_DESC")]
    public string ReqDesc { get; set; }


    [Column("ASSET_ID")]
    public int AssetId { get; set; }

    [Column("EMP_NO")]
    public int EmpNo { get; set; }

    [NotMapped]
    public virtual VempDtl VempDtl { get; set; }

    [Column("QUANTITY")]
    public int Quantity { get; set; }

    [Column("REQ_STAT")]
    public int ReqStat { get; set; }

    [NotMapped]
    public virtual RequestStatus RequestStatus { get; set; }
    // public virtual TempTrgMas TempTrgMas { get; set; }

    [Column("REQ_DATE")]
    public DateTime ReqDate { get; set; } = DateTime.Now;

    [Column("LPO_NO")]
    public int LpoNo { get; set; }

    [Column("PURCHASE_DATE")]
    public DateTime PuchaseDate { get; set; } = DateTime.Now;

    [Column("BENEFICIARY_NAME")]
    public string BeneficiaryName { get; set; }

    [Column("AMOUNT")]
    public int Amount { get; set; }

    [Column("DELIVERED_QTY")]
    public int DeliveredQty { get; set; }

    [Column("LOC_ID")]
    public int LocId { get; set; }


    [NotMapped]
    public virtual AssetLocation AssetLocation { get; set; }


    [Column("R_REMARK")]
    public string RRemark { get; set; }

    [Column("GM_APRROVAL")]
    public int GmApproval { get; set; }
}


