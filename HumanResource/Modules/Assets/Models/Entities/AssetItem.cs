﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using HumanResource.Modules.Inventory.Models.Entities;

namespace HumanResource.Modules.Assets.Models.Entities
{
    [Table("ASSET_ITEM")]
    public class AssetItem
    {
        
        [Key]
        [Column("ITEM_ID")]
        public int ItemId { get; set; }

        [Column("ITEM_NAME")]
        public string AssetName { get; set; }

        [Column("CAT_ID")]
        public int CatId { get; set; } 
        public virtual InventoryCategory Category { get; set; }


      //  public virtual ICollection<AssetItem> AssetItems { get; set; }
        
        
      

    }
}
