﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Assets.Models.Entities
{
    [Table("REQUEST_STATUS")]
    public class RequestStatus
    {
        [Key]
        [Column("RS_ID")]
        public int RsId { get; set; }

        [Column("RS_NAME")]
        public string RsName { get; set; }

        public virtual ICollection<AssetRequest> AssetRequests { get; set; }
        
    }
}
