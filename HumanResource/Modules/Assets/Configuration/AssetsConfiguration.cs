using HumanResource.Modules.Assets.Providers;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Assets.Configuration
{
    /// <summary>
    /// Configuration for the Assets module
    /// </summary>
    public static class AssetsConfiguration
    {
        /// <summary>
        /// Registers all Assets module services
        /// </summary>
        public static IServiceCollection AddAssetsServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, AssetsNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, AssetsBadgeProvider>();

            // Register module services
            // services.AddScoped<AssetsService>();

            return services;
        }
    }
} 