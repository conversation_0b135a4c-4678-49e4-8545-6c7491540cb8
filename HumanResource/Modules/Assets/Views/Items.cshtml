﻿@using HumanResource.Modules.Assets.ViewModels
@model AssetsViewModel



<div class="d-flex justify-content-between py-2">


    <h3>@Model._("Assets")</h3>
    <div>
        <a href="#" class="btn btn-primary rounded-pill shadow btn-sm " data-toggle="modal" data-target="#create-modal1">
            <i class="fas fa-plus"></i>  @Model._("New Asset")
        </a>
    </div>
  
</div>

<div class="card shadow">
    <table class="table datatable table-striped">
        <thead>
            <tr>

                <th>@Model._("Asset Id")</th>
                <th>@Model._("Asset Name")</th>
                <th>@Model._("Category")</th>
                

            </tr>
        </thead>
        <tbody>
            @foreach (var assetitem in Model.assetItems)
            {
                <tr>
                    <td><a href="/Assets/Item/@assetitem.ItemId">@assetitem.ItemId</a></td>
                    <td>
                        <a href="/Assets/Item/@assetitem.ItemId" class="d-flex">

                            <div>
                                @assetitem.AssetName
                                <br>
                                <span class="text-muted">@assetitem.AssetName</span>
                            </div>

                        </a>


                    </td>
                    <td>@assetitem.CatId</td>
                    
                </tr>
            }
        </tbody>
    </table>
</div>

<form action="/Assets/Items" method="post" class="ajax">
    <div class="modal fade" id="create-modal1" tabindex="-1" role="dialog" aria-labelledby="new-leave-modalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-vehicle-modalLabel">@Model._l("New request")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="app">
                    <div class="row">

                        <div class="col-lg-6">
                            <label for="CatId">@Model._("Category")</label>
                            <div>
                                <select name="CatId" class="form-control ">
                                   @* --select2*@
                                    @foreach (var cat in Model.InventoryCategories)
                                    {
                                        <option value="@cat.Id">@cat.Name</option>
                                    }
                                </select>
                            </div>

                        </div>
                   
                        <div class="col-lg-6">
                            <label for="">@Model._("Name")</label>
                            <input type="text" required class="form-control" name="AssetName">
                        </div>
                        </div>                   
                </div>
           
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("Close")</button>
                <button type="submit" class="btn btn-primary">@Model._l("Submit")</button>
            </div>
            </div>
        </div>
    </div>
 </form>
   







@*<script>
    function get_code() {
        $.get("/Inventory/GetNextCode", function (data) {
            console.log(data)

            $('#new-item-code').val(data);
        });
    }
</script>*@