﻿@using HumanResource.Modules.Assets.ViewModels
@model AssetsViewModel

<div class="d-flex justify-content-between py-2">



    <h3>@Model._l("AssetDtls")</h3>

    <div>
        <button data-toggle="modal" data-target="#new-asset-modal" class="btn btn-primary rounded-pill"><i class="fa fa-plus"></i> @Model._l("Add Furniture")</button>

    </div>
</div>
<div class="card shadow">

    <table class="table datatable table-hover ">
        <thead>
            <tr>
                <td>@Model._l("Asset Id")</td>
                <td>@Model._l("ASSET NAME")</td>
                <td>@Model._l("Description")</td>
                <td>@Model._l("OWNED BY")</td>
                <td>@Model._l("OWN DATE")</td>
                <td>@Model._l("WARRANTY YEARS")</td>

            </tr>
        </thead>
        <tbody>
            @foreach (var AssetDtls in Model.AssetDtlss)
            {
                @if (AssetDtls.CatId == 11)
                {

                    <tr>
                        <td><a href="/Assets/ViewAsset/@AssetDtls.AssetId">@AssetDtls.AssetId</a></td>
                        <td>@AssetDtls.AssetName</td>
                        <td>@AssetDtls.AssetDesc</td>
                        <td>@AssetDtls.OwnedBy</td>
                        <td>@AssetDtls.OwnDate</td>
                        <td>@AssetDtls.WarrantyYears</td>
                    </tr>
                }}
        </tbody>
    </table>
</div>


<form action="/Assets/AssetDtls" class="ajax" method="post">
    <div class="modal fade" id="new-asset-modal" tabindex="-1" role="dialog" aria-labelledby="new-asset-modal" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-asset-modal">@Model._l("New Furniture")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="">@Model._l("Asset Name")</label>
                            <input type="text" name="assetDtls.AssetName" class="form-control" required><br>

                            <label for="">@Model._l("Description")</label>
                            <input type="text" name="assetDtls.AssetDesc" class="form-control" required><br>

                            <label for="">@Model._l("Requested by")</label>
                            <input type="number" name="assetRequest.EmpNo" class="form-control" required><br>

                            <label for="">@Model._l("Owned By")</label>
                            <input type="number" name="assetDtls.OwenedBy" class="form-control"><br>

                            <label for="">@Model._l("OWN_DATE")</label>
                            <input type="date" name="assetDtls.OwnDate" class="form-control"><br>

                        </div>

                        <div class="col-lg-6">
                            <label for="">@Model._l("Current Location")</label>
                            <input type="text" name="assetRequest.currentLocation" class="form-control" required><br>

                            <label for="">@Model._l("LPO")</label>
                            <input type="number" name="assetRequest.lpo" class="form-control" required><br>

                            <label for="">@Model._l("PURCHASE DATE")</label>
                            <input type="date" name="assetRequest.purchaseDate" class="form-control" required><br>

                            <label for="">@Model._l("BENEFICIARY NAME")</label>
                            <input type="text" name="assetRequest.BeneficiaryName" class="form-control" required /><br>

                            <label for="">@Model._l("WARRANTY YEARS")</label>
                            <input type="number" name="assetDtls.WarrantyYears" class="form-control" required><br>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex">
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                </div>
            </div>
        </div>
    </div>

</form>