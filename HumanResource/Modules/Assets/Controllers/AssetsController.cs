﻿using Microsoft.AspNetCore.Mvc;
using HumanResource.Modules.Assets.Models.Entities;
using HumanResource.Modules.Assets.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Assets.Forms;
using HumanResource.Core.UI.Models;

namespace HumanResource.Modules.Assets.Controllers;

[Area("Assets")]
[Route("Assets")]
public class AssetsController : BaseController
{

    public AssetsViewModel _v;


    public List<string> _statuses;

    public AssetsController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _v = new AssetsViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "assets";



    }


    public IActionResult Index()
    {
        return View();
    }


    // ASSET ITEMS
    [HttpGet("Items")]
    public IActionResult Items()
    {

        if (!Can("inventory-admin|inventory-department"))
            return StatusCode(403);

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المخازن", Url=$"/Inventory/Index"},
            new Breadcrumb {Label="الاصول", Url=$"/Inventory/Index"},
        };

        _v.Page.Title = "Assets";

        _v.assetItems = _db.AssetItem.ToList();
        _v.InventoryCategories = _db.InventoryCategory.Where(c => c.IsAsset == 1).ToList();

        return View(_v);
    }

    [HttpPost("Items")]
    public IActionResult CreateItem(AssetItem assetItem)
    {

        // if (!Can("inventory-admin|inventory-department"))
        //   return StatusCode(403);

        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }


        var nid = 0;
        if (_context.AssetItem.Count() == 0)
        {
            nid = (int)1001;
        }
        else
        {
            nid = (int)_context.AssetItem.Max(m => m.ItemId) + 1;

        }
        var AddItem = new AssetItem
        {
            ItemId = nid,
            AssetName = assetItem.AssetName,
            CatId = assetItem.CatId
        };

        _context.AssetItem.Add(AddItem);
        _context.SaveChanges();

        return Json(new
        {
            success = true,
            message = new List<string> { _("Item has been Added Successfully") },
            action = "reload",
        });

    }


    [HttpGet("Item/{Id}")]

    public IActionResult Item(int Id)
    {


        var item = _db.AssetItem.Find(Id);

        if (item == null)
            return StatusCode(404);

        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="المخازن", Url=$"#"},
            new Breadcrumb {Label="الاصول", Url=$"/Assets/Items"},
            new Breadcrumb {Label=item.AssetName, Url=$"#"},
        };

        _v.Page.Back = $"/Assets/Items";

        _v.Page.Title = item.AssetName;

        _v.AssetItem = item;


        return View(_v);
    }


    [HttpGet("AssetDtls")]
    public IActionResult AssetDtls()
    {
        _v.Page.Class = " ";
        _v.Page.Reload = true;

        _v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Assets", Url=$"/Assets/AssetDtls"},
        };

        _v.AssetDtlss = _context.AssetDtls.ToList();
        return View(_v);
    }

    [HttpPost("AssetDtls")]


    public async Task<IActionResult> AssetDtls(AddExistingAsset post)
    {

        //if (!Can("Training-admin|Training-department"))
        //    return StatusCode(403);

        var id = (int)_context.AssetDtls.Max(m => m.AssetId) + 1;

        var EAsset = new AssetDtls
        {
            AssetId = id,
            CatId = 11,
            AssetName = post.assetDtls.AssetName,
            AssetDesc = post.assetDtls.AssetDesc,
            OwnedBy = post.assetDtls.OwnedBy,
            OwnDate = post.assetDtls.OwnDate,
            WarrantyYears = post.assetDtls.WarrantyYears
        };

        _context.AssetDtls.Add(EAsset);
        await _context.SaveChangesAsync();


        //    foreach (var item in post.TempReqDtls)
        //    {
        //        var nid = (int)_context.TempReqDtls.Max(m => m.Id) + 1;
        //        var CourseReqDtl = new TempReqDtls
        //        {
        //            Id = nid,
        //            EmpNo = item.EmpNo,
        //            ReqNo = CourseReq.ReqNo,
        //            StartDate = CourseReq.CourseStartDate,
        //            EndDate = CourseReq.CourseEndDate,

        //        };

        //        _context.TempReqDtls.Add(CourseReqDtl);
        //        await _context.SaveChangesAsync();

        //    }

        //    if (post.TempTrgReq.File != null && post.TempTrgReq.File.Length > 0)
        //    {

        //        var FileId = await _h.UploadAsync(post.TempTrgReq.File);
        //        CourseReq.FileId = FileId;
        //        _context.Update(CourseReq);
        //        await _context.SaveChangesAsync();

        //    }

        //}



#pragma warning restore CS0472 // The result of the expression is always the same since a value of this type is never equal to 'null'
        return Json(new
        {
            success = true,
            message = new List<string> { _("Asset Added Successfully") },
            action = "reload",
        });


    }
}





