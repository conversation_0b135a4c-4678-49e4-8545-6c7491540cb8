﻿using HumanResource.Modules.Inventory.Models.Entities;
using HumanResource.Modules.Assets.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Assets.ViewModels;

public class AssetsViewModel : BaseViewModel
{


    hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
    public AssetsViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _db = context;


    }

    public List<AssetItem> assetItems { get; set; }

    public List<InventoryCategory> InventoryCategories { get; set; }
    public InventoryCategory InventoryCategory { get; set; }

    public AssetItem AssetItem { get; set; }

    public List<AssetDtls> AssetDtlss { get; set; }
    public AssetDtls AssetDtls { get; set; }
}

