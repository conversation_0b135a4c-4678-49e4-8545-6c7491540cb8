using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.ViewModels;
using Microsoft.AspNetCore.Mvc;

namespace HumanResource.Modules.Settings.Controllers;

[Area("Settings")]
[Route("Settings/System")]
[Can("admin")]
public class SystemController : BaseController
{
    private BaseViewModel _v;

    public SystemController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, 
        AppHelper helper)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new BaseViewModel(context, bccontext, httpContextAccessor, helper);
        _v.Page.Active = "settings-system";
    }

    // GET: /Settings/System
    public IActionResult Index()
    {
        // Settings are accessed directly in the view using Model._h.Settings.Get()
        return View(_v);
    }
    
    // GET: /Settings/System/Get?key={key}
    [HttpGet("Get")]
    public IActionResult Get(string key)
    {
        if (string.IsNullOrEmpty(key))
        {
            return BadRequest("Key is required");
        }
        
        // Only allow system settings
        if (!key.StartsWith("system."))
        {
            return BadRequest("Invalid system setting key");
        }
        
        var value = _h.Settings.Get(key);
        return Json(new { key, value });
    }
    
    // POST: /Settings/System/Set
    [HttpPost("Set")]
    public IActionResult Set(string key, string value)
    {
        if (string.IsNullOrEmpty(key))
        {
            return BadRequest("Key is required");
        }
        
        // Only allow system settings
        if (!key.StartsWith("system."))
        {
            return BadRequest("Invalid system setting key");
        }
        
        _h.Settings.Set(key, value);
        return Json(new { success = true, key, value });
    }
    
    // POST: /Settings/System/SaveSystemSettings
    [HttpPost("SaveSystemSettings")]
    public IActionResult SaveSystemSettings(IFormCollection form)
    {
        _h.Settings.Set("system.upload_dir", form["upload_dir"]);
                               
        return ResponseHelper.Json(success: true, action: "reload");
    }
} 