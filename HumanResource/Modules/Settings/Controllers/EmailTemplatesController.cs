using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.Services;
using HumanResource.Modules.Shared.ViewModels;
using Microsoft.AspNetCore.Mvc;

namespace HumanResource.Modules.Settings.Controllers;

[Area("Settings")]
[Route("Settings/EmailTemplates")]
[Can("admin")]
public class EmailTemplatesController : BaseController
{
    private BaseViewModel _v;
    private readonly EmailService _emailService;

    // Fixed template names - no dynamic add/remove
    private readonly List<string> _templates = new List<string>
    {
        "attendence.warning1",
        "attendence.warning2"
    };

    public EmailTemplatesController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, 
        <PERSON><PERSON><PERSON><PERSON><PERSON> helper,
        EmailService emailService)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new BaseViewModel(context, bccontext, httpContextAccessor, helper);
        _v.Page.Active = "settings-email-templates";
        _emailService = emailService;
    }

    // GET: /Settings/EmailTemplates
    public IActionResult Index()
    {
        try
        {
            // Debug information
            if (_templates == null)
            {
                ViewBag.DebugInfo = "Templates list is null";
                ViewBag.Templates = new List<string>();
            }
            else
            {
                ViewBag.DebugInfo = $"Templates count: {_templates.Count}";
                ViewBag.Templates = _templates;
            }
            
            // Check if _v is null
            if (_v == null)
            {
                ViewBag.DebugInfo += " | BaseViewModel is null";
            }
            
            return View(_v);
        }
        catch (Exception ex)
        {
            ViewBag.DebugInfo = $"Exception in Index: {ex.Message}";
            ViewBag.Templates = new List<string>();
            return View(_v);
        }
    }
    
    // GET: /Settings/EmailTemplates/GetTemplate?name={name}
    [HttpGet("GetTemplate")]
    public IActionResult GetTemplate(string name)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Template name is required" }
                );
            }
            
            // Only allow fixed templates
            if (!_templates.Contains(name))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Invalid template name" }
                );
            }
            
            var content = _emailService.GetTemplate(name);
            return ResponseHelper.Json(
                success: true,
                data: new { name, content },
                message: new List<string> { "Template retrieved successfully" }
            );
        }
        catch (Exception ex)
        {
            return ResponseHelper.Json(
                success: false,
                message: new List<string> { "An error occurred while retrieving the template" }
            );
        }
    }
    
    // POST: /Settings/EmailTemplates/SaveTemplate
    [HttpPost("SaveTemplate")]
    public IActionResult SaveTemplate(string name, string content)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Template name is required" }
                );
            }
            
            // Only allow fixed templates
            if (!_templates.Contains(name))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Invalid template name" }
                );
            }
            
            // Save template using settings service (since templates are stored as settings)
            var templateKey = $"email.template.{name}";
            _h.Settings.Set(templateKey, content);
            
            return ResponseHelper.Json(
                success: true,
                data: new { name, content },
                message: new List<string> { "Template saved successfully" },
                action: "reload"
            );
        }
        catch (Exception ex)
        {
            return ResponseHelper.Json(
                success: false,
                message: new List<string> { "An error occurred while saving the template" }
            );
        }
    }
    
    // POST: /Settings/EmailTemplates/PreviewTemplate
    [HttpPost("PreviewTemplate")]
    public IActionResult PreviewTemplate(string name, Dictionary<string, string> variables)
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Template name is required" }
                );
            }
            
            // Only allow fixed templates
            if (!_templates.Contains(name))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Invalid template name" }
                );
            }
            
            string content = _emailService.GetTemplate(name);
            if (string.IsNullOrEmpty(content))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { $"Template '{name}' not found" }
                );
            }
            
            string processed = _emailService.BindVariables(content, variables);
            return ResponseHelper.Json(
                success: true,
                data: new { name, content, processed },
                message: new List<string> { "Template preview generated successfully" }
            );
        }
        catch (Exception ex)
        {
            return ResponseHelper.Json(
                success: false,
                message: new List<string> { "An error occurred while generating the template preview" }
            );
        }
    }
    
    /// <summary>
    /// Get user-friendly display name for template
    /// </summary>
    private string GetTemplateDisplayName(string templateName)
    {
        return templateName switch
        {
            "attendence.warning1" => "Attendance Warning - First Warning",
            "attendence.warning2" => "Attendance Warning - Second Warning",
            _ => templateName
        };
    }
} 