using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.Services;
using HumanResource.Modules.Shared.ViewModels;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Net.Mail;

namespace HumanResource.Modules.Settings.Controllers;


[Area("Settings")]
[Route("Settings")]
[Can("admin")]
public class SettingsController : BaseController
{
    private BaseViewModel _v;
    private readonly EmailService _emailService;

    public SettingsController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, 
        AppHelper helper,
        EmailService emailService)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new BaseViewModel(context, bccontext, httpContextAccessor, helper);
        _v.Page.Active = "settings";
        _emailService = emailService;
    }

    // GET: /Settings
    public IActionResult Index()
    {

        
        return View(_v);
    }





    [HttpPost("saveEmailSettings")]
    public IActionResult saveEmailSettings(IFormCollection form)
    {
        _h.Settings.Set("email.smtp_server", form["smtp_server"]);
        _h.Settings.Set("email.smtp_port", form["smtp_port"].ToString());
        _h.Settings.Set("email.smtp_username", form["smtp_username"]);
        _h.Settings.Set("email.smtp_password", form["smtp_password"]);
        _h.Settings.Set("email.smtp_enable_ssl", form["smtp_enable_ssl"].ToString());   
        _h.Settings.Set("email.sender_name", form["sender_name"]);
        _h.Settings.Set("email.sender_address", form["sender_address"]);
        _h.Settings.Set("email.reply_to", form["reply_to"]);
        _h.Settings.Set("email.footer", form["footer"]);
                                
        return ResponseHelper.Json(success:true, action: "reload");
    }
    
    // POST: /Settings/TestEmailConnection
    [HttpPost("TestEmailConnection")]
    public IActionResult TestEmailConnection(string server, int port, string username, string password, bool enableSsl)
    {
        try
        {
            using (var client = new SmtpClient(server, port))
            {
                client.UseDefaultCredentials = false;
                client.Credentials = new NetworkCredential(username, password);
                client.EnableSsl = enableSsl;
                client.Timeout = 10000; // 10 seconds timeout
                
                // Try to connect to the SMTP server
                client.Send("<EMAIL>", "<EMAIL>", "Test Connection", "This is a test message");
                
                return Json(new { success = true });
            }
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = ex.Message });
        }
    }
    

} 