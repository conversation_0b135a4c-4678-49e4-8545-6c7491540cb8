using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Attributes;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Modules.Shared.ViewModels;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Net.Mail;

namespace HumanResource.Modules.Settings.Controllers;

[Area("Settings")]
[Route("Settings/Email")]
[Can("admin")]
public class EmailController : BaseController
{
    private BaseViewModel _v;

    public EmailController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, 
        AppHelper helper)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new BaseViewModel(context, bccontext, httpContextAccessor, helper);
        _v.Page.Active = "settings-email";
    }

    // GET: /Settings/Email
    public IActionResult Index()
    {
        // Email settings are accessed directly in the view using Model._h.Settings.Get()
        return View(_v);
    }
    
    // GET: /Settings/Email/Get?key={key}
    [HttpGet("Get")]
    public IActionResult Get(string key)
    {
        try
        {
            if (string.IsNullOrEmpty(key))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Key is required" }
                );
            }
            
            // Only allow email settings
            if (!key.StartsWith("email."))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Invalid email setting key" }
                );
            }
            
            var value = _h.Settings.Get(key);
            return ResponseHelper.Json(
                success: true,
                data: new { key, value },
                message: new List<string> { "Setting retrieved successfully" }
            );
        }
        catch (Exception ex)
        {
            return ResponseHelper.Json(
                success: false,
                message: new List<string> { "An error occurred while retrieving the setting" }
            );
        }
    }
    

    
    // POST: /Settings/Email/SaveEmailSettings
    [HttpPost("SaveEmailSettings")]
    public IActionResult SaveEmailSettings(IFormCollection form)
    {
        try
        {
            // Save regular text fields
            _h.Settings.Set("email.smtp_server", form["smtp_server"]);
            _h.Settings.Set("email.smtp_port", form["smtp_port"].ToString());
            _h.Settings.Set("email.smtp_username", form["smtp_username"]);
            _h.Settings.Set("email.smtp_password", form["smtp_password"]);
            _h.Settings.Set("email.sender_name", form["sender_name"]);
            _h.Settings.Set("email.sender_address", form["sender_address"]);
            _h.Settings.Set("email.reply_to", form["reply_to"]);
            _h.Settings.Set("email.footer", form["footer"]);
            _h.Settings.Set("email.test_email_address", form["test_email_address"]);
            
            // Handle checkbox values properly
            // Checkboxes send "true" when checked, nothing when unchecked
            var sslEnabled = string.IsNullOrEmpty(form["smtp_enable_ssl"]) ? "false" : "true";
            _h.Settings.Set("email.smtp_enable_ssl", sslEnabled);
            
            var testMode = string.IsNullOrEmpty(form["is_test_mode"]) ? "false" : "true";
            _h.Settings.Set("email.is_test_mode", testMode);
                                    
            return ResponseHelper.Json(
                success: true, 
                message: new List<string> { "Email settings saved successfully" },
                action: "reload"
            );
        }
        catch (Exception ex)
        {
            return ResponseHelper.Json(
                success: false,
                message: new List<string> { "An error occurred while saving email settings" }
            );
        }
    }
    
    // POST: /Settings/Email/TestEmailConnection
    [HttpPost("TestEmailConnection")]
    public IActionResult TestEmailConnection(string server, int port, string username, string password, bool enableSsl)
    {
        try
        {
            if (string.IsNullOrEmpty(server))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "SMTP server is required" }
                );
            }

            if (string.IsNullOrEmpty(username))
            {
                return ResponseHelper.Json(
                    success: false,
                    message: new List<string> { "Username is required" }
                );
            }

            using (var client = new SmtpClient(server, port))
            {
                client.UseDefaultCredentials = false;
                client.Credentials = new NetworkCredential(username, password);
                client.EnableSsl = enableSsl;
                client.Timeout = 10000; // 10 seconds timeout
                
                // Try to connect to the SMTP server
                client.Send("<EMAIL>", "<EMAIL>", "Test Connection", "This is a test message");
                
                return ResponseHelper.Json(
                    success: true,
                    message: new List<string> { "Email connection test successful" }
                );
            }
        }
        catch (Exception ex)
        {
            return ResponseHelper.Json(
                success: false,
                message: new List<string> { $"Email connection test failed: {ex.Message}" }
            );
        }
    }
} 