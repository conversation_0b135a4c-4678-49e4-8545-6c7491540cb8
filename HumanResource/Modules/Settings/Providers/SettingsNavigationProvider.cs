using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Settings.Providers
{
    /// <summary>
    /// Navigation provider for the Settings module
    /// </summary>
    public class SettingsNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Settings";
        public int Priority => 51;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Administration Group navigation items
            var administrationNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "settings-system",
                    Label = "إعدادات النظام",
                    Active = "settings-system",
                    Icon = "<i class=\"fas fa-cogs\"></i>",
                    Rights = new List<Right> { Right.Admin },
                    Priority = 20,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "settings-system",
                            Label = "إعدادات النظام",
                            Rights = new List<Right> { Right.Admin },
                            Url = "/Settings/System/"
                        }
                    }
                },
                new NavItem
                {
                    Name = "settings-email",
                    Label = "إعدادات البريد",
                    Active = "settings-email",
                    Icon = "<i class=\"fas fa-envelope\"></i>",
                    Rights = new List<Right> { Right.Admin },
                    Priority = 21,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "settings-email",
                            Label = "إعدادات البريد",
                            Rights = new List<Right> { Right.Admin },
                            Url = "/Settings/Email/"
                        }
                    }
                },
                new NavItem
                {
                    Name = "settings-email-templates",
                    Label = "قوالب البريد",
                    Active = "settings-email-templates",
                    Icon = "<i class=\"fas fa-file-alt\"></i>",
                    Rights = new List<Right> { Right.Admin },
                    Priority = 22,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "settings-email-templates",
                            Label = "قوالب البريد",
                            Rights = new List<Right> { Right.Admin },
                            Url = "/Settings/EmailTemplates/"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Administration] = administrationNavItems;

            return navigation;
        }
    }
} 