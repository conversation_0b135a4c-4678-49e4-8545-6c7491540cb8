using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using Microsoft.EntityFrameworkCore;
using SettingsEntity = HumanResource.Modules.Settings.Models.Entities.Settings;


namespace HumanResource.Modules.Settings.Services
{
    public class SettingsService
    {
        private readonly hrmsContext _context;

        public SettingsService(hrmsContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Get a setting value by key
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <returns>The setting value or null if not found</returns>
        public string Get(string key)
        {
            var setting = _context.Settings.Find(key);
            return setting?.Value;
        }

        /// <summary>
        /// Get a setting value by key with a default value if not found
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">Default value to return if setting is not found</param>
        /// <returns>The setting value or the default value if not found</returns>
        public string Get(string key, string defaultValue)
        {
            if(string.IsNullOrEmpty(key))
            {
                return defaultValue;
            }
            var value = Get(key);
            return value ?? defaultValue;
        }

        /// <summary>
        /// Get a boolean setting value
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">Default value to return if setting is not found</param>
        /// <returns>The boolean value of the setting</returns>
        public bool GetBool(string key, bool defaultValue = false)
        {
            var value = Get(key);
            if (value == null)
                return defaultValue;
                
            return value.ToLower() == "true" || value == "1";
        }

        /// <summary>
        /// Get an integer setting value
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="defaultValue">Default value to return if setting is not found or not a valid integer</param>
        /// <returns>The integer value of the setting</returns>
        public int GetInt(string key, int defaultValue = 0)
        {
            var value = Get(key);
            if (value == null)
                return defaultValue;
                
            if (int.TryParse(value, out int result))
                return result;
                
            return defaultValue;
        }

        /// <summary>
        /// Set a setting value
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The setting value</param>
        public void Set(string key, string value)
        {
            var setting = _context.Settings.Find(key);
            
            if (setting == null)
            {
                // Create new setting
                setting = new SettingsEntity { Key = key, Value = value };
                _context.Settings.Add(setting);
            }
            else
            {
                // Update existing setting
                setting.Value = value;
                _context.Entry(setting).State = EntityState.Modified;
            }
            
            _context.SaveChanges();
        }

        /// <summary>
        /// Set a boolean setting value
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The boolean value</param>
        public void Set(string key, bool value)
        {
            Set(key, value ? "true" : "false");
        }

        /// <summary>
        /// Set an integer setting value
        /// </summary>
        /// <param name="key">The setting key</param>
        /// <param name="value">The integer value</param>
        public void Set(string key, int value)
        {
            Set(key, value.ToString());
        }

        /// <summary>
        /// Remove a setting
        /// </summary>
        /// <param name="key">The setting key to remove</param>
        /// <returns>True if setting was removed, false if it didn't exist</returns>
        public bool Remove(string key)
        {
            var setting = _context.Settings.Find(key);
            if (setting == null)
                return false;
                
            _context.Settings.Remove(setting);
            _context.SaveChanges();
            return true;
        }

        public List<SettingsEntity> GetAll()
        {
            return _context.Settings.ToList();
        }
    }
} 