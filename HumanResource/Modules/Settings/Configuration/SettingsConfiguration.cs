using HumanResource.Modules.Settings.Providers;
using HumanResource.Modules.Settings.Services;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Settings.Configuration
{
    /// <summary>
    /// Configuration for the Settings module
    /// </summary>
    public static class SettingsConfiguration
    {
        /// <summary>
        /// Registers all Settings module services
        /// </summary>
        public static IServiceCollection AddSettingsServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, SettingsNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, SettingsBadgeProvider>();

            // Register module services
            services.AddScoped<SettingsService>();

            return services;
        }
    }
} 