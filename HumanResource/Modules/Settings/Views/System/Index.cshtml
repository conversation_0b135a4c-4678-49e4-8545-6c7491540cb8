@using HumanResource.Modules.Shared.ViewModels
@model BaseViewModel

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-cogs mr-2"></i> System Settings
            </h3>
        </div>
        <div class="card-body">
            <form class="ajax" action="@Url.Action("SaveSystemSettings", "System")" method="post">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">File Storage Configuration</h4>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="upload_dir">Upload Directory Path</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="upload_dir" name="upload_dir" 
                                               value="@(Model?._h?.Settings?.Get("system.upload_dir") ?? "")" 
                                               placeholder="e.g., /uploads or C:\uploads">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-secondary" onclick="selectDirectory()">
                                                <i class="fas fa-folder-open"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">
                                        Specify the directory path where uploaded files will be stored. 
                                        Use absolute paths for production environments.
                                    </small>
                                </div>
                                
                                <div class="form-group">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        <strong>Note:</strong> Changes to the upload directory will take effect immediately. 
                                        Ensure the directory exists and has proper write permissions.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">Current Status</h4>
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <strong>Current Upload Directory:</strong>
                                    <br>
                                    <code id="current-upload-dir">@(Model?._h?.Settings?.Get("system.upload_dir") ?? "Not set")</code>
                                </div>
                                
                                <hr>
                                
                                <div class="info-item">
                                    <strong>Directory Status:</strong>
                                    <br>
                                    <span id="directory-status" class="badge badge-secondary">Checking...</span>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="checkDirectoryStatus()">
                                        <i class="fas fa-sync-alt"></i> Check Status
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-right mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> Save System Settings
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetToDefaults()">
                        <i class="fas fa-undo mr-1"></i> Reset to Defaults
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function selectDirectory() {
    // This would typically open a directory picker dialog
    // For now, we'll show a simple prompt
    var currentPath = document.getElementById('upload_dir').value;
    var newPath = prompt('Enter the upload directory path:', currentPath);
    if (newPath !== null && newPath.trim() !== '') {
        document.getElementById('upload_dir').value = newPath.trim();
    }
}

function checkDirectoryStatus() {
    var uploadDir = document.getElementById('upload_dir').value;
    var statusElement = document.getElementById('directory-status');
    
    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
    statusElement.className = 'badge badge-secondary';
    
    // Simple validation - in a real implementation, this would check server-side
    if (!uploadDir || uploadDir.trim() === '') {
        statusElement.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Not Set';
        statusElement.className = 'badge badge-warning';
    } else {
        // Simulate a check - in real implementation, make an AJAX call to verify directory
        setTimeout(function() {
            statusElement.innerHTML = '<i class="fas fa-check"></i> Path Valid';
            statusElement.className = 'badge badge-success';
        }, 1000);
    }
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset system settings to their default values?')) {
        document.getElementById('upload_dir').value = '/uploads';
        checkDirectoryStatus();
    }
}

// Check directory status on page load
$(document).ready(function() {
    checkDirectoryStatus();
    
    // Update current display when input changes
    $('#upload_dir').on('input', function() {
        $('#current-upload-dir').text($(this).val());
    });
});
</script> 