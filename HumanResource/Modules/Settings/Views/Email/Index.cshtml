@using HumanResource.Modules.Shared.ViewModels
@model BaseViewModel

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-envelope mr-2"></i> Email Configuration
            </h3>
        </div>
        <div class="card-body">
            <form class="ajax" action="@Url.Action("SaveEmailSettings", "Email")" method="post" >
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">
                                    <i class="fas fa-server mr-1"></i> SMTP Configuration
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="smtp_server">SMTP Server</label>
                                    <input type="text" class="form-control" id="smtp_server" name="smtp_server" 
                                           value="@(Model?._h?.Settings?.Get("email.smtp_server") ?? "")"
                                           placeholder="smtp.gmail.com">
                                    <small class="form-text text-muted">Enter the SMTP server address</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp_port">SMTP Port</label>
                                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                           value="@(Model?._h?.Settings?.Get("email.smtp_port") ?? "587")"
                                           placeholder="587">
                                    <small class="form-text text-muted">Common ports: 25, 587 (TLS), 465 (SSL)</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp_username">SMTP Username</label>
                                    <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                           value="@(Model?._h?.Settings?.Get("email.smtp_username") ?? "")"
                                           placeholder="<EMAIL>">
                                    <small class="form-text text-muted">Username for SMTP authentication</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="smtp_password">SMTP Password</label>
                                    <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                           value="@(Model?._h?.Settings?.Get("email.smtp_password") ?? "")"
                                           placeholder="••••••••">
                                    <small class="form-text text-muted">Password for SMTP authentication</small>
                                </div>
                                
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="smtp_enable_ssl" name="smtp_enable_ssl" 
                                            @((Model?._h?.Settings?.Get("email.smtp_enable_ssl") ?? "true") == "true" ? "checked" : "")>
                                        <label class="form-check-label" for="smtp_enable_ssl">
                                            Enable SSL/TLS
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Enable secure connection (recommended)</small>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_test_mode" name="is_test_mode" 
                                            @((Model?._h?.Settings?.Get("email.is_test_mode") ?? "false") == "true" ? "checked" : "")>
                                        <label class="form-check-label" for="is_test_mode">
                                            Test Mode
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Enable test mode to send emails to test email address</small>
                                </div>

                                <div class="form-group">
                                    <label for="test_email_address">Test Email Address</label>
                                    <input type="email" class="form-control" id="test_email_address" name="test_email_address" 
                                           value="@(Model?._h?.Settings?.Get("email.test_email_address") ?? "")"
                                           placeholder="<EMAIL>">
                                    <small class="form-text text-muted">Email address to send test emails to</small>
                                </div>

                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">
                                    <i class="fas fa-envelope-open-text mr-1"></i> Email Defaults
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="sender_name">Sender Name</label>
                                    <input type="text" class="form-control" id="sender_name" name="sender_name" 
                                           value="@(Model?._h?.Settings?.Get("email.sender_name") ?? "")"
                                           placeholder="HR System">
                                    <small class="form-text text-muted">Display name for outgoing emails</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="sender_address">Sender Email Address</label>
                                    <input type="email" class="form-control" id="sender_address" name="sender_address" 
                                           value="@(Model?._h?.Settings?.Get("email.sender_address") ?? "")"
                                           placeholder="<EMAIL>">
                                    <small class="form-text text-muted">Default sender email address</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="reply_to">Reply-To Email Address</label>
                                    <input type="email" class="form-control" id="reply_to" name="reply_to" 
                                           value="@(Model?._h?.Settings?.Get("email.reply_to") ?? "")"
                                           placeholder="<EMAIL>">
                                    <small class="form-text text-muted">Email address for replies (optional)</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="footer">Default Email Footer</label>
                                    <textarea class="form-control" id="footer" name="footer" rows="4"
                                              placeholder="Best regards,&#10;HR Department&#10;Company Name">@(Model?._h?.Settings?.Get("email.footer") ?? "")</textarea>
                                    <small class="form-text text-muted">Footer text for all outgoing emails</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="card-title">
                                    <i class="fas fa-tools mr-1"></i> Test & Validation
                                </h4>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    Test your email configuration before saving to ensure it works properly.
                                </div>
                                
                                <div class="form-group">
                                    <div class="input-group">
                                        <input type="email" class="form-control" id="test_email" 
                                               placeholder="<EMAIL>" value="@(Model?._h?.Settings?.Get("email.sender_address") ?? "")">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-outline-success" id="testEmailConnection">
                                                <i class="fas fa-paper-plane mr-1"></i> Send Test Email
                                            </button>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Send a test email to verify configuration</small>
                                </div>
                                
                                <div id="test-result" class="mt-2" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-right mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> Save Email Settings
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetEmailForm()">
                        <i class="fas fa-undo mr-1"></i> Reset Form
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Test email connection
    $('#testEmailConnection').click(function() {
        var server = $('#smtp_server').val();
        var port = parseInt($('#smtp_port').val());
        var username = $('#smtp_username').val();
        var password = $('#smtp_password').val();
        var enableSsl = $('#smtp_enable_ssl').is(':checked');
        var testEmail = $('#test_email').val();
        
        if (!server || !port || !username || !password || !testEmail) {
            showTestResult('Please fill in all required fields before testing.', 'warning');
            return;
        }
        
        var $button = $(this);
        var originalText = $button.html();
        $button.html('<i class="fas fa-spinner fa-spin mr-1"></i> Testing...').prop('disabled', true);
        
        $.ajax({
            url: '@Url.Action("TestEmailConnection", "Email")',
            type: 'POST',
            data: {
                server: server,
                port: port,
                username: username,
                password: password,
                enableSsl: enableSsl
            },
            success: function(response) {
                if (response.success) {
                    showTestResult('Email configuration test successful! SMTP connection is working.', 'success');
                } else {
                    showTestResult('Email test failed: ' + (response.message || 'Unknown error'), 'danger');
                }
            },
            error: function() {
                showTestResult('Error occurred while testing email configuration.', 'danger');
            },
            complete: function() {
                $button.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Auto-fill sender address as test email when changed
    $('#sender_address').on('input', function() {
        var senderEmail = $(this).val();
        if (senderEmail && $('#test_email').val() === '') {
            $('#test_email').val(senderEmail);
        }
    });
});

function showTestResult(message, type) {
    var alertClass = 'alert-' + type;
    var iconClass = type === 'success' ? 'fa-check-circle' : 
                   type === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle';
    
    $('#test-result').html(
        '<div class="alert ' + alertClass + ' alert-dismissible fade show">' +
        '<i class="fas ' + iconClass + ' mr-2"></i>' + message +
        '<button type="button" class="close" data-dismiss="alert">' +
        '<span>&times;</span></button></div>'
    ).show();
}

function resetEmailForm() {
    if (confirm('Are you sure you want to reset the email form? This will clear all current values.')) {
        $('form')[0].reset();
        $('#test-result').hide();
    }
}
</script> 