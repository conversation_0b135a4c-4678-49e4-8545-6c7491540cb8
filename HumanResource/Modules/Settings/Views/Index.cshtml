@using HumanResource.Modules.Shared.ViewModels
@model BaseViewModel



<div class="container-fluid">
    <ul class="nav nav-tabs card-header-tabs ml-1" id="settings-tabs" role="tablist">
    <li class="nav-item">
        <a class="nav-link active" id="general-tab" data-toggle="tab" href="#general" role="tab" aria-controls="general" aria-selected="true">
            <i class="fas fa-cogs mr-1"></i> اعدادات عامة
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" id="email-tab" data-toggle="tab" href="#email" role="tab" aria-controls="email" aria-selected="false">
            <i class="fas fa-envelope mr-1"></i> اعدادات البريد
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" id="templates-tab" data-toggle="tab" href="#templates" role="tab" aria-controls="templates" aria-selected="false">
            <i class="fas fa-file-alt mr-1"></i> القوالب البريدية
        </a>
    </li>
</ul>
    <!-- Tabs Control -->
    <div class="card">
        <div class="card-header">
            
        </div>
        <div class="card-body">
            <div class="tab-content" id="settings-tab-content">
                <!-- General Settings Tab -->
                <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#settingModal" onclick="newSetting()">
                                    <i class="fas fa-plus"></i> جديد
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table id="settings-table" class="table table-bordered table-striped datatable">
                            <thead>
                                <tr>
                                    <th>Key</th>
                                    <th>Value</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var setting in ViewBag.Settings)
                                {
                                    if (!setting.Key.StartsWith("email."))
                                    {
                                        <tr>
                                            <td>@setting.Key</td>
                                            <td>@(setting.Value.Length > 50 ? setting.Value.Substring(0, 50) + "..." : setting.Value)</td>
                                            <td>
                                                <button class="btn btn-sm btn-info" onclick="editSetting('@setting.Key')">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="deleteSetting('@setting.Key')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Email Settings Tab -->
                <div class="tab-pane fade" id="email" role="tabpanel" aria-labelledby="email-tab">
                    <form  class="ajax" action="@Url.Action("saveEmailSettings", "Settings")" method="post">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">SMTP Configuration</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="smtp_server">SMTP Server</label>
                                            <input type="text" class="form-control" id="smtp_server" name="smtp_server" 
                                                   value="@Model._h.Settings.Get("email.smtp_server")">
                                        </div>
                                        <div class="form-group">
                                            <label for="smtp_port">SMTP Port</label>
                                            <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                                   value="@Model._h.Settings.Get("email.smtp_port")">
                                        </div>
                                        <div class="form-group">
                                            <label for="smtp_username">SMTP Username</label>
                                            <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                                    value="@Model._h.Settings.Get("email.smtp_username")">
                                        </div>
                                        <div class="form-group">
                                            <label for="smtp_password">SMTP Password</label>
                                            <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                                   value="@Model._h.Settings.Get("email.smtp_password")">
                                        </div>
                                        <div class="form-group">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="smtp_enable_ssl" name="smtp_enable_ssl" 
                                                    @(Model._h.Settings.Get("email.smtp_enable_ssl") == "true" ? "checked" : "")>
                                                <label class="form-check-label" for="smtp_enable_ssl">Enable SSL</label>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">Email Defaults</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label for="email_sender_name">Sender Name</label>
                                            <input type="text" class="form-control" id="email_sender_name" name="sender_name" 
                                                   value="@Model._h.Settings.Get("email.sender_name")">
                                        </div>
                                        <div class="form-group">
                                            <label for="email_sender_address">Sender Email Address</label>
                                            <input type="email" class="form-control" id="email_sender_address" name="sender_address" 
                                                   value="@Model._h.Settings.Get("email.sender_address")">
                                        </div>
                                        <div class="form-group">
                                            <label for="email_reply_to">Reply-To Email Address</label>
                                            <input type="email" class="form-control" id="email_reply_to" name="reply_to" 
                                                   value="@Model._h.Settings.Get("email.reply_to")">
                                        </div>
                                        <div class="form-group">
                                            <label for="email_footer">Default Email Footer</label>
                                            <textarea class="form-control" id="email_footer" name="footer" rows="3">@Model._h.Settings.Get("email.footer")</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-right mt-3">
                            <button type="submit" class="btn btn-primary">Save Email Settings</button>
                            <button type="button" class="btn btn-success" id="testEmailConnection">Test Connection</button>
                        </div>
                    </form>
                </div>
                
                <!-- Email Templates Tab -->
                <div class="tab-pane fade" id="templates" role="tabpanel" aria-labelledby="templates-tab">
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-primary" onclick="newTemplate()">
                                    <i class="fas fa-plus"></i> New Template
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table id="templates-table" class="table table-bordered table-striped datatable">
                            <thead>
                                <tr>
                                    <th>Template Name</th>
                                    <th>Content Preview</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var setting in ViewBag.Settings)
                                {
                                    if (setting.Key.StartsWith("email.template."))
                                    {
                                        var templateName = setting.Key.Substring("email.template.".Length);
                                        <tr>
                                            <td>@templateName</td>
                                            <td>@(setting.Value.Length > 50 ? setting.Value.Substring(0, 50) + "..." : setting.Value)</td>
                                            <td>
                                                <button class="btn btn-sm btn-info" onclick="editTemplate('@templateName')">
                                                    <i class="fas fa-edit"></i> تعديل
                                                </button>
                                                <button class="btn btn-sm btn-success" onclick="previewTemplate('@templateName')">
                                                    <i class="fas fa-eye"></i> عرض
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="deleteTemplate('@templateName')">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Setting Modal -->
<div class="modal fade" id="settingModal" tabindex="-1" role="dialog" aria-labelledby="settingModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="settingModalLabel">Setting</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="settingForm">
                    <div class="form-group">
                        <label for="settingKey">Key</label>
                        <input type="text" class="form-control" id="settingKey" placeholder="Enter setting key">
                    </div>
                    <div class="form-group">
                        <label for="settingValue">Value</label>
                        <textarea class="form-control" id="settingValue" rows="3" placeholder="Enter setting value"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveSetting">Save Setting</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this setting? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Template Modal -->
<div class="modal fade" id="templateModal" tabindex="-1" role="dialog" aria-labelledby="templateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templateModalLabel">Email Template</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="templateForm">
                    <div class="form-group">
                        <label for="templateName">Template Name</label>
                        <input type="text" class="form-control" id="templateName" placeholder="Enter template name">
                    </div>
                    <div class="form-group">
                        <label for="templateContent">Template Content (HTML)</label>
                        <textarea class="form-control" id="templateContent" rows="10" placeholder="Enter template content with variables like {name}, {email}, etc."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Available Variables</label>
                        <p class="text-muted">
                            Use curly braces to define variables: <code>{variable_name}</code><br>
                            Common variables: <code>{title}</code>, <code>{text}</code>, <code>{url}</code>, <code>{name}</code>, <code>{email}</code>
                        </p>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveTemplate">Save Template</button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Template Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="previewVariables">Test Variables (JSON)</label>
                    <textarea class="form-control" id="previewVariables" rows="4">{"name":"John Doe","email":"<EMAIL>","title":"Test Title","text":"This is a test message","url":"https://example.com"}</textarea>
                </div>
                <button type="button" class="btn btn-info mb-3" id="renderPreview">Render with Variables</button>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Preview Result</h5>
                    </div>
                    <div class="card-body" id="previewResult">
                        <!-- Preview content will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize DataTables
          
            
            // Handle tab selection from URL hash
            var hash = window.location.hash;
            if (hash) {
                $(`#settings-tabs a[href="${hash}"]`).tab('show');
            }
            
            // Update hash on tab change
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                window.location.hash = e.target.hash;
            });
            
            // Save general setting
            $('#saveSetting').on('click', function() {
                var key = $('#settingKey').val();
                var value = $('#settingValue').val();
                
                if (!key) {
                    alert('Setting key is required');
                    return;
                }
                
                $.ajax({
                    url: '@Url.Action("Set", "Settings")',
                    type: 'POST',
                    data: { 
                        key: key,
                        value: value
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Setting saved successfully');
                            $('#settingModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            alert('Failed to save setting');
                        }
                    },
                    error: function() {
                        alert('An error occurred');
                    }
                });
            });
      
            // Test email connection
            $('#testEmailConnection').on('click', function() {
                var server = $('#smtp_server').val();
                var port = $('#smtp_port').val();
                var username = $('#smtp_username').val();
                var password = $('#smtp_password').val();
                var enableSsl = $('#smtp_enable_ssl').prop('checked');
                
                if (!server || !port) {
                    alert('SMTP server and port are required');
                    return;
                }
                
                $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Testing...');
                
                $.ajax({
                    url: '@Url.Action("TestEmailConnection", "Settings")',
                    type: 'POST',
                    data: { 
                        server: server,
                        port: port,
                        username: username,
                        password: password,
                        enableSsl: enableSsl
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Connection successful!');
                        } else {
                            alert('Connection failed: ' + response.message);
                        }
                        $('#testEmailConnection').prop('disabled', false).html('Test Connection');
                    },
                    error: function() {
                        alert('An error occurred during the test');
                        $('#testEmailConnection').prop('disabled', false).html('Test Connection');
                    }
                });
            });
            
            // Save template
            $('#saveTemplate').on('click', function() {
                var name = $('#templateName').val();
                var content = $('#templateContent').val();
                
                if (!name) {
                    alert('Template name is required');
                    return;
                }
                
                $.ajax({
                    url: '@Url.Action("SaveTemplate", "Settings")',
                    type: 'POST',
                    data: { 
                        name: name,
                        content: content
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Template saved successfully');
                            $('#templateModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            alert('Failed to save template');
                        }
                    },
                    error: function() {
                        alert('An error occurred');
                    }
                });
            });
            
            // Render preview
            $('#renderPreview').on('click', function() {
                var templateName = $('#previewModal').data('template-name');
                var variables;
                
                try {
                    variables = JSON.parse($('#previewVariables').val());
                } catch (e) {
                    alert('Invalid JSON format for variables');
                    return;
                }
                
                $.ajax({
                    url: '@Url.Action("PreviewTemplate", "Settings")',
                    type: 'POST',
                    data: { 
                        name: templateName,
                        variables: variables
                    },
                    success: function(response) {
                        $('#previewResult').html(response.processed);
                    },
                    error: function() {
                        alert('An error occurred during preview');
                    }
                });
            });
            
            // Confirm delete general setting
            $('#confirmDelete').on('click', function() {
                var settingKey = $('#deleteModal').data('setting-key');
                
                $.ajax({
                    url: '@Url.Action("Remove", "Settings")',
                    type: 'POST',
                    data: { 
                        key: settingKey
                    },
                    success: function(response) {
                        if (response.success) {
                            alert('Setting deleted successfully');
                            $('#deleteModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            alert('Failed to delete setting');
                        }
                    },
                    error: function() {
                        alert('An error occurred');
                    }
                });
            });
        });
        
        // Open new setting modal
        function newSetting() {
            $('#settingModalLabel').text('New Setting');
            $('#settingKey').val('').prop('readonly', false);
            $('#settingValue').val('');
            $('#settingModal').modal('show');
        }
        
        // Open edit setting modal
        function editSetting(key) {
            $('#settingModalLabel').text('Edit Setting');
            
            $.ajax({
                url: '@Url.Action("Get", "Settings")',
                type: 'GET',
                data: { key: key },
                success: function(response) {
                    $('#settingKey').val(response.key).prop('readonly', true);
                    $('#settingValue').val(response.value);
                    $('#settingModal').modal('show');
                },
                error: function() {
                    alert('Failed to load setting');
                }
            });
        }
        
        // Open delete confirmation modal
        function deleteSetting(key) {
            $('#deleteModalLabel').text('Delete: ' + key);
            $('#deleteModal').data('setting-key', key);
            $('#deleteModal').modal('show');
        }
        
        // Open new template modal
        function newTemplate() {
            $('#templateModalLabel').text('New Email Template');
            $('#templateName').val('').prop('readonly', false);
            $('#templateContent').val('');
            $('#templateModal').modal('show');
        }
        
        // Open edit template modal
        function editTemplate(name) {
            $('#templateModalLabel').text('Edit Email Template');
            
            $.ajax({
                url: '@Url.Action("GetTemplate", "Settings")',
                type: 'GET',
                data: { name: name },
                success: function(response) {
                    $('#templateName').val(response.name).prop('readonly', true);
                    $('#templateContent').val(response.content);
                    $('#templateModal').modal('show');
                },
                error: function() {
                    alert('Failed to load template');
                }
            });
        }
        
        // Open preview modal
        function previewTemplate(name) {
            $('#previewModalLabel').text('Preview: ' + name);
            $('#previewModal').data('template-name', name);
            $('#previewResult').html('');
            $('#previewModal').modal('show');
        }
        
        // Open delete template confirmation modal
        function deleteTemplate(name) {
            $('#deleteModalLabel').text('Delete Template: ' + name);
            $('#deleteModal').data('setting-key', 'email.template.' + name);
            $('#deleteModal').modal('show');
        }
    </script>
} 