@using HumanResource.Modules.Shared.ViewModels
@model BaseViewModel



<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Email Templates</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#templateModal" onclick="newTemplate()">
                            <i class="fas fa-plus"></i> New Template
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table id="templates-table" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Template Name</th>
                                <th>Content Preview</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var template in ViewBag.Templates)
                            {
                                <tr>
                                    <td>@template.Key</td>
                                    <td>@(template.Value.Length > 50 ? template.Value.Substring(0, 50) + "..." : template.Value)</td>
                                    <td>
                                        <button class="btn btn-sm btn-info" onclick="editTemplate('@template.Key')">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <button class="btn btn-sm btn-success" onclick="previewTemplate('@template.Key')">
                                            <i class="fas fa-eye"></i> Preview
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteTemplate('@template.Key')">
                                            <i class="fas fa-trash"></i> Delete
                                        </button>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Modal -->
<div class="modal fade" id="templateModal" tabindex="-1" role="dialog" aria-labelledby="templateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="templateModalLabel">Email Template</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="templateForm">
                    <div class="form-group">
                        <label for="templateName">Template Name</label>
                        <input type="text" class="form-control" id="templateName" placeholder="Enter template name">
                    </div>
                    <div class="form-group">
                        <label for="templateContent">Template Content (HTML)</label>
                        <textarea class="form-control" id="templateContent" rows="10" placeholder="Enter template content with variables like {name}, {email}, etc."></textarea>
                    </div>
                    <div class="form-group">
                        <label>Available Variables</label>
                        <p class="text-muted">
                            Use curly braces to define variables: <code>{variable_name}</code><br>
                            Common variables: <code>{title}</code>, <code>{text}</code>, <code>{url}</code>, <code>{name}</code>, <code>{email}</code>
                        </p>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveTemplate">Save Template</button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Template Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="previewVariables">Test Variables (JSON)</label>
                    <textarea class="form-control" id="previewVariables" rows="4">{"name":"John Doe","email":"<EMAIL>","title":"Test Title","text":"This is a test message","url":"https://example.com"}</textarea>
                </div>
                <button type="button" class="btn btn-info mb-3" id="renderPreview">Render with Variables</button>
                
                <div class="card">
                    <div class="card-header">
                        <h5>Preview Result</h5>
                    </div>
                    <div class="card-body" id="previewResult">
                        <!-- Preview content will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this template? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Initialize DataTable
            $('#templates-table').DataTable({
                "paging": true,
                "lengthChange": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true
            });
            
            // Save template
            $('#saveTemplate').on('click', function() {
                var name = $('#templateName').val();
                var content = $('#templateContent').val();
                
                if (!name) {
                    toastr.error('Template name is required');
                    return;
                }
                
                $.ajax({
                    url: '@Url.Action("SaveTemplate", "Settings")',
                    type: 'POST',
                    data: { 
                        name: name,
                        content: content
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success('Template saved successfully');
                            $('#templateModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            toastr.error('Failed to save template');
                        }
                    },
                    error: function() {
                        toastr.error('An error occurred');
                    }
                });
            });
            
            // Render preview
            $('#renderPreview').on('click', function() {
                var templateName = $('#previewModal').data('template-name');
                var variables;
                
                try {
                    variables = JSON.parse($('#previewVariables').val());
                } catch (e) {
                    toastr.error('Invalid JSON format for variables');
                    return;
                }
                
                $.ajax({
                    url: '@Url.Action("PreviewTemplate", "Settings")',
                    type: 'POST',
                    data: { 
                        name: templateName,
                        variables: variables
                    },
                    success: function(response) {
                        $('#previewResult').html(response.processed);
                    },
                    error: function() {
                        toastr.error('An error occurred during preview');
                    }
                });
            });
            
            // Confirm delete
            $('#confirmDelete').on('click', function() {
                var templateName = $('#deleteModal').data('template-name');
                
                $.ajax({
                    url: '@Url.Action("DeleteTemplate", "Settings")',
                    type: 'POST',
                    data: { 
                        name: templateName
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success('Template deleted successfully');
                            $('#deleteModal').modal('hide');
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            toastr.error('Failed to delete template');
                        }
                    },
                    error: function() {
                        toastr.error('An error occurred');
                    }
                });
            });
        });
        
        // Open new template modal
        function newTemplate() {
            $('#templateModalLabel').text('New Email Template');
            $('#templateName').val('').prop('readonly', false);
            $('#templateContent').val('');
            $('#templateModal').modal('show');
        }
        
        // Open edit template modal
        function editTemplate(name) {
            $('#templateModalLabel').text('Edit Email Template');
            
            $.ajax({
                url: '@Url.Action("GetTemplate", "Settings")',
                type: 'GET',
                data: { name: name },
                success: function(response) {
                    $('#templateName').val(response.name).prop('readonly', true);
                    $('#templateContent').val(response.content);
                    $('#templateModal').modal('show');
                },
                error: function() {
                    toastr.error('Failed to load template');
                }
            });
        }
        
        // Open preview modal
        function previewTemplate(name) {
            $('#previewModalLabel').text('Preview: ' + name);
            $('#previewModal').data('template-name', name);
            $('#previewResult').html('');
            $('#previewModal').modal('show');
        }
        
        // Open delete confirmation modal
        function deleteTemplate(name) {
            $('#deleteModalLabel').text('Delete: ' + name);
            $('#deleteModal').data('template-name', name);
            $('#deleteModal').modal('show');
        }
    </script>
} 