@using HumanResource.Modules.Shared.ViewModels
@model BaseViewModel

@functions {
    string GetTemplateDisplayName(string templateName)
    {
        return templateName switch
        {
            "attendence.warning1" => "Attendance Warning 1",
            "attendence.warning2" => "Attendance Warning 2",
            _ => templateName
        };
    }
}

<h3 class="">
     Email Templates
</h3>

<div class="row">
    <div class="col-md-6 mb-4">
        <form action="@Url.Action("SaveTemplate", "EmailTemplates")" method="post" class="ajax">
            <input type="hidden" name="name" value="attendence.warning1">
            <div class="card template-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope mr-1"></i>
                        Attendance Warning 1
                    </h5>
                    <span class="badge badge-primary">attendence.warning1</span>
                </div>
                <div class="card-body">
                    <div class="template-preview">
                        <textarea name="content" class="border p-2 mt-2 bg-light form-control" rows="10"
                            style="max-height: 300px; overflow-y: auto;">@Model._h.Settings.Get("email.template.attendence.warning1", "")</textarea>
                        <p>
                            <strong>Available Variables:</strong>
                            <code>
                                            {EmpNameA}
                                            {EmpNo}
                                            {DgDespA}
                                            {DesgDespA}
                                            {DeptDespA}
                                            {DesgType}
                                            {EmailId}
                                            {NatId}
                                            {GradeRank}
                                            {DesgCode}
                                            {Date}
                                        </code>
                        </p>
                    </div>

                    <div class="template-actions mt-3">

                        <button class="btn btn-primary btn-sm" type="submit">
                            <i class="fa fa-save mr-1"></i> Save
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <div class="col-md-6 mb-4">
        <form action="@Url.Action("SaveTemplate", "EmailTemplates")" method="post" class="ajax">
            <input type="hidden" name="name" value="attendence.warning2">
            <div class="card template-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope mr-1"></i>
                        Attendance Warning 2
                    </h5>
                    <span class="badge badge-primary">attendence.warning2</span>
                </div>
                <div class="card-body">
                    <div class="template-preview">
                        <textarea name="content" class="border p-2 mt-2 bg-light form-control" rows="10"
                            style="max-height: 300px; overflow-y: auto;">@Model._h.Settings.Get("email.template.attendence.warning2", "")</textarea>
                        <p>
                            <strong>Available Variables:</strong>
                            <code>
                                            {EmpNameA}
                                            {EmpNo}
                                            {DgDespA}
                                            {DesgDespA}
                                            {DeptDespA}
                                            {DesgType}
                                            {EmailId}
                                            {NatId}
                                            {GradeRank}
                                            {DesgCode}
                                            {Date}
                                        </code>
                        </p>
                    </div>

                    <div class="template-actions mt-3">

                        <button class="btn btn-primary btn-sm" type="submit">
                            <i class="fa fa-save mr-1"></i> Save
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@if (ViewBag.Templates?.Count == 0)
{
    <div class="text-center py-5">
        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No Templates Configured</h4>
        <p class="text-muted">Email templates will appear here when configured by the system administrator.</p>
    </div>
}
