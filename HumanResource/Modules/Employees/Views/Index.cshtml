﻿@using HumanResource.Modules.Employees.ViewModels
@using HumanResource.Modules.Shared.Models.Entities.HRMS
@model EmployeesViewModel



<div class="d-flex justify-content-between align-items-center mb-3">
    <h3>
        الموظفون
    </h3>
    <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
        <i class="fa fa-filter"></i> عرض الفلتر
    </button>
</div>

<div class="collapse mb-3" id="filterCollapse">
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0">خيارات التصفية</h5>
        </div>
        <div class="card-body">
            <form method="get" action="/Employees/Index" id="filterForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label fw-bold">الدائرة</label>
                            <select name="dgCode" class="form-control" id="dgCodeSelect" onchange="updateDepartments()">
                                <option value="">الكل</option>
                                @foreach (var dg in Model.DgCodes)
                                {
                                    if (Model.SelectedDgCode == dg.DgCode)
                                    {
                                        <option value="@dg.DgCode" selected>@dg.DgDespA</option>
                                    }
                                    else
                                    {
                                        <option value="@dg.DgCode">@dg.DgDespA</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label fw-bold">القسم</label>
                            <select name="deptCode" class="form-control" id="deptCodeSelect">
                                <option value="">الكل</option>
                                @foreach (var dept in Model.DeptCodes?.Where(d => !Model.SelectedDgCode.HasValue || d.DgCode == Model.SelectedDgCode) ?? Enumerable.Empty<TDeptCode>())
                                {
                                    if (Model.SelectedDeptCode == dept.DeptCode)
                                    {
                                        <option value="@dept.DeptCode" selected>@dept.DeptDespA</option>
                                    }
                                    else
                                    {
                                        <option value="@dept.DeptCode">@dept.DeptDespA</option>
                                    }
                                }
                            </select>
                        </div>
                    </div>
                </div>
                <div class="d-flex justify-content-end mt-3">
                    <button type="submit" class="btn btn-primary px-4">تطبيق</button>
                    <a href="/Employees/Index" class="btn btn-secondary px-4 mr-2">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="card">
    <table class="table table-striped datatable">
        <thead class="bg-primary">
            <tr>
                <th>
                </th>
                <th>
                    الرقم
                </th>
                <th>
                    الاسم
                </th>
                
                <th>الدائرة</th>
                <th>القسم</th>
                <th>الدرجة المالية</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var emp in Model.VempDtls)
            {
                <tr>
                    <td>
                        <a href="~/Employees/Update/@emp.EmpNo">
                            @if (@emp.ProfileImage != null)
                            {
                                <img src="@Model._h.GetFile(@emp.ProfileImage)" style="max-height: 50px" />
                            }
                            else
                            {
                                <img src="/assets/images/user60x60.png" style="max-height: 50px" />
                            }
                        </a>
                    </td>
                    <td>
                        <a href="~/Employees/Update/@emp.EmpNo">@emp.EmpNo</a>
                    </td>

                    <td>@emp.EmpNameA</td>
                    <td>@emp.DgDespA</td>
                    <td>@emp.DeptDespA</td>

                    <td>@emp.GradRank</td>
                </tr>
            }
        </tbody>
    </table>
</div>

<script>
    function updateDepartments() {
        var dgCode = document.getElementById('dgCodeSelect').value;
        var deptSelect = document.getElementById('deptCodeSelect');
        
        // Clear existing options
        deptSelect.innerHTML = '<option value="">الكل</option>';
        
        // If no DG selected, show all departments
        if (!dgCode) {
            @foreach (var dept in Model.DeptCodes ?? Enumerable.Empty<TDeptCode>())
            {
                <text>
                deptSelect.innerHTML += '<option value="@dept.DeptCode">@dept.DeptDespA</option>';
                </text>
            }
        } else {
            // Filter departments by selected DG
            @foreach (var dept in Model.DeptCodes ?? Enumerable.Empty<TDeptCode>())
            {
                <text>
                if ('@dept.DgCode' == dgCode) {
                    deptSelect.innerHTML += '<option value="@dept.DeptCode">@dept.DeptDespA</option>';
                }
                </text>
            }
        }
    }
</script>
