﻿@using HumanResource.Modules.Employees.ViewModels
@model EmployeesViewModel

@{
    Layout = Model.Page.Layout;
}

@Html.Partial("_Tabs")

<div class="card" id="app">
    <div class="card-body">
        <div class="d-flex justify-content-between">
            <div class="d-flex">
                <div>
                    <input type="month" name="month" class="form-control" id="month-input" />
                </div>
                <button type="button" class="btn btn-link" id="search-btn" @@click="get_days()"><i class="fa fa-search"></i></button>
            </div>
            <div class="d-flex align-items-center">
                <span class="badge badge-warning mr-2 p-2">استئذان</span>
                <span class="badge badge-danger mr-2 p-2">تأخير</span>
                <span class="badge badge-info mr-2 p-2">مهمة رسمية</span>
            </div>
        </div>
    </div>

    <table class="table table-striped table-hover" v-if="days_list.length>0">
        <thead class="bg-primary">
            <tr>
                <th>اليوم</th>
                <th>الدخول</th>
                <th>الخروج</th>
                <th>الساعات</th>
                <th>ساعات العمل</th>
                <th>اضافي</th>
                <th>الاستئذان / التأخير</th>
            </tr>
        </thead>
        <tbody>
            <template v-for="(day, index) in days_list">
                <tr :class="{'table-warning': hasExcuseType(day, 1), 'table-danger': hasExcuseType(day, 2), 'table-info': hasExcuseType(day, 3)}">
                    <td>{{ _ddd(day.day) }}</td>
                    <td>{{ _format_time(day.firstEntry) }}</td>
                    <td>{{ _format_time(day.lastEntry) }}</td>
                    <td>{{ _format_hours(day.workDuration) }}</td>
                    <td>{{ _format_hours(day.hours) }}</td>
                    <td>{{ _format_hours(day.extra) }}</td>
                    <td>
                        <template v-if="day.excuses && day.excuses.length > 0">
                            <button class="btn btn-sm btn-outline-secondary" @@click="toggleExcuseDetails(index)">
                                {{ day.excuses.length }} طلب
                                <i :class="{'fa-chevron-down': !day.showDetails, 'fa-chevron-up': day.showDetails}" class="fa"></i>
                            </button>
                            
                            <!-- Badge indicators for excuse types -->
                            <template v-for="type in getExcuseTypes(day)">
                                <span v-if="type === 1" class="badge badge-warning mr-1">استئذان</span>
                                <span v-if="type === 2" class="badge badge-danger mr-1">تأخير</span>
                                <span v-if="type === 3" class="badge badge-info mr-1">مهمة رسمية</span>
                            </template>
                        </template>
                        <span v-else>--</span>
                    </td>
                </tr>
                <!-- Excuse details row -->
                <tr v-if="day.excuses && day.excuses.length > 0 && day.showDetails" class="excuse-details-row">
                    <td colspan="7" class="p-0">
                        <div class="p-3 bg-light">
                            <h6 class="mb-3">تفاصيل الاستئذان / التأخير</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead>
                                        <tr>
                                            <th>النوع</th>
                                            <th>من</th>
                                            <th>إلى</th>
                                            <th>المدة</th>
                                            <th>الحالة</th>
                                            <th>ملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="excuse in day.excuses" :class="getExcuseRowClass(excuse)">
                                            <td>
                                                <span v-if="excuse.reqType === 1" class="badge badge-warning">استئذان</span>
                                                <span v-else-if="excuse.reqType === 2" class="badge badge-danger">تأخير</span>
                                                <span v-else-if="excuse.reqType === 3" class="badge badge-info">مهمة رسمية</span>
                                                <span v-else>{{ getExcuseType(excuse.reqType) }}</span>
                                            </td>
                                            <td>{{ _format_time(excuse.timeFrom) }}</td>
                                            <td>{{ _format_time(excuse.timeTo) }}</td>
                                            <td>{{ calculateDuration(excuse.timeFrom, excuse.timeTo) }}</td>
                                            <td>
                                                <span v-if="excuse.status === 0" class="badge badge-warning">معلق</span>
                                                <span v-else-if="excuse.status === 1" class="badge badge-success">موافق عليه</span>
                                                <span v-else-if="excuse.status === 2" class="badge badge-primary">تحت الموافقة</span>
                                                <span v-else-if="excuse.status === 3" class="badge badge-danger">مرفوض</span>
                                                <span v-else>{{ getExcuseStatus(excuse.status) }}</span>
                                            </td>
                                            <td>{{ excuse.reason || '--' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>
    
    <div v-if="days_list.length === 0" class="text-center p-5">
        <p class="text-muted">لا توجد بيانات متوفرة، يرجى اختيار شهر للبحث</p>
    </div>
</div>

<style>
    .has-excuses {
        background-color: rgba(255, 243, 205, 0.3) !important;
    }
    
    .excuse-type-1 {
        background-color: rgba(255, 193, 7, 0.1) !important;
    }
    
    .excuse-type-2 {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }
    
    .excuse-type-3 {
        background-color: rgba(23, 162, 184, 0.1) !important;
    }
    
    .excuse-details-row {
        background-color: #f8f9fa !important;
    }
</style>

<script>
    let app = new Vue({
        el: "#app",
        data: {
            days_list: [],
        },
        mounted() {
            // Set default month to current month
            const today = new Date();
            const month = (today.getMonth() + 1).toString().padStart(2, '0');
            const year = today.getFullYear();
            $("#month-input").val(`${year}-${month}`);
            
            // Load data for current month
            this.get_days();
        },
        methods: {
            get_days() {
                let job = this;
                var inputDate = new Date($("#month-input").val());
                var year = inputDate.getFullYear();
                var month = inputDate.getMonth() + 1;

                $.get("/Employees/GetAttendence?empNo="+@Model.Employee.EmpNo+"&year=" + year + "&month=" + month, function (data) {
                    // Add showDetails property to each day
                    job.days_list = data.data.map(day => {
                        day.showDetails = false;
                        return day;
                    });
                }, 'json');
            },
            toggleExcuseDetails(index) {
                // Toggle the showDetails property for the clicked day
                Vue.set(this.days_list[index], 'showDetails', !this.days_list[index].showDetails);
            },
            hasExcuseType(day, type) {
                return day.excuses && day.excuses.some(e => e.reqType === type);
            },
            getExcuseTypes(day) {
                if (!day.excuses || day.excuses.length === 0) return [];
                return [...new Set(day.excuses.map(e => e.reqType))];
            },
            getExcuseType(reqType) {
                switch(reqType) {
                    case 1: return "استئذان";
                    case 2: return "تأخير";
                    case 3: return "مهمة رسمية";
                    default: return "غير معروف";
                }
            },
            getExcuseStatus(status) {
                switch(status) {
                    case 0: return "معلق";
                    case 1: return "موافق عليه";
                    case 2: return "تحت الموافقة";
                    case 3: return "مرفوض";
                    default: return "غير معروف";
                }
            },
            getExcuseRowClass(excuse) {
                return `table-${excuse.reqType === 1 ? 'warning' : excuse.reqType === 2 ? 'danger' : 'info'}`;
            },
            calculateDuration(from, to) {
                if (!from || !to) return "--";
                
                const fromTime = new Date(from);
                const toTime = new Date(to);
                
                if (fromTime.getHours() === 0 || toTime.getHours() === 0) return "--";
                
                const diffMs = toTime - fromTime;
                const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMins = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                
                return `${diffHrs}:${diffMins.toString().padStart(2, '0')}`;
            },
            _d(date) {
                return window._d(date);
            },
            _ddd(date) {
                return window._ddd(date);
            },
            _format_time(_date) {
                if (!_date) return "--";
                
                const date = new Date(_date);
                if (date.getHours() === 0 && date.getMinutes() === 0) return "--";

                let h = date.getHours();
                let i = date.getMinutes();
                let a = h >= 12 ? 'PM':'AM';

                h = h % 12;
                h = h ? h : 12;
                i = i < 10 ? '0' + i : i;

                return `${h}:${i} ${a}`;
            },
            _format_hours(hours) {
                if (!hours) return "0:00";
                
                const h = Math.floor(hours);
                const frac = hours - h;
                const i = Math.round(60 * frac);
                
                return `${h}:${i.toString().padStart(2, '0')}`;
            }
        }
    });
</script>