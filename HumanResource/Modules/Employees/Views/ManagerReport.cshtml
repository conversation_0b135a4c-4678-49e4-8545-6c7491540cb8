@using HumanResource.Modules.Employees.ViewModels
@model EmployeesViewModel

<div class="container-fluid">
    <!-- Report Header -->
    <div class="d-flex justify-content-between mb-4">
        <h3>تقرير الحضور والانصراف لفريق العمل</h3>
        
        <a href="javascript:window.print()" class="btn btn-outline-primary">
            <i class="fa fa-print"></i> طباعة التقرير
        </a>
    </div>
    
    <!-- Manager Information -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="m-0">المدير: @ViewBag.Manager.EmpNameA</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <strong>المدير:</strong> @ViewBag.Manager.EmpNameA
                </div>
                <div class="col-md-4">
                    <strong>الرقم الوظيفي:</strong> @ViewBag.Manager.EmpNo
                </div>
                <div class="col-md-4">
                    <strong>المسمى الوظيفي:</strong> @ViewBag.ManagerTitle
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-12">
                    <form method="get" action="/Employees/ManagerReport/@ViewBag.Manager.EmpNo" id="filterForm" class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>من تاريخ</label>
                                <input type="date" name="from" class="form-control" value="@ViewBag.FromDate.ToString("yyyy-MM-dd")" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>إلى تاريخ</label>
                                <input type="date" name="to" class="form-control" value="@ViewBag.ToDate.ToString("yyyy-MM-dd")" />
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(ViewBag.Message))
    {
        <div class="alert alert-info">
            @ViewBag.Message
        </div>
    }
    else if (!Model.AttendanceReport.Any())
    {
        <div class="alert alert-warning">
            لا توجد بيانات لعرضها
        </div>
    }
    else
    {
        <!-- Summary Stats -->
        <div class="row mb-4">
            <div class="col-md-3 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    عدد الموظفين في الفريق</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @Model.AttendanceReport.Sum(d => d.TotalEmployees)
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-users fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    معدل الالتزام</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @{
                                        double expectedHours = Model.AttendanceReport.Sum(d => d.ExpectedWorkHours);
                                        double actualHours = Model.AttendanceReport.Sum(d => d.ActualWorkHours);
                                        double complianceRate = expectedHours > 0 ? (actualHours / expectedHours) * 100 : 0;
                                    }
                                    @complianceRate.ToString("F1")%
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-percentage fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    الموظفين في إجازة اليوم</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @Model.AttendanceReport.Sum(d => d.EmployeeStats.Count(e => e.IsCurrentlyOnLeave))
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-3 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    حالات التأخير</div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @Model.AttendanceReport.Sum(d => d.LateCount)
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Departments and Employees -->
        @foreach (var dept in Model.AttendanceReport)
        {
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">@dept.DepartmentName - @dept.TotalEmployees موظف</h6>
                    <span class="badge @(dept.ComplianceRate >= 90 ? "badge-success" : 
                                     dept.ComplianceRate >= 75 ? "badge-warning" : "badge-danger") p-2">
                        معدل الالتزام: @dept.ComplianceRate.ToString("F1")%
                    </span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="bg-light">
                                <tr>
                                    <th>الموظف</th>
                                    <th>الحالة</th>
                                    <th>أيام العمل</th>
                                    <th>ساعات العمل</th>
                                    <th>الإجازات</th>
                                    <th>التأخير</th>
                                    <th>الاستئذانات</th>
                                    <th>المهمات الرسمية</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var emp in dept.EmployeeStats)
                                {
                                    <tr class="@(emp.IsCurrentlyOnLeave ? "bg-light-yellow" : "")">
                                        <td>
                                            <a href="/Employees/Attendence/@emp.EmpNo">@emp.EmpName</a><br/>
                                            <small class="text-muted">@emp.EmpNo</small>
                                        </td>
                                        <td>
                                            @if (emp.IsCurrentlyOnLeave)
                                            {
                                                <span class="badge badge-warning">في إجازة</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-success">متواجد</span>
                                            }
                                        </td>
                                        <td>@emp.WorkDays / @dept.TotalWorkdays</td>
                                        <td>@emp.WorkHours.ToString("F1")</td>
                                        <td>
                                            @if (emp.LeaveDaysCount > 0)
                                            {
                                                <span class="badge badge-info">@emp.LeaveDaysCount يوم</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (emp.LateCount > 0)
                                            {
                                                <span>@emp.LateCount مرة</span><br/>
                                                <small>@emp.LateHours.ToString("F1") ساعة</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (emp.ExcuseCount > 0)
                                            {
                                                <span>@emp.ExcuseCount مرة</span><br/>
                                                <small>@emp.ExcuseHours.ToString("F1") ساعة</small>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (emp.OfficialMissionCount > 0)
                                            {
                                                <span class="badge badge-primary">@emp.OfficialMissionCount</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
    }
</div>

<style media="print">
    @@media print {
        .card {
            break-inside: avoid;
        }
        
        .no-print {
            display: none;
        }
        
        .card-body {
            padding: 10px;
        }
        
        h3 {
            font-size: 18px;
        }
        
        .table td, .table th {
            padding: 0.5rem;
        }
    }
    
    .bg-light-yellow {
        background-color: rgba(255, 243, 205, 0.5);
    }
</style> 