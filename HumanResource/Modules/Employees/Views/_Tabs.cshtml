﻿<div class="d-flex my-2">

    <div>
        <a class="btn btn-sm mr-1  btn-@(ViewContext.RouteData.Values["action"].ToString()=="Update" ? "primary" : "")" href="~/Employees/Update/@Model.Employee.EmpNo">الملف الشخصي</a>

        <a class="btn btn-sm mr-1   btn-@(ViewContext.RouteData.Values["action"].ToString()=="Shift" ? "primary" : "")" href="~/Employees/Shift/@Model.Employee.EmpNo">جدول العمل</a>

        <a class="btn btn-sm mr-1   btn-@(ViewContext.RouteData.Values["action"].ToString()=="Attendence" ? "primary" : "")" href="~/Employees/Attendence/@Model.Employee.EmpNo">الحضور</a>

        <a class="btn btn-sm mr-1   btn-@(ViewContext.RouteData.Values["action"].ToString()=="Leave" ? "primary" : "")" href="~/Employees/Leave/@Model.Employee.EmpNo">الاجازات</a>
    </div>

</div>