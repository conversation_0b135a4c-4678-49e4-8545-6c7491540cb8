﻿@using HumanResource.Modules.Employees.ViewModels
@model EmployeesViewModel

@{
    Layout = Model.Page.Layout;
}

@Html.Partial("_Tabs")

<div class="row">
    <div class="col-lg-8">
        <form class="ajax" method="post" action="/Employees/Update/@Model.Employee.EmpNo">
            <div class="card ">
                <div class="card-body">

                    @if (Model.Employee.ProfileImage != null)
                    {
                        <img src="@Model._h.GetFile(Model.Employee.ProfileImage)" style="max-height: 100px" />
                    }
                    else
                    {
                        <img src="/assets/images/user60x60.png" style="max-height: 100px" />
                    }

                    <input name="ProfileImage" type="file" />

                    <br />

                    <label>الرقم</label>
                    <input class="form-control" disabled value="@Model.Employee.EmpNo" />
                    <br />
                    <label>الاسم</label>
                    <input class="form-control" disabled value="@Model.Employee.EmpNameA" />
                    <br />
                    <label>الدائرة</label>
                    <input class="form-control" disabled value="@Model.Employee.DgDespA" />
                    <br />
                    <label>القسم</label>
                    <input class="form-control" disabled value="@Model.Employee.DeptDespA" />
                    <br />
                    <label>الدرجة المالية</label>
                    <input class="form-control" disabled value="@Model.Employee.GradRank" />

                </div>


                <div class="card-footer">
                    <button class="btn btn-primary"><i class="fa fa-save"></i> حفظ</button>
                </div>
            </div>
        </form>
    </div>
</div>