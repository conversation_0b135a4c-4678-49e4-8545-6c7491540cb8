@using HumanResource.Modules.Employees.ViewModels
@model EmployeesViewModel

<div class="container-fluid">
    <!-- Report Header -->
    <div class="d-flex justify-content-between mb-4">
        <h3>تقرير الحضور والانصراف والاستئذان</h3>
        
        <a href="javascript:window.print()" class="btn btn-outline-primary">
            <i class="fa fa-print"></i> طباعة التقرير
        </a>
    </div>
    
    <!-- Filter Card -->
    <div class="card shadow mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="m-0">فلترة البيانات</h5>
        </div>
        <div class="card-body">
            <form method="get" action="/Employees/AttendanceReport" id="filterForm" class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>من تاريخ</label>
                        <input type="date" name="from" class="form-control" value="@ViewBag.FromDate.ToString("yyyy-MM-dd")" />
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>إلى تاريخ</label>
                        <input type="date" name="to" class="form-control" value="@ViewBag.ToDate.ToString("yyyy-MM-dd")" />
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>الدائرة</label>
                        <select name="dgCode" class="form-control" id="dgCodeSelect">
                            <option value="">الكل</option>
                            @foreach (var dg in ViewBag.DGs)
                            {
                                if (ViewBag.DgCode == dg.DgCode)
                                {
                                    <option value="@dg.DgCode" selected>@dg.DgDespA</option>
                                }
                                else
                                {
                                    <option value="@dg.DgCode">@dg.DgDespA</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>القسم</label>
                        <select name="deptCode" class="form-control">
                            <option value="">الكل</option>
                            @foreach (var dept in ViewBag.Departments)
                            {
                                if (ViewBag.DeptCode == dept.DeptCode)
                                {
                                    <option value="@dept.DeptCode" selected>@dept.DeptDespA</option>
                                }
                                else
                                {
                                    <option value="@dept.DeptCode">@dept.DeptDespA</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-12 mt-3">
                    <button type="submit" class="btn btn-primary">تطبيق الفلتر</button>
                    <a href="/Employees/AttendanceReport" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الموظفين</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.TotalEmployees</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                معدل الالتزام</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.OverallComplianceRate%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                ساعات الاستئذان</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.TotalExcuseHours.ToString("F1") ساعة</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-sign-out-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                ساعات التأخير</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.TotalLateHours.ToString("F1") ساعة</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <!-- Compliance Rate Chart -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">معدل الالتزام حسب القسم</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="complianceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Work Hours Chart -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">ساعات العمل الفعلية مقابل المتوقعة</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="hoursChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Additional Charts Row -->
    <div class="row">
        <!-- Excuse & Lateness Chart -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الاستئذانات والتأخير حسب القسم</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="excuseChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Leave Hours Distribution -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع ساعات الاستئذان والتأخير</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:300px;">
                        <canvas id="leaveDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Data -->
    @foreach (var dept in Model.AttendanceReport)
    {
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    @dept.DepartmentName (@dept.DGName) - @dept.TotalEmployees موظف
                </h6>
                <span class="badge @(dept.ComplianceRate >= 90 ? "badge-success" : 
                                    dept.ComplianceRate >= 75 ? "badge-warning" : "badge-danger") p-2">
                    معدل الالتزام: @dept.ComplianceRate.ToString("F1")%
                </span>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body p-3">
                                <h6 class="card-title">ساعات العمل</h6>
                                <p class="card-text mb-0">المتوقعة: @dept.ExpectedWorkHours.ToString("F1") ساعة</p>
                                <p class="card-text">الفعلية: @dept.ActualWorkHours.ToString("F1") ساعة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body p-3">
                                <h6 class="card-title">الاستئذان</h6>
                                <p class="card-text mb-0">عدد المرات: @dept.ExcuseCount</p>
                                <p class="card-text">إجمالي الساعات: @dept.ExcuseHours.ToString("F1")</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body p-3">
                                <h6 class="card-title">التأخير</h6>
                                <p class="card-text mb-0">عدد المرات: @dept.LateCount</p>
                                <p class="card-text mb-0">إجمالي الساعات: @dept.LateHours.ToString("F1")</p>
                                <hr class="my-2" />
                                <p class="card-text mb-0 small">
                                    <span class="badge badge-success">أقل من 30 دقيقة:</span> 
                                    @dept.EmployeeStats.Sum(e => e.MinorLateCount)
                                </p>
                                <p class="card-text mb-0 small">
                                    <span class="badge badge-warning">30-60 دقيقة:</span> 
                                    @dept.EmployeeStats.Sum(e => e.ModerateLateCount)
                                </p>
                                <p class="card-text small">
                                    <span class="badge badge-danger">أكثر من 60 دقيقة:</span> 
                                    @dept.EmployeeStats.Sum(e => e.SevereLateCount)
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body p-3">
                                <h6 class="card-title">العمل الإضافي</h6>
                                <p class="card-text mb-0">إجمالي الساعات: @dept.OvertimeHours.ToString("F1")</p>
                                <p class="card-text">المهمات الرسمية: @dept.OfficialMissionCount</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employee Data Table -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="bg-light">
                            <tr>
                                <th>الموظف</th>
                                <th>أيام العمل</th>
                                <th>ساعات العمل</th>
                                <th>العمل الإضافي</th>
                                <th colspan="2">التأخير</th>
                                <th>الاستئذانات</th>
                                <th>المهمات الرسمية</th>
                                <th>أيام الإجازات</th>
                            </tr>
                            <tr>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th>المرات <small class="text-muted">(يوم)</small></th>
                                <th>التفاصيل <small class="text-muted">(دقائق)</small></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var emp in dept.EmployeeStats.OrderByDescending(e => e.LateHours + e.ExcuseHours))
                            {
                                <tr>
                                    <td><a href="/Employees/Attendence/@emp.EmpNo">@emp.EmpName (@emp.EmpNo)</a></td>
                                    <td>@emp.WorkDays</td>
                                    <td>@emp.WorkHours.ToString("F1")</td>
                                    <td>@emp.OvertimeHours.ToString("F1")</td>
                                    <td>
                                        @emp.LateCount مرة<br/>
                                        <small>@emp.LateHours.ToString("F1") ساعة</small>
                                    </td>
                                    <td>
                                        @if (emp.MinorLateCount > 0 || emp.ModerateLateCount > 0 || emp.SevereLateCount > 0)
                                        {
                                            <div class="small">
                                                @if (emp.MinorLateCount > 0)
                                                {
                                                    <span class="badge badge-success">
                                                        <small>30</small>
                                                        </span> 
                                                    @emp.MinorLateCount 
                                                    <small>مرة</small> 
                                                }
                                                @if (emp.ModerateLateCount > 0)
                                                {
                                                    <span class="badge badge-warning">30-60</span> 
                                                    @emp.ModerateLateCount 
                                                    <small>مرة</small>
                                                }
                                                @if (emp.SevereLateCount > 0)
                                                {
                                                    <span class="badge badge-danger">> 60</span> 
                                                    @emp.SevereLateCount 
                                                    <small>مرة</small>
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="text-success">-</span>
                                        }
                                    </td>
                                    <td>
                                        @emp.ExcuseCount مرة<br/>
                                        <small>@emp.ExcuseHours.ToString("F1") ساعة</small>
                                    </td>
                                    <td>@emp.OfficialMissionCount</td>
                                    <td>
                                        @if (emp.LeaveDaysCount > 0)
                                        {
                                            <span class="badge badge-info">@emp.LeaveDaysCount يوم</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Compliance Rate Chart
        const complianceCtx = document.getElementById('complianceChart').getContext('2d');
        const complianceChart = new Chart(complianceCtx, {
            type: 'bar',
            data: {
                labels: [@Html.Raw(ViewBag.DeptLabels)],
                datasets: [{
                    label: 'معدل الالتزام %',
                    data: [@ViewBag.ComplianceRates],
                    backgroundColor: [@Html.Raw(ViewBag.ChartColors)],
                    borderColor: [@Html.Raw(ViewBag.ChartColors)],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Work Hours Chart
        const hoursCtx = document.getElementById('hoursChart').getContext('2d');
        const hoursChart = new Chart(hoursCtx, {
            type: 'bar',
            data: {
                labels: [@Html.Raw(ViewBag.DeptLabels)],
                datasets: [
                    {
                        label: 'ساعات العمل الفعلية',
                        data: [@ViewBag.ActualHours],
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'ساعات العمل المتوقعة',
                        data: [@ViewBag.ExpectedHours],
                        backgroundColor: 'rgba(153, 102, 255, 0.7)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Excuse and Lateness Chart
        const excuseCtx = document.getElementById('excuseChart').getContext('2d');
        const excuseChart = new Chart(excuseCtx, {
            type: 'bar',
            data: {
                labels: [@Html.Raw(ViewBag.DeptLabels)],
                datasets: [
                    {
                        label: 'ساعات التأخير',
                        data: [@ViewBag.LateHours],
                        backgroundColor: 'rgba(255, 99, 132, 0.7)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'ساعات الاستئذان',
                        data: [@ViewBag.ExcuseHours],
                        backgroundColor: 'rgba(255, 159, 64, 0.7)',
                        borderColor: 'rgba(255, 159, 64, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'الساعات'
                        }
                    }
                }
            }
        });
        
        // Leave Distribution Doughnut Chart
        const leaveDistCtx = document.getElementById('leaveDistributionChart').getContext('2d');
        const leaveDistChart = new Chart(leaveDistCtx, {
            type: 'doughnut',
            data: {
                labels: ['ساعات التأخير', 'ساعات الاستئذان', 'ساعات العمل الإضافي'],
                datasets: [{
                    data: [
                        @ViewBag.TotalLateHours, 
                        @ViewBag.TotalExcuseHours, 
                        @ViewBag.TotalOvertimeHours
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(75, 192, 192, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(75, 192, 192, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: true,
                        text: 'توزيع الساعات'
                    }
                }
            }
        });
    </script>
}

    <style media="print">
        @@media print {
            .card {
                break-inside: avoid;
            }
            
            .no-print {
                display: none;
            }
            
            .card-body {
                padding: 10px;
            }
            
            h3 {
                font-size: 18px;
            }
            
            .table td, .table th {
                padding: 0.5rem;
            }
        }
    </style>
