﻿@using HumanResource.Modules.Employees.ViewModels
@model EmployeesViewModel

@{
    Layout = Model.Page.Layout;
}

<div class="card">
    <table class="table">
        <thead>
            <tr>
                <th>المديرية</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var dg in Model.DgReport.Dgs)
            {
                <tr>
                    <td>
                        @dg.Dg.DgDespA
                    </td>

                    <td>
                        @dg.Counts["maleCount"]
                    </td>

                    <td>
                        @dg.Counts["femaleCount"]
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>