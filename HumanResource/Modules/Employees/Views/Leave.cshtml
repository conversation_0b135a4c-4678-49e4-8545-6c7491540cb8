@using HumanResource.Modules.Employees.ViewModels
@model EmployeesViewModel
@{
    Layout = Model.Page.Layout;
}

@Html.Partial("_Tabs")



<div class="card shadow">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 col-sm-6 border rounded p-3 text-dark m-1">
                <div class="d-flex">
                    <h3 class="font-weight-semibold">@ViewBag.AnnualLeaveBalance يوم</h3>
                </div>

            </div>
        
        </div>
    </div>
    
    @if (Model.Leaves == null || !Model.Leaves.Any())
    {
        <div class="alert alert-info m-3">
            <i class="fa fa-info-circle"></i> لا توجد سجلات إجازات لهذا الموظف
        </div>
    }
    else
    {
        <table class="table datatable">
            <thead class="bg-primary">
                <tr>
                    <th>الرقم</th>
                    <th>نوع الاجازة</th>
                    <th>من تاريخ</th>
                    <th>إلى تاريخ</th>
                    <th>عدد الأيام</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.Leaves)
                {
                    <tr>
                        <td>@<EMAIL><EMAIL></td>
                        <td>
                            @item.TleaveCode.LeaveDespA
                        </td>
                        <td>
                            @Model._d(item.LeaveStartDate)
                        </td>
                        <td>
                            @Model._d(item.LeaveEndDate)
                        </td>
                        <td>
                            @{
                                DateTime end = (DateTime)item.LeaveEndDate.AddDays(1);
                                DateTime start = (DateTime)item.LeaveStartDate;
                                TimeSpan diff = end.Subtract(start);
                                @diff.ToString("dd")
                            }
                        </td>
                        <td>
                            @Html.Raw(Model.renderLeaveSatus(item.ReqStat))
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    }
</div>

<script>
    $(document).ready(function() {
     
    });
</script>