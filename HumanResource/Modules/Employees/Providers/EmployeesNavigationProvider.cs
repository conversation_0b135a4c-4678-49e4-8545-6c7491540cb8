using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Employees.Providers
{
    /// <summary>
    /// Navigation provider for the Employees module
    /// </summary>
    public class EmployeesNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Employees";
        public int Priority => 24;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // HR Group navigation items
            var hrNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "hr",
                    Label = "الموظفون",
                    Active = "hr",
                    Icon = "<i class=\"far fa-users\"></i>",
                    Rights = new List<Right> { Right.LeavesDepartment, Right.OvertimeDepartment, Right.OvertimeAdmin, Right.LeavesAdmin, Right.OvertimeHrManager, Right.Hr },
                    Priority = 25,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "hr",
                            Label = "القائمة",
                            Rights = new List<Right> { Right.Hr },
                            Url = "/Employees/Index"
                        },
                        new NavLink
                        {
                            Name = "hr",
                            Label = "تقرير ساعات العمل",
                            Rights = new List<Right> { Right.HrReports },
                            Url = "/Employees/AttendanceReport"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.HR] = hrNavItems;

            return navigation;
        }
    }
} 