﻿using HumanResource.Modules.Shared.Models.Entities.HRMS;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Employees.Models.Entities
{
    [Table("EMPSKILL")]
    public class EmpSkill
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }
        [Column("NAME")]
        public string Name { get; set; }


        public virtual ICollection<SpecialiedSkill> SpecialiedSkills { get; set; }
    }
}
