﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Employees.Models.Entities;

public class UserRole
{
    [Key]
    public int Id { get; set; }  // Primary key

    [Required]
    public int StaffId { get; set; } // StaffId

    [Required]
    [StringLength(100)]
    public string Role { get; set; } // Role

    [DataType(DataType.Date)]
    public DateTime CreatedAt { get; set; } = DateTime.Now; // CreatedAt

    [Required]
    public int CreatedBy { get; set; } // CreatedBy

    [NotMapped]
    public List<string> Rights { get; set; }
}