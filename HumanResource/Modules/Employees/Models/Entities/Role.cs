﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace HumanResource.Modules.Employees.Models.Entities;


[Table("TROLES")]
public class Role
{

    [Key]
    [Column("CODE")]
    public int Code { get; set; }


    [Required]
    [MaxLength(250)]
    [Column("NAME")]
    public string Name { get; set; }

    [Column("REM")]
    public string Rem { get; set; }

    [Column("RIGHTS")]
    public string Rights { get; set; }



    [NotMapped]
    public VempDtl EmpDtls { get; set; }

    [NotMapped]
    public List<string> _Rights
    {
        get
        {
            return string.IsNullOrEmpty(Rights)
                ? new List<string>()
                : Rights.Split('|', StringSplitOptions.RemoveEmptyEntries).ToList();
        }
    }

    public ICollection<RoleEmp> RoleEmps { get; set; }






}

