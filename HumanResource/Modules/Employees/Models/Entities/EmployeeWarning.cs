using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HumanResource.Modules.Attendence.Models.Enums;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Employees.Models.Entities
{
    [Table("EMPLOYEE_WARNINGS")]
    public class EmployeeWarning
    {
        [Key]
        [Column("GUID")]
        [StringLength(100)]
        [Unicode(false)]
        public string Guid { get; set; } = string.Empty;

        public EmployeeWarning()
        {
            Guid = System.Guid.NewGuid().ToString();
        }


        [Column("TITLE")]
        [StringLength(100)]
        [Unicode(false)]
        public string? Title { get; set; }

        [Column("BODY")]
        [StringLength(1000)]
        [Unicode(false)]
        public string? Body { get; set; }

        [Column("WARNING_DATE")]
        public DateTime? WarningDate { get; set; }

        [Column("EMP_NO")]
        [Precision(6)]
        public int? EmpNo { get; set; }

        [Column("TIMESTAMP")]
        public DateTime? Timestamp { get; set; }

        [Column("NOTE")]  // department note 
        public string? Note { get; set; }


        [Column("STATUS")]
        public EmployeeWarningStatus Status { get; set; } = EmployeeWarningStatus.New;

    }
} 