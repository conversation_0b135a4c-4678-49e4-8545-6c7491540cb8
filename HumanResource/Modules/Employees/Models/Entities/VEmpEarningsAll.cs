﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Employees.Models.Entities
{
    [Keyless]
    [Table("VEMP_EARNINGS_All")]
    public class VEmpEarningsAll
    {
        [Column("EMP_NO")]
        public int EmpNo { get; set; }

        [Column("UNIT_CODE")]
        public int UnitCode { get; set; }

        [Column("EARN_CODE")]
        public int EarnCode { get; set; }

        [Column("FROM_DATE")]
        public DateTime? FromDate { get; set; }

        [Column("TO_DATE")]
        public DateTime? ToDate { get; set; }


        [Column("EARN_AMT")]
        public float? EarnAmt { get; set; }
    }
}
