﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Employees.Models.Entities;

[Keyless]
public partial class VempTrg
{
    [Column("UNIT_CODE")]
    [Precision(2)]
    public int UnitCode { get; set; }

    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    [Column("EMP_NAME_A")]
    [StringLength(120)]
    [Unicode(false)]
    public string EmpNameA { get; set; }

    [Required]
    [Column("COURSE_NAME_A")]
    [StringLength(255)]
    [Unicode(false)]
    public string CourseNameA { get; set; }

    [Required]
    [Column("COURSE_COUNTRY")]
    [StringLength(25)]
    [Unicode(false)]
    public string CourseCountry { get; set; }

    [Column("COURSE_START_DATE", TypeName = "DATE")]
    public DateTime CourseStartDate { get; set; }

    [Column("COURSE_END_DATE", TypeName = "DATE")]
    public DateTime CourseEndDate { get; set; }

    [Required]
    [Column("INSTITUTE")]
    [StringLength(40)]
    [Unicode(false)]
    public string Institute { get; set; }
}
