﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Employees.Models.Entities;

[Table("VEMP_FTLS")]
public partial class VempDtl
{



    //[NotMapped]
    public ICollection<RoleEmp> RoleEmps { get; set; }

    [Column("EMP_NO")]
    public int? EmpNo { get; set; }

    [Column("EMP_NAME_A")]
    [StringLength(120)]
    [Unicode(false)]
    public string EmpNameA { get; set; } = string.Empty;


    [Column("DG_CODE")]
    [Precision(2)]
    public byte? DgCode { get; set; }

    [Column("DG_DESP_A")]
    [StringLength(60)]
    [Unicode(false)]
    public string DgDespA { get; set; }

    [Column("DEPT_CODE")]
    [Precision(2)]
    public int? DeptCode { get; set; }

    [Column("DEPT_DESP_A")]
    [StringLength(70)]
    [Unicode(false)]
    public string DeptDespA { get; set; }



    [Column("DESGN_CODE")]
    [Precision(4)]
    public int? DesgnCode { get; set; }




    [Column("DESG_CODE")]
    [StringLength(150)]
    [Unicode(false)]
    public string DesgCode { get; set; }

    [Column("DESG_TYPE")]
    [Precision(2)]
    public int? DesgType { get; set; }

    [Column("EMAIL_ID")]
    [StringLength(200)]
    [Unicode(false)]
    public string EmailId { get; set; }

    [Column("NAT_ID")]
    [StringLength(10)]
    [Unicode(false)]
    public string NatId { get; set; }



    [Column("GRADE_RANK")]
    [Unicode(false)]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public string GradRank { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.


    [Column("BIRTH_DATE")]
    [Unicode(false)]
    public DateTime? BirthDate { get; set; }

    [Column("APPOINT_DATE")]
    [Unicode(false)]
    public DateTime? AppointDate { get; set; }


    [Column("DESGN_HIER_LEVEL")]
    [Precision(2)]
    public int? DesgnHierLevel { get; set; }

    [Column("GSC_APPOINT_DATE")]
    public DateTime? GscAppointDate { get; set; }

    [Column("PROFILE_IMAGE")]
    public string ProfileImage { get; set; } = null;

    [Column("SEXINFO")]
    public string SexInfo { get; set; }

    
    [Column("CAT_CODE")]
    public byte? CatCode { get; set; }

    [Column("SECTION_CODE")]
    public int? SectionCode { get; set; }
    
    


    [NotMapped]
    public string ProfileImageUrl { get; set; } = "";

    public virtual ICollection<TempTrgHist> TempTrgHist { get; set; }
    public virtual ICollection<SpecialiedSkill> SpecialiedSkills { get; set; }
}
