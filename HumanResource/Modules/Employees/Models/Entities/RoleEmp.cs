﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;


namespace HumanResource.Modules.Employees.Models.Entities;

[Table("ROLE_EMP")]
public class RoleEmp
{


    [Key, Column("EMP_NO")]
    public int? EmpNo { get; set; }


    [Key, Column("ROLE_CODE")]
    public int? RoleCode { get; set; }

    public virtual VempDtl EmpDtls { get; set; }
    public virtual Role Role { get; set; }

}

