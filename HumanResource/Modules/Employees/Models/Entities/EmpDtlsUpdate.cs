﻿using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Employees.Models.Entities
{
    [Table("EMP_DTLS_UPDATE")]
    public class EmpDtlsUpdate
    {
        [Key]
        [Column("EMP_ON")]
        public int EmpNo { get; set; }

        [Column("SECTION_NO")]
        public int SectionNo { get; set; }

        [Column("PASSPORT_NO")]
        public string Passport { get; set; }

        [Column("NATIONAL_ID_NO")]
        public int NationalID { get; set; }

        [Column("ID_EXPIRY_DATE")]
        public DateTime IdEpiryDate { get; set; }

        [Column("CUR_ADDRESS")]
        public string CurAddress { get; set; }

        [Column("PER_ADRESS")]
        public string PerAddress { get; set; }

        [Column("MAR_STAT_CODE")]
        public int MarStat { get; set; }

        [Column("PHONE_NO")]
        public int PhoneNo { get; set; }

        [Column("REMARKS")]
        public int Remarks { get; set; }

        [Column("UPDATE_DATE")]
        public DateTime UpadteDate { get; set; }

        [Column("PASSPORT_EXPIRE")]
        public DateTime PassportExpire { get; set; }
    }
}
