﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace HumanResource.Modules.Employees.Models.Entities
{
    [Keyless]

    public partial class EmpMaxOualDet
    {
        [Column("EMP_NO")]
        public int? EmpNo { get; set; }

        [Column("SUBJ_DESP_A")]
        public string SubjDespA { get; set; }

        [Column("QUAL_DESP_E")]
        public string QualDespE { get; set; }

        [Column("QUAL_DESP_A")]
        public string QualDespA { get; set; }

        [Column("QUAL_TO_DATE")]
        public DateTime QualToDate { get; set; }

        [Column("QUAL_HIER_LEVEL")]
        public int? QualHierLevel { get; set; }
    }
}
