﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Employees.Models.Entities;

[Keyless]
public partial class VempQual
{
    [Column("UNIT_CODE")]
    [Precision(2)]
    public int UnitCode { get; set; }

    [Column("EMP_NO")]
    [Precision(5)]
    public short EmpNo { get; set; }

    [Column("QUAL_CODE")]
    [Precision(2)]
    public int QualCode { get; set; }

    [Column("SUBJ_CODE")]
    [Precision(3)]
    public int SubjCode { get; set; }

    [Required]
    [Column("QUAL")]
    [StringLength(40)]
    [Unicode(false)]
    public string Qual { get; set; }

    [Required]
    [Column("SUBJECT")]
    [StringLength(120)]
    [Unicode(false)]
    public string Subject { get; set; }

    [Column("QUAL_HIER_LEVEL")]
    [Precision(2)]
    public int QualHierLevel { get; set; }

    [Required]
    [Column("UNIVERSITY")]
    [StringLength(40)]
    [Unicode(false)]
    public string University { get; set; }

    [Column("QUAL_TO_DATE", TypeName = "DATE")]
    public DateTime QualToDate { get; set; }

    [Required]
    [Column("QUAL_COUNTRY")]
    [StringLength(25)]
    [Unicode(false)]
    public string QualCountry { get; set; }
}
