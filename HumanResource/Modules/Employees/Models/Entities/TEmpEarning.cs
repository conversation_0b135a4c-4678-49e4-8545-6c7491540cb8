﻿using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Employees.Models.Entities;

[Table("TEMP_EARNINGS")]
[Keyless]
public class TEmpEarning
{

    [Column("EMP_NO")]
    public int EmpNo { get; set; }

    [Column("UNIT_CODE")]
    public int UnitCode { get; set; }

    [Column("EARN_CODE")]
    public int EarnCode { get; set; }

    [Column("TO_DATE")]
    public DateTime? ToDate { get; set; }

    [Column("FROM_DATE")]
    public DateTime? FromDate { get; set; }

    [Column("EARN_AMT")]
    public float? EarnAmt { get; set; }


}

