﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace HumanResource.Modules.Employees.Models.Entities;

[Table("VEMP_WORK_HOURS_NEW")]
[Keyless]
public class EmpWorkHours
{

    [Column("EMP_NO")]
    public int EmpNo { get; set; }

    [Column("WORK_DATE")]
    public DateTime Day { get; set; }


    [Column("WORK_HOURS_IN_SHIFT")]
    public float Hours { get; set; }

    [Column("OVERTIME_HOURS")]
    public float Extra { get; set; }

    [Column("WORK_DURATION")]
    public float WorkDuration { get; set; }

    [Column("FIRST_ENTRY")]
    public DateTime FirstEntry { get; set; }

    [Column("LAST_ENTRY")]
    public DateTime LastEntry { get; set; }

    [Column("IS_HOLIDAY")]
    public int IsHoliday { get; set; }



    [NotMapped]
    public string Rate { get; set; }


}