﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace HumanResource.Modules.Employees.Models.Entities;

[Table("TEMP_WORK_SHIFT")]
public class TEmpWorkShift
{
    [Key]
    [Column("GUID")]
    public string Guid { get; set; }

    public TEmpWorkShift()
    {
        Guid = System.Guid.NewGuid().ToString();
    }

    [Column("EMP_NO")]
    public int? EmpNo { get; set; } = null;

    [Required]
    [Column("SHIFT_START_DATE")]
    public DateTime? ShiftStartDate { get; set; } 

    [Required]
    [Column("SHIFT_END_DATE")]
    public DateTime? ShiftEndDate { get; set; }

    [Required]
    [Column("SHIFT_START_H")]
    public int ShiftStartH { get; set; }

    [Required]
    [Column("SHIFT_START_I")]
    public int ShiftStartI { get; set; }

    [Required]
    [Column("SHIFT_END_H")]
    public int ShiftEndH { get; set; }

    [Required]
    [Column("SHIFT_END_I")]
    public int ShiftEndI { get; set; }
}
