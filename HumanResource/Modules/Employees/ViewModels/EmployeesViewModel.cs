﻿using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.DTOs;
using HumanResource.Modules.Shared.ViewModels;
using HumanResource.Modules.Shared.Models.Entities.HRMS;

namespace HumanResource.Modules.Employees.ViewModels;

// Data class for attendance statistics report
public class AttendanceReportData
{
    public string DepartmentName { get; set; }
    public string DGName { get; set; }
    public int TotalEmployees { get; set; }
    public int TotalWorkdays { get; set; }
    public double ExpectedWorkHours { get; set; }
    public double ActualWorkHours { get; set; }
    public double OvertimeHours { get; set; }
    public double LateHours { get; set; }
    public double ExcuseHours { get; set; }
    public int ExcuseCount { get; set; }
    public int LateCount { get; set; }
    public int OfficialMissionCount { get; set; }
    public double ComplianceRate { get; set; } // Percentage of expected hours actually worked
    
    // Stats by employee
    public List<EmployeeAttendanceStats> EmployeeStats { get; set; } = new List<EmployeeAttendanceStats>();
}

public class EmployeeAttendanceStats
{
    public int EmpNo { get; set; }
    public string EmpName { get; set; }
    public int WorkDays { get; set; }
    public double WorkHours { get; set; }
    public double OvertimeHours { get; set; }
    public double LateHours { get; set; }
    public int LateCount { get; set; }
    public double ExcuseHours { get; set; }
    public int ExcuseCount { get; set; }
    public int OfficialMissionCount { get; set; }
    
    // Lateness category counts
    public int MinorLateCount { get; set; } // Up to 30 minutes late
    public int ModerateLateCount { get; set; } // 30-60 minutes late
    public int SevereLateCount { get; set; } // More than 60 minutes late
    
    // Leave tracking
    public int LeaveDaysCount { get; set; } // Number of days with approved leave
    public bool IsCurrentlyOnLeave { get; set; } // Whether the employee is on leave today
}

public class EmployeesViewModel : BaseViewModel
{
    public enum LeaveStatus
    {
        Pending = 0,
        Approved = 1,
        Generated = 4,
        Rejected = 3,
        Finalized = 6
    }

    hrmsContext _db;

    public EmployeesViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _db = context;

    }



    public List<VempDtl> VempDtls { get; set; }
    public List<VempDtl> Managers { get; set; }
    public VempDtl VempDtl { get; set; }
    public VempDtl Employee { get; set; }


    public TEmpWorkShift Shift { get; set; }
    public List<TEmpWorkShift> Shifts { get; set; }

    public DgReport DgReport { get; set; }

    // Filter properties
    public List<TdgCode> DgCodes { get; set; }
    public List<TDeptCode> DeptCodes { get; set; }
    public int? SelectedDgCode { get; set; }
    public int? SelectedDeptCode { get; set; }
    
    // Leave properties
    public List<TleaveAplTx> Leaves { get; set; }
    
    // Attendance Report properties
    public List<AttendanceReportData> AttendanceReport { get; set; }
    
    // Helper methods for leave rendering
    public string renderLeaveSatus(int? req_stat)
    {
        if (req_stat == null)
            return "<span class=\"badge bg-secondary\">Unknown</span>";
            
        switch (req_stat)
        {
            case (int)LeaveStatus.Pending:
                return "<span class=\"badge bg-warning text-white\">معلق <i class=\"fas fa-clock\"></i></span>";
            case (int)LeaveStatus.Approved:
                return "<span class=\"badge bg-success\">موافق عليه <i class=\"fas fa-check\"></i></span>";
            case (int)LeaveStatus.Rejected:
                return "<span class=\"badge bg-danger\">مرفوض <i class=\"fas fa-times\"></i></span>";
            case (int)LeaveStatus.Generated:
                return "<span class=\"badge bg-info\">جاهز <i class=\" fas fa-exclamation \"></i></span>";
            case (int)LeaveStatus.Finalized:
                return "<span class=\"badge bg-primary\">مكتمل <i class=\"fas fa-check-double\"></i></span>";
            default:
                return "<span class=\"badge bg-secondary\">Unknown</span>";
        }
    }
}
