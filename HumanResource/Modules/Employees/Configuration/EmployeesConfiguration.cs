using HumanResource.Modules.Employees.Providers;
using HumanResource.Modules.Employees.Services;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Modules.Employees.Configuration
{
    /// <summary>
    /// Configuration for the Employees module
    /// </summary>
    public static class EmployeesConfiguration
    {
        /// <summary>
        /// Registers all Employees module services
        /// </summary>
        public static IServiceCollection AddEmployeesServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, EmployeesNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, EmployeesBadgeProvider>();

            // Register module services
            services.AddScoped<EmployeeService>();

            return services;
        }
    }
} 