﻿
using Microsoft.EntityFrameworkCore;
using Oracle.ManagedDataAccess.Client;
using Oracle.ManagedDataAccess.Types;
using System.Data;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Employees.Services
{
    public class EmployeeService
    {

        private readonly hrmsContext _db;


        public EmployeeService(hrmsContext context)
        {
            _db = context;

        }

        public VempDtl Find(int empNo)
        {
            return _db.VempDtls.Find(empNo);
        }

        public VempDtl Get(int empNo)
        {
            return Find(empNo);
        }

        public List<VempDtl> Managers(int? empNo)
        {

            var emp = Find(empNo.Value);

            using (var ctx = new hrmsContext())
            using (var cmd = ctx.Database.GetDbConnection().CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "MANGERAPPROVAL";

                var EMPNOParam = new OracleParameter("inEMPNO", OracleDbType.Int32, empNo, ParameterDirection.Input);
                var DGParam = new OracleParameter("inDG", OracleDbType.Int32, emp.DgCode, ParameterDirection.Input);
                var DEPTParam = new OracleParameter("inDEPT", OracleDbType.Int32, emp.DeptCode, ParameterDirection.Input);
                var DESGNParam = new OracleParameter("inDESGN", OracleDbType.Int32, emp.DesgnHierLevel, ParameterDirection.Input);
                var l_idParam = new OracleParameter("Outl_id", OracleDbType.RefCursor, ParameterDirection.Output);

                cmd.Parameters.AddRange(new[] { EMPNOParam, DGParam, DEPTParam, DESGNParam, l_idParam });
                cmd.Connection.Open();
                var result = cmd.ExecuteNonQuery();
                OracleDataReader dr = ((OracleRefCursor)cmd.Parameters[4].Value).GetDataReader();

                List<VempDtl> Mangers = new List<VempDtl>();
                while (dr.Read())
                {
                    Mangers.Add(Find(Convert.ToInt32(dr["EmpNo"])));

                }

                cmd.Connection.Close();

                return Mangers;

            }

        }

        public List<VempDtl> Managers(int empNo)
        {
            using (var ctx = new hrmsContext())
            using (var cmd = ctx.Database.GetDbConnection().CreateCommand())
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandText = "MANGERAPPROVAL";

                var EMPNOParam = new OracleParameter("inEMPNO", OracleDbType.Int32, empNo, ParameterDirection.Input);
                var DGParam = new OracleParameter("inDG", OracleDbType.Int32, Find(empNo).DgCode, ParameterDirection.Input);
                var DEPTParam = new OracleParameter("inDEPT", OracleDbType.Int32, Find(empNo).DeptCode, ParameterDirection.Input);
                var DESGNParam = new OracleParameter("inDESGN", OracleDbType.Int32, Find(empNo).DesgnHierLevel, ParameterDirection.Input);
                var l_idParam = new OracleParameter("Outl_id", OracleDbType.RefCursor, ParameterDirection.Output);

                cmd.Parameters.AddRange(new[] { EMPNOParam, DGParam, DEPTParam, DESGNParam, l_idParam });
                cmd.Connection.Open();
                var result = cmd.ExecuteNonQuery();
                OracleDataReader dr = ((OracleRefCursor)cmd.Parameters[4].Value).GetDataReader();

                List<VempDtl> Mangers = new List<VempDtl>();
                while (dr.Read())
                {
                    Mangers.Add(Find(Convert.ToInt32(dr["EmpNo"])));

                }

                cmd.Connection.Close();


                return Mangers;

            }
        }

    }

}
