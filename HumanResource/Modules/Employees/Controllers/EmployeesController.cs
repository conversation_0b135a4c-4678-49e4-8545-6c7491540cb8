﻿using HumanResource.Core.Helpers.Attributes;
using Microsoft.AspNetCore.Mvc;
using HumanResource.Core.UI.Models;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Employees.ViewModels;
using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.Helpers.Utilities;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Execuses.Models.Enums;

namespace HumanResource.Modules.Employees.Controllers;

// Data Transfer Object for combining attendance and excuse data
public class AttendanceData
{
    public int EmpNo { get; set; }
    public DateTime Day { get; set; }
    public float Hours { get; set; } = 0;
    public float Extra { get; set; } = 0;
    public float WorkDuration { get; set; } = 0;
    public DateTime? FirstEntry { get; set; }
    public DateTime? LastEntry { get; set; }
    public int IsHoliday { get; set; } = 0;
    public List<Absent> Excuses { get; set; } = new List<Absent>();
    
    // Constructor to create from EmpWorkHours
    public AttendanceData(EmpWorkHours workHours = null)
    {
        if (workHours != null)
        {
            EmpNo = workHours.EmpNo;
            Day = workHours.Day;
            Hours = workHours.Hours;
            Extra = workHours.Extra;
            WorkDuration = workHours.WorkDuration;
            FirstEntry = workHours.FirstEntry;
            LastEntry = workHours.LastEntry;
            IsHoliday = workHours.IsHoliday;
        }
    }
}

[Area("Employees")]
[Route("Employees")]
[Can("hr")]
public class EmployeesController : BaseController
{


    public EmployeesViewModel _v;

    public EmployeesController(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, bccontext, httpContextAccessor, helper)
    {
        _db = context;
        _v = new EmployeesViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "hr";

    }

    [HttpGet("Index")]
    public IActionResult Index(int? dgCode = null, int? deptCode = null)
    {

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="الموظفون", Url=$"/Employees/Index"},
        };

        _v.Page.Title = "الموظفون";
        _v.Page.Reload = true;

        var employees = _db.VempDtl.AsQueryable();

        // Apply filters if provided
        if (dgCode.HasValue)
        {
            employees = employees.Where(e => e.DgCode == dgCode.Value);
            _v.SelectedDgCode = dgCode;
        }

        if (deptCode.HasValue)
        {
            employees = employees.Where(e => e.DeptCode == deptCode.Value);
            _v.SelectedDeptCode = deptCode;
        }

        _v.VempDtls = employees.ToList();
        
        // Get all departments and DGs for filter dropdowns
        _v.DgCodes = _db.TdgCodes.ToList();
        _v.DeptCodes = _db.TDeptCode.ToList();

        return View(_v);
    }

    [HttpGet("Update/{EmpNo}")]
    public IActionResult Update(int EmpNo)
    {

        var emp = _h.Employee().Find(EmpNo);

        if (emp == null)
            return StatusCode(404);

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="الموظفون", Url=$"/Employees/Index"},
            new Breadcrumb {Label=emp.EmpNameA, Url=$"/Employees/Index"},
        };

        _v.Page.Title = emp.EmpNameA;
        _v.Page.Reload = true;
        _v.Page.Back = $"/Employees/Index";
        _v.Page.Layout = "_ProfileLayout";

        _v.Managers = _h.Employee().Managers(EmpNo);
        _v.Employee = emp;

        return View(_v);
    }

    [HttpPost("Update/{EmpNo}")]
    [Can("hr")]
    public async Task<IActionResult> Update(int EmpNo, [FromForm] IFormFile ProfileImage)
    {
        var emp = _db.VempDtls.FirstOrDefault(x => x.EmpNo == EmpNo);

        if (emp == null)
            return StatusCode(404);


        string FileGuid = null;

        if (ProfileImage != null && ProfileImage.Length > 0)
        {
            FileGuid = await _h.UploadAsync(ProfileImage, employeeId: EmpNo, StorageHelper.FileType.Photos);
        }


        if (EmpNo > 1000)
        {
            var con = _db.ConEmpMas.Find(EmpNo);

            if (emp == null)
                return StatusCode(404);

            if (FileGuid != null)
            {
                con.FileGuid = FileGuid;

                _db.Update(con);
                _db.SaveChanges();
            }
        }

        if (EmpNo < 1000)
        {
            var employee = _db.EmployeeMas.Find(EmpNo);

            if (employee == null)
                return StatusCode(404);

            if (FileGuid != null)
            {
                employee.FileGuid = FileGuid;

                _db.Update(employee);
                _db.SaveChanges();
            }
        }

        return Json(new
        {
            success = true,
            message = new List<string> { "تم تحديث بيانات الموظف" },
            action = "reload",
        });
    }


    [HttpGet("Shift/{EmpNo}")]
    public IActionResult Shift(int EmpNo)
    {

        var emp = _h.Employee().Find(EmpNo);

        if (emp == null)
            return StatusCode(404);

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="الموظفون", Url=$"/Employees/Index"},
            new Breadcrumb {Label=emp.EmpNameA, Url=$"/Employees/Index"},
        };

        _v.Page.Title = emp.EmpNameA;
        _v.Page.Reload = true;
        _v.Page.Back = $"/Employees/Index";
        _v.Page.Layout = "_ProfileLayout";

        _v.Managers = _h.Employee().Managers(EmpNo);

        _v.Employee = emp;

        _v.Shifts = _db.TEmpWorkShifts
            .Where(r => r.EmpNo == EmpNo)
            .OrderBy(o => o.ShiftEndDate)
            .ToList();

        return View(_v);
    }

    [HttpPost("Shift/{EmpNo}")]
    public IActionResult Shift(int EmpNo, TEmpWorkShift post)
    {

        if (!IsValid(ModelState))
        {
            return ResponseHelper.Json(success: false, message: ValidateErrors(ModelState));
        }

        var emp = _h.Employee().Find(EmpNo);

        if (emp == null)
            return StatusCode(404);

        if (post.ShiftStartDate > post.ShiftEndDate || post.ShiftStartH > post.ShiftEndH || post.ShiftStartH > 24 || post.ShiftEndH > 24 || post.ShiftStartI > 60 || post.ShiftEndI > 60)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "جدول العمل غير صالح" });
        }

        var c1 = _db.TEmpWorkShifts
            .Where(r => r.EmpNo == EmpNo
            && (
                r.ShiftStartDate <= post.ShiftStartDate && r.ShiftEndDate >= post.ShiftStartDate
                || r.ShiftStartDate <= post.ShiftEndDate && r.ShiftEndDate >= post.ShiftEndDate
                )
             )
            .FirstOrDefault();

        if (c1 != null)
        {
            return ResponseHelper.Json(success: false, message: new List<string> { "تاريخ العمل غير صالح" });
        }

        if (post.ShiftStartH > 24)
            post.ShiftStartH = 24;

        if (post.ShiftStartH < 0)
            post.ShiftStartH = 0;

        if (post.ShiftEndH > 24)
            post.ShiftEndH = 24;

        if (post.ShiftEndH < 0)
            post.ShiftEndH = 0;

        var shift = new TEmpWorkShift
        {
            EmpNo = EmpNo,
            ShiftStartDate = post.ShiftStartDate,
            ShiftEndDate = post.ShiftEndDate,
            ShiftStartH = post.ShiftStartH,
            ShiftStartI = post.ShiftStartI,
            ShiftEndH = post.ShiftEndH,
            ShiftEndI = post.ShiftEndI,
        };

        _db.TEmpWorkShifts.Add(shift);
        _db.SaveChanges();

        return ResponseHelper.Json(success: true, message: new List<string> { _("Created successfully") }, action: "reload");
    }

    [HttpGet("Shift/Delete/{Guid}")]
    public IActionResult ShiftDelete(string Guid)
    {

        var shift = _db.TEmpWorkShifts.Where(r => r.Guid == Guid).FirstOrDefault();

        if (shift == null)
            return StatusCode(404);

        var empNo = shift.EmpNo;

        _db.Remove(shift);
        _db.SaveChanges();

        return Redirect($"/Employees/Shift/{empNo}");
    }


    [HttpGet("Attendence/{EmpNo}")]
    public IActionResult Attendence(int EmpNo)
    {

        var emp = _h.Employee().Find(EmpNo);

        if (emp == null)
            return StatusCode(404);

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="الموظفون", Url=$"/Employees/Index"},
            new Breadcrumb {Label=emp.EmpNameA, Url=$"/Employees/Index"},
        };

        _v.Page.Title = emp.EmpNameA;
        _v.Page.Reload = true;
        _v.Page.Back = $"/Employees/Index";
        _v.Page.Layout = "_ProfileLayout";

        _v.Managers = _h.Employee().Managers(EmpNo);
        _v.Employee = emp;



        return View(_v);
    }

    [HttpGet("Leave/{EmpNo}")]
    public IActionResult Leave(int EmpNo)
    {

        var emp = _h.Employee().Find(EmpNo);

        if (emp == null)
            return StatusCode(404);

        _v.Page.Breadcrumb = new List<Breadcrumb> {
            new Breadcrumb {Label="الموظفون", Url=$"/Employees/Index"},
            new Breadcrumb {Label=emp.EmpNameA, Url=$"/Employees/Update/{EmpNo}"},
            new Breadcrumb {Label="الإجازات", Url=$"/Employees/Leave/{EmpNo}"},
        };

        _v.Page.Title = emp.EmpNameA;
        _v.Page.Reload = true;
        _v.Page.Back = $"/Employees/Update/{EmpNo}";
        _v.Page.Layout = "_ProfileLayout";

        _v.Managers = _h.Employee().Managers(EmpNo);
        _v.Employee = emp;

        // Get employee leave history including leave type information
        var leave = _db.TleaveAplTxs
            .Include(l => l.TleaveCode)
            .Where(r => r.EmpNo == EmpNo && r.CancelFlag != 1 && r.ReqStat != 3)
            .OrderByDescending(r => r.LeaveStartDate)
            .ToList();

        _v.Leaves = leave;

        // Get leave balances for current employee
        ViewBag.AnnualLeaveBalance = _h.Leave().Balance(EmpNo, 1); // Annual leave code is 1
        
        return View(_v);
    }


    [HttpGet("GetAttendence")]
    public IActionResult GetAttendence(int empNo, int year, int month)
    {
        if (year == 0 || month == 0 || empNo < 1)
        {
            return StatusCode(404);
        }

        DateTime dateFrom = new DateTime(year, month, 1);
        DateTime dateTo = new DateTime(year, month, DateTime.DaysInMonth(year, month));

        var attendanceDataList = new List<AttendanceData>();

        // Get work hours records
        var workHoursRecords = _db.EmpWorkHours
            .Where(ro =>
                ro.EmpNo == empNo
                && ro.Day >= dateFrom
                && ro.Day <= dateTo
            )
            .OrderByDescending(ro => ro.Day)
            .ToList();

        // Get excuses for this employee in the date range
        var excuses = _db.Absents
            .Where(a => 
                a.EmpNo == empNo && 
                a.OrderDate.HasValue && 
                a.OrderDate.Value.Month == month && 
                a.OrderDate.Value.Year == year &&
                (a.Status == (int)AbsentStatus.Approved || a.Status == (int)AbsentStatus.Pending || a.Status == (int)AbsentStatus.NeedsDGApproval || a.Status == (int)AbsentStatus.ApprovedByDg))
            .ToList();

        // Create day-by-day attendance data
        for (DateTime date = dateFrom; date <= dateTo; date = date.AddDays(1))
        {
            var workHoursRecord = workHoursRecords.FirstOrDefault(r => r.Day == date);
            var dayExcuses = excuses.Where(e => e.OrderDate.HasValue && e.OrderDate.Value.Date == date.Date).ToList();

            // Create attendance data object
            var attendanceData = new AttendanceData(workHoursRecord);
            attendanceData.Day = date;
            attendanceData.EmpNo = empNo;
            attendanceData.Excuses = dayExcuses;
            
            attendanceDataList.Add(attendanceData);
        }

        return Json(new
        {
            data = attendanceDataList
        });
    }
    
    [HttpGet("AttendanceReport")]
    [Can("hr-reports")]
    public IActionResult AttendanceReport(DateTime? from = null, DateTime? to = null, int? dgCode = null, int? deptCode = null)
    {

        // Set default date range to current month if not specified
        DateTime dateFrom = from ?? new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        DateTime dateTo = to ?? dateFrom.AddMonths(1).AddDays(-1);
        
        // Store filter values in ViewBag
        ViewBag.FromDate = dateFrom;
        ViewBag.ToDate = dateTo;
        ViewBag.DgCode = dgCode;
        ViewBag.DeptCode = deptCode;
        
        // Get all employees
        var employees = _db.VempDtls.AsQueryable();
        
        // Apply department and DG filters if provided
        if (dgCode.HasValue)
        {
            employees = employees.Where(e => e.DgCode == dgCode.Value);
        }
        
        if (deptCode.HasValue)
        {
            employees = employees.Where(e => e.DeptCode == deptCode.Value);
        }
        
        // Get the filtered list of employees
        var filteredEmployees = employees.ToList();
        
        // Get all work hours records for the selected date range
        var workHours = _db.EmpWorkHours
            .Where(wh => wh.Day >= dateFrom && wh.Day <= dateTo)
            .ToList();
        
        // Get all excuses for the selected date range
        var excuses = _db.Absents
            .Where(a => a.OrderDate >= dateFrom && a.OrderDate <= dateTo &&
                   (a.Status == (int)AbsentStatus.Approved || a.Status == (int)AbsentStatus.Pending || a.Status == (int)AbsentStatus.NeedsDGApproval || a.Status == (int)AbsentStatus.ApprovedByDg) ) // Only count approved excuses or those under DG approval
            .ToList();
            
        // Pre-load all approved leaves for the date range
        var allLeaves = _db.TleaveAplTxs
            .Where(l => l.LeaveStartDate <= dateTo && l.LeaveEndDate >= dateFrom &&
                 (l.ReqStat == 1 || l.ReqStat == 4 || l.ReqStat == 6)) // Approved, Generated, or Finalized leaves
            .ToList();
            
        // Pre-load all holidays for the date range and convert to HashSet for O(1) lookups
        var holidays = _db.Tholidays
            .Where(h => h.Holiday >= dateFrom && h.Holiday <= dateTo)
            .Select(h => h.Holiday)
            .ToHashSet();
        
        // Department and DG dictionaries for lookup
        var departments = _db.TDeptCode
            .GroupBy(d => d.DeptCode)
            .ToDictionary(g => g.Key, g => g.First().DeptDespA);
            
        var dgs = _db.TdgCodes
            .ToDictionary(dg => (int)dg.DgCode, dg => dg.DgDespA);
            
        // Create Department to DG mapping that can handle duplicate department codes
        // Instead of a dictionary, use a lookup to handle cases where the same department code
        // exists under different DGs
        var deptToDgMapping = _db.TDeptCode
            .Where(d => d.DgCode != null)
            .ToLookup(d => d.DeptCode, d => new { d.DeptCode, d.DgCode, DeptName = d.DeptDespA });
            
        // Calculate total days excluding weekends in the date range
        int totalWorkdays = 0;
        
        // Create a HashSet of all workdays in the range (excluding weekends)
        var workdayDates = new HashSet<DateTime>();
        for (DateTime date = dateFrom; date <= dateTo; date = date.AddDays(1))
        {
            // Skip weekends (Friday = 5, Saturday = 6)
            if (date.DayOfWeek != DayOfWeek.Friday && date.DayOfWeek != DayOfWeek.Saturday)
            {
                totalWorkdays++;
                workdayDates.Add(date.Date);
            }
        }
        
        // Group data by department
        var departmentsData = new List<AttendanceReportData>();
        
        // Group employees by department
        var deptGroups = filteredEmployees
            .Where(e => e.DeptCode.HasValue)
            .GroupBy(e => e.DeptCode.Value);
            
        // Process each department
        foreach (var deptGroup in deptGroups)
        {
            var currentDeptCode = deptGroup.Key;
            string deptName = departments.ContainsKey(currentDeptCode) ? departments[currentDeptCode] : "Unknown";
            
            // Get DG for this department from the mapping
            int mappedDgCode = 0;
            string dgName = "Unknown";
            
            // Get employees in this department
            var deptEmployees = deptGroup.ToList();
            
            // Find the correct DG by checking which one contains these employees
            // First, get all possible department-DG combinations for this department code
            var possibleDepts = deptToDgMapping[currentDeptCode].ToList();
            
            if (possibleDepts.Any())
            {
                // If there's only one possibility, use it
                if (possibleDepts.Count == 1)
                {
                    mappedDgCode = possibleDepts[0].DgCode;
                    deptName = possibleDepts[0].DeptName; // Use the correct department name
                }
                else
                {
                    // If there are multiple possibilities, try to find the correct one by checking employee DGs
                    var employeeDgCodes = deptEmployees
                        .Where(e => e.DgCode.HasValue)
                        .Select(e => e.DgCode.Value)
                        .GroupBy(dg => dg)
                        .OrderByDescending(g => g.Count())
                        .Select(g => g.Key)
                        .ToList();
                        
                    if (employeeDgCodes.Any())
                    {
                        // Use the most common DG among employees in this department
                        int mostCommonDg = employeeDgCodes.First();
                        
                        // Find the department-DG combination that matches this DG
                        var matchingDept = possibleDepts.FirstOrDefault(d => d.DgCode == mostCommonDg);
                        if (matchingDept != null)
                        {
                            mappedDgCode = matchingDept.DgCode;
                            deptName = matchingDept.DeptName;
                        }
                        else
                        {
                            // If no match, just use the first one
                            mappedDgCode = possibleDepts[0].DgCode;
                            deptName = possibleDepts[0].DeptName;
                        }
                    }
                    else
                    {
                        // If no employee DGs, just use the first one
                        mappedDgCode = possibleDepts[0].DgCode;
                        deptName = possibleDepts[0].DeptName;
                    }
                }
            }
            
            // Get DG name
            if (dgs.ContainsKey(mappedDgCode))
            {
                dgName = dgs[mappedDgCode];
            }
            
            // Create report data object for this department
            var deptData = new AttendanceReportData
            {
                DepartmentName = deptName,
                DGName = dgName,
                TotalEmployees = deptEmployees.Count,
                TotalWorkdays = totalWorkdays,
                ExpectedWorkHours = totalWorkdays * 7 * deptEmployees.Count // 7 hour workday
            };
            
            // Process each employee in this department
            foreach (var employee in deptEmployees)
            {
                if (!employee.EmpNo.HasValue) continue;
                
                var empNo = employee.EmpNo.Value;
                
                // Get work hours for this employee
                var empWorkHours = workHours.Where(wh => wh.EmpNo == empNo).ToList();
                
                // Get excuses for this employee
                var empExcuses = excuses.Where(e => e.EmpNo == empNo).ToList();
                
                // Get approved leaves for this employee and organize by date for quick lookup
                var empLeaves = allLeaves.Where(l => l.EmpNo == empNo).ToList();
                
                // Create a HashSet of dates when employee has approved leave
                var leaveDates = new HashSet<DateTime>();
                foreach (var leave in empLeaves)
                {
                    for (DateTime date = leave.LeaveStartDate; date <= leave.LeaveEndDate; date = date.AddDays(1))
                    {
                        leaveDates.Add(date.Date);
                    }
                }
                
                // Count only work days (excluding weekends) when calculating leave days
                int leaveDaysCount = 0;
                foreach (var leaveDate in leaveDates)
                {
                    // Skip weekends (Friday = 5, Saturday = 6)
                    if (leaveDate.DayOfWeek != DayOfWeek.Friday && leaveDate.DayOfWeek != DayOfWeek.Saturday)
                    {
                        // Skip holidays
                        if (!holidays.Contains(leaveDate))
                        {
                            leaveDaysCount++;
                        }
                    }
                }
                
                // Calculate statistics
                double actualWorkHours = empWorkHours.Sum(wh => wh.WorkDuration);

                // If employee is a high-level manager (DesgnHierLevel < 4), consider full work hours
                if(employee.DesgnHierLevel < 4)
                {
                    actualWorkHours = 7 * totalWorkdays;
                }
                else
                {
                    // Create HashSet of days with work records for O(1) lookup
                    var workRecordDays = new HashSet<DateTime>(empWorkHours.Select(wh => wh.Day.Date));
                    
                    // For other employees, add full work hours for days with approved leave or holidays
                    foreach (var date in workdayDates)
                    {
                        // If already has work record, skip (already counted in the sum above)
                        if (workRecordDays.Contains(date))
                        {
                            continue;
                        }
                        
                        // Check if employee has an approved leave for this day (O(1) lookup now)
                        bool hasApprovedLeave = leaveDates.Contains(date);
                            
                        // Check if it's a holiday (O(1) lookup now)
                        bool isHoliday = holidays.Contains(date);
                        
                        // If employee has approved leave or it's a holiday, add full work hours
                        if (hasApprovedLeave || isHoliday)
                        {
                            actualWorkHours += 7; // Add a full workday (7 hours)
                        }
                    }
                }

                double overtimeHours = empWorkHours.Sum(wh => wh.Extra);
                
                
                // Calculate late hours for employee attendance
                // This code tracks when employees arrive late to work (after the official start time of 8:30 AM)
                // We count all arrivals after the official start time
                double lateHours = 0;  // Total hours of lateness
                int lateCount = 0;     // Number of days employee was late
                int minorLateCount = 0;   // Up to 30 minutes late
                int moderateLateCount = 0; // 30-60 minutes late
                int severeLateCount = 0;   // More than 60 minutes late
                
                // Skip lateness tracking for employees with DesgnHierLevel < 4
                if (employee.DesgnHierLevel >= 4)
                {
                    foreach (var wh in empWorkHours)  // Loop through each work day record
                    {
                        // Only process records with valid entry times
                        if (wh.FirstEntry != default)
                        {
                            var entryTime = wh.FirstEntry;  // When employee first clocked in
                            var currentDate = wh.Day.Date;  // Current date being processed
                            
                            // Check if employee has an approved leave for this day - O(1) lookup
                            bool hasApprovedLeave = leaveDates.Contains(currentDate);
                            
                            // Skip lateness tracking if employee has an approved leave for this day
                            if (hasApprovedLeave)
                            {
                                continue;
                            }
 
                            bool isHoliday = holidays.Contains(currentDate);
 
                            if(isHoliday)
                            {
                                continue;
                            }
                            
                            // Define the official start time (8:30 AM)
                            var officialStartTime = new DateTime(entryTime.Year, entryTime.Month, entryTime.Day, 8, 30, 0);
                            
                            // Check if employee arrived after official start time
                            if (entryTime > officialStartTime)
                            {
                                // Calculate how many hours employee was late by subtracting
                                // the actual entry time from the official start time
                                double lateMinutes = (entryTime - officialStartTime).TotalMinutes;
                                lateHours += lateMinutes / 60.0;
                                lateCount++; // Increment the count of late days
                                
                                // Categorize lateness
                                if (lateMinutes <= 30)
                                {
                                    minorLateCount++;
                                }
                                else if (lateMinutes <= 60)
                                {
                                    moderateLateCount++;
                                }
                                else
                                {
                                    severeLateCount++;
                                }
                            }
                        }
                    }
                }
                
                // Calculate excuse hours by type
                double excuseHours = 0;
                int excuseCount = 0;
                int officialMissionCount = 0;
                
                // Also skip excuse tracking for employees with DesgnHierLevel < 4
                if (employee.DesgnHierLevel >= 4)
                {
                    foreach (var excuse in empExcuses)
                    {
                        if (excuse.TimeFrom.HasValue && excuse.TimeTo.HasValue)
                        {
                            var excuseDate = excuse.OrderDate?.Date;
                            
                            // Check if employee has an approved leave for this day - O(1) lookup
                            bool hasApprovedLeave = excuseDate.HasValue && leaveDates.Contains(excuseDate.Value);
                            
                            // Skip excuse tracking if employee has an approved leave for this day
                            if (hasApprovedLeave)
                            {
                                continue;
                            }
                            
                            double duration = (excuse.TimeTo.Value - excuse.TimeFrom.Value).TotalHours;
                            
                            if (excuse.ReqType == 1) // استئذان (Excuse)
                            {
                                excuseHours += duration;
                                excuseCount++;
                            }
                            else if (excuse.ReqType == 2) // تأخير (Late)
                            {
                                lateHours += duration;
                                lateCount++;
                            }
                            else if (excuse.ReqType == 3) // مهمة رسمية (Official mission)
                            {
                                officialMissionCount++;
                            }
                        }
                    }
                }
                
                // Calculate total work days with attendance
                int workDays = empWorkHours.Count;

                if(employee.DesgnHierLevel < 4)
                {
                    workDays = totalWorkdays;
                }
                
                // Add employee stats to department data
                var empStats = new EmployeeAttendanceStats
                {
                    EmpNo = empNo,
                    EmpName = employee.EmpNameA,
                    WorkDays = workDays,
                    WorkHours = actualWorkHours,
                    OvertimeHours = overtimeHours,
                    LateHours = lateHours,
                    LateCount = lateCount,
                    ExcuseHours = excuseHours,
                    ExcuseCount = excuseCount,
                    OfficialMissionCount = officialMissionCount,
                    MinorLateCount = minorLateCount,
                    ModerateLateCount = moderateLateCount,
                    SevereLateCount = severeLateCount,
                    LeaveDaysCount = leaveDaysCount
                };
                
                deptData.EmployeeStats.Add(empStats);
                
                // Add to department totals
                deptData.ActualWorkHours += actualWorkHours;
                deptData.OvertimeHours += overtimeHours;
                deptData.LateHours += lateHours;
                deptData.ExcuseHours += excuseHours;
                deptData.ExcuseCount += excuseCount;
                deptData.LateCount += lateCount;
                deptData.OfficialMissionCount += officialMissionCount;
            }
            
            // Calculate compliance rate
            if (deptData.ExpectedWorkHours > 0)
            {
                deptData.ComplianceRate = deptData.ActualWorkHours / deptData.ExpectedWorkHours * 100;
            }
            
            departmentsData.Add(deptData);
        }
        
        // Sort departments by compliance rate ascending (worst performers first)
        departmentsData = departmentsData.OrderBy(d => d.ComplianceRate).ToList();
        
        // Store departments data in the view model
        _v.AttendanceReport = departmentsData;
        
        // Prepare data for charts
        PrepareChartData(departmentsData);
        
        // Get departments and DGs for filter dropdowns
        ViewBag.Departments = _db.TDeptCode.OrderBy(d => d.DeptDespA).ToList();
        ViewBag.DGs = _db.TdgCodes.OrderBy(d => d.DgDespA).ToList();
        
        return View(_v);
    }
    
    private void PrepareChartData(List<AttendanceReportData> departmentsData)
    {
        // Prepare department names for chart labels
        ViewBag.DeptLabels = string.Join(",", departmentsData.Select(d => $"'{d.DepartmentName}'"));
        
        // Prepare compliance rate data
        ViewBag.ComplianceRates = string.Join(",", departmentsData.Select(d => d.ComplianceRate.ToString("F1")));
        
        // Prepare work hours vs expected hours
        ViewBag.ActualHours = string.Join(",", departmentsData.Select(d => d.ActualWorkHours.ToString("F1")));
        ViewBag.ExpectedHours = string.Join(",", departmentsData.Select(d => d.ExpectedWorkHours.ToString("F1")));
        
        // Prepare late and excuse hours data
        ViewBag.LateHours = string.Join(",", departmentsData.Select(d => d.LateHours.ToString("F1")));
        ViewBag.ExcuseHours = string.Join(",", departmentsData.Select(d => d.ExcuseHours.ToString("F1")));
        
        // Prepare leave and excuse count data
        ViewBag.ExcuseCount = string.Join(",", departmentsData.Select(d => d.ExcuseCount.ToString()));
        ViewBag.LeaveData = string.Join(",", departmentsData.Select(d => d.EmployeeStats.Sum(e => e.LateCount).ToString()));
        
        // Calculate totals
        ViewBag.TotalEmployees = departmentsData.Sum(d => d.TotalEmployees);
        ViewBag.TotalActualHours = departmentsData.Sum(d => d.ActualWorkHours);
        ViewBag.TotalExpectedHours = departmentsData.Sum(d => d.ExpectedWorkHours);
        ViewBag.TotalLateHours = departmentsData.Sum(d => d.LateHours);
        ViewBag.TotalExcuseHours = departmentsData.Sum(d => d.ExcuseHours);
        ViewBag.TotalOvertimeHours = departmentsData.Sum(d => d.OvertimeHours);
        ViewBag.TotalExcuseCount = departmentsData.Sum(d => d.ExcuseCount);
        ViewBag.TotalLeaveCount = departmentsData.Sum(d => d.EmployeeStats.Sum(e => e.LateCount));
        
        double overallComplianceRate = ViewBag.TotalExpectedHours > 0 
            ? ViewBag.TotalActualHours / ViewBag.TotalExpectedHours * 100 
            : 0;
        ViewBag.OverallComplianceRate = overallComplianceRate.ToString("F1");
        
        // Generate colors for charts
        int count = departmentsData.Count;
        var colors = new List<string>();
        for (int i = 0; i < count; i++)
        {
            var hue = 200 - i * 200 / Math.Max(count, 1); // Blue to red gradient
            colors.Add($"'hsl({hue}, 70%, 60%)'");
        }
        ViewBag.ChartColors = string.Join(",", colors);
    }

}