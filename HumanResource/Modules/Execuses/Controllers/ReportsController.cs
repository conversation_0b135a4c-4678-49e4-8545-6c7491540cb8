using Microsoft.AspNetCore.Mvc;
using HumanResource.Core.Helpers.Attributes;
using System.Threading.Tasks;
using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Modules.Execuses.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Core.Data;
using HumanResource.Modules.Execuses.Models.Enums;

namespace HumanResource.Modules.Execuses.Controllers;

[Area("Execuses")]
[Route("Execuses/Reports")]
public class ReportsController : BaseController
{
    public ReportsViewModel _v;

    public ReportsController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, 
        AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _v = new ReportsViewModel(context, httpContextAccessor, helper);
        _v.Page.Active = "excuses";
        _v.Helper = helper;
        _v.Auth = Auth();
    }

    #region Monthly Summary Report

    [HttpGet("Monthly")]
    public IActionResult Monthly(int? empNo = null, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            _v.Page.Title = "Monthly Excuse Summary";
            _v.Page.Active = "excuses";

            // Set default date range (current month)
            var currentDate = DateTime.Now;
            _v.Page.Filter.DateFrom = new DateTime(currentDate.Year, currentDate.Month, 1);
            _v.Page.Filter.DateTo = _v.Page.Filter.DateFrom.Value.AddMonths(1).AddDays(-1);

            // Handle date filter from query string
            var fromQuery = from;
            var toQuery = to;
            var empNoQuery = empNo;

            if (fromQuery != null)
            {
                _v.Page.Filter.DateFrom = from;
            }

            if (toQuery != null)
            {
                _v.Page.Filter.DateTo = toQuery.Value.Date.Add(new TimeSpan(23, 59, 59));
            }

            // Get filtered data
            var query = _context.Absents.AsQueryable();

            // Apply date filter
            query = query.Where(a => a.OrderDate >= _v.Page.Filter.DateFrom && 
                                    a.OrderDate <= _v.Page.Filter.DateTo);

            // Apply employee filter if specified
            if (empNoQuery != null)
            {
                query = query.Where(a => a.EmpNo == empNoQuery.Value);
            }

            // Get the data
            _v.Absents = query.ToList();

            // Calculate summary statistics
            _v.MonthlySummary = GenerateMonthlySummary(_v.Absents);

            ViewBag.EmpNo = empNoQuery;
            ViewBag.From = fromQuery;
            ViewBag.To = toQuery;

            return View(_v);
        }
        catch (Exception)
        {
            return RedirectToAction("Errorpage404", "Account");
        }
    }

    #endregion

    #region Department Summary Report

    [HttpGet("Department")]
    public IActionResult Department(int? empNo = null, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            _v.Page.Title = "Department Excuse Summary";
            _v.Page.Active = "excuses";

            // Set default date range (current month)
            var currentDate = DateTime.Now;
            _v.Page.Filter.DateFrom = new DateTime(currentDate.Year, currentDate.Month, 1);
            _v.Page.Filter.DateTo = _v.Page.Filter.DateFrom.Value.AddMonths(1).AddDays(-1);

            // Handle date filter from query string
            var fromQuery = from;
            var toQuery = to;
            var empNoQuery = empNo;

            if (fromQuery != null)
            {
                _v.Page.Filter.DateFrom = from;
            }

            if (toQuery != null)
            {
                _v.Page.Filter.DateTo = toQuery.Value.Date.Add(new TimeSpan(23, 59, 59));
            }

            // Get filtered data
            var query = _context.Absents
                .Where(a => a.OrderDate >= _v.Page.Filter.DateFrom && 
                           a.OrderDate <= _v.Page.Filter.DateTo);

            // Apply employee filter if specified
            if (empNoQuery != null)
            {
                query = query.Where(a => a.EmpNo == empNoQuery.Value);
            }

            var absents = query.ToList();

            // Generate department summary
            _v.DepartmentSummary = GenerateDepartmentSummary(absents);

            ViewBag.EmpNo = empNoQuery;
            ViewBag.From = fromQuery;
            ViewBag.To = toQuery;

            return View(_v);
        }
        catch (Exception)
        {
            return RedirectToAction("Errorpage404", "Account");
        }
    }

    #endregion

    #region Detailed Report

    [HttpGet("Detailed")]
    public IActionResult Detailed(int? empNo = null, DateTime? from = null, DateTime? to = null, int? status = null, int? type = null)
    {
        try
        {
            _v.Page.Title = "Detailed Excuse Report";
            _v.Page.Active = "excuses";

            // Set default date range (current month)
            var currentDate = DateTime.Now;

            
            _v.Page.Filter.DateFrom = new DateTime(currentDate.Year, currentDate.Month, 1);
            _v.Page.Filter.DateTo = _v.Page.Filter.DateFrom.Value.AddMonths(1).AddDays(-1);

            // Handle filters from query string
            var fromQuery = from;
            var toQuery = to;
            var statusQuery = status;
            var typeQuery = type;
            var empNoQuery = empNo;

            if (fromQuery != null)
            {
                _v.Page.Filter.DateFrom = from;
            }

            if (toQuery != null)
            {
                _v.Page.Filter.DateTo = toQuery.Value.Date.Add(new TimeSpan(23, 59, 59));
            }

            // Build query
            var query = _context.Absents.AsQueryable();

            // Apply date filter
            query = query.Where(a => a.OrderDate >= _v.Page.Filter.DateFrom && 
                                    a.OrderDate <= _v.Page.Filter.DateTo);

            // Apply status filter
            if (statusQuery != null)
            {
                query = query.Where(a => a.Status == statusQuery.Value);
            }

            // Apply type filter
            if (typeQuery != null)
            {
                query = query.Where(a => a.ReqType == typeQuery.Value);
            }

            // Apply employee filter
            if (empNoQuery != null)
            {
                query = query.Where(a => a.EmpNo == empNoQuery.Value);
            }

            // Get the data
            _v.Absents = query.OrderByDescending(a => a.OrderDate).ToList();

            ViewBag.EmpNo = empNoQuery;
            ViewBag.From = fromQuery;
            ViewBag.To = toQuery;
            ViewBag.Status = statusQuery;
            ViewBag.Type = typeQuery;

            return View(_v);
        }
        catch (Exception)
        {
            return RedirectToAction("Errorpage404", "Account");
        }
    }

    #endregion

    #region Helper Methods

    private List<MonthlySummaryItem> GenerateMonthlySummary(List<Absent> absents)
    {
        var summary = new List<MonthlySummaryItem>();

        // Group by employee
        var employeeGroups = absents.GroupBy(a => a.EmpNo);

        foreach (var group in employeeGroups)
        {
            var item = new MonthlySummaryItem
            {
                EmpNo = group.Key,
                ExcuseHours = CalculateHours(group.Where(a => a.ReqType == (int)AbsentType.Excuse).ToList()),
                DelayHours = CalculateHours(group.Where(a => a.ReqType == (int)AbsentType.Delay).ToList()),
                OfficialMissionHours = CalculateHours(group.Where(a => a.ReqType == (int)AbsentType.Mission).ToList()),
                TotalRequests = group.Count(),
                ApprovedRequests = group.Count(a => a.Status == (int)AbsentStatus.Approved),
                PendingRequests = group.Count(a => a.Status == (int)AbsentStatus.Pending),
                RejectedRequests = group.Count(a => a.Status == (int)AbsentStatus.Rejected)
            };

            summary.Add(item);
        }

        return summary.OrderBy(s => s.EmpNo).ToList();
    }

    private List<DepartmentSummaryItem> GenerateDepartmentSummary(List<Absent> absents)
    {
        var summary = new List<DepartmentSummaryItem>();

        // Create a dictionary mapping employee numbers to department names
        var empDepartments = new Dictionary<int, string>();
        foreach (var emp in _context.VempDtls.Where(e => e.EmpNo > 0).ToList())
        {
            if (emp.EmpNo.HasValue && !string.IsNullOrEmpty(emp.DeptDespA))
            {
                empDepartments[emp.EmpNo.Value] = emp.DeptDespA;
            }
        }

        // Group by department using the employee-department mapping
        var departmentGroups = absents.GroupBy(a => 
            a.EmpNo.HasValue && empDepartments.ContainsKey(a.EmpNo.Value) 
                ? empDepartments[a.EmpNo.Value] 
                : "Unknown Department");

        foreach (var group in departmentGroups)
        {
            var item = new DepartmentSummaryItem
            {
                DepartmentName = group.Key,
                TotalRequests = group.Count(),
                ExcuseRequests = group.Count(a => a.ReqType == (int)AbsentType.Excuse),
                DelayRequests = group.Count(a => a.ReqType == (int)AbsentType.Delay),
                OfficialMissionRequests = group.Count(a => a.ReqType == (int)AbsentType.Mission),
                ApprovedRequests = group.Count(a => a.Status == (int)AbsentStatus.Approved),
                PendingRequests = group.Count(a => a.Status == (int)AbsentStatus.Pending || a.Status == (int)AbsentStatus.NeedsDGApproval),
                RejectedRequests = group.Count(a => a.Status == (int)AbsentStatus.Rejected),
                TotalHours = CalculateHours(group.ToList())
            };

            summary.Add(item);
        }

        return summary.OrderByDescending(s => s.TotalRequests).ToList();
    }

    private double CalculateHours(List<Absent> absents)
    {
        double totalHours = 0;
        foreach (var absent in absents)
        {
            if (absent.TimeTo.HasValue && absent.TimeFrom.HasValue)
            {
                totalHours += (absent.TimeTo.Value - absent.TimeFrom.Value).TotalHours;
            }
        }
        return totalHours;
    }

    #endregion
} 