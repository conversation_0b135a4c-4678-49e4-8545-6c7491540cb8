using Microsoft.AspNetCore.Mvc;
using HumanResource.Core.Helpers.Attributes;
using System.Threading.Tasks;
using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Modules.Execuses.ViewModels;
using HumanResource.Modules.Shared.Controllers;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Core.Data;
using HumanResource.Core.Helpers.Extinctions;
using HumanResource.Core.UI.Models;
using HumanResource.Modules.Execuses.Models.Enums;

namespace HumanResource.Modules.Execuses.Controllers;

[Area("Execuses")]
[Route("Execuses")]
public class ExecusesController : BaseController
{
    public ExecuseViewModel _v;
    private const int MAX_EMPLOYEE_HOURS_PER_MONTH = 7;


    public ExecusesController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _v = new ExecuseViewModel(context, httpContextAccessor, helper);
        _v.Page.Active = "hr";
        _v.Helper = helper;
        _v.Auth = Auth();
    }

    #region Employee Excuses

    [HttpGet("My")]
    public IActionResult My()
    {
        var todayDate = DateTime.Now;
        int currentMonth = todayDate.Month;
        int currentYear = todayDate.Year;

        // Get all excuses for the current employee
        _v.Absents = _context.Absents.Where(a => a.EmpNo == _v.Profile.EmpNo).ToList();
        
        // Get current attendance
        var timeIn = _db.EmpWorkHours
            .Where(e => e.Day == todayDate.Date && e.EmpNo == _v.Profile.EmpNo)
            .FirstOrDefault();

        ViewBag.hasAttendance = timeIn != null;

        if (timeIn != null)
            ViewBag.TimeEnter = Convert.ToDateTime(timeIn.FirstEntry);
        else
            ViewBag.TimeEnter = Convert.ToDateTime("00:00");

        // Calculate total excuse hours for current month by type
        var excuseHours = GetMonthlyExcuseHours(_v.Profile.EmpNo.Value, currentMonth, currentYear, (int)AbsentType.Excuse);
        var delayHours = GetMonthlyExcuseHours(_v.Profile.EmpNo.Value, currentMonth, currentYear, (int)AbsentType.Delay);
        
        // Set values for the view
        ViewBag.ExcuseHours = excuseHours;
        ViewBag.ExcuseRemainingHours = Math.Max(0, MAX_EMPLOYEE_HOURS_PER_MONTH - excuseHours);
        ViewBag.ExcuseExceedLimit = excuseHours > MAX_EMPLOYEE_HOURS_PER_MONTH;
        
        ViewBag.DelayHours = delayHours;
        ViewBag.DelayRemainingHours = Math.Max(0, MAX_EMPLOYEE_HOURS_PER_MONTH - delayHours);
        ViewBag.DelayExceedLimit = delayHours > MAX_EMPLOYEE_HOURS_PER_MONTH;
        
        // For backward compatibility
        ViewBag.MonthlyHours = excuseHours + delayHours;
        ViewBag.RemainingHours = Math.Max(0, MAX_EMPLOYEE_HOURS_PER_MONTH - excuseHours);
        ViewBag.ExceedLimit = excuseHours > MAX_EMPLOYEE_HOURS_PER_MONTH || delayHours > MAX_EMPLOYEE_HOURS_PER_MONTH;

        return View(_v);
    }

    [HttpPost("My/Create")]
    public async Task<IActionResult> MyCreate(Absent absent)
    {
        if (!IsValid(ModelState))
        {
            var response = new
            {
                success = false,
                message = ValidateErrors(ModelState),
                action = "",
            };

            return Json(response);
        }

        try
        {

            if(absent.TimeFrom > absent.TimeTo)
            {
                return Json(new { 
                    success = false, 
                    message = new List<string> { "حدث خطأ في الوقت المدخل" } 
                });
            }
            var todayDate = DateTime.Now;
            
            // Configure dates based on request type
            if (absent.ReqType == (int)AbsentType.Excuse) // Excuse
            {
                // Check if TimeFrom is in the past
                if (absent.TimeFrom < todayDate)
                {
                    return Json(new { 
                        success = false, 
                        message = new List<string> { "الوقت المدخل أقل من الوقت الحالي" } 
                    });
                }
            }
            else if (absent.ReqType == (int)AbsentType.Delay) // Delay
            {
                // Check if we have attendance for today
                var timeIn = _db.EmpWorkHours
                    .Where(e => e.Day == todayDate.Date && e.EmpNo == _v.Profile.EmpNo)
                    .FirstOrDefault();
                    
                if (timeIn == null)
                {
                    return Json(new { 
                        success = false, 
                        message = new List<string> { "لا يوجد حضور لهذا اليوم" } 
                    });
                }

                absent.TimeFrom = new DateTime(todayDate.Year, todayDate.Month, todayDate.Day, 8, 30, 0);
                
                // TimeEntry is provided via hidden field
                absent.TimeTo = timeIn.FirstEntry;
            }

            // Validate working hours
            DateTime startDay = new DateTime(todayDate.Year, todayDate.Month, todayDate.Day, 7, 30, 0);
            DateTime endDay = new DateTime(todayDate.Year, todayDate.Month, todayDate.Day, 15, 30, 0);

            if (absent.TimeFrom < startDay || absent.TimeTo > endDay)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "الوقت يجب أن يكون بين 7:30 صباحًا و 3:30 مساءً" },
                    action = "",
                });
            }

            // Check for overlapping requests
            bool hasOverlap = _context.Absents.Any(e => 
                e.EmpNo == _v.Profile.EmpNo && 
                e.ReqType == absent.ReqType && 
                e.OrderDate.Value.Date == todayDate.Date && 
                (e.TimeFrom <= absent.TimeFrom && e.TimeTo >= absent.TimeFrom || 
                 e.TimeFrom <= absent.TimeTo && e.TimeTo >= absent.TimeTo ||
                 e.TimeFrom >= absent.TimeFrom && e.TimeTo <= absent.TimeTo));

            if (hasOverlap)
            {
                return Json(new
                {
                    success = false,
                    message = new List<string> { "يوجد طلب مع الوقت المدخل" },
                    action = "",
                });
            }

            // Calculate hours for this request
            double requestHours = 0;
            if (absent.TimeTo.HasValue && absent.TimeFrom.HasValue)
            {
                requestHours = (absent.TimeTo.Value - absent.TimeFrom.Value).TotalHours;
            }

            // Ensure the date part is today's date while preserving the time component
            if (absent.TimeFrom.HasValue)
            {
                var timeComponent = absent.TimeFrom.Value.TimeOfDay;
                absent.TimeFrom = todayDate.Date.Add(timeComponent);
            }
            
            if (absent.TimeTo.HasValue)
            {
                var timeComponent = absent.TimeTo.Value.TimeOfDay;
                absent.TimeTo = todayDate.Date.Add(timeComponent);
            }

            // Check if this request would exceed monthly limit for types 1 and 2
            int currentMonth = todayDate.Month;
            int currentYear = todayDate.Year;
            
            // Only apply limit to request types 1 (Excuse) and 2 (Delay)
            if (absent.ReqType == (int)AbsentType.Excuse || absent.ReqType == (int)AbsentType.Delay)
            {
                double currentMonthlyTotal = GetMonthlyExcuseHours(_v.Profile.EmpNo.Value, currentMonth, currentYear, absent.ReqType);
                
                if (currentMonthlyTotal + requestHours > MAX_EMPLOYEE_HOURS_PER_MONTH)
                {
                    string requestTypeName = absent.ReqType == (int)AbsentType.Excuse ? "الاستئذان" : "التأخير";
                    return Json(new
                    {
                        success = false,
                        message = new List<string> { $"لا يمكن إنشاء الطلب. تجاوز الحد الأقصى المسموح لساعات {requestTypeName} الشهرية ({MAX_EMPLOYEE_HOURS_PER_MONTH} ساعات). الساعات المستخدمة: {currentMonthlyTotal:F2}، الساعات المطلوبة: {requestHours:F2}" },
                        action = "",
                    });
                }
            }
            
            // Generate new request number
            if (absent.ReqNo == 0)
            {
                if (_context.Absents.Count() == 0)
                {
                    absent.ReqNo = 1001;
                }
                else
                {
                    absent.ReqNo = _context.Absents.Max(m => m.ReqNo) + 1;
                }
            }

            // Set request details
            absent.EmpNo = _v.Profile.EmpNo;
            absent.OrderDate = todayDate;
            absent.Status = (int)AbsentStatus.Pending; // Pending
            
            _context.Absents.Add(absent);
            _context.SaveChanges();

            await _h.Notify().Create(empNo: absent.EmpNo.Value, title: "تم إنشاء طلب جديد");
            await _h.Notify().Create(empNo: absent.MangerNo.Value, title: " لديك طلب جديد من قبل " + _v.Profile.EmpNameA);

            return Json(new
            {
                success = true,
                message = new List<string> { "تم إنشاء الطلب بنجاح" },
                action = "reload",
            });
        }
        catch (Exception ex)
        {
            return Json(new
            {
                success = false,
                message = new List<string> { $"خطأ: {ex.Message}" },
                action = "",
            });
        }
    }

    #endregion

    #region Manager Approvals

    [HttpGet("Manager")]
    [Can(Right.DepartmentManager, Right.AbsentAdmin)]
    public IActionResult Manager()
    {
        try
        {
            _v.Page.Reload = true;
            _v.Page.Title = "طلبات الاستئذان والتأخير";
            _v.Page.Active = "ManagerApprovals";



            return View(_v);
        }
        catch (Exception)
        {
            return RedirectToAction("Errorpage404", "Account");
        }
    }

    [Can(Right.DepartmentManager, Right.AbsentAdmin)]
    [HttpPost("Manager/Datatable")]
    public IActionResult ManagerDatatable([FromForm] DataTableHelper datatable, string actionFilter = "all")
    {
        try
        {
            // Get action filter from query string if not provided in form
            if (string.IsNullOrEmpty(actionFilter))
            {
                actionFilter = HttpContext.Request.Query["actionFilter"].ToString();
                if (string.IsNullOrEmpty(actionFilter))
                    actionFilter = "all";
            }

            // Build base query
            var query = _context.Absents.Where(m => 
                (m.MangerNo == _v.Profile.EmpNo || Can(Right.AbsentAdmin)));

            // Apply tab filtering
            if (actionFilter == "needs-action")
            {
                query = query.Where(a => a.Status == (int)AbsentStatus.Pending);
            }

            // Apply search filter
            if (!string.IsNullOrEmpty(datatable.Search.Value))
            {
                query = query.Where(f => f.EmpNo.ToString().Contains(datatable.Search.Value) ||
                                        f.ReqNo.ToString().Contains(datatable.Search.Value));
            }

            var total = query.Count();
            var filteredTotal = total;

            // Apply ordering
            var orderedQuery = ManagerApplyOrdering(query, datatable.Order[0].Column, datatable.Order[0].Dir);

            // Apply pagination
            var data = orderedQuery.Skip(datatable.Start).Take(datatable.Length).ToList();

            // Transform data for table based on tab type
            var table = data.Select(request => {
                var employee = _v._h.StaffData(request.EmpNo ?? 0);
                var empName = employee != null ? employee.EmpNameA : request.EmpNo.ToString();
                
                var duration = "";
                if (request.TimeFrom.HasValue && request.TimeTo.HasValue)
                {
                    var hours = (request.TimeTo.Value - request.TimeFrom.Value).TotalHours;
                    duration = _v._h.FormatHour(float.Parse(hours.ToString()));
                }

                var requestTypeBadge = "";
                if (request.ReqType == (int)AbsentType.Excuse)
                {
                    requestTypeBadge = "<span class='badge bg-primary rounded-pill px-3 py-2'>استئذان</span>";
                }
                else if (request.ReqType == (int)AbsentType.Delay)
                {
                    requestTypeBadge = "<span class='badge bg-warning text-dark rounded-pill px-3 py-2'>تأخير</span>";
                }
                else if (request.ReqType == (int)AbsentType.Mission)
                {
                    requestTypeBadge = "<span class='badge bg-info text-dark rounded-pill px-3 py-2'>مهمة رسمية</span>";
                }

                string totalBadge = "";
                string actions = "";
                string status = "";
                string approvalDate = "";
                string reason = "";

               
                    // Calculate monthly total for this request type
                    var month = request.TimeFrom.HasValue ? request.TimeFrom.Value.Month : DateTime.Now.Month;
                    var year = request.TimeFrom.HasValue ? request.TimeFrom.Value.Year : DateTime.Now.Year;
                    
                    var totalTypeHours = 0.0;
                    if (request.EmpNo.HasValue)
                    {
                        var empRequests = _context.Absents.Where(a => 
                            a.EmpNo == request.EmpNo && 
                            a.TimeFrom.HasValue && a.TimeTo.HasValue &&
                            a.TimeFrom.Value.Month == month && 
                            a.TimeFrom.Value.Year == year && 
                            a.ReqType == request.ReqType &&
                            (a.Status == (int)AbsentStatus.Approved || a.Status == (int)AbsentStatus.Pending || a.Status == (int)AbsentStatus.NeedsDGApproval) && 
                            a.MangerApproval != (int)ApprovalStatus.Rejected);
                            
                        foreach (var req in empRequests)
                        {
                            if (req.TimeTo.HasValue && req.TimeFrom.HasValue)
                            {
                                totalTypeHours += (req.TimeTo.Value - req.TimeFrom.Value).TotalHours;
                            }
                        }
                    }
                    
                    var limit = MAX_EMPLOYEE_HOURS_PER_MONTH;
                    var badgeClass = totalTypeHours <= limit ? "bg-success" : "bg-danger";
                    var label = request.ReqType == (int)AbsentType.Mission ? "مهمة رسمية" : 
                              (request.ReqType == (int)AbsentType.Excuse ? "استئذان" : "تأخير");
                    
                    totalBadge = $"<span class='badge {badgeClass} rounded-pill px-3 py-2' data-bs-toggle='tooltip' title='إجمالي ساعات {label} لهذا الشهر'>" +
                                   $"{_v._h.FormatHour(float.Parse(totalTypeHours.ToString()))}";
                    
                    if(request.ReqType != (int)AbsentType.Mission)
                    {
                        totalBadge += $" / {limit} ساعة";
                    }
                    totalBadge += "</span>";
                    

                    actions = $"<div class='btn-group'>" +
                                $"<a href='/Execuses/Manager/Action?ReqNo={request.ReqNo}&value=1' class='btn btn-sm btn-success' onclick='return confirm(\"هل أنت متأكد من موافقتك على هذا الطلب؟\")'>" +
                                $"<i class='fas fa-check'></i></a>" +
                                $"<button class='btn btn-sm btn-danger' data-toggle='modal' data-target='#reject-modal' onclick='showRejectModal({request.ReqNo})'>" +
                                $"<i class='fas fa-times'></i></button></div>";

                    reason = $"<span class='text-truncate d-inline-block' style='max-width: 150px;' data-toggle='tooltip' title='{request.Reason}'>{request.Reason}</span>";

                    if (request.Status == (int)AbsentStatus.NeedsDGApproval)
                    {
                        status = "<span class='badge bg-warning text-dark rounded-pill px-3'><i class='fas fa-clock'></i> يحتاج موافقة المدير العام</span>";
                    }
                    else if (request.Status == (int)AbsentStatus.ApprovedByDg)
                    {
                        status = "<span class='badge bg-success rounded-pill px-3'><i class='fas fa-check'></i> موافقة من قبل المدير العام</span>";
                        actions = "";
                    }
                    else if (request.Status == (int)AbsentStatus.Rejected)
                    {
                        status = "<span class='badge bg-danger rounded-pill px-3'><i class='fas fa-times'></i> مرفوض</span>";
                    }
                    else if (request.Status == (int)AbsentStatus.Approved)
                    {
                        status = "<span class='badge bg-success rounded-pill px-3'><i class='fas fa-check'></i> موافقة</span>";
                        actions = "";
                    }
                

                return new
                {
                    reqNo = $"<span class='fw-bold'>{request.ReqNo}</span>",
                    employee = $"{request.EmpNo} - {empName}",
                    requestType = requestTypeBadge,
                    orderDate = _v._d(request.OrderDate),
                    timeFrom = request.TimeFrom?.ToString("HH:mm") ?? "",
                    timeTo = request.TimeTo?.ToString("HH:mm") ?? "",
                    duration = duration,
                    reason = reason,
                    total = totalBadge,
                    actions = actionFilter == "needs-action" ? actions : status,
                };
            }).ToList();

            var output = new
            {
                datatable.Draw,
                recordsTotal = total,
                recordsFiltered = filteredTotal,
                data = table
            };

            return Json(output);
        }
        catch (Exception)
        {
            return Json(new { error = "حدث خطأ في تحميل البيانات" });
        }
    }

    private IQueryable<Absent> ManagerApplyOrdering(IQueryable<Absent> query, int column, string direction)
    {
        var isAscending = direction == "asc";

        return column switch
        {
            0 => isAscending ? query.OrderBy(o => o.ReqNo) : query.OrderByDescending(o => o.ReqNo),
            1 => isAscending ? query.OrderBy(o => o.EmpNo) : query.OrderByDescending(o => o.EmpNo),
            2 => isAscending ? query.OrderBy(o => o.ReqType) : query.OrderByDescending(o => o.ReqType),
            3 => isAscending ? query.OrderBy(o => o.OrderDate) : query.OrderByDescending(o => o.OrderDate),
            4 => isAscending ? query.OrderBy(o => o.TimeFrom) : query.OrderByDescending(o => o.TimeFrom),
            5 => isAscending ? query.OrderBy(o => o.TimeTo) : query.OrderByDescending(o => o.TimeTo),
            _ => isAscending ? query.OrderBy(o => o.OrderDate) : query.OrderByDescending(o => o.OrderDate)
        };
    }

    [HttpGet("Manager/Action"), HttpPost("Manager/Action")]
    [Can(Right.DepartmentManager, Right.AbsentAdmin)]
    public async Task<IActionResult> ManagerAction(int ReqNo, int value, string? DeclineNote = null)
    {
        try
        {
            // Validate input parameters
            if (ReqNo <= 0 || value != (int)ApprovalStatus.Approved && value != (int)ApprovalStatus.Rejected)
            {
                TempData["Error"] = "حدث خطأ في البيانات المدخلة";
                return RedirectToAction("Manager");
            }

            // Require decline note when rejecting
            if (value == (int)ApprovalStatus.Rejected && string.IsNullOrWhiteSpace(DeclineNote))
            {
                TempData["Error"] = "يجب إدخال سبب الرفض";
                return RedirectToAction("Manager");
            }

            var request = _context.Absents.FirstOrDefault(a => a.ReqNo == ReqNo);
            if (request == null)
            {
                TempData["Error"] = "لم يتم العثور على الطلب";
                return RedirectToAction("Manager");
            }

            // Check if employee exists
            if (!request.EmpNo.HasValue)
            {
                TempData["Error"] = "لم يتم العثور على الموظف";
                return RedirectToAction("Manager");
            }

            request.MangerApproval = value;
            request.MangerApprDate = DateTime.Now;
            request.MangerRemark = DeclineNote;

            // If manager approves and the hours exceed limit, route to DG
            if (value == (int)ApprovalStatus.Approved) // Approved
            {
                int currentMonth = DateTime.Now.Month;
                int currentYear = DateTime.Now.Year;
                double requestHours = 0;

                if (request.TimeTo.HasValue && request.TimeFrom.HasValue)
                {
                    requestHours = (request.TimeTo.Value - request.TimeFrom.Value).TotalHours;
                }

                // Check if request exceeds limit or if it's an official mission (which always needs DG approval)
                bool needsDGApproval = false;

                if (request.ReqType == (int)AbsentType.Mission)
                {
                    // Official missions always need DG approval
                    needsDGApproval = true;
                }
                else if (request.ReqType == (int)AbsentType.Excuse || request.ReqType == (int)AbsentType.Delay)
                {
                    // Check if this request would exceed the limit for its type
                    double typeHours = GetMonthlyExcuseHours(request.EmpNo.Value, currentMonth, currentYear, request.ReqType);
                    if (typeHours > MAX_EMPLOYEE_HOURS_PER_MONTH)
                    {
                        needsDGApproval = true;
                    }
                }

                if (needsDGApproval)
                {
                    // Route to DG for additional approval
                    request.Status = (int)AbsentStatus.NeedsDGApproval; // Needs DG approval
                    TempData["Success"] = "تم الموافقة وإرسال الطلب إلى المدير العام للموافقة النهائية";
                    await _h.Notify().Create(empNo: request.EmpNo.Value, title: "تم إرسال طلب استئذان/تاخير إلى المدير العام للموافقة النهائية");
                }
                else
                {
                    // No DG approval needed, mark as approved
                    request.Status = (int)AbsentStatus.Approved; // Approved
                    TempData["Success"] = "تم الموافقة على الطلب بنجاح";
                    await _h.Notify().Create(empNo: request.EmpNo.Value, title: "تم الموافقة على طلب استئذان/تاخير");
                }
            }
            else if (value == (int)ApprovalStatus.Rejected) // Rejected
            {
                request.Status = (int)AbsentStatus.Rejected; // Rejected
                TempData["Success"] = "تم رفض الطلب بنجاح";
                await _h.Notify().Create(empNo: request.EmpNo.Value, title: "تم رفض طلب استئذان/تاخير");
            }

            _context.Absents.Update(request);
            _context.SaveChanges();

            return RedirectToAction("Manager");
        }
        catch (Exception ex)
        {
            TempData["Error"] = "حدث خطأ في معالجة الطلب: " + ex.Message;
            return RedirectToAction("Manager");
        }
    }

  

    #endregion

    #region DG Approvals

    [HttpGet("Dg")]
    [Can(Right.AbsentDGeneral, Right.AbsentAdmin)]
    public IActionResult DG()
    {
        try
        {
            _v.Page.Reload = true;
            _v.Page.Title = "Excuse Approval requests";
            _v.Page.Active = "DgApprovals";

            // Set date range for filtering
            _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
            _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

            // Handle date filter from query string
            var fromQueryString = HttpContext.Request.Query["from"].ToString();
            var toQueryString = HttpContext.Request.Query["to"].ToString();

            if (!string.IsNullOrEmpty(fromQueryString) && DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }

            if (!string.IsNullOrEmpty(toQueryString) && DateTime.TryParse(toQueryString, out DateTime to))
            {
                _v.Page.Filter.DateTo = to.Date.Add(new TimeSpan(23, 59, 59));
            }

            // Get requests that need DG approval (status 2 or manager approved and either official mission or over limit)
            var requests = _context.Absents
                .Where(m => m.Status == (int)AbsentStatus.NeedsDGApproval && 
                       m.MangerApproval == (int)ApprovalStatus.Approved && 
                       m.OrderDate >= _v.Page.Filter.DateFrom && 
                       m.OrderDate <= _v.Page.Filter.DateTo)
                .ToList();

            // Enhance with monthly totals
            foreach (var request in requests)
            {
                // You could add more information here if needed
            }

            _v.Absents = requests;
            return View(_v);
        }
        catch (Exception)
        {
            return RedirectToAction("Errorpage404", "Account");
        }
    }

    [HttpPost("Dg/Action"), HttpGet("Dg/Action")]
    [Can(Right.AbsentDGeneral, Right.AbsentAdmin)]
    public async Task<IActionResult> DgAction(int ReqNo, int value, string? DeclineNote = null)
    {
        try
        {
            var request = _context.Absents.FirstOrDefault(a => a.ReqNo == ReqNo);
            if (request != null)
            {
                request.DGMangerNo = _v.Profile.EmpNo;
                request.DGMangerApproval = value;
                request.DGMangerApprDate = DateTime.Now;
                request.DGMangerRemark = DeclineNote;

                // Update the status based on DG's decision
                if (value == (int)ApprovalStatus.Approved) // Approved
                {
                    request.Status = (int)AbsentStatus.Approved; // Final Approval
                }
                else if (value == (int)ApprovalStatus.Rejected) // Rejected
                {
                    request.Status = (int)AbsentStatus.Rejected; // Rejected
                }

                _context.Absents.Update(request);
                _context.SaveChanges();

                await _h.Notify().Create(empNo: request.EmpNo.Value, title: "تم الموافقة على طلب استئذان/تاخير");
            }

            return RedirectToAction("DG");
        }
        catch (Exception)
        {
            return RedirectToAction("Errorpage404", "Account");
        }
    }



    #endregion

    #region Helper Methods

    /// <summary>
    /// Calculates the total excuse hours for an employee in a specific month and year for a specific request type
    /// </summary>
    private double GetMonthlyExcuseHours(int empNo, int month, int year, int? reqType = null)
    {
        var query = _context.Absents
            .Where(a => a.EmpNo == empNo && 
                   a.TimeFrom.HasValue && a.TimeTo.HasValue &&
                   a.TimeFrom.Value.Month == month && 
                   a.TimeFrom.Value.Year == year &&
                   (a.Status == (int)AbsentStatus.Approved || a.Status == (int)AbsentStatus.Pending || a.Status == (int)AbsentStatus.NeedsDGApproval || a.Status == (int)AbsentStatus.ApprovedByDg) ); // Don't count rejected requests
                   
        // Filter by request type if specified
        if (reqType.HasValue)
        {
            query = query.Where(a => a.ReqType == reqType.Value);
        }
        // Otherwise get types 1 and 2 (exclude type 3 which has no limit)
        else
        {
            query = query.Where(a => a.ReqType == (int)AbsentType.Excuse || a.ReqType == (int)AbsentType.Delay);
        }
        
        var requests = query.ToList();
        double totalHours = 0;
        
        foreach (var request in requests)
        {
            if (request.TimeTo.HasValue && request.TimeFrom.HasValue)
            {
                totalHours += (request.TimeTo.Value - request.TimeFrom.Value).TotalHours;
            }
        }

        return totalHours;
    }

    /// <summary>
    /// Gets the excuse status as a string with badge formatting
    /// </summary>

    #endregion
}