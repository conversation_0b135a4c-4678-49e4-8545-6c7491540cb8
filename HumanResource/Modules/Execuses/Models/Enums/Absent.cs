namespace HumanResource.Modules.Execuses.Models.Enums
{
    public enum AbsentStatus : int
    {
        Pending = 0,           // معلق - طلب جديد في انتظار موافقة المدير
        Approved = 1,          // معتمد - طل<PERSON> معتمد نهائياً
        NeedsDGApproval = 2,   // يحتاج موافقة المدير العام - معتمد من المدير المباشر ولكن يحتاج موافقة المدير العام
        Rejected = 3,           // مرفوض - طلب مرفوض من المدير المباشر أو المدير العام
        ApprovedByDg = 6,       // معتمد من قبل المدير العام
    }

    public enum AbsentType : int
    {
        Excuse = 1, // استئذان
        Delay = 2, // تأخير
        Mission = 3, // مهمة
    }

    public enum ApprovalStatus : int
    {
        Pending = 0,           // معلق - في انتظار الموافقة
        Approved = 1,          // معتمد - تم الموافقة
        Rejected = 2           // مرفوض - تم الرفض
    }

}
