﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Execuses.Models.Entities;

[Table("TLEAVE_ABSENT")]
public partial class Absent
{
    [Key]
    [Column("REQ_NO")]
    //[Precision(10)]
    public int ReqNo { get; set; }

    [Column("REQ_TYPE")]
    //[Precision(10)]
    public int? ReqType { get; set; }

    [Column("EMP_NO")]
   // [Precision(10)]
    public int? EmpNo { get; set; }

    [Column("TIME_FROM")]
    //[Precision(7)]
    public DateTime? TimeFrom { get; set; }

    [Column("TIME_TO")]
   // [Precision(7)]
    public DateTime? TimeTo { get; set; }

    [Column("REASON")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public string? Reason { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

    [Column("STATUS")]
    //[Precision(10)]
    public int? Status { get; set; }

    [Column("MANGER_NO")]
    [Precision(10)]
    public int? MangerNo { get; set; }

    [Column("MANGER_APPROVAL")]
   // [Precision(10)]
    public int? MangerApproval { get; set; }

    [Column("MANGER_APPR_DATE")]
    public DateTime? MangerApprDate { get; set; }

    [Column("ORDER_DATE")]
    //[Precision(7)]
    public DateTime? OrderDate { get; set; }

    [NotMapped]
    public DateTime? TimeEntry { get; set; }


    [Column("MANGER_REMARK")]
    //[Precision(7)]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public string? MangerRemark { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.


    [Column("DGMANGER_APPROVAL")]
    // [Precision(10)]
    public int? DGMangerApproval { get; set; }

    [Column("DGMANGER_APPR_DATE")]
   
    public DateTime? DGMangerApprDate { get; set; }

    [Column("DGMANGER_REMARK")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public string? DGMangerRemark { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
  
    [Column("DGMANGER_NO")]
    [Precision(10)]
    public int? DGMangerNo { get; set; }
}
