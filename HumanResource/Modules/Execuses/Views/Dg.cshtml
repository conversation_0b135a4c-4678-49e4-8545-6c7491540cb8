@using HumanResource.Modules.Execuses.ViewModels
@using HumanResource.Modules.Execuses.Models.Enums
@model ExecuseViewModel

@Html.Partial("_DgAppTabs")

    <!-- Notifications -->
    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i> @TempData["Success"]
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i> @TempData["Error"]
        </div>
    }



    <!-- Tab Content -->
    <div class="row">
        <div class="col-12">

                    <div class="card">
                        <div class="">
                            <div class="table-responsive">
                                <table class="table table-hover datatable" id="dg-approval-table">
                                    <thead class="bg-primary">
                                        <tr>
                                            <th>@Model._l("رقم الطلب")</th>
                                            <th>@Model._l("الموظف")</th>
                                            <th>@Model._l("نوع الطلب")</th>
                                            <th>@Model._l("التاريخ")</th>
                                            <th>@Model._l("من")</th>
                                            <th>@Model._l("إلى")</th>
                                            <th>@Model._l("المدة")</th>
                                            <th>@Model._l("الاجمالي الشهري")</th>
                                            <th>@Model._l("السبب")</th>
                                            <th>@Model._l("المسؤول")</th>
                                            <th>@Model._l("الاجراءات")</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var request in Model.Absents)
                                        {
                                            <tr>
                                                <td><span class="fw-bold">@request.ReqNo</span></td>
                                                <td>
                                                    @{
                                                        var employee = Model._h.StaffData(request.EmpNo);
                                                        var empName = employee != null ? employee.EmpNameA : request.EmpNo.ToString();
                                                    }
                                                    @empName
                                                </td>
                                                <td>
                                                    @if (request.ReqType == (int)AbsentType.Excuse)
                                                    {
                                                        <span class="badge bg-primary rounded-pill px-3 py-2">@Model._l("الاستئذان")</span>
                                                    }
                                                    else if (request.ReqType == (int)AbsentType.Delay)
                                                    {
                                                        <span class="badge bg-warning text-dark rounded-pill px-3 py-2">@Model._l("التأخير")</span>
                                                    }
                                                    else if (request.ReqType == (int)AbsentType.Mission)
                                                    {
                                                        <span class="badge bg-info text-dark rounded-pill px-3 py-2">@Model._l("المهمة الرسمية")</span>
                                                    }
                                                </td>
                                                <td>@Model._d(request.OrderDate)</td>
                                                <td>@request.TimeFrom.Value.ToString("HH:mm")</td>
                                                <td>@request.TimeTo.Value.ToString("HH:mm")</td>
                                                <td>
                                                    @{
                                                        var duration = "";
                                                        if (request.TimeFrom.HasValue && request.TimeTo.HasValue)
                                                        {
                                                            var hours = (request.TimeTo.Value - request.TimeFrom.Value).TotalHours;
                                                            duration = Model._h.FormatHour(float.Parse(hours.ToString()));
                                                        }
                                                    }
                                                    @duration
                                                </td>
                                                <td>
                                                    @{
                                                        var month = request.TimeFrom.HasValue ? request.TimeFrom.Value.Month : DateTime.Now.Month;
                                                        var year = request.TimeFrom.HasValue ? request.TimeFrom.Value.Year : DateTime.Now.Year;
                                                        var totalTypeHours = 0.0;
                                                        
                                                        if (request.EmpNo.HasValue)
                                                        {
                                                            var empRequests = Model.Absents.Where(a => 
                                                                a.EmpNo == request.EmpNo && 
                                                                a.TimeFrom.HasValue && a.TimeTo.HasValue &&
                                                                a.TimeFrom.Value.Month == month && 
                                                                a.TimeFrom.Value.Year == year && 
                                                                a.ReqType == request.ReqType &&
                                                                (a.Status == 1 || a.Status == 0 || a.Status == 2) && 
                                                                a.MangerApproval != 2);
                                                                
                                                            foreach (var req in empRequests)
                                                            {
                                                                if (req.TimeTo.HasValue && req.TimeFrom.HasValue)
                                                                {
                                                                    totalTypeHours += (req.TimeTo.Value - req.TimeFrom.Value).TotalHours;
                                                                }
                                                            }
                                                        }
                                                        
                                                        var limit = 7;
                                                        var overLimit = request.ReqType != 3 && totalTypeHours > limit;
                                                        var badgeClass = overLimit ? "bg-danger" : "bg-success";
                                                        var label = request.ReqType == 3 ? Model._l("مهمة رسمية") : 
                                                                  (request.ReqType == 1 ? Model._l("استئذان") : Model._l("تأخير"));
                                                    }
                                                    <span class="badge @badgeClass rounded-pill px-3 py-2" data-toggle="tooltip" 
                                                          title="@Model._l("إجمالي ساعات") @label @Model._l("لهذا الشهر")">
                                                        @Model._h.FormatHour(float.Parse(totalTypeHours.ToString())) 
                                                        @if(request.ReqType != 3)
                                                        {
                                                            <span>/ @limit @Model._l("ساعة")</span>
                                                        }
                                                        else
                                                        {
                                                            <span>(@Model._l("بدون حد"))</span>
                                                        }
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="text-truncate d-inline-block" style="max-width: 150px;" data-toggle="tooltip" title="@request.Reason">
                                                        @request.Reason
                                                    </span>
                                                </td>
                                                <td>
                                                    @{
                                                        var manager = Model._h.StaffData(request.MangerNo ?? 0);
                                                        var managerName = manager != null ? manager.EmpNameA : request.MangerNo.ToString();
                                                    }
                                                    <span data-toggle="tooltip" title="@Model._l("تم الموافقة عليه في") @Model._dt(request.MangerApprDate)">
                                                        @managerName
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="/Execuses/DgAction?ReqNo=@request.ReqNo&value=1" class="btn btn-sm btn-success" onclick="return confirm('@Model._l("هل أنت متأكد من موافقتك على هذا الطلب؟")')">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-danger" data-toggle="modal" data-target="#<EMAIL>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                        
                                                        <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#<EMAIL>">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>

                                                    <!-- Reject Modal -->
                                                    <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>-label" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered">
                                                            <div class="modal-content">
                                                                <form action="/Execuses/DgAction" method="post">
                                                                    <div class="modal-header">
                                                                        <h3 class="modal-title" id="<EMAIL>-label">@Model._l("رفض الطلب")</h3>
                                                                    </div>
                                                                    <div class="modal-body">
                                                                        <input type="hidden" name="ReqNo" value="@request.ReqNo" />
                                                                        <input type="hidden" name="value" value="2" />
                                                                        <div class="mb-3">
                                                                            <label for="DeclineNote" class="form-label">@Model._l("سبب الرفض")</label>
                                                                            <textarea class="form-control" id="DeclineNote" name="DeclineNote" rows="3" required></textarea>
                                                                        </div>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("إلغاء")</button>
                                                                        <button type="submit" class="btn btn-danger">@Model._l("تأكيد الرفض")</button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- Details Modal -->
                                                    <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>-label" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="<EMAIL>-label">@Model._l("تفاصيل الطلب")</h5>
                                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <div class="card mb-3 border-primary">
                                                                        <div class="card-header bg-primary text-white">
                                                                            <h6 class="mb-0">@Model._l("معلومات الموظف")</h6>
                                                                        </div>
                                                                        <div class="card-body">
                                                                            <div class="row">
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("الموظف"):</p>
                                                                                    <p class="mb-3 fw-bold">@empName</p>
                                                                                </div>
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("القسم"):</p>
                                                                                    <p class="mb-3 fw-bold">
                                                                                        @(employee != null ? employee.DeptDespA : "-")
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="card mb-3 border-info">
                                                                        <div class="card-header bg-info text-white">
                                                                            <h6 class="mb-0">@Model._l("معلومات الطلب")</h6>
                                                                        </div>
                                                                        <div class="card-body">
                                                                            <div class="row mb-3">
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("نوع الطلب"):</p>
                                                                                    <p class="mb-0 fw-bold">
                                                                                        @(request.ReqType == (int)AbsentType.Excuse ? Model._l("الاستئذان") : 
                                                                                        request.ReqType == (int)AbsentType.Delay ? Model._l("التأخير") : 
                                                                                        request.ReqType == (int)AbsentType.Mission ? Model._l("المهمة الرسمية") : "")
                                                                                    </p>
                                                                                </div>
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("تاريخ الطلب"):</p>
                                                                                    <p class="mb-0 fw-bold">@Model._d(request.OrderDate)</p>
                                                                                </div>
                                                                            </div>
                                                                            
                                                                            <div class="row mb-3">
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("من"):</p>
                                                                                    <p class="mb-0 fw-bold">@Model._dt(request.TimeFrom)</p>
                                                                                </div>
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("إلى"):</p>
                                                                                    <p class="mb-0 fw-bold">@Model._dt(request.TimeTo)</p>
                                                                                </div>
                                                                            </div>
                                                                            
                                                                            <div class="row mb-3">
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("المدة"):</p>
                                                                                    <p class="mb-0 fw-bold">@duration</p>
                                                                                </div>
                                                                                <div class="col-6">
                                                                                                                                                                        <p class="mb-1 text-muted">@Model._l("الاجمالي الشهري"):</p>
                                                                                    <p class="mb-0 fw-bold @(overLimit ? "text-danger" : "text-success")">
                                                                                        @Model._h.FormatHour(float.Parse(totalTypeHours.ToString()))
                                                                                        @if(request.ReqType != 3)
                                                                                        {
                                                                                            <span>/ @limit @Model._l("ساعة")</span>
                                                                                        }
                                                                                        else
                                                                                        {
                                                                                            <span>(@Model._l("بدون حد"))</span>
                                                                                        }
                                                                                        @if(overLimit)
                                                                                        {
                                                                                            <i class="fas fa-exclamation-triangle ms-1 text-danger"></i>
                                                                                        }
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                            
                                                                            <div class="row">
                                                                                <div class="col-12">
                                                                                    <p class="mb-1 text-muted">@Model._l("السبب"):</p>
                                                                                    <p class="mb-0">@request.Reason</p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    
                                                                    <div class="card border-success">
                                                                        <div class="card-header bg-success text-white">
                                                                            <h6 class="mb-0">@Model._l("موافقة المدير")</h6>
                                                                        </div>
                                                                        <div class="card-body">
                                                                            <div class="row mb-3">
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("المدير"):</p>
                                                                                    <p class="mb-0 fw-bold">@managerName</p>
                                                                                </div>
                                                                                <div class="col-6">
                                                                                    <p class="mb-1 text-muted">@Model._l("تاريخ الموافقة"):</p>
                                                                                    <p class="mb-0 fw-bold">@Model._dt(request.MangerApprDate)</p>
                                                                                </div>
                                                                            </div>
                                                                            
                                                                            @if(!string.IsNullOrEmpty(request.MangerRemark))
                                                                            {
                                                                                <div class="row">
                                                                                    <div class="col-12">
                                                                                        <p class="mb-1 text-muted">@Model._l("ملاحظات المدير"):</p>
                                                                                        <p class="mb-0">@request.MangerRemark</p>
                                                                                    </div>
                                                                                </div>
                                                                            }
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("إغلاق")</button>
                                                                    <a href="/Execuses/DgAction?ReqNo=@request.ReqNo&value=1" class="btn btn-success">
                                                                        <i class="fas fa-check me-1"></i>@Model._l("موافقة")
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

         
    </div>

@section Scripts {
<script>
    $(document).ready(function() {
        // Initialize DataTables

        
        // Auto-close alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Initialize tooltips
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>

<style>
    .badge {
        font-weight: 500;
    }
    
    .table th {
        white-space: nowrap;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
</style>
} 