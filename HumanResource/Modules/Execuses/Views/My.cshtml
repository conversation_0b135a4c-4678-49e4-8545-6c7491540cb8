@using HumanResource.Modules.Execuses.ViewModels
@using HumanResource.Modules.Execuses.Models.Enums
@model ExecuseViewModel


    <div class="d-flex justify-content-between align-items-center mb-2">
        <h3 class="card-title mb-0">@Model._l("الاستئذانات")</h3>
        <button data-toggle="modal" data-target="#new-request-modal" class="btn btn-primary rounded-3">
            <i class="fa fa-plus"></i> @Model._l("طلب جديد")
        </button>
    </div>

    <div class="row ">
        <div class="col-12 col-md-6 ">
            <div class="card shadow">
                <div class="card-body">
                    <h5 class="card-title">@Model._l("الساعات الشهرية")</h5>
                    
                    <!-- Excuse Hours -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span><i class="fas fa-sign-out-alt text-primary mr-2"></i> @Model._l("الاستئذان")</span>
                            <span class="text-muted">@Model._l("الحد الشهري"): 7 @Model._l("ساعة")</span>
                        </div>
                        <div class="progress rounded-pill" style="height: 20px;">
                            @{
                                var excuseHours = ViewBag.ExcuseHours ?? 0;
                                var maxHours = 7;
                                var excusePercentage = Math.Min((excuseHours / maxHours) * 100, 100);
                                var excuseProgressClass = "bg-success";
                                
                                if (excusePercentage > 75 && excusePercentage < 100)
                                {
                                    excuseProgressClass = "bg-warning";
                                }
                                else if (excusePercentage >= 100)
                                {
                                    excuseProgressClass = "bg-danger";
                                }
                            }
                            <div class="progress-bar rounded-pill @excuseProgressClass" role="progressbar" style="width: @excusePercentage%;" aria-valuenow="@excuseHours" aria-valuemin="0" aria-valuemax="@maxHours">
                                @Model._h.FormatHour(float.Parse(excuseHours.ToString())) @Model._l("ساعة")
                            </div> 
                        </div>
                        <div class="mt-1">
                            <span class="text-muted">@Model._l("الساعات المتبقية"):</span>
                            <span class="fw-bold">@Model._h.FormatHour(float.Parse(ViewBag.ExcuseRemainingHours.ToString()))</span>
                        </div>
                        @if (ViewBag.ExcuseExceedLimit == true)
                        {
                            <div class="alert alert-warning mt-2 py-2" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @Model._l("لقد قمت بتجاوز الحد الشهري المسموح به للاستئذانات")
                            </div>
                        }
                    </div>
                    
                    <!-- Delay Hours -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span><i class="fas fa-clock text-warning mr-2"></i> @Model._l("التأخير")</span>
                            <span class="text-muted">@Model._l("الحد الشهري"): 7 @Model._l("ساعة")</span>
                        </div>
                        <div class="progress rounded-pill" style="height: 20px;">
                            @{
                                var delayHours = ViewBag.DelayHours ?? 0;
                                var delayPercentage = Math.Min((delayHours / maxHours) * 100, 100);
                                var delayProgressClass = "bg-success";
                                
                                if (delayPercentage > 75 && delayPercentage < 100)
                                {
                                    delayProgressClass = "bg-warning";
                                }
                                else if (delayPercentage >= 100)
                                {
                                    delayProgressClass = "bg-danger";
                                }
                            }
                            <div class="progress-bar rounded-pill @delayProgressClass" role="progressbar" style="width: @delayPercentage%;" aria-valuenow="@delayHours" aria-valuemin="0" aria-valuemax="@maxHours">
                                @Model._h.FormatHour(float.Parse(delayHours.ToString())) @Model._l("ساعة")
                            </div> 
                        </div>
                        <div class="mt-1">
                            <span class="text-muted">@Model._l("الساعات المتبقية"):</span>
                            <span class="fw-bold">@Model._h.FormatHour(float.Parse(ViewBag.DelayRemainingHours.ToString()))</span>
                        </div>
                        @if (ViewBag.DelayExceedLimit == true)
                        {
                            <div class="alert alert-warning mt-2 py-2" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @Model._l("لقد قمت بتجاوز الحد الشهري المسموح به للتأخير")
                            </div>
                        }
                    </div>
                    
                 

                    <div class="mt-3">
                        @if(ViewBag.hasAttendance)
                        {
                            <span class="text-muted">@Model._l("وقت  الدخول"):</span>
                            <span class="fw-bold">@ViewBag.TimeEnter.ToString("hh:mm tt")</span>
                        }else{
                            <span class="text-muted">@Model._l("لا يوجد وقت دخول"):</span>
                            
                        }
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-6">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">@Model._l("أنواع الطلبات")</h5>
                    <div class="row mt-3">
                        <div class="col-12 col-md-4 ">
                            <div class="card text-center bg-light">
                                <div class="card-body py-3">
                                    <i class="fas fa-sign-out-alt fa-2x"></i>
                                    <h6 class="mb-0">@Model._l("الاستئذان")</h6>
                                    <p class="text-muted small">@Model._l("الاستئذان خلال ساعات العمل")</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-4  ">
                            <div class="card  text-center bg-light border-2 border-primary">
                                <div class="py-3 card-body ">
                                    <i class="fas fa-clock fa-2x"></i>
                                    <h6 class="mb-0">@Model._l("التأخير")</h6>
                                    <p class="text-muted small">@Model._l("الوصول إلى العمل بعد الوقت المحدد")</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-4 ">
                            <div class="card text-center bg-light">
                                <div class="card-body py-3">
                                    <i class="fas fa-briefcase fa-2x"></i>
                                    <h6 class="mb-0">@Model._l("المهام الرسمية")</h6>
                                    <p class="text-muted small">@Model._l("العمل خارج المكتب")</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="excuses-table">
                            <thead class="bg-primary text-white">
                                <tr>
                                    <th scope="col">@Model._l("الرقم")</th>
                                    <th scope="col">@Model._l("النوع")</th>
                                    <th scope="col">@Model._l("تاريخ الطلب")</th>
                                    <th scope="col">@Model._l("من")</th>
                                    <th scope="col">@Model._l("إلى")</th>
                                    <th scope="col">@Model._l("المدة")</th>
                                    <th scope="col">@Model._l("السبب")</th>
                                    <th scope="col">@Model._l("الحالة")</th>
                                    <th scope="col">@Model._l("الملاحظات")</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var request in Model.Absents.OrderByDescending(r => r.OrderDate))
                                {
                                    <tr>
                                        <td>@request.ReqNo</td>
                                        <td>
                                            @if (request.ReqType == (int)AbsentType.Excuse)
                                            {
                                                <span class="badge bg-primary rounded-pill px-3 py-2">@Model._l("الاستئذان")</span>
                                            }
                                            else if (request.ReqType == (int)AbsentType.Delay)
                                            {
                                                <span class="badge bg-warning rounded-pill px-3 py-2">@Model._l("التأخير")</span>
                                            }
                                            else if (request.ReqType == (int)AbsentType.Mission)
                                            {
                                                <span class="badge bg-info rounded-pill px-3 py-2">@Model._l("المهام الرسمية")</span>
                                            }
                                        </td>
                                        <td>@Model._d(request.OrderDate)</td>
                                        <td>@request.TimeFrom.Value.ToString("HH:mm")</td>
                                        <td>@request.TimeTo.Value.ToString("HH:mm")</td>
                                        <td>
                                            @{
                                                var duration = "";
                                                if (request.TimeFrom.HasValue && request.TimeTo.HasValue)
                                                {
                                                    var hours = (request.TimeTo.Value - request.TimeFrom.Value).TotalHours;
                                                    duration = Model._h.FormatHour(float.Parse(hours.ToString()));
                                                 
                                                }
                                            }
                                            @duration
                                        </td>
                                        <td>
                                            <span class="text-truncate d-inline-block" style="max-width: 150px;" data-bs-toggle="tooltip" title="@request.Reason">
                                                @request.Reason
                                            </span>
                                        </td>
                                        <td>
                                            @Html.Raw(Model.renderExcuseStatus(request.Status,request.MangerApproval,request.DGMangerApproval))
                                      
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(request.MangerRemark))
                                            {
                                                <span class="text-truncate d-inline-block" style="max-width: 150px;" data-bs-toggle="tooltip" title="@request.MangerRemark">
                                                    @request.MangerRemark
                                                </span>
                                            }
                                            else if (!string.IsNullOrEmpty(request.DGMangerRemark))
                                            {
                                                <span class="text-truncate d-inline-block" style="max-width: 150px;" data-bs-toggle="tooltip" title="@request.DGMangerRemark">
                                                    @request.DGMangerRemark
                                                </span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- New Request Modal -->
<form action="/Execuses/My/Create" method="post" class="ajax">
    <div class="modal fade" id="new-request-modal"  aria-labelledby="new-request-modalLabel" aria-hidden="true">
        <div class="modal-dialog  modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="new-request-modalLabel">@Model._l("طلب جديد")</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="app">
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="form-group mb-3">
                                <label class="form-label fw-bold">@Model._l("نوع الطلب")</label>
                                <div class="row g-2">
                                    <div class="col-md-4">
                                        <div class="custom-control custom-radio">
                                            <input class="custom-control-input" type="radio" v-model="selectedOption" value="1" id="type1" name="ReqType" v-on:change="handleRadioChange(1)" required />
                                            <label class="custom-control-label" for="type1">
                                                <i class="fas fa-sign-out-alt mr-1 text-primary"></i> @Model._l("الاستئذان")
                                            </label>
                                        </div>
                                    </div>
                                    @if(ViewBag.TimeEnter > new DateTime(DateTime.Today.Year,DateTime.Today.Month,DateTime.Today.Day,8,30,0))
                                    {
                                        @if(ViewBag.hasAttendance)
                                        {
                                            <div class="col-md-4">
                                                <div class="custom-control custom-radio">
                                                    <input class="custom-control-input" type="radio" v-model="selectedOption" value="2" id="type2" name="ReqType" v-on:change="handleRadioChange(2)" required />
                                                    <label class="custom-control-label" for="type2">
                                                        <i class="fas fa-clock mr-1 text-warning"></i> @Model._l("التأخير")
                                                    </label>
                                                </div>
                                            </div>
                                        }
                                    }
                                    <div class="col-md-4">
                                        <div class="custom-control custom-radio">
                                            <input class="custom-control-input" type="radio" v-model="selectedOption" value="3" id="type3" name="ReqType" v-on:change="handleRadioChange(3)" required />
                                            <label class="custom-control-label" for="type3">
                                                <i class="fas fa-briefcase mr-1 text-info"></i> @Model._l("المهام الرسمية")
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6" >
                            <div class="form-group mb-3">
                                <label for="timeFrom" class="form-label fw-bold">@Model._l("من")</label>
                                <input type="time" id="timeFrom" v-model="fromTimeValue" :disabled="isFromInputDisabled" class="form-control" required />
                                <input type="hidden" name="TimeFrom" v-model="fromValue" />
                            </div>
                        </div>
                        
                        <div class="col-md-6" >
                            <div class="form-group mb-3">
                                <label for="timeTo" class="form-label fw-bold">@Model._l("إلى")</label>
                                <input type="time" id="timeTo" v-model="toTimeValue" :disabled="isToInputDisabled" class="form-control" required />
                                <input type="hidden" name="TimeTo" v-model="toValue" />
                            </div>
                        </div>

                        <div class="col-12" v-if="calculateDuration > 0 && selectedOption != 3">
                            <div class="alert bg-light border-2 border-primary mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                @Model._l("المدة"): <strong>{{ Math.floor(calculateDuration).toString().padStart(2, '0') }}:{{ Math.round((calculateDuration % 1) * 60).toString().padStart(2, '0') }}</strong>
                               
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="form-group mb-3">
                                <label for="reason" class="form-label fw-bold">@Model._l("السبب")</label>
                                <textarea id="reason" name="Reason" v-model="reason" class="form-control" rows="3" ></textarea>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group mb-3">
                                <label for="manager" class="form-label fw-bold">@Model._l("المسؤول المباشر")</label>
                                <select id="manager" name="MangerNo"  class="form-select select2" >
                                    <option value="" disabled>@Model._l("اختر المسؤول المباشر")</option>
                                    @foreach (var staff in Model._h.Managers(Model.Auth.EmpNo))
                                    {
                                        <option value="@staff.EmpNo">@staff.EmpNameA (@staff.EmpNo)</option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    @if (ViewBag.ExceedLimit == true)
                    {
                        <div class="alert alert-warning mb-0 mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            @Model._l("ملاحظة: لقد قمت بتجاوز الحد الشهري المسموح به للاستئذانات")
                        </div>
                    }
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                        <i class="fas fa-times me-1"></i>@Model._l("إلغاء")
                    </button>
                    <button type="submit" class="btn btn-primary" >
                        <i class="fa fa-save "></i> @Model._l("إرسال ")
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

@section Scripts {
    <script>
        let app = new Vue({
            el: "#app",
            data: {
                selectedOption: 1,
                toValue: '',
                fromValue: '',
                fromTimeValue: '',
                toTimeValue: '',
                isToInputDisabled: false,
                isFromInputDisabled: false,
                selectedManager: '',
                reason: '',
                hasAttendance: @((ViewBag.hasAttendance)?"true":"false"),
                timeEnter: '@ViewBag.TimeEnter.ToString("yyyy-MM-dd HH:mm")'
            },
            computed: {
                calculateDuration() {
                    if (this.fromValue && this.toValue) {
                        const from = new Date(this.fromValue);
                        const to = new Date(this.toValue);
                        const hours = (to - from) / (1000 * 60 * 60);
                        return hours;
                    }
                    return 0;
                },
            },
            methods: {
                handleRadioChange(option) {
                    this.selectedOption = option;
                    const now = new Date();
                    
                    if (option === 1) {
                        // Excuse
                        this.fromValue = moment().format('YYYY-MM-DDTHH:mm');
                        this.fromTimeValue = moment().add(5, 'minutes').format('HH:mm');
                        
                        // Check if entry time is available or valid
                        let entryTime = new Date(this.timeEnter);
                        const isEntryTimeValid = entryTime.getHours() !== 0 || entryTime.getMinutes() !== 0;
                        
                        // If no valid entry time, use current time
                        if (!isEntryTimeValid) {
                            entryTime = new Date();
                        }
                        
                        const baseTime = new Date(entryTime);
                        baseTime.setHours(7, 30, 0); // Set to 7:30 AM
                        
                        if (entryTime > baseTime) {
                            // If entry time is after 7:30, use entry time
                             const defaultEndTime = new Date();
                            defaultEndTime.setHours(14, 30, 0); // 2:30 PM
                            this.toValue = moment(defaultEndTime).format('YYYY-MM-DDTHH:mm');
                            this.toTimeValue = moment(defaultEndTime).format('HH:mm');
                        } else {
                            // Otherwise use 7:30 + 7 hours (2:30 PM)
                            const defaultEndTime = new Date();
                            defaultEndTime.setHours(14, 30, 0); // 2:30 PM
                            this.toValue = moment(defaultEndTime).format('YYYY-MM-DDTHH:mm');
                            this.toTimeValue = moment(defaultEndTime).format('HH:mm');
                        }
                        
                        this.isToInputDisabled = false;
                        this.isFromInputDisabled = false;
                    }
                    else if (option === 2) {
                        // Delay
                        this.fromValue = moment(new Date()).format('YYYY-MM-DDT08:30');
                        this.fromTimeValue = '08:30';
                        this.isToInputDisabled = true;
                        this.isFromInputDisabled = true;
                        
                        // Check if entry time is available or valid
                        const entryTime = new Date(this.timeEnter);
                        const isEntryTimeValid = entryTime.getHours() !== 0 || entryTime.getMinutes() !== 0;
                        
                        if (isEntryTimeValid) {
                            this.toValue = moment(this.timeEnter).format('YYYY-MM-DDTHH:mm');
                            this.toTimeValue = moment(this.timeEnter).format('HH:mm');
                        } else {
                            // If no valid entry time, use current time
                            this.toValue = moment().format('YYYY-MM-DDTHH:mm');
                            this.toTimeValue = moment().format('HH:mm');
                        }
                        
                        
                    }
                    else {
                        // Official Mission
                        this.fromValue = '';
                        this.toValue = '';
                        this.fromTimeValue = '';
                        this.toTimeValue = '';
                        this.isToInputDisabled = false;
                        this.isFromInputDisabled = false;
                    }
                },
                updateFromDateTime() {
                    if (this.fromTimeValue && this.selectedOption > 0) {
                        // Keep the date portion and update only the time
                        let dateTime = this.fromValue ? new Date(this.fromValue) : new Date();
                        let [hours, minutes] = this.fromTimeValue.split(':');
                        dateTime.setHours(parseInt(hours), parseInt(minutes), 0);
                        this.fromValue = moment(dateTime).format('YYYY-MM-DDTHH:mm');
                    }
                },
                updateToDateTime() {
                    if (this.toTimeValue && this.selectedOption > 0) {
                        // Keep the date portion and update only the time
                        let dateTime = this.toValue ? new Date(this.toValue) : new Date();
                        let [hours, minutes] = this.toTimeValue.split(':');
                        dateTime.setHours(parseInt(hours), parseInt(minutes), 0);
                        this.toValue = moment(dateTime).format('YYYY-MM-DDTHH:mm');
                    }
                }
            },
            watch: {
                fromTimeValue: function() {
                    this.updateFromDateTime();
                },
                toTimeValue: function() {
                    this.updateToDateTime();
                }
            },
            mounted() {
                // Initialize tooltips
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl)
                });

                this.selectedOption = 1;
                this.handleRadioChange(1);
            }
        });

        // Initialize DataTable
        $(document).ready(function() {
            createDatatable("#excuses-table");
        });
    </script>
    
    <style>
        .icon-circle {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }
        
        .custom-radio .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .badge {
            font-weight: 500;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        .form-check {
            padding: 10px;
            border-radius: 5px;
            transition: background-color 0.2s;
        }
        
        .form-check:hover {
            background-color: rgba(0,0,0,0.05);
        }
    </style>
} 