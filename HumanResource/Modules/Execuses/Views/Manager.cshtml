@using HumanResource.Modules.Execuses.ViewModels
@model ExecuseViewModel



@Html.Partial("_MangerAppTabs")

<!-- Notifications -->
@if (TempData["Success"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i> @TempData["Success"]
    </div>
}

@if (TempData["Error"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i> @TempData["Error"]
    </div>
}



<!-- Tab Content -->
<div class="row">
    <div class="col-12">

        <div class="card">
            <div class="card-header">
                <div class="d-flex ">

                    <div class="d-flex mx-2">
                        <div class="form-check-inline">
                            <input type="radio" class="form-check-input" id="filter-needs-action" checked
                                name="request-filter" value="needs-action">
                            <label class="form-check-label" for="filter-needs-action">تحتاج إجراء</label>
                        </div>
                        <div class="form-check-inline">
                            <input type="radio" class="form-check-input" id="filter-all" name="request-filter"
                                value="all">
                            <label class="form-check-label" for="filter-all">الكل</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="">
                <div class="table-responsive">
                    <table class="table table-hover" id="table">
                        <thead class="bg-primary">
                            <tr>
                                <th>@Model._l("الرقم")</th>
                                <th>@Model._l("الموظف")</th>
                                <th>@Model._l("نوع الطلب")</th>
                                <th>@Model._l("التاريخ")</th>
                                <th>@Model._l("من")</th>
                                <th>@Model._l("إلى")</th>
                                <th>@Model._l("المدة")</th>
                                <th>@Model._l("السبب")</th>
                                <th>@Model._l("الاجمالي")</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>





    </div>
</div>
<div class="modal fade" id="reject-modal" tabindex="-1" aria-labelledby="reject-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form action="/Execuses/Manager/Action" method="post">
                <div class="modal-header bg-danger">
                    <h3 class="modal-title" id="reject-modal-label">@Model._l("رفض الطلب")</h3>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="ReqNo" value="" />
                    <input type="hidden" name="value" value="2" />
                    <div class="mb-3">
                        <label for="DeclineNote" class="form-label">@Model._l("سبب الرفض")</label>
                        <textarea class="form-control" id="DeclineNote" name="DeclineNote" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">@Model._l("إلغاء")</button>
                    <button type="submit" class="btn btn-danger">@Model._l("رفض")</button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
    $(document).ready(function () {
        // Initialize Pending Requests DataTable
        var table = createDatatable('#table', true, {
            ajax: {
                url: "/Execuses/Manager/Datatable",
                type: "POST",
                data: function (d) {
                    d.tabType = "pending";
                    d.actionFilter = $('input[name="request-filter"]:checked').val();
                }
            },
            ordering: true,
            columns: [
                { "data": "reqNo", "title": "@Model._l("الرقم")" },
                { "data": "employee", "title": "@Model._l("الموظف")" },
                { "data": "requestType", "title": "@Model._l("نوع الطلب")" },
                { "data": "orderDate", "title": "@Model._l("التاريخ")" },
                { "data": "timeFrom", "title": "@Model._l("من")" },
                { "data": "timeTo", "title": "@Model._l("إلى")" },
                { "data": "duration", "title": "@Model._l("المدة")" },
                { "data": "reason", "title": "@Model._l("السبب")" },
                { "data": "total", "title": "@Model._l("الاجمالي")" },
                { "data": "actions", "title": "" },
            ]
        });



        // Handle radio button filtering for pending tab
        $('input[name="request-filter"]').on('change', function () {
            // Reload the pending datatable with the new filter
            table.ajax.reload();
        });



        // Auto-close alerts after 5 seconds
        setTimeout(function () {
            $('.alert').fadeOut('slow');
        }, 5000);

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });


    function showRejectModal(reqNo) {
        $('#reject-modal').modal('show');
        $('#reject-modal input[name="ReqNo"]').val(reqNo);
    }
</script>

<style>
    .badge {
        font-weight: 500;
    }

    .table th {
        white-space: nowrap;
    }

    .table-responsive {
        overflow-x: auto;
    }
</style>