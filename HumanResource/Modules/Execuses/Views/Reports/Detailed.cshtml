@model HumanResource.Modules.Execuses.ViewModels.ReportsViewModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
                <h3 class="">
                         تقرير تفصيلي للاستئذان
                    </h3>
            <div class="card">

                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="get" class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="from" class="form-control" 
                                           value="@Model.Page.Filter.DateFrom.Value.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="to" class="form-control" 
                                           value="@Model.Page.Filter.DateTo.Value.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">رقم الموظف</label>
                                    <input type="number" name="empNo" class="form-control" 
                                           value="@ViewBag.EmpNo" placeholder="اختياري" />
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">نوع الطلب</label>
                                    <select name="type" class="form-control">
                                        <option value="">الكل</option>
                                        <option value="1" selected="@(ViewBag.Type == 1)">استئذان</option>
                                        <option value="2" selected="@(ViewBag.Type == 2)">تأخير</option>
                                        <option value="3" selected="@(ViewBag.Type == 3)">مهمة رسمية</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">الحالة</label>
                                    <select name="status" class="form-control">
                                        <option value="">الكل</option>
                                        <option value="0" selected="@(ViewBag.Status == 0)">قيد المراجعة</option>
                                        <option value="1" selected="@(ViewBag.Status == 1)">موافق</option>
                                        <option value="2" selected="@(ViewBag.Status == 2)">يحتاج موافقة المدير العام</option>
                                        <option value="3" selected="@(ViewBag.Status == 3)">مرفوض</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>@Model.Absents.Count</h5>
                                    <p>إجمالي الطلبات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>@Model.Absents.Count(a => a.Status == 1)</h5>
                                    <p>الطلبات المعتمدة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5>@Model.Absents.Count(a => a.Status == 0 || a.Status == 2)</h5>
                                    <p>قيد المراجعة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5>@Model.Absents.Count(a => a.Status == 3)</h5>
                                    <p>الطلبات المرفوضة</p>
                                </div>
                            </div>
                        </div>
                    </div>

      

                    <!-- Detailed Reports Table -->
                    
                </div>
                <div class="table-responsive">
                        <table class="table table-striped table-hover" id="detailedTable">
                            <thead class="bg-primary text-white">
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>رقم الموظف</th>
                                    <th>تاريخ الطلب</th>
                                    <th>نوع الطلب</th>
                                    <th>من وقت</th>
                                    <th>إلى وقت</th>
                                    <th>المدة (ساعات)</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Absents)
                                {
                                    var duration = item.TimeTo.HasValue && item.TimeFrom.HasValue 
                                        ? (item.TimeTo.Value - item.TimeFrom.Value).TotalHours 
                                        : 0;
                                    <tr>
                                        <td><strong>@item.ReqNo</strong></td>
                                        <td>@item.EmpNo</td>
                                        <td>@item.OrderDate?.ToString("yyyy-MM-dd")</td>
                                        <td>
                                            <span class="badge @(item.ReqType == 1 ? "bg-primary" : item.ReqType == 2 ? "bg-warning" : "bg-info")">
                                                @Model.GetRequestTypeName(item.ReqType)
                                            </span>
                                        </td>
                                        <td>@item.TimeFrom?.ToString("HH:mm")</td>
                                        <td>@item.TimeTo?.ToString("HH:mm")</td>
                                        <td>
                                           @Model._h.FormatHour((float)duration)
                                            @if (duration > 7 && (item.ReqType == 1 || item.ReqType == 2))
                                            {
                                                <span class="badge bg-danger ms-1">تجاوز الحد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Reason))
                                            {
                                                <span title="@item.Reason">
                                                    @(item.Reason.Length > 30 ? item.Reason.Substring(0, 30) + "..." : item.Reason)
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            @Html.Raw(Model.GetStatusBadge(item.Status, item.MangerApproval, item.DGMangerApproval))
                                        </td>

                                
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Absents.Any())
                    {
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i> لا توجد طلبات للفترة والفلاتر المحددة
                        </div>
                    }
            </div>
        </div>
    </div>
</div>

@section Scripts {

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            createDatatable('#detailedTable');
        });


  
    </script>
} 