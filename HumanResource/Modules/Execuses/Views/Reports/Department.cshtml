@model HumanResource.Modules.Execuses.ViewModels.ReportsViewModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h3 class="">
                         ملخص الاستئذان حسب الدائرة
                    </h3>
            <div class="card">
               
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="from" class="form-control" 
                                           value="@Model.Page.Filter.DateFrom.Value.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="to" class="form-control" 
                                           value="@Model.Page.Filter.DateTo.Value.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>@Model.DepartmentSummary.Count</h5>
                                    <p>عدد الدوائر</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>@Model.DepartmentSummary.Sum(s => s.TotalRequests)</h5>
                                    <p>إجمالي الطلبات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5>@Model.DepartmentSummary.Sum(s => s.TotalHours).ToString("F1")</h5>
                                    <p>إجمالي الساعات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5>@Model.DepartmentSummary.Sum(s => s.ApprovedRequests)</h5>
                                    <p>الطلبات المعتمدة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Department Summary Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="departmentTable">
                            <thead class="bg-primary text-white">
                                <tr>
                                    <th>الدائرة</th>
                                    <th>إجمالي الطلبات</th>
                                    <th>طلبات الاستئذان</th>
                                    <th>طلبات التأخير</th>
                                    <th>المهام الرسمية</th>
                                    <th>المعتمدة</th>
                                    <th>قيد المراجعة</th>
                                    <th>المرفوضة</th>
                                    <th>إجمالي الساعات</th>
                                    <th>معدل الموافقة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.DepartmentSummary)
                                {
                                    var approvalRate = item.TotalRequests > 0 ? (item.ApprovedRequests * 100.0 / item.TotalRequests) : 0;
                                    <tr>
                                        <td><strong>@item.DepartmentName</strong></td>
                                        <td>@item.TotalRequests</td>
                                        <td>
                                            <span class="badge bg-primary">@item.ExcuseRequests</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">@item.DelayRequests</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.OfficialMissionRequests</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">@item.ApprovedRequests</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning">@item.PendingRequests</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">@item.RejectedRequests</span>
                                        </td>
                                        <td><strong>@Model._h.FormatHour((float)item.TotalHours)</strong></td>
                                        <td>
                                            <div class="progress" style="width: 60px;">
                                                <div class="progress-bar @(approvalRate >= 80 ? "bg-success" : approvalRate >= 60 ? "bg-warning" : "bg-danger")" 
                                                     role="progressbar" 
                                                     style="width: @(approvalRate)%">
                                                    @approvalRate.ToString("F0")%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.DepartmentSummary.Any())
                    {
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i> لا توجد بيانات للفترة المحددة
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Department Details Chart -->
@if (Model.DepartmentSummary.Any())
{
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
               
                <div class="card-body" style="height: 800px;">
                    <canvas id="departmentChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            if ($('.table').length > 0) {
                createDatatable('#departmentTable');
            }

            // Create Department Chart
            @if (Model.DepartmentSummary.Any())
            {
                <text>
                var ctx = document.getElementById('departmentChart').getContext('2d');
                var departmentChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: [@Html.Raw(string.Join(",", Model.DepartmentSummary.Select(d => $"'{d.DepartmentName}'")))],
                        datasets: [{
                            label: 'عدد الطلبات',
                            data: [@string.Join(",", Model.DepartmentSummary.Select(d => d.TotalRequests))],
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 205, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)',
                                'rgba(153, 102, 255, 0.8)',
                                'rgba(255, 159, 64, 0.8)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)',
                                'rgba(54, 162, 235, 1)',
                                'rgba(255, 205, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(153, 102, 255, 1)',
                                'rgba(255, 159, 64, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'توزيع الطلبات حسب الدائرة'
                            },
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                </text>
            }
        });
    </script>
} 