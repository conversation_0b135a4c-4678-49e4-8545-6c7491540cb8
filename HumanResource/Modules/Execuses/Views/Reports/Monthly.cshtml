@model HumanResource.Modules.Execuses.ViewModels.ReportsViewModel
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-chart-bar"></i> ملخص الاستئذان الشهري
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="from" class="form-control" 
                                           value="@ViewBag.From" />
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="to" class="form-control" 
                                           value="@ViewBag.To" />
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">رقم الموظف</label>
                                    <input type="number" name="empNo" class="form-control" 
                                           value="@ViewBag.EmpNo" placeholder="اختياري" />
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> بحث
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5>@Model.MonthlySummary.Count</h5>
                                    <p>عدد الموظفين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5>@Model.MonthlySummary.Sum(s => s.TotalRequests)</h5>
                                    <p>إجمالي الطلبات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h5>@Model.MonthlySummary.Sum(s => s.TotalHours).ToString("F1")</h5>
                                    <p>إجمالي الساعات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <h5>@Model.MonthlySummary.Count(s => s.ExceedsLimit)</h5>
                                    <p>الموظفين المتجاوزين للحد</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Summary Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الموظف</th>
                                    <th>ساعات الاستئذان</th>
                                    <th>ساعات التأخير</th>
                                    <th>ساعات المهام الرسمية</th>
                                    <th>إجمالي الساعات</th>
                                    <th>عدد الطلبات</th>
                                    <th>المعتمدة</th>
                                    <th>قيد المراجعة</th>
                                    <th>المرفوضة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.MonthlySummary)
                                {
                                    <tr class="@(item.ExceedsLimit ? "table-warning" : "")">
                                        <td>@item.EmpNo</td>
                                        <td>
                                            @item.ExcuseHours.ToString("F1")
                                            @if (item.ExcuseHours > 7)
                                            {
                                                <span class="badge bg-danger ms-1">تجاوز الحد</span>
                                            }
                                        </td>
                                        <td>
                                            @item.DelayHours.ToString("F1")
                                            @if (item.DelayHours > 7)
                                            {
                                                <span class="badge bg-danger ms-1">تجاوز الحد</span>
                                            }
                                        </td>
                                        <td>@item.OfficialMissionHours.ToString("F1")</td>
                                        <td><strong>@item.TotalHours.ToString("F1")</strong></td>
                                        <td>@item.TotalRequests</td>
                                        <td><span class="badge bg-success">@item.ApprovedRequests</span></td>
                                        <td><span class="badge bg-warning">@item.PendingRequests</span></td>
                                        <td><span class="badge bg-danger">@item.RejectedRequests</span></td>
                                        <td>
                                            @if (item.ExceedsLimit)
                                            {
                                                <span class="badge bg-danger">تجاوز الحد</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">ضمن الحد</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.MonthlySummary.Any())
                    {
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i> لا توجد بيانات للفترة المحددة
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize DataTable if needed
            if ($('.table').length > 0) {
                $('.table').DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                    },
                    order: [[0, 'asc']],
                    pageLength: 25
                });
            }
        });
    </script>
} 