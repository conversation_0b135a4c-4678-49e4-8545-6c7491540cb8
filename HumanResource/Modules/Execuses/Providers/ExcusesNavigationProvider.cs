using HumanResource.Core.Data;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;


namespace HumanResource.Modules.Execuses.Providers
{
    /// <summary>
    /// Navigation provider for the Excuses module
    /// </summary>
    public class ExcusesNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Excuses";
        public int Priority => 23;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // HR Group navigation items
            var hrNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "excuses",
                    Label = "الأعذار",
                    Active = "excuses",
                    Icon = "<i class=\"far fa-clipboard-check\"></i>",
                    Rights = new List<Right> { Right.LeavesHrManager },
                    Priority = 16,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "excuses",
                            Label = "تقرير الأعذار",
                            Rights = new List<Right> { Right.LeavesHrManager },
                            Url = "/Execuses/Reports/Detailed"
                        },

                        new NavLink
                        {
                            Name = "excuses",
                            Label = "تقرير الأعذار حسب الدائرة",
                            Rights = new List<Right> { Right.LeavesHrManager },
                            Url = "/Execuses/Reports/Department"
                        }
                        
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.HR] = hrNavItems;

            return navigation;
        }
    }
} 