using HumanResource.Core.Contexts;
using HumanResource.Core.Helpers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Modules.Shared.Models.DTOs;
using HumanResource.Modules.Execuses.Models.Entities;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Execuses.Models.Enums;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Execuses.Providers
{   
    /// <summary>
    /// Task provider for the Execuses module
    /// </summary>
    public class ExecusesTaskProvider : ITaskProvider
    {
        private readonly hrmsContext _context;
        private readonly AppHelper _appHelper;

        public string ModuleName => "Execuses";

        public ExecusesTaskProvider(hrmsContext context, AppHelper appHelper)
        {
            _context = context;
            _appHelper = appHelper;
        }

        public async Task<List<UserTask>> GetTasksAsync()
        {
            var tasks = new List<UserTask>();

            try
            {
                // Get current user info
                var currentUser = _appHelper.Auth();
                if (currentUser?.EmpNo == null)
                {
                    return tasks;
                }

                // Department manager tasks
                if (_appHelper.Can(Right.DepartmentManager))
                {
                    var execuses = await _context.Absents
                        .Where(ro => ro.MangerNo == currentUser.EmpNo.Value
                                  && (ro.Status == (int)AbsentStatus.Pending))
                        .CountAsync();

                    if (execuses > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "رئيس القسم",
                            Text = $"الاستذان والتاخير ({execuses})",
                            Url = "/Execuses/Manager",
                            Priority = TaskPriority.Medium,
                        });
                    }
                }
                

                if (_appHelper.Can(Right.AbsentDGeneral))
                {
                    var execuses = await _context.Absents
                        .Where(ro => ro.MangerNo == currentUser.EmpNo.Value 
                                  && (ro.Status == (int)AbsentStatus.Pending))
                        .CountAsync();

                    if (execuses > 0)
                    {
                        tasks.Add(new UserTask
                        {
                            Title = "المدير العام",
                            Text = $"الاستذان والتاخير ({execuses})",
                            Url = "/Execuses/Dg",
                            Priority = TaskPriority.Medium,
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break task loading
                Console.WriteLine($"Error loading execuses tasks: {ex.Message}");
            }

            return tasks;
        }
    }
} 