using HumanResource.Modules.Execuses.Providers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Execuses.Configuration
{
    /// <summary>
    /// Configuration for the Excuses module
    /// </summary>
    public static class ExcusesConfiguration
    {
        /// <summary>
        /// Registers all Excuses module services
        /// </summary>
        public static IServiceCollection AddExcusesServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, ExcusesNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, ExcusesBadgeProvider>();

            // Register task providers
            services.AddScoped<ITaskProvider, ExecusesTaskProvider>();

            // Register module services
            // services.AddScoped<ExcusesService>();

            return services;
        }
    }
} 