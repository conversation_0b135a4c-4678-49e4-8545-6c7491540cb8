using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.DTOs;
using HumanResource.Modules.Shared.ViewModels;
using HumanResource.Modules.Execuses.Models.Enums;

namespace HumanResource.Modules.Execuses.ViewModels;

public class ExecuseViewModel : BaseViewModel
{

    public ExecuseViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
    }

    public List<Absent> Absents { get; set; }
    public Absent Absent { get; set; }
    public List<Workdays> WorkingHours { get; set; }
    public List<Absent> GetAbsents()
    {
        return Absents.OrderByDescending(a => a.OrderDate).ToList();
    }

    public string renderExcuseStatus(int? status, int? managerApproval, int? dgApproval)
    {
        if (managerApproval == (int)AbsentStatus.Rejected)
            return "<span class=\"badge rounded-pill px-3 badge-danger\"><i class=\"fas fa-times\"></i> مرفوض</span>";
            
        if (status == (int)AbsentStatus.NeedsDGApproval) // Needs DG approval
        {
            if (dgApproval == (int)AbsentStatus.ApprovedByDg)
                return "<span class=\"badge rounded-pill px-3 badge-success\"><i class=\"fas fa-check-double\"></i> موافق من المدير العام</span>";
            else if (dgApproval == (int)AbsentStatus.Rejected)
                return "<span class=\"badge rounded-pill px-3 badge-danger\"><i class=\"fas fa-times\"></i> مرفوض من الديوان</span>";
            else
                return "<span class=\"badge rounded-pill px-3 badge-warning\"><i class=\"fas fa-clock\"></i> قيد المراجعة</span>";
        }
        
        if (status == (int)AbsentStatus.Approved) // Approved
            return "<span class=\"badge rounded-pill px-3 badge-success\"><i class=\"fas fa-check\"></i> موافق</span>";
            
        // Default - Pending
        return "<span class=\"badge rounded-pill px-3 badge-warning\"><i class=\"fas fa-clock\"></i> قيد المراجعة</span>";
    }

}