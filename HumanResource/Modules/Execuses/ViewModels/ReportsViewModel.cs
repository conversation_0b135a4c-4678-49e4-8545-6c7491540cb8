using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.DTOs;
using HumanResource.Modules.Shared.ViewModels;

namespace HumanResource.Modules.Execuses.ViewModels;

public class ReportsViewModel : BaseViewModel
{
    public ReportsViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, 
        AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
    }

    public List<Absent> Absents { get; set; } = new List<Absent>();
    public List<MonthlySummaryItem> MonthlySummary { get; set; } = new List<MonthlySummaryItem>();
    public List<DepartmentSummaryItem> DepartmentSummary { get; set; } = new List<DepartmentSummaryItem>();

    public string GetRequestTypeName(int? reqType)
    {
        return reqType switch
        {
            1 => "استئذان",
            2 => "تأخير",
            3 => "مهمة رسمية",
            _ => "غير محدد"
        };
    }

    public string GetStatusName(int? status)
    {
        return status switch
        {
            0 => "قيد المراجعة",
            1 => "موافق",
            2 => "يحتاج موافقة المدير العام",
            3 => "مرفوض",
            _ => "غير محدد"
        };
    }

    public string GetStatusBadge(int? status, int? managerApproval, int? dgApproval)
    {
        if (managerApproval == 2)
            return "<span class=\"badge bg-danger\"><i class=\"fas fa-times\"></i> مرفوض</span>";
            
        if (status == 2) // Needs DG approval
        {
            if (dgApproval == 1)
                return "<span class=\"badge bg-success\"><i class=\"fas fa-check-double\"></i> موافق نهائياً</span>";
            else if (dgApproval == 2)
                return "<span class=\"badge bg-danger\"><i class=\"fas fa-times\"></i> مرفوض من المدير العام</span>";
            else
                return "<span class=\"badge bg-warning\"><i class=\"fas fa-clock\"></i> ينتظر موافقة المدير العام</span>";
        }
        
        if (status == 1) // Approved
            return "<span class=\"badge bg-success\"><i class=\"fas fa-check\"></i> موافق</span>";
        
        if (status == 3) // Rejected
            return "<span class=\"badge bg-danger\"><i class=\"fas fa-times\"></i> مرفوض</span>";
            
        // Default - Pending
        return "<span class=\"badge bg-warning\"><i class=\"fas fa-clock\"></i> قيد المراجعة</span>";
    }
}

public class MonthlySummaryItem
{
    public int? EmpNo { get; set; }
    public string EmpName { get; set; } = string.Empty;
    public double ExcuseHours { get; set; }
    public double DelayHours { get; set; }
    public double OfficialMissionHours { get; set; }
    public double TotalHours => ExcuseHours + DelayHours + OfficialMissionHours;
    public int TotalRequests { get; set; }
    public int ApprovedRequests { get; set; }
    public int PendingRequests { get; set; }
    public int RejectedRequests { get; set; }
    public bool ExceedsLimit => ExcuseHours > 7 || DelayHours > 7;
}

public class DepartmentSummaryItem
{
    public string DepartmentName { get; set; } = string.Empty;
    public int TotalRequests { get; set; }
    public int ExcuseRequests { get; set; }
    public int DelayRequests { get; set; }
    public int OfficialMissionRequests { get; set; }
    public int ApprovedRequests { get; set; }
    public int PendingRequests { get; set; }
    public int RejectedRequests { get; set; }
    public double TotalHours { get; set; }
} 