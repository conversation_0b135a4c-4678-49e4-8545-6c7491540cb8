﻿using HumanResource.Core.Contexts;
using HumanResource.Core.Data;
using HumanResource.Core.Helpers;
using HumanResource.Language;
using HumanResource.Modules.Employees.Models.Entities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.CodeAnalysis;
using System.Data;
using System.Net;
using System.Security.Claims;

namespace HumanResource.Modules.Shared.Controllers
{
    public class BaseController : Controller
    {
        protected ClaimsIdentity Identity { get; private set; }

        public readonly hrmsContext _context;
        public hrmsContext _db;
        public bcContext _bc;
        public readonly IHttpContextAccessor _http;
        public AppHelper _h;
        private VempDtl Profile;


        public Translator Translator { get; set; } = new Translator();


        

        public BaseController(hrmsContext context, IHttpContextAccessor httpContextAccessor, AppHelper helper)
        {
            _h = helper;
            _context = context;
            _db = context;
            _http = httpContextAccessor;
            
      
                Profile = _h.Auth();

        }
           

        public BaseController(hrmsContext context, bcContext bccontext, IHttpContextAccessor httpContextAccessor, AppHelper helper)
        {
            _h = helper;
            _context = context;
            _db = context;
            _bc = bccontext;
            _http = httpContextAccessor;
            
            Profile = _h.Auth();
        }

        [NonAction]
        public VempDtl Auth()
        {
            return Profile;
        }

        [NonAction]
        public bool Can(string role)
        {
            return _h.Can(role);
        }

        [NonAction]
        public bool Can(Right right)
        {
            return _h.Can(right);
        }

        [NonAction]
        public bool Can(params Right[] right)
        {
            return _h.Can(right);
        }


        [NonAction]
        public bool IsValid(ModelStateDictionary ModelState)
        {
            return ModelState.IsValid;

        }

        [NonAction]
        public string _l(string lable)
        {
            return Translator.Tr(lable);
        }

        [NonAction]
        public string _(string lable)
        {
            return Translator.Tr(lable);
        }

        [NonAction]
        public string tr(string lable)
        {
            return Translator.Tr(lable);
        }

        [NonAction]
        public List<string> ValidateErrors(ModelStateDictionary ModelState)
        {
            return ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();

        }

        [NonAction]
        public string Ec(string input)
        {
            return input;
#pragma warning disable CS0162 // Unreachable code detected
            return _h.Ec(input);
#pragma warning restore CS0162 // Unreachable code detected
        }

        [NonAction]
        public string Ec(int input)
        {
            string inputString = input.ToString();
            return inputString;
#pragma warning disable CS0162 // Unreachable code detected
            return _h.Ec(inputString);
#pragma warning restore CS0162 // Unreachable code detected
        }

        [NonAction]
        public string Dc(string input)
        {

            return input;
            return _h.Dc(input);
#pragma warning restore CS0162 // Unreachable code detected
        }

        [NonAction]
        public int DcInt(string input)
        {
            return Convert.ToInt32(input);

#pragma warning disable CS0162 // Unreachable code detected
            if (IsUrlEncoded(input))
            {
                input = WebUtility.UrlDecode(input);
            }
#pragma warning restore CS0162 // Unreachable code detected

            string dc = _h.Dc(input);
            return Convert.ToInt32(dc);
        }

        [NonAction]
        public bool IsUrlEncoded(string str)
        {
            if (string.IsNullOrEmpty(str)) return false;

            try
            {
                string decoded = WebUtility.UrlDecode(str);
                return decoded != str;
            }
            catch (Exception ex)
            {
                return false;
            }


        }

        [NonAction]
        public string expError(Exception ex)
        {
            
            string fullMessage = ex.Message;

            Exception inner = ex.InnerException;

            while (inner != null)
            {
                fullMessage += " -> " + inner.Message;
                inner = inner.InnerException;
            }

            return fullMessage; 
        }

        [NonAction]
        public void Log(string code, string type, string description, params (string Key, object Value)[] variables)
        {
            _h.Log(code, type, description, variables);
        }

        [NonAction]
        public void Log(int code, string type, string description, params (string Key, object Value)[] variables)
        {
            _h.Log(code, type, description, variables);
        }

    }
}
