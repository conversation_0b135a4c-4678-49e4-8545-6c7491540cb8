﻿using Microsoft.AspNetCore.Mvc;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Shared.Controllers
{
    public class AuthController : Controller
    {
   
        private readonly hrmsContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;


        public AuthController(hrmsContext context, IHttpContextAccessor httpContextAccessor) 
        {
       
            _context = context;
            
            _httpContextAccessor = httpContextAccessor;

        }



        public IActionResult Login(string returnUrl = null)
        {
        

       
            var EmpNo = 1018 ; //default user for testing

            var User = _context.VempDtls.Where(m => m.EmpNo == 1018).Select(x => new VempDtl { NatId = x.NatId, EmpNo = x.EmpNo, EmpNameA = x.EmpNameA, EmailId = x.EmailId }).FirstOrDefault();//.ToList();


            var newUserModel = new UserLogin();
            newUserModel.UserName = User.EmpNameA;
            return View(newUserModel);
        }

 
        [HttpPost]
        public IActionResult Login(UserLogin userLogin, string returnUrl = null)
        {
            return RedirectToAction("UserDashboard", "Account");
        }

    }
}
