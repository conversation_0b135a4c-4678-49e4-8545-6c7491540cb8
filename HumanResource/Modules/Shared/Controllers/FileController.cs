﻿using Microsoft.AspNetCore.Mvc;
using System.IO;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using Microsoft.EntityFrameworkCore;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Shared.Controllers;


public class FileController : BaseController
{

    AppHelper _h;

    public FileController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {
        _h = helper;
    }

    [HttpGet("File")]
    public async Task<IActionResult> Index(string name)
    {
        if (string.IsNullOrEmpty(name)) 
            return BadRequest();

        var FileMetadata = await _db.Uploads.Where(f=>f.Name == name).FirstOrDefaultAsync();

        if (FileMetadata == null)
            return StatusCode(404);

        // Get base upload directory from settings
        string baseUploadDirectory = _h.Settings.Get("system.upload_dir", @"C:\hrmsUploads");
        
        
        // Construct the full file path
        string FilePath = Path.Combine(baseUploadDirectory, FileMetadata.Name);

        if (!System.IO.File.Exists(FilePath))
            return NotFound("File not exists");

        var memory = new MemoryStream();
        using (var stream = new FileStream(FilePath, FileMode.Open))
        {
            await stream.CopyToAsync(memory);
        }

        memory.Position = 0;
        
        string contentType = GetContentType(FilePath);
        
        // For image files, display in browser instead of downloading
        if (contentType.StartsWith("image/"))
        {
            return File(memory, contentType);
        }
        
        // For other file types, continue with download behavior
        return File(memory, contentType, FileMetadata.Label ?? Path.GetFileName(FilePath));
    }

    [HttpGet("File/Get/{guid}")]
    public async Task<IActionResult> Get(string guid)
    {
        var FileMetadata = await _db.Uploads.FindAsync(guid);

        if (FileMetadata == null)
            return StatusCode(404);

        // Get base upload directory from settings
        string baseUploadDirectory = _h.Settings.Get("system.upload_dir", @"C:\hrmsUploads");
        
        // Construct the full file path
        string FilePath = Path.Combine(baseUploadDirectory, FileMetadata.Name);

        if (!System.IO.File.Exists(FilePath))
            return NotFound("File not exists");

        var memory = new MemoryStream();
        using (var stream = new FileStream(FilePath, FileMode.Open))
        {
            await stream.CopyToAsync(memory);
        }

        memory.Position = 0;
        
        string contentType = GetContentType(FilePath);
        
        // For image files, display in browser instead of downloading
        if (contentType.StartsWith("image/"))
        {
            return File(memory, contentType);
        }
        
        // For other file types, continue with download behavior
        return File(memory, contentType, FileMetadata.Label);
    }

    private string GetContentType(string FilePath)
    {
        var types = new Dictionary<string, string>
        {
            {".txt", "text/plain" },
            {".pdf", "application/pdf" },
            {".doc", "application/vnd.ms-word" },
            {".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
            {".jpg", "image/jpeg" },
            {".jpeg", "image/jpeg" },
            {".png", "image/png" },
            {".gif", "image/gif" },
            {".csv", "text/csv" },
            {".xls", "application/vnd.ms-excel" },
            {".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
        };

        var ext = Path.GetExtension(FilePath).ToLowerInvariant();

        return types.TryGetValue(ext, out var type) ? type : "application/octet-stream";
    }
}

