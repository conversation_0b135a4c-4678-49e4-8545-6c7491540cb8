using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;


using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;


namespace HumanResource.Modules.Shared.Controllers
{
    [Route("api/notifications")]
    [ApiController]
    public class NotificationsController : BaseController
    {

        public NotificationsController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

  
    }

        /// <summary>
        /// Get a list of unread notifications for the current user
        /// </summary>
        /// <param name="lastTimestamp">Optional timestamp in format yyyy-MM-dd-HH-mm-ss to filter notifications</param>
        /// <returns>List of unread notifications</returns>
        /// 
        
        [HttpGet("unread")]
        public IActionResult GetUnreadNotifications(
            [FromQuery] string lastTimestamp = null)
        {
            // Get the employee ID from the session or claims
            int empNo = _h.Auth().EmpNo ?? 0;

            if (empNo <= 0)
            {
                return Unauthorized(new { message = "User not authenticated or employee ID not found" });
            }

            try
            {
                // Get unread notifications
                var notifications = _h.Notify().List(empNo);
                
                // Filter by timestamp if provided
                if (!string.IsNullOrEmpty(lastTimestamp))
                {
                    DateTime dateTime;
                    if (DateTime.TryParseExact(lastTimestamp, "yyyy-MM-dd-HH-mm-ss", 
                        System.Globalization.CultureInfo.InvariantCulture, 
                        System.Globalization.DateTimeStyles.AssumeUniversal, 
                        out dateTime))
                    {
                        notifications = notifications.Where(n => n.Timestamp >= dateTime).ToList();
                    }
                }

                // Get total unread count
                int totalUnreadCount = _h.Notify().GetUnreadCount(empNo);

                if(notifications.Count > 0) 
                {
                    return Ok(new
                    {
                        count = totalUnreadCount,
                        lastTimestamp = DateTime.UtcNow.ToString("yyyy-MM-dd-HH-mm-ss"),
                        notifications = notifications.Select(n => new
                        {
                            id = n.Guid,
                            title = n.Title,
                            text = n.Text,
                            url = n.Url,
                            timestamp = n.Timestamp.ToString("yyyy-MM-dd-HH-mm-ss")
                        })
                    });
                }

                return Ok(new
                {
                    count = totalUnreadCount,
                    lastTimestamp = DateTime.UtcNow.ToString("yyyy-MM-dd-HH-mm-ss"),
                    notifications = new List<object>()
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        /// <summary>
        /// Mark a notification as read
        /// </summary>
        /// <param name="id">Notification GUID</param>
        /// <returns>Status of the operation</returns>
        [HttpPost("{id}/read")]
        public async Task<IActionResult> MarkAsRead(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return BadRequest(new { message = "Notification ID is required" });
                }

                await _h.Notify().Get(Guid.Parse(id));
                return Ok(new
                {
                    message = "Notification marked as read"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }


        /// <summary>
        /// Mark all notifications as read for the current user
        /// </summary>
        /// <returns>Status of the operation</returns>
        [HttpPost("mark-all-read")]
        public async Task<IActionResult> MarkAllAsRead()
        {
            // Get the employee ID from the session or claims
            int empNo = _h.Auth().EmpNo ?? 0;

            if (empNo <= 0)
            {
                return Unauthorized(new { message = "User not authenticated or employee ID not found" });
            }

            try
            {
                await _h.Notify().MarkAllAsRead(empNo);
                return Ok(new { message = "All notifications marked as read" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
} 