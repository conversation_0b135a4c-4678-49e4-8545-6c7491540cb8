﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Shared.ViewModels;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Extensions;
using HumanResource.Core.UI.Services;
using HumanResource.Core.UI.Models;

namespace HumanResource.Modules.Shared.Controllers;


public class ReportController : BaseController
{

    public ReportViewModel _v;
   
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public hrmsContext  _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword

    public ReportController(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _v = new ReportViewModel(context, httpContextAccessor, helper);

        _v.Page.Active = "reports";

        _db = context;

    }


   
    public IActionResult Losthours()
    {

        

        _v.Page.Breadcrumb = new List<Breadcrumb> {
             new Breadcrumb {Label="Reports", Url=$"/Reports/Index"},
             new Breadcrumb {Label="Lost hours report", Url=$"/Reports/Index"},
        };

        _v.Page.Filter.DateTo = DateTime.Today.Add(new TimeSpan(23, 59, 59));
        _v.Page.Filter.DateFrom = DateTime.Today.AddMonths(-6);

        var fromQueryString = HttpContext.Request.Query["from"].ToString();
        var toQueryString = HttpContext.Request.Query["to"].ToString();


        if (!string.IsNullOrEmpty(fromQueryString))
        {
            if (DateTime.TryParse(fromQueryString, out DateTime from))
            {
                _v.Page.Filter.DateFrom = from;
            }
        }

        if (!string.IsNullOrEmpty(toQueryString))
        {
            if (DateTime.TryParse(toQueryString, out DateTime to))
            {
                to = to.Date.Add(new TimeSpan(23, 59, 59));
                _v.Page.Filter.DateTo = to;
            }
        }


        int minYear = _v.Page.Filter.DateFrom.Value.Year;
        int maxYear = _v.Page.Filter.DateTo.Value.Year;
        int minMonth = _v.Page.Filter.DateFrom.Value.Month;
        int maxMonth = _v.Page.Filter.DateTo.Value.Month;


        string rawQuery = $" SELECT EMP_NO,EMP_NAME_A,DG_CODE,DG_DESP_A,DEPT_CODE,DEPT_DESP_A, SUM(TOTAL_OUT) AS TOTAL_OUT FROM REPORT_TOTAL_HOURS_LOST "
            + $" WHERE DATE_YEAR >= " + minYear
            + $" AND DATE_YEAR   <= " + maxYear
            + $" AND DATE_MONTH  >= " + minMonth
            + $" AND DATE_MONTH  <= " + maxMonth;


        rawQuery += " GROUP BY EMP_NO,EMP_NAME_A,DG_CODE,DG_DESP_A,DEPT_CODE,DEPT_DESP_A ";

        _v.VReportLostHours = _db.VReportLostHours.FromSqlRaw(rawQuery).ToList();


        string rawQuery2 = $" SELECT 1 AS EMP_NO,'a' AS EMP_NAME_A,DG_CODE,DG_DESP_A,1 as DEPT_CODE, 'a' as DEPT_DESP_A, SUM(TOTAL_OUT) AS TOTAL_OUT FROM REPORT_TOTAL_HOURS_LOST "
            + $" WHERE DATE_YEAR >= " + minYear
            + $" AND DATE_YEAR   <= " + maxYear
            + $" AND DATE_MONTH  >= " + minMonth
            + $" AND DATE_MONTH  <= " + maxMonth;


        rawQuery2 += "  GROUP BY DG_CODE,DG_DESP_A ";


        var groupedList = _v.VReportLostHours;


        DateTime startDate = new DateTime(minYear, minMonth, 1);
        DateTime endDate = new DateTime(maxYear, maxMonth, DateTime.DaysInMonth(maxYear,maxMonth));

        TimeSpan diffDayes = endDate - startDate;
        int totoalDays = diffDayes.Days;

        var charts = groupedList
            .GroupBy(d => d.DgDespA)
            .Select(group =>
            {
                int dgCount = _db.VempDtls.Where(d => d.DgDespA == group.Key).Count() ;
                var totalCount = dgCount * 7 * totoalDays;
                var totalHouers = group.Sum(d => d.Hours);


                return new LosthoursCharDate1
                {
                    Name = group.Key,
                    DgCount = dgCount,
                    Percent = (double)totalHouers / totalCount * 100,
                };


            });



        ViewBag.Charts = charts;
        ViewBag.TotoalDays = totoalDays;
        ViewBag.EmpCount = _db.VempDtls.Where(d=>d.EmpNo<1000).Count();

        return View(_v);
    }













    public class LosthoursCharDate1
    {
        public int DgCount { set; get; }
        public string Name { set; get; }
        public double Percent { set; get; }
    }





}

