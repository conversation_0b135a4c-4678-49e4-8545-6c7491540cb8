﻿using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.Entities.HRMS;

namespace HumanResource.Modules.Shared.Services
{
    public class UserNotificationService
    {
        private hrmsContext _db;

        public UserNotificationService(hrmsContext db)
        {
            _db = db;
        }

        public async Task Create(int empNo, string title, string text = "", string url = "#")
        {
            _db.TUserNotifications.Add(new TUserNotifications
            {
                EmpNo = empNo,
                Title = title,
                Text = text,
                Url = url,
            });

           await _db.SaveChangesAsync();
        }

        public async Task Remove(Guid guid)
        {
           var notification = _db.TUserNotifications.Find(guid);
            
            if (notification != null)
            {
                notification.ReadFlag = 1;
                _db.Update(notification);

                await _db.SaveChangesAsync();
            }
        }

        public List<TUserNotifications> List(int EmpNo)
        {
            var notification = _db.TUserNotifications.Where(r=>r.EmpNo == EmpNo && r.ReadFlag == 0).ToList();

            return notification;
        }

        public async Task Get(Guid guid)
        {
            var notification = _db.TUserNotifications.Find(guid);

            if (notification != null)
            {
                notification.ReadFlag = 1;
                _db.Update(notification);

                await _db.SaveChangesAsync();
            }
        }

        public int GetUnreadCount(int empNo)
        {
            return _db.TUserNotifications.Count(n => n.EmpNo == empNo && n.ReadFlag == 0);
        }

        public async Task MarkAllAsRead(int empNo)
        {
            var notifications = _db.TUserNotifications.Where(n => n.EmpNo == empNo && n.ReadFlag == 0);
            foreach (var notification in notifications)
            {
                notification.ReadFlag = 1;
            }
            await _db.SaveChangesAsync();
        }
    }
}
