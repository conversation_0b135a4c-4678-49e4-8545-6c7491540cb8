using System.Net;
using System.Net.Mail;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using HumanResource.Modules.Settings.Services;

namespace HumanResource.Modules.Shared.Services
{
    /// <summary>
    /// Service for sending emails to users and managing email templates, as well as
    /// handling notifications that need to be sent both in-app and via email
    /// </summary>
    public class EmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly SettingsService _settingsService;
        private readonly UserNotificationService _notificationService;
        private readonly RazorViewRenderer _razorViewRenderer;
        private const string TemplatePrefix = "email.template.";

        public EmailService(
            ILogger<EmailService> logger, 
            SettingsService settingsService,
            UserNotificationService notificationService,
            IServiceProvider serviceProvider,
            IRazorViewEngine razorViewEngine,
            ITempDataProvider tempDataProvider,
            IHttpContextAccessor httpContextAccessor)
        {
            _logger = logger;
            _settingsService = settingsService;
            _notificationService = notificationService;
            _razorViewRenderer = new RazorViewRenderer(
                razorViewEngine,
                tempDataProvider,
                serviceProvider,
                httpContextAccessor);
        }

        #region Razor View Renderer

        /// <summary>
        /// Internal class that handles rendering Razor views to strings
        /// </summary>
        private class RazorViewRenderer
        {
            private readonly IRazorViewEngine _razorViewEngine;
            private readonly ITempDataProvider _tempDataProvider;
            private readonly IServiceProvider _serviceProvider;
            private readonly IHttpContextAccessor _httpContextAccessor;

            public RazorViewRenderer(
                IRazorViewEngine razorViewEngine,
                ITempDataProvider tempDataProvider,
                IServiceProvider serviceProvider,
                IHttpContextAccessor httpContextAccessor)
            {
                _razorViewEngine = razorViewEngine;
                _tempDataProvider = tempDataProvider;
                _serviceProvider = serviceProvider;
                _httpContextAccessor = httpContextAccessor;
            }

            public async Task<string> RenderViewToStringAsync<TModel>(string viewName, TModel model)
            {
                var httpContext = _httpContextAccessor?.HttpContext ?? new DefaultHttpContext { RequestServices = _serviceProvider };
                var actionContext = new ActionContext(httpContext, new RouteData(), new ActionDescriptor());

                using var sw = new StringWriter();
                
                // If the view name starts with '/', remove it as it's unnecessary for view location
                if (viewName.StartsWith("/"))
                {
                    viewName = viewName.Substring(1);
                }
                
                // Try to find the view
                var viewResult = _razorViewEngine.FindView(actionContext, viewName, false);

                if (viewResult.View == null)
                {
                    // If not found, try looking in EmailTemplates directory
                    viewName = $"EmailTemplates/{viewName}";
                    viewResult = _razorViewEngine.FindView(actionContext, viewName, false);
                }

                if (viewResult.View == null)
                {
                    viewName = viewName.Replace('/', '\\');
                    viewResult = _razorViewEngine.GetView("~/", $"~/Views/{viewName}.cshtml", false);
                }

                if (viewResult.View == null)
                {
                    throw new ArgumentNullException($"Unable to find view '{viewName}'");
                }

                var viewDictionary = new ViewDataDictionary(new EmptyModelMetadataProvider(), new ModelStateDictionary())
                {
                    Model = model
                };

                var viewContext = new ViewContext(
                    actionContext,
                    viewResult.View,
                    viewDictionary,
                    new TempDataDictionary(actionContext.HttpContext, _tempDataProvider),
                    sw,
                    new HtmlHelperOptions()
                );

                await viewResult.View.RenderAsync(viewContext);
                return sw.ToString();
            }
        }

        #endregion

        #region Email Sending Methods

        /// <summary>
        /// Send an email with plain text content
        /// </summary>
        /// <param name="to">Recipient email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body content (plain text)</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task SendEmailAsync(string to, string subject, string body)
        {

            var viewTempalte = "default";

            body = body.Replace("\n", "<br>");

            var template = await ProcessTemplateAsync(viewTempalte, new Dictionary<string, string> { { "subject", subject }, { "body", body } });

            await SendEmailAsync(to, subject, template, true);
        }

        /// <summary>
        /// Send an email with HTML content
        /// </summary>
        /// <param name="to">Recipient email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="htmlBody">Email body content (HTML)</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task SendHtmlEmailAsync(string to, string subject, string htmlBody)
        {
            await SendEmailAsync(to, subject, htmlBody, true);
        }
    
  
        #endregion

        #region Template Management Methods

        /// <summary>
        /// Get a template by its name
        /// </summary>
        /// <param name="templateName">Name of the template</param>
        /// <returns>The template content or null if not found</returns>
        public string GetTemplate(string templateName)
        {
            // First check if a physical file exists in the EmailTemplates directory
            string templatePath = Path.Combine(Directory.GetCurrentDirectory(), "Modules", "Shared", "Views", "EmailTemplates", $"{templateName}.cshtml");
            if (File.Exists(templatePath))
            {
                try
                {
                    return File.ReadAllText(templatePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error reading template file '{templatePath}': {ex.Message}");
                }
            }
            
            // Fallback to database/settings storage if file not found
            return _settingsService.Get($"{TemplatePrefix}{templateName}");
        }

 

    

        /// <summary>
        /// Process a template by replacing variables with values
        /// </summary>
        /// <param name="templateName">Name of the template</param>
        /// <param name="variables">Dictionary of variable names and values</param>
        /// <returns>Processed template content with variables replaced</returns>
        public async Task<string> ProcessTemplateAsync(string templateName, Dictionary<string, string> variables)
        {
            try
            {
                // First try to render using the Razor view engine
                try
                {
                    return await _razorViewRenderer.RenderViewToStringAsync(templateName, variables);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to render template '{templateName}' using Razor view engine: {ex.Message}");
                    
                    // Fall back to database template and simple variable replacement
                    var template = GetTemplate(templateName);
                    if (string.IsNullOrEmpty(template))
                    {
                        _logger.LogWarning($"Template '{templateName}' not found");
                        return null;
                    }

                    return BindVariables(template, variables);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing template '{templateName}': {ex.Message}");
                return null;
            }
        }

        // Keep the synchronous version for backward compatibility
        public string ProcessTemplate(string templateName, Dictionary<string, string> variables)
        {
            return ProcessTemplateAsync(templateName, variables).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Bind variables to a template
        /// </summary>
        /// <param name="template">Template content</param>
        /// <param name="variables">Dictionary of variable names and values</param>
        /// <returns>Template with variables replaced</returns>
        public string BindVariables(string template, Dictionary<string, string> variables)
        {
            if (variables == null || !variables.Any())
            {
                return template;
            }

            string result = template;
            foreach (var variable in variables)
            {
                result = result.Replace($"{{{variable.Key}}}", variable.Value);
            }

            return result;
        }

        #endregion
      
        #region Private Email Methods

        private async Task SendEmailAsync(string to, string subject, string body, bool isHtml)
        {
            if (string.IsNullOrEmpty(to))
            {
                throw new ArgumentException("Recipient email address is required", nameof(to));
            }

            try
            {
                using var message = new MailMessage();
                
                // Check if we're in development mode and should redirect emails
                if(GetIsTestMode())
                {
                    message.To.Add(_settingsService.Get("email.test_email_address"));
                }
                else
                {
                    message.To.Add(to);  
                }

                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = isHtml;
                message.From = new MailAddress(GetSmtpUsername());

                using var client = CreateSmtpClient();
                await client.SendMailAsync(message);
                _logger.LogInformation($"Email sent to: {to}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending email: {ex.Message}");
                throw;
            }
        }

        private SmtpClient CreateSmtpClient()
        {
            var client = new SmtpClient(GetSmtpServer(), GetSmtpPort());
            client.UseDefaultCredentials = false;
            client.Credentials = new NetworkCredential(GetSmtpUsername(), GetSmtpPassword());
            client.EnableSsl = GetSmtpEnableSsl();
            return client;
        }

        private string GetSmtpServer()
        {
            return _settingsService.Get("email.smtp_server", "mail.gsc.local");
        }

        private int GetSmtpPort()
        {
            return _settingsService.GetInt("email.smtp_port", 25);
        }

        private string GetSmtpUsername()
        {
            return _settingsService.Get("email.smtp_username", "<EMAIL>");
        }

        private string GetSmtpPassword()
        {
            return _settingsService.Get("email.smtp_password", "Gsc@123");
        }

        private bool GetSmtpEnableSsl()
        {
            return _settingsService.GetBool("email.smtp_enable_ssl", false);
        }

        private bool GetIsTestMode()
        {
            return _settingsService.GetBool("email.is_test_mode", false);
        }

        #endregion

     
    }
} 