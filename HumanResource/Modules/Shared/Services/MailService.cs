﻿using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;
using HumanResource.Modules.Shared.Models.Enums;

namespace HumanResource.Modules.Shared.Services;
public class MailService
{

    private readonly hrmsContext _db;
    public MailService(hrmsContext context)
    {
        _db = context;

    }
    
    public TincomingMail Create(int transType)
    {
        return Create((TransTypeEnum)transType);
    }

    /// <summary>
    /// Create a new incoming mail
    /// </summary>
    /// <param name="transTypeEnum">The type of transaction</param>
    /// <returns>The incoming mail</returns>
    public TincomingMail Create(TransTypeEnum transTypeEnum)
    {

        string LetterSubj = "";

        int transType = (int)transTypeEnum;

        var type = _db.TTransType.FirstOrDefault(ro => ro.TransType == transType);

        if (type == null)
            return null;


        LetterSubj = type.TransDespA;


        int year = Convert.ToInt32(DateTime.Now.ToString("yy"));

        // get the last mail
        var last = _db.TLastMailNo.Where(ro => ro.DocYear == year).FirstOrDefault();

        if (last == null)
        {
            var newLastMail = new TLastMailNo
            {
                DocYear = year,
                DeptInd = 1,
                LastInMailNo = 1,
                LastOutMailNo = 1
            };

            _db.TLastMailNo.AddAsync(newLastMail);
            _db.SaveChangesAsync();

            last = newLastMail;
        }
        else
        {
            // add 1 to the current last mail
            last.LastInMailNo = last.LastInMailNo + 1;
            last.LastOutMailNo = last.LastOutMailNo + 1;

            _db.Update(last);
            _db.SaveChangesAsync();
        }

        // add the mail to TINCOMING_DOC
        var incomingDoc = _db.TIncomingDoc.Where(ro => ro.InYear == year && ro.TransType == transType).FirstOrDefault();

        if (incomingDoc == null)
        {
            var newIncomingDoc = new TIncomingDoc
            {
                InYear = year,
                InDeptInd = 1,
                InMailNo = last.LastInMailNo,
                TransType = transType
            };

            _db.TIncomingDoc.AddAsync(incomingDoc);
            _db.SaveChangesAsync();

            incomingDoc = newIncomingDoc;
        }


        // add to the Out mail 

        var orderType = _db.TOrderType.Where(ro => ro.OrderType == transType).FirstOrDefault();

        if (orderType != null)
        {
            var outgoinMail = new TOutgoingMail
            {
                InYear = year,
                InDeptNo = 1,
                OutMailNo = last.LastInMailNo,
                OrderType = orderType.OrderType,
                UserId = "HrmsMVC",
            };

            _db.TOutgoingMail.AddAsync(outgoinMail);
            _db.SaveChangesAsync();

        }


        var LastDocSlNoMax = _db.TincomingMails.Where(x => x.InYear == year).Any()
            ? _db.TincomingMails.Where(x => x.InYear == year).Max(m => m.LastDocSlNo)
            : 0;

        TincomingMail mail = new TincomingMail();

        mail.InYear = year;
        mail.InDeptInd = 1;
        mail.InMailNo = last.LastInMailNo;

        mail.LastDocSlNo = Convert.ToInt32(LastDocSlNoMax) + 1;

        mail.LetterDate = DateTime.Now;
        mail.LetterRefNo = DateTime.Now.Year;
        mail.RecdDate = DateTime.Now;
        mail.RecdTime = DateTime.Now;

        mail.DocType = 2; // 2 للاستمارات من الجدول TDOC_TYPE

        mail.LetterSubj = LetterSubj;

        _db.TincomingMails.Add(mail);

        _db.SaveChanges();

        return mail;

    }
}