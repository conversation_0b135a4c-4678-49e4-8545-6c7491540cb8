namespace HumanResource.Modules.Shared.Services
{
    /// <summary>
    /// Service interface for formatting various data types including dates, times, amounts, and hours
    /// </summary>
    public interface IFormattingService
    {
        #region Date and Time Formatting
        
        /// <summary>
        /// Format date as yyyy-MM-dd
        /// </summary>
        /// <param name="date">Date to format</param>
        /// <returns>Formatted date string or empty string if null</returns>
        string Date(DateTime? date);
        
        /// <summary>
        /// Format datetime as yyyy-MM-dd hh:mm tt
        /// </summary>
        /// <param name="date">DateTime to format</param>
        /// <returns>Formatted datetime string</returns>
        string DateTime(DateTime? date);
        
        /// <summary>
        /// Format date with day name as yyyy-MM-dd ddd
        /// </summary>
        /// <param name="date">Date to format</param>
        /// <returns>Formatted date string with day name</returns>
        string DateWithDay(DateTime? date);
        
        /// <summary>
        /// Format time only as hh:mm tt
        /// </summary>
        /// <param name="date">DateTime to extract time from</param>
        /// <returns>Formatted time string</returns>
        string Time(DateTime? date);
        
        #endregion

        #region Hours Formatting
        
        /// <summary>
        /// Format hours as HH:MM format
        /// </summary>
        /// <param name="hours">Hours as float</param>
        /// <returns>Formatted hours string (e.g., "2:30")</returns>
        string Hours(float hours);
        
        /// <summary>
        /// Format hours as HH:MM format
        /// </summary>
        /// <param name="hours">Hours as nullable float</param>
        /// <returns>Formatted hours string or "0:00" if null</returns>
        string Hours(float? hours);
        
        /// <summary>
        /// Format hours as HH:MM format
        /// </summary>
        /// <param name="hours">Hours as double</param>
        /// <returns>Formatted hours string (e.g., "2:30")</returns>
        string Hours(double hours);
        
        #endregion

        #region Amount Formatting
        
        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        /// <param name="amount">Amount as float</param>
        /// <returns>Formatted amount string</returns>
        string Amount(float amount);
        
        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        /// <param name="amount">Amount as nullable float</param>
        /// <returns>Formatted amount string or "0.000" if null</returns>
        string Amount(float? amount);
        
        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        /// <param name="amount">Amount as double</param>
        /// <returns>Formatted amount string</returns>
        string Amount(double amount);
        
        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        /// <param name="amount">Amount as int</param>
        /// <returns>Formatted amount string</returns>
        string Amount(int amount);
        
        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        /// <param name="amount">Amount as decimal</param>
        /// <returns>Formatted amount string</returns>
        string Amount(decimal amount);
        
        #endregion

        #region Currency Formatting
        
        /// <summary>
        /// Format currency with specified currency symbol
        /// </summary>
        /// <param name="amount">Amount to format</param>
        /// <param name="currencySymbol">Currency symbol (default: "ر.ع")</param>
        /// <returns>Formatted currency string</returns>
        string Currency(decimal amount, string currencySymbol = "ر.ع");
        
        /// <summary>
        /// Format currency with specified currency symbol
        /// </summary>
        /// <param name="amount">Amount to format</param>
        /// <param name="currencySymbol">Currency symbol (default: "ر.ع")</param>
        /// <returns>Formatted currency string</returns>
        string Currency(double amount, string currencySymbol = "ر.ع");
        
        #endregion

        #region Number Formatting
        
        /// <summary>
        /// Format number with thousands separator
        /// </summary>
        /// <param name="number">Number to format</param>
        /// <returns>Formatted number string</returns>
        string Number(int number);
        
        /// <summary>
        /// Format number with thousands separator
        /// </summary>
        /// <param name="number">Number to format</param>
        /// <returns>Formatted number string</returns>
        string Number(long number);
        
        /// <summary>
        /// Format percentage
        /// </summary>
        /// <param name="value">Value as decimal (0.5 for 50%)</param>
        /// <param name="decimalPlaces">Number of decimal places</param>
        /// <returns>Formatted percentage string</returns>
        string Percentage(decimal value, int decimalPlaces = 1);
        
        #endregion
    }
} 