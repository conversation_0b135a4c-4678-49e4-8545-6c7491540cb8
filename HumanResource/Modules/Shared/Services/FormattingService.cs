using System.Globalization;

namespace HumanResource.Modules.Shared.Services
{
    /// <summary>
    /// Service for formatting various data types including dates, times, amounts, and hours
    /// Consolidates formatting functionality previously scattered across helpers and view models
    /// </summary>
    public class FormattingService : IFormattingService
    {
        private readonly CultureInfo _culture;

        public FormattingService()
        {
            // Use Arabic culture for number formatting
            _culture = new CultureInfo("ar-OM"); // Oman Arabic
        }

        #region Date and Time Formatting

        /// <summary>
        /// Format date as yyyy-MM-dd
        /// </summary>
        public string Date(DateTime? date)
        {
            if (date == null)
                return "";

            return date.Value.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// Format datetime as yyyy-MM-dd hh:mm tt
        /// </summary>
        public string DateTime(DateTime? date)
        {
            if (date == null)
                return "";

            return date.Value.ToString("yyyy-MM-dd hh:mm tt");
        }

        /// <summary>
        /// Format date with day name as yyyy-MM-dd ddd
        /// </summary>
        public string DateWithDay(DateTime? date)
        {
            if (date == null)
                return "";

            return date.Value.ToString("yyyy-MM-dd ddd");
        }

        /// <summary>
        /// Format time only as hh:mm tt
        /// </summary>
        public string Time(DateTime? date)
        {
            if (date == null)
                return "";

            return date.Value.ToString("hh:mm tt");
        }

        #endregion

        #region Hours Formatting

        /// <summary>
        /// Format hours as HH:MM format
        /// </summary>
        public string Hours(float hours)
        {
            int totalMin = (int)(hours * 60);
            int wholeHours = totalMin / 60;
            int minutes = totalMin % 60;

            return $"{wholeHours}:{minutes:D2}";
        }

        /// <summary>
        /// Format hours as HH:MM format
        /// </summary>
        public string Hours(float? hours)
        {
            if (hours == null)
                return "0:00";

            return Hours(hours.Value);
        }

        /// <summary>
        /// Format hours as HH:MM format
        /// </summary>
        public string Hours(double hours)
        {
            return Hours((float)hours);
        }

        #endregion

        #region Amount Formatting

        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        public string Amount(float amount)
        {
            return amount.ToString("F3", _culture);
        }

        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        public string Amount(float? amount)
        {
            if (amount == null)
                return "0.000";

            return amount.Value.ToString("F3", _culture);
        }

        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        public string Amount(double amount)
        {
            return amount.ToString("F3", _culture);
        }

        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        public string Amount(int amount)
        {
            return amount.ToString("F3", _culture);
        }

        /// <summary>
        /// Format amount with 3 decimal places
        /// </summary>
        public string Amount(decimal amount)
        {
            return amount.ToString("F3", _culture);
        }

        #endregion

        #region Currency Formatting

        /// <summary>
        /// Format currency with specified currency symbol
        /// </summary>
        public string Currency(decimal amount, string currencySymbol = "ر.ع")
        {
            return $"{Amount(amount)} {currencySymbol}";
        }

        /// <summary>
        /// Format currency with specified currency symbol
        /// </summary>
        public string Currency(double amount, string currencySymbol = "ر.ع")
        {
            return $"{Amount(amount)} {currencySymbol}";
        }

        #endregion

        #region Number Formatting

        /// <summary>
        /// Format number with thousands separator
        /// </summary>
        public string Number(int number)
        {
            return number.ToString("N0", _culture);
        }

        /// <summary>
        /// Format number with thousands separator
        /// </summary>
        public string Number(long number)
        {
            return number.ToString("N0", _culture);
        }

        /// <summary>
        /// Format percentage
        /// </summary>
        public string Percentage(decimal value, int decimalPlaces = 1)
        {
            string format = decimalPlaces > 0 ? $"P{decimalPlaces}" : "P0";
            return value.ToString(format, _culture);
        }

        #endregion

        #region Extension Methods for Backward Compatibility

        /// <summary>
        /// Legacy method name for date formatting (backward compatibility)
        /// </summary>
        public string _d(DateTime? date) => Date(date);

        /// <summary>
        /// Legacy method name for datetime formatting (backward compatibility)
        /// </summary>
        public string _dt(DateTime? date) => DateTime(date);

        /// <summary>
        /// Legacy method name for date with day formatting (backward compatibility)
        /// </summary>
        public string _ddd(DateTime? date) => DateWithDay(date);

        /// <summary>
        /// Legacy method name for time formatting (backward compatibility)
        /// </summary>
        public string _t(DateTime? date) => Time(date);

        /// <summary>
        /// Legacy method name for amount formatting (backward compatibility)
        /// </summary>
        public string _amount(float amount) => Amount(amount);

        /// <summary>
        /// Legacy method name for amount formatting (backward compatibility)
        /// </summary>
        public string _amount(float? amount) => Amount(amount);

        /// <summary>
        /// Legacy method name for amount formatting (backward compatibility)
        /// </summary>
        public string _amount(double amount) => Amount(amount);

        /// <summary>
        /// Legacy method name for amount formatting (backward compatibility)
        /// </summary>
        public string _amount(int amount) => Amount(amount);

        #endregion
    }
} 