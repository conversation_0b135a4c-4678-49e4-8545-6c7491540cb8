using HumanResource.Modules.Shared.Providers;
using HumanResource.Modules.Shared.Services;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Shared.Configuration
{
    /// <summary>
    /// Configuration for the Shared module
    /// </summary>
    public static class SharedConfiguration
    {
        /// <summary>
        /// Registers all Shared module services
        /// </summary>
        public static IServiceCollection AddSharedServices(this IServiceCollection services)
        {
            // Register navigation providers
            services.AddScoped<INavigationProvider, ReportsNavigationProvider>();
            services.AddScoped<INavigationProvider, ApprovalsNavigationProvider>();

            // Register badge providers (create when needed)
            // services.AddScoped<IBadgeProvider, ReportsBadgeProvider>();
            // services.AddScoped<IBadgeProvider, ApprovalsBadgeProvider>();

  
            // Register module services
            // services.AddScoped<ReportsService>();
            // services.AddScoped<ApprovalsService>();
            
            // Register shared services used across modules
            services.AddScoped<MailService>();
            services.AddScoped<EmailService>();
            services.AddScoped<UserNotificationService>();
            services.AddScoped<IFormattingService, FormattingService>();

            return services;
        }
    }
}
