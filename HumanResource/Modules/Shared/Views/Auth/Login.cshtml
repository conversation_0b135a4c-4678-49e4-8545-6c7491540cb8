﻿@using HumanResource.Modules.Shared.Models.Entities
@model UserLogin
@{
    Layout = "_HomeLayout";
}

<div class="d-flex h-100 justify-content-center align-items-center ">


<form class="login-form  wmin-sm-500" asp-action="login" asp-controller="Auth" id="login-form">
    <div class="card shadow ">
        <div class="card-body">
            <div class="text-center">
                <img class="mb-3" src="~/img/gsclogo.png" alt="GSC Logo" style="height:132px" />


            </div>
            <div class="text-center text-dark">
                <h1 class="display-9 mb-0">مرحبا !</h1>
                <h1 class="subheading-1 mb-5">@Model.UserName</h1>

                <div class="progress rounded-pill border-1 border-primary bg-white shadow-0">
                        <div class="progress-bar  bg-success rounded-pill " role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemax="100" id="loading-progress-bar"></div>
                </div>

                <br>
                <br>
            </div>

            <div style="display: none;">
                <div class="row">

                    <div class="form-group">
                        <label asp-for="UserName" class="control-label"></label>
                        <input asp-for="UserName" class="form-control" outlined />
                        <span asp-validation-for="UserName" class="text-danger"></span>
                    </div>


                </div>
                <div class="row">

                    <div class="form-group">
                        <label asp-for="Password" class="control-label"></label>
                        <input asp-for="Password" class="form-control" outlined icontrailing="visibility_off" type="password" />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>
                    <div class="d-flex align-items-center">
                        <mwc-formfield label="Remember password"><mwc-checkbox></mwc-checkbox></mwc-formfield>
                    </div>

                </div>

                <div class="form-group mt-3">
                    <input type="submit" value="Login" class="btn btn-primary btn-block" />
                </div>
            </div>
        </div>
    </div>
</form>
</div>
<script>
    $(document).ready(function () {
        setTimeout(function () {
            $("#login-form").submit()
        }, 2000);

        loadingBar()
    });

    function loadingBar(){
        var progress = document.getElementById("loading-progress-bar");
        var width = 0;
        var intervel = setInterval(function(){
            width+= Math.random()*10;
            width = Math.min(width,100);
            progress.style.width = width+"%";
            progress.setAttribute("aria-valuenow",width);

            if(width >= 100){
                clearInterval(intervel);
            }
        },3000 / (100/10));
    }
</script>