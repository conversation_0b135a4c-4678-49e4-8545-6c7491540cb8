@model Dictionary<string, string>


<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@(Model.ContainsKey("subject") ? Model["subject"] : "GSC")</title>
    <style>
        body {
            font-family: Arial, Tahoma, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9f9f9;
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0ad4e;
            padding-bottom: 10px;
        }
        .header h1 {
            color: #f0ad4e;
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #777;
            font-size: 14px;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin: 15px 0;
            border-right: 4px solid #f0ad4e;
        }
        .signature {
            margin-top: 30px;
        }
        .employee-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .employee-info p {
            margin: 5px 0;
        }
        .custom-message {
            padding: 15px;
            background-color: #e9f5ff;
            border-radius: 5px;
            margin: 15px 0;
            border-right: 4px solid #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container">

        <div class="content">
            <p>
                @Model["body"]
            </p>
        </div>
        <div class="footer">
            <p>هذا البريد الإلكتروني تم إرساله تلقائياً من نظام الموارد البشرية، يرجى عدم الرد عليه.</p>
            <p>تاريخ الإرسال: @DateTime.Now.ToString("yyyy-MM-dd HH:mm")</p>
        </div>
    </div>
</body>
</html> 