﻿@{
    Layout = "_Layout";
}
<div class="row">
    <div class="col-md-4">

        <div class="card shadow border-0 bg-primary " style="border:0 !important">
            <div class="card-body py-3 text-white">
                <div class="row">
                    <div class="col-md-12 ">
                        <div class="text-center">
                            @if (!string.IsNullOrEmpty(Model.Employee.ProfileImage))
                            {
                                <div class="profile-thumb mx-auto"
                                style="width: 80px;height: 80px;border-radius: 40px;background: url('@Model._h.GetFile(Model.Employee.ProfileImage)') no-repeat; background-position: center center;background-size: cover;position:relative; object-fit:cover">
                                </div>
                            }
                            else
                            {
                                <div class="profile-thumb mx-auto"
                                     style="width: 80px;height: 80px;border-radius: 40px;background: url('/assets/images/user60x60.png') no-repeat; background-position: center center;background-size: cover; position:relative;">
                                </div>
                            }
                        </div>
                        <div class="d-flex align-items-center">

                            <div class=" align-items-center text-xl-left flex-column flex-xl-column m-0 ">

                                <div class="media-body text-white pl-3">
                                    <br>
                                    <h1 class="mb-0 text-center"></h1>
                                    <h2 class="mb-0">@Model.Employee.EmpNameA  (@Model.Employee.EmpNo)</h2>

                                    <h4 class="text-uppercase pt-21">
                                        <i>@Model.Employee.DesgCode</i>
                                    </h4>
                                    <p class="text-uppercase ">
                                        @Model.Employee.DeptDespA
                                    </p>

                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8" id="">
        @RenderBody()
    </div>

</div>