﻿@{

    var CurrentRoute = Context.Request.Path;

}
<div class="d-flex  mb-1" >

    <a 
    
    class="btn btn-sm mr-1 btn-@(CurrentRoute=="/Execuses/Manager" ? "primary" : "")" 
    href="~/Execuses/Manager">@Model._l("الاستئذان") <span class="badge badge-@(@Model.ManagerTabsCounter()["Execuses"]>0?"danger":"light border") px-2 rounded-pill">@Model.ManagerTabsCounter()["Execuses"]</span></a>

    <a 
    class="btn btn-sm mr-1 btn-@(CurrentRoute=="/Leaves/Manager" ? "primary" : "")"
       href="~/Leaves/Manager">@Model._l("الاجازات") <span class="badge badge-@(@Model.ManagerTabsCounter()["Leaves"]>0?"danger":"light border") px-2 rounded-pill">@Model.ManagerTabsCounter()["Leaves"]</span></a>

    <a 
    class="btn btn-sm mr-1 btn-@(CurrentRoute=="/Inventory/Manager" ? "primary" : "")" 
    href="~/Inventory/Manager" >@Model._l("المخزن")</a>


    <a 
    class="btn btn-sm mr-1 btn-@(CurrentRoute=="/Transports/Manager" ? "primary" : "")" 
       href="~/Transports/Manager">@Model._l("المواصلات") <span class="badge badge-@(@Model.ManagerTabsCounter()["Transports"]>0?"danger":"light border") px-2 rounded-pill">@Model.ManagerTabsCounter()["Transports"]</span></a>



    <a 
    class="btn btn-sm mr-1 btn-@(CurrentRoute=="/Training/ManagerAproval" ? "primary" : "")"
       href="~/Training/ManagerApproval">
        @Model._l("الدراسات")
        <span class="badge badge-@(@Model.ManagerTabsCounter()["Training"]>0?"danger":"light border") px-2 rounded-pill">@Model.ManagerTabsCounter()["Training"]</span>
   </a>


    <a 
    class="btn btn-sm mr-1 btn-@(CurrentRoute=="/OverTime/Manager" ? "primary" : "")"
       href="~/OverTime/Manager">@Model._l("العمل الاضافي") <span class="badge badge-@(@Model.ManagerTabsCounter()["OverTime"]>0?"danger":"light border") px-2 rounded-pill">@Model.ManagerTabsCounter()["OverTime"]</span></a>


   


@*    <a style=" border-top-right-radius:15px; border-top-left-radius:15px;"
       class="nav-item nav-link @(CurrentRoute=="/Inventory/Technical" ? "active" : "")"
       href="~/Inventory/Technical">@Model._l("Technical Note")</a>*@




</div>