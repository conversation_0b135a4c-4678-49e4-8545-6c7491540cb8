﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">

@Html.Partial("Inc/_Head")
<body class=" @(Model?.Page?.Class ?? "")">
    <style>
        #popoverlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
        }
    </style>
    <div id="popoverlay"></div>







    <!-- /navbar content -->

    <div class="content-wrapper">

        <!-- Page header -->

        <div class="page-content">

            @Html.Partial("Inc/_Sidebar")
            <div class="content pt-0 pl-1">
                @Html.Partial("Inc/_Header")


                <div class="page-header py-0 print-hide">

                    <div class="page-header-content header-elements-inline  rounded bg px-0 alpha-gray">
                        <div class="page-title py-2 d-flex ">
                            <h4>
                                @{
                                    var n = 1;
                                    if (@Model.Page?.Breadcrumb != null)
                                    {
                                        foreach (var item in Model.Page.Breadcrumb)
                                        {
                                            if (n != Model.Page.Breadcrumb.Count)
                                            {
                                                <a href="@item.Url"><span class="font-weight-semibold text-grey">@Model._l(item.Label)</span></a> <text>/</text>
                                            }
                                            else
                                            {
                                                <span class="font-weight-semibold text-grey">@Model._l(item.Label)</span>
                                            }
                                            n++;
                                        }
                                    }
                                }
                            </h4>
                        </div>
                        <div class="header-elements d-none text-center text-sm-left mb-2 mb-sm-0">
                            @{
                                if (@Model.Page?.Filter?.DateFrom != null)
                                {
                                    <button type="buttom" data-toggle="modal" data-target="#filter-modal" class="btn btn-link text-muted border-bottom-2 border-bottom-grey-300 rounded-0 mx-1  btn-sm ">
                                        <i class="far fa-calendar-alt"></i>
                                        @Model.Page.Filter.DateFrom.ToString("dd/MM/yyyy") - @Model.Page.Filter.DateTo.ToString("dd/MM/yyyy")
                                    </button>

                                    <!-- Here goes your modal HTML, replace PHP with Razor syntax as necessary -->

                                    <div class="modal" id="filter-modal" tabindex="-1" role="dialog" aria-labelledby="filter-modal-title" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                                        <div class="modal-dialog modal-dialog-centered" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h3 class="modal-title" id="filter-modal-title"><i class="far fa-calendar-alt"></i> @Model._("Filter")</h3>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
     
                                                    <form action="#" method="get" class="form-row mb-2 no_csrf" id="filter-form">
                                                        <div class="col">
                                                            <label for="">من</label>
                                                            <input name="from" id="from" type="date" class="form-control text-center" value="@Model.Page.Filter.DateFrom.ToString("yyyy-MM-dd")">

                                                        </div>
                                                        <div class="col">
                                                            <label for="">الى</label>
                                                            <input name="to" id="to" type="date" class="form-control text-center" value="@Model.Page.Filter.DateTo.ToString("yyyy-MM-dd")">
                                                        </div>




                                                    </form>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">الغاء</button>

                                                    <button onclick="$('#filter-form').submit();" type="button" class="btn btn-primary">تطبيق</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }

                            @{
                                if (Model.Page?.Reload != null)
                                {
                                    <button type="button" onclick="window.location.href = window.location.href" class="btn   btn-link text-muted" title="@Model._l("Reload")">
                                        <i class="fa fa-sync-alt"></i>

                                    </button>
                                }
                            }

                            @{
                                if (Model.Page?.Back != null)
                                {
                                    <a href="@Model.Page.Back" class="btn   btn-link text-dark btn-sm text-muted" title="@Model._l("Back")">
                                        <i class="fa fa-arrow-left"></i>

                                    </a>
                                }
                            }


                        </div>
                    </div>
                </div>

                <div class="row mt-1">
                    <div class="col">
                        <div class="alerts"></div>
                        @RenderBody()
                    </div>
                </div>
            </div>


            <!-- Modal --> @*tabindex="-5" aria-labelledby="exampleModalLabel" aria-hidden="true"*@
            <div class="modal fade" id="form-modal" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel"></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                        </div>

                    </div>
                </div>
            </div>



            @await RenderSectionAsync("Scripts", required: false)



            @Html.Partial("Inc/_Foot")


</body>
</html>