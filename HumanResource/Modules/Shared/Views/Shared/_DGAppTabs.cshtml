﻿@{
    var CurrentRoute = Context.Request.Path;
}
<div class="d-flex mb-1" >

    @if (Model.Can("Absent-dgeneral|Absent-admin"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Execuses/Dg" ? "primary":"")"
           href="~/Execuses/Dg">
            @Model._l("الاستئذان")
            <span class="badge badge-@(@Model.DGManagerTabsCounter()["Execuses"]>0?"danger":"light") px-2 ">@Model.DGManagerTabsCounter()["Execuses"]</span>

        </a>

    }

    @if (Model.Can("transports-dgeneral|transports-admin"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Transports/Dg" ? "primary":"")"
           href="~/Transports/Dg">
            @Model._l("المواصلات")
        </a>

    }

    @if (Model.Can("leaves-dgeneral|leaves-admin"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Leaves/Dg" ? "primary":"")"
           href="~/Leaves/Dg">
            @Model._l("الاجازات") <span class="badge badge-light text-@(@Model.DGManagerTabsCounter()["Leaves"]>0?"danger":"dark") px-2 ">@Model.DGManagerTabsCounter()["Leaves"]</span>
        </a>
    }

    @if (Model.Can("Training-admin|Training-DG"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Training/DGManagerApproval" ? "primary":"")"
           href="~/Training/DGManagerApproval">
            @Model._l("الدراسات")
            <span class="badge badge-@(@Model.DGManagerTabsCounter()["Studys"]>0?"danger":"light") px-2 ">@Model.DGManagerTabsCounter()["Studys"]</span>
        </a>
    }
   
    @if (Model.Can("Training-admin|Training-DG"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Training/ManagerViewCourse" ? "primary":"")"
           href="~/Training/ManagerViewCourse">
            @Model._l("التدريبات")
            
        </a>

    }


    @if (Model.Can("overtime-dg"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Overtime/Dg" ? "primary":"")"
           href="~/Overtime/Dg">
            @Model._l("العمل الاضافي التقديري")
            <span class="badge badge-@(@Model.DGManagerTabsCounter()["Overtime"]>0?"danger":"light") px-2 ">@Model.DGManagerTabsCounter()["Overtime"]</span>
        </a>

    }

    @if (Model.Can("inventory-dgeneral|inventory-admin"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Inventory/Dg" ? "primary":"")"
           href="~/Inventory/Dg">
            المخازن
            <span class="badge badge-@(@Model.DGManagerTabsCounter()["Inventory"]>0?"danger":"light") px-2 ">@Model.DGManagerTabsCounter()["Inventory"]</span>
        </a>
    }


   @* @if (Model.Can("Leave-dgeneral|Leave-admin"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Leaves/DGApproval" ? "primary":"")"
           href="~/Leaves/DGApproval">
        @Model._l("Leaves")
        </a>
    }*@
    
</div>