﻿

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@Model._l(Model.Page.Title)</title>


    <link href="~/assets/css/icons/icomoon/styles.min.css" rel="stylesheet" type="text/css">

    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> -->
    <link href="~/assets/css/icons/fontawesome/all.css" rel="stylesheet" type="text/css">

   
    <link href="~/assets/css/rtl/bootstrap.css" rel="stylesheet">
    <link href="~/assets/css/rtl/bootstrap_limitless.css" rel="stylesheet">
    <link href="~/assets/css/rtl/layout.css" rel="stylesheet">

    <link href="~/assets/css/rtl/components.css" rel="stylesheet">
    <link href="~/assets/css/rtl/colors.css" rel="stylesheet">
 
    <link href='~/assets/css/ElMessiri.css' rel='stylesheet'>
    <link href="~/assets/css/custom.css?d=@DateTime.Today.ToString("yyyy-MM-dd")" rel="stylesheet">
    <script src="~/assets/js/jquery.min.js"></script>


    <script src="~/assets/js/plugins/ui/moment/moment.min.js"></script>


    <script src="~/assets/js/plugins/extensions/jquery_ui/widgets.min.js"></script>

    <script src="~/assets/js/main/bootstrap.bundle.min.js"></script>
    <script src="~/assets/js/plugins/loaders/blockui.min.js"></script>
    <script src="~/assets/js/vue.min.js"></script>
    <script src="~/assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="~/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="~/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="~/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="~/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="~/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="~/assets/js/plugins/notifications/sweet_alert.min.js"></script>
    <script src="~/assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="~/assets/js/plugins//notifications/pnotify.min.js"></script>
    <script src="~/assets/js/chart.js"></script>
    <script src="~/assets/js/chartjs-plugin-datalabels.js"></script>


    <script src="~/assets/js/main/app.js?d=@DateTime.Today.ToString("yyyy-MM-dd")"></script>
    <script src="~/assets/js/main/custom.js?d=@DateTime.Today.ToString("yyyy-MM-dd")"></script>

    <style>

    </style>

</head>
