﻿@using HumanResource.Core.UI.Services
@inject NavigationService NavigationService

@{
    var navigation = await NavigationService.GetNavigationAsync();
}

<div class="sidebar sidebar-light  sidebar-main bg-light  sidebar-expand-md  print-hide m-1 shadow"
    style="border-radius: 10px;">
    <!-- Sidebar mobile toggler -->
    <div class="sidebar-mobile-toggler text-center ">
        <a href="#" class="sidebar-mobile-main-toggle">
            <i class="fas fa-times"></i>
        </a>
        APP
        <a href="#" class="sidebar-mobile-expand">
            <i class="icon-screen-full"></i>
            <i class="icon-screen-normal"></i>
        </a>
    </div>
    <!-- /sidebar mobile toggler -->
    <!-- Sidebar content -->
    <div class="sidebar-content">

        <!-- Main navigation -->

        <div class="card px-4 py-2">
                  <img class="mb-3" src="~/img/gsclogo.png" alt="GSC Logo" style="height:100%" />
        </div>
        <div class="card card-sidebar-mobile">
            <ul class="nav nav-sidebar" data-nav-type="accordion">
                @foreach (var group in navigation)
                {
                    @if(group.Name.Length > 0 && !string.IsNullOrEmpty(group.Name)){
                        <li class="nav-item-header">
                            <div class="font-size-xs line-heigt-xs text-center">
                                <hr class="mt-0 pt-0">
                                <strong class="text-primary">@group.Name</strong>
                            </div>
                        </li>
                    }
                    @foreach (var item in group.NavItems.Where(i => !i.Hidden))
                    {
                        var len = item.Links.Count(l => !l.Hidden);
                        bool submenu = len > 1;

                        if(submenu){
                            <li class="nav-item nav-item-submenu @(Model.Page.Active == item.Active ? " active nav-item-expanded nav-item-open" : "" )">
                                <a href="#" class="nav-link @(Model.Page.Active == item.Active ? " active" : "collapsed" )">
                                    @Html.Raw(item.Icon)
                                    <span>@Model._l(item.Label)</span>
                                    @if (item.Badge > 0)
                                    {
                                    <span class="badge badge-danger align-self-center ml-auto">@item.Badge</span>
                                    }
                                </a>
                                <ul class="nav nav-group-sub " data-submenu-title="@item.Label">
                                    @foreach (var link in item.Links.Where(l => !l.Hidden))
                                    {
                                        <li class="nav-item">
                                            <a class="nav-link pl-1" href="@link.Url">
                                                @if (!string.IsNullOrEmpty(link.Icon))
                                                {
                                                <i class="@link.Icon"></i>
                                                }
                                                <span>@Model._l(link.Label)</span>
                                                @if (link.Badge > 0)
                                                {
                                                <span class="badge badge-danger align-self-center ml-auto">@link.Badge</span>
                                                }
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </li>
                        }else if(len == 1){
                            var visibleLink = item.Links.First(l => !l.Hidden);
                            <li class="nav-item  @(Model.Page.Active == item.Active ? " active " : "")">
                                <a href="@visibleLink.Url" class="nav-link @(Model.Page.Active == item.Active ? " active" : "" )">
                                    @Html.Raw(item.Icon)
                                    <span>@Model._l(item.Label)</span>
                                    @if (item.Badge > 0)
                                    {
                                    <span class="badge badge-danger align-self-center ml-auto">@item.Badge</span>
                                    }
                                </a>
                            </li>
                        }
                    }
                }
            </ul>

        </div>
        <!-- /main navigation -->

    </div>
    <!-- /sidebar content -->
</div>