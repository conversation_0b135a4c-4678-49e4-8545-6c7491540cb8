﻿<!-- /header with logos -->
<div class="navbar navbar-expand-md navbar-dark bg-primary mb-2 mt-1 shadow border-0" style="border-radius: 10px;">

    <!-- Header with logos -->
    <!-- /header with logos -->
    <!-- Mobile controls -->
    <div class="d-flex flex-1 d-md-none">
        <div class="navbar-brand mr-auto">
            <a href="#" class="d-inline-block text-light">
                <strong>APP</strong>
            </a>
        </div>

        <!--  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-mobile">
          <i class="fas fa-angle-down"></i>
        </button> -->


        <button class="navbar-toggler sidebar-mobile-main-toggle" type="button">
            <i class="icon-paragraph-justify3">cc</i>
        </button>

    </div>
    <!-- /mobile controls -->
    <!-- Navbar content -->
    <div class="collapse navbar-collapse d-md-flex" id="navbar-mobile">

        <ul class="navbar-nav">
            <li class="nav-item">
                <a href="#" class="navbar-nav-link sidebar-control sidebar-main-toggle d-none d-md-block ">
                    <i class="icon-paragraph-justify3"></i>
                </a>
            </li>
        </ul>
        <ul class="navbar-nav">




            <li class="nav-item">
                <a href="~/Account/UserDashboard" class="navbar-nav-link">@Model._l("الرئيسية")</a>
            </li>




            <li class="nav-item nav-item dropdown ">
                <a href="#" class="navbar-nav-link dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
                    @Model._l("الاستمارات")
                </a>

                <div class="dropdown-menu dropdown-menu-right">

                    <a href="~/Transports/My" class="dropdown-item">@Model._("المركبات")</a>


                    <a href="~/OverTime/My/" class="dropdown-item">@Model._(" العمل الاضافي")</a>

                    <a href="~/Execuses/My" class="dropdown-item">@Model._("الاستئذانات")</a>

                    @if (Model.Can("admin"))
                    {
                        <a href="~/Training/CourseReq" class="dropdown-item">@Model._(" التدريب")</a>
                        <a href="~/Inventory/My/" class="dropdown-item">@Model._("المخازن")</a>
                        <a href="~/Training/EmpRequest" class="dropdown-item">  @Model._("طلبات الدراسة")</a>
                        <a href="~/Leaves/My" class="dropdown-item">@Model._("الاجازات")</a>
                    }
                </div>



            </li>


        </ul>
        <ul class="navbar-nav">
            @if (Model.AppEnvironment == "Development")
            {
                <li class="nav-item">
                    <a href="#" class="navbar-nav-link disabled" disabled>UAT Mode</a>
                </li>
            }
        </ul>



    </div>

    <!-- Navbar content -->
    <div class="collapse navbar-collapse d-md-flex ml-auto justify-content-end" id="navbar-mobile">


        <ul class="navbar-nav ">

            <!-- Desktop Notification Permission Button -->
            <li class="nav-item">
                <button type="button" class="btn btn-link navbar-nav-link notification-permission-button" style="display: none; cursor: pointer;">
                    <i class="fas fa-bell"></i> @Model._l("تفعيل الإشعارات")
                </button>
            </li>

    

            @*<li class="nav-item nav-item dropdown ">
                <a href="#" class="navbar-nav-link  " data-toggle="dropdown" aria-expanded="true">


                    <span>
                        <i class="fas fa-flag-checkered mx-1"></i>
                        التنبيهات
                        @if (Model.UserTasks.Count > 0)
                        {
                            <span class="badge badge-pill  bg-danger mt-1">@Model.UserTasks.Count</span>
                        }

                </a>

                <div class="dropdown-menu dropdown-menu-right ">
                    @foreach (var nf in Model.UserTasks)
                    {
                        <a href="@nf.Url" class="dropdown-item">
                            <i class="@nf.Icon mr-3"></i>
                            <div>
                                <h6 class="my-0">@nf.Title</h6>
                                <p class="my-0 text-muted">@nf.Text</p>
                            </div>
                        </a>
                    }

                    @if (Model.UserTasks.Count == 0)
                    {
                        <a href="#" class="dropdown-item text-muted">
                            <i class="fa fa-check  mr-3"></i>
                            <div>
                                <h6 class="my-0">لا يوجد تنبيهات جديدة</h6>
                                <p class="my-0 text-muted"></p>
                            </div>
                        </a>
                    }


                </div>



            </li>*@
            @* <li class="nav-item nav-item dropdown ">
            <a href="#" class="navbar-nav-link dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
            Arabic
            </a> *@

            @* <div class="dropdown-menu dropdown-menu-right ">
            <a href="#" class="dropdown-item"> Arabic</a>
            <a href="#" class="dropdown-item"> English</a>

            </div> *@
            @* </li> *@
        </ul>

        <ul class="navbar-nav ">
            <li class="nav-item nav-item dropdown ">
                <a href="#" class="navbar-nav-link dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
                    @if (Model.Profile.ProfileImage != null)
                    {
                        <img src="@Model._h.GetFile(Model.Profile.ProfileImage)" class="rounded-circle mr-2" style="width: 24px; height: 24px; object-fit: cover;">
                    }
                    else
                    {
                        <img src="/assets/images/user60x60.png" class="rounded-circle mr-2" style="width: 24px; height: 24px; object-fit: cover;">
                    }
                    @Model.Auth.EmpNameA
                </a>

                <div class="dropdown-menu dropdown-menu-right" style="min-width: 280px;">
                    <div class="dropdown-header d-flex align-items-center py-3">
                        <div class="mr-3">
                            @if (Model.Profile.ProfileImage != null)
                            {
                                <img src="@Model._h.GetFile(Model.Profile.ProfileImage)" class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;">
                            }
                            else
                            {
                                <img src="/assets/images/user60x60.png" class="rounded-circle" style="width: 48px; height: 48px; object-fit: cover;">
                            }
                        </div>
                        <div>
                            <h6 class="mb-0">@Model.Profile.EmpNameA</h6>
                            <small class="text-muted">@Model.Profile.EmpNo</small>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>
                    <div class="dropdown-item-text">
                        <div class="d-flex align-items-center mb-2">
                            <i class="far fa-id-badge text-muted mr-2"></i>
                            <span class="text-muted small">@Model.Profile.DesgCode</span>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="far fa-building text-muted mr-2"></i>
                            <span class="text-muted small">@Model.Profile.DeptDespA</span>
                        </div>
                    </div>
                    @* <div class="dropdown-divider"></div>
                 
                    <a href="~/Account/Profile" class="dropdown-item">
                        <i class="fas fa-user mr-3"></i>
                        @Model._l("الملف الشخصي")
                    </a> *@
               
                </div>
            </li>
        </ul>

    </div>




</div>


