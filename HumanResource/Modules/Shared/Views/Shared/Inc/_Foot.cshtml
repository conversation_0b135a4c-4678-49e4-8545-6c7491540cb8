﻿</div>
</div>


	<!-- Enhanced Loading Styles -->
	<style>
		/* Loading link indicators */
		.loading-link {
			opacity: 0.7;
			pointer-events: none;
			position: relative;
		}
		
		.loading-link:after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 4px;
		}
		
		/* Enhanced loading modal */
		.swal2-popup {
			border-radius: 12px !important;
			box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
		}
		
		.swal2-timer-progress-bar {
			background: linear-gradient(90deg, #28a745, #20c997) !important;
		}
		
		/* Navigation loading feedback */
		.nav-item.loading {
			opacity: 0.6;
		}
		
		.nav-item.loading .nav-link:after {
			content: '';
			display: inline-block;
			width: 12px;
			height: 12px;
			border: 2px solid transparent;
			border-top: 2px solid currentColor;
			border-radius: 50%;
			animation: spin 1s linear infinite;
			margin-left: 8px;
		}
		
		@@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}
		
		/* Form loading improvements */
		.form-loading {
			position: relative;
			pointer-events: none;
		}
		
		.form-loading:after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(248, 249, 250, 0.8);
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 1000;
		}
		
		/* Smooth transitions */
		.nav-link, .btn, a {
			transition: opacity 0.2s ease, transform 0.1s ease;
		}
		
		.nav-link:hover, .btn:hover, a:hover {
			transform: translateY(-1px);
		}

	</style>

	<script>
		if ($(".nav-group-sub li .active").length) {
			$(".nav-group-sub li .active").parent("li").parent("ul").parent(".nav-item-submenu").addClass("active nav-item-expanded nav-item-open")
			// $(".nav-group-sub li .active").parent("ul").css({"display":"block"})
		}


		$('.select-search').select2();


		function notifi(msg = "", type = 'success') {
			new PNotify({
				text: msg,
				addclass: 'bg-' + type + ' border-' + type
			});
		}
		if (typeof swal == 'undefined') {
			console.warn('Warning - sweet_alert.min.js is not loaded.');

		}
		function doswal(options) {
			var swalInit = swal.mixin({
				buttonsStyling: false,
				confirmButtonClass: 'btn btn-primary',
				cancelButtonClass: 'btn btn-light'
			});

			swalInit.fire(options);
		}

		function loading(action = 'start') {
			if (action == 'start') {
				notice = new PNotify({
					text: "Please wait",
					addclass: 'bg-light border-dark',
					type: 'info',
					icon: 'icon-spinner4 spinner',
					hide: false,
					buttons: {
						closer: false,
						sticker: false
					},
					opacity: .9,
					width: "170px"
				});

			} else if (action == 'remove' || action == 'hide') {
				try {
					notice.remove();
				} catch (e) {

				}
			}
		}


		function createDatatable(selector, showlength=true, extraOptions = {}){
			const defaultOptions = {
				language:{
					search:'<span></span> _INPUT_',
					searchPlaceholder:'Search',
					langthMenu: '<span>@Model._("Show"):</span> _MENU_',
					paginate:{'first':'@Model._("Show")','last':'@Model._("Last")','next':'@Model._("Next")','previous':'@Model._("previous")'}

				},
				autoWidth:false,
				dom:'<"datatable-header"fB ' + (showlength ? 'l':'') + '><"datatable-scroll-warp"t><"datatable-footer"ip>',
				stateSave:true,
				responsive:true,
				buttons:{
					dom:{
						buttons:{}
					},
					buttons:[
						{
							extend:'excel',
							text:'<i class="far fa-file-spreadsheet"></i>',
							className:'btn btn-sm btn-link border  text-primary ',
							title:'اكسل'
						}
					]
 
				},
			};

			if('ajax' in extraOptions){
				defaultOptions.processing = true;
				defaultOptions.serverSide = true;
				if(typeof extraOptions.ajax === 'string'){


					extraOptions.ajax = {
						url:extraOptions.ajax,
						type:'POST'
					}
				}
				
			}

			const options = {...defaultOptions, ...extraOptions};

			$(selector).DataTable(options);

			return $(selector).DataTable();

		}

		function initDatatable(){
			$.each($.fn.dataTable.tables(true),function(){

				let table = $(this).DataTable();
				let oldOptions = table.settings()[0].oInit;

				$(this).data('oldOptions',oldOptions);

				table.destroy();

				let selector = '#' + $(this).attr('id');
				let storedOptions = '#' + $(this).data('oldOptions');

				createDatatable(selector,true,storedOptions);

			});
		}


		$(document).ajaxStop(function () {
			loading("remove")
			$(".ui-pnotify.bg-light.border-dark").remove()
		});

		$(document).ajaxStart(function () {
			if (document.hasFocus()) {
				loading()
			} else {

			}
		});


		$(document).ready(function () {
			var datatables = [];
			$('table.datatable').each(function (index, el) {

				responsive = $(el).attr("aresponsive") ? $(el).attr("aresponsive") : true;

				processing = false;
				autoWidth = $(el).attr("aautoWidth") ? $(el).attr("aautoWidth") : false;

				buttons = $(el).attr("abuttons") ? $(el).attr("abuttons").replace(/\[|\]/g, '').split(',') : [];

				print_columns = null;
				hide = $(el).attr("ahide") ? $(el).attr("ahide") : []
				unsort = $(el).attr("aunsort") ? $(el).attr("aunsort") : []
				showlength = $(el).attr("showlength") ? ($(el).attr("showlength") == "false" ? false : true) : true;
				length = $(el).attr("length") ? ($(el).attr("length").split(",")) : [25, 50, 100];
				order = $(el).attr("aorder") ? JSON.parse($(el).attr("aorder").replace(/'/g, '"')) : [0, "desc"]
				console.log($(el).attr("showlength"))
				console.log(unsort)
				tableajax = null;

				var length = length.map(function (x) {
					return parseInt(x, 10);
				});


				tablebuttons = []
				for (var i = 0; i < buttons.length; i++) {
					if (buttons[i] == "colvis") {
						tablebuttons.push({
							extend: 'colvis',
							text: '<i class="icon-grid3"></i>',
							className: 'btn bg-indigo-400 btn-icon dropdown-toggle',
							postfixButtons: ['colvisRestore'],
						})
					}

					if (buttons[i] == "print") {
						tablebuttons.push({

							extend: 'print',
							className: 'btn btn-light border',
							exportOptions: {
								stripHtml: true,
								columns: print_columns
							}

						})
					}

					if (buttons[i] == "copyHtml5") {
						tablebuttons.push({

							extend: 'copyHtml5',
							className: 'btn btn-light border',
							exportOptions: {
								stripHtml: true,
								columns: print_columns
							}

						})
					}
				}

				if ($(el).hasClass("ajax")) {
					url = $(el).data("aurl") ? $(el).data("aurl") : "";
					tableajax = {
						'url': url, // json datasource
						type: "post",  // method  , by default get
					}


					processing = true;


				}

				console.log(unsort)

				datatables[index] = $(el).dataTable({
					"lengthMenu": [
						length,
						length
					],
					// lengthMenu:10,
					"order": [order],
					language: {
						search: '<span></span> _INPUT_',
						searchPlaceholder: 'Search',
						lengthMenu: '<span>Show:</span> _MENU_',
						paginate: { 'first': 'First', 'last': 'Last', 'next': 'Next', 'previous': 'Previous' }
					},
					"columnDefs": [
						{ "orderable": false, "targets": unsort },

					],
					dom: '<"datatable-header"fB' + (showlength ? 'l' : '') + '><"datatable-scroll-wrap"t><"datatable-footer"ip>',
					responsive: responsive,
					autoWidth: autoWidth,
					processing: processing,
					stateSave: true,
					serverSide: $(el).hasClass("ajax"),
					ajax: tableajax,
					buttons: {



						buttons: tablebuttons,
					},






				})
			});

			function reloadDatatable() {
				console.log(datatables)
				$('table.datatable.ajax').each(function (index, el) {
					$(el).DataTable().ajax.reload();
				})
			}


			// $("form.ajax").each(function(index, el) {
			$("body ").on('submit', 'form.ajax', function (event) {

				event.preventDefault()

				var form = $(this);
				var action = form.attr("action") ? form.attr("action") : "/";
				var method = form.attr("method") ? form.attr("method") : "post";

				var submit_button = form.find('button[type!=button]').attr('name');

				var formData = new FormData(this);

				if (submit_button) {
					formData.append(submit_button, 1);

				}
				formData.append("ajax_form", 1);



				if (method.toLowerCase() == "get") {
					console.log(action)
					for (var pair of formData.entries()) {

						action = addParams(action, pair)
					}

					location.href = action
				} else {
					var submit_button2 = form.find('button[type!=button]')
					$(submit_button2).prop('disabled', true)
					btntxt = $(submit_button2).text();
					$(submit_button2).empty()
					$(submit_button2).append('<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>');
					$.ajax({
						url: action,
						type: method,
						dataType: 'json',
						data: formData,
						cache: false,
						contentType: false,
						processData: false,
						success: function (data) {
							$(submit_button2).prop('disabled', false)
							$(submit_button2).empty()
							$(submit_button2).text(btntxt)
							var swall_text = "<ul>";
							messagesToShow=[];

							if(typeof data.message ==='string'){
								messagesToShow.push(data.message)
								swall_text += "<li>" + data.message + "</li>";

								@* notifi(data.message[i], data.type) *@

								if (form.find('.message').length == 1) {
									form.find('.message').empty();
									form.find('.message').append('<div class="alert alert-' + (data.type == 'error' ? 'danger' : data.type) + '">' + data.message + '</div>')
								}
							}else if(data.message !== null && Array.isArray(data.message)){
								for (i = 0; i < data.message.length; i++) {


									// doswal({
									// 	"type":data.type,
									// 	'text':data.message[i],
									// 	toast: true,
									// 	showConfirmButton: false,
									// 	position: 'top-right',
									// 	timer: 4000
									// });
									//
								
									messagesToShow.push(data.message[i])
									swall_text += "<li>" + data.message[i] + "</li>";

									@* notifi(data.message[i], data.type) *@

									if (form.find('.message').length == 1) {
										form.find('.message').empty();
										form.find('.message').append('<div class="alert alert-' + (data.type == 'error' ? 'danger' : data.type) + '">' + data.message[i] + '</div>')
									}
								}
							}

							
							swall_text += "</ul>"
							if(messagesToShow.length>0){


							    doswal({
								 	"type":(data.success == true?"success":"error"),
								 	'html':swall_text,
								 	toast: true,
								 	showConfirmButton: false,
								 	position: 'top-right',
									timer: 4000
								 });
							}



							try {
								if (data.action=="reload") {

									localStorage.setItem('messages', JSON.stringify(data.message));
									// Store the data.success in local storage
									localStorage.setItem('success', data.success);

									location.reload()
								}else{
									eval(data.action);
								}
								
							} catch (e) {
								console.error(e)
							}

							try {
								if (data.success == true) {
									if (form.children('.modal').length == 1) {
										form.children('.modal').modal('hide')
									}
								}

							} catch (e) {
								if (form.children('.modal').length == 1) {
									form.children('.modal').modal('hide')
								}
							}

							@* csrf_update(data.csrf) *@
							@* reloadDatatable(); *@
						}
					})
						.done(function () {
							console.log("success");
						})
						.fail(function () {
							console.log("error");
						})
						.always(function () {
							console.log("complete");
						});
				}

			});
			// });

			const uniqId = (() => {
				let i = 0;
				return () => {
					return (i++) + "tk";
				}
			})();

			// $("body a.popup").each(function(i,el){
			$('body ').on('click', 'a.popup', function (event) {
				// $('#popup-container').empty();
				event.preventDefault()

				var link = $(this);

				var url = link.attr("href");

				$.ajax({
					url: url,
					method: 'get',
					success: function (html) {
						id = uniqId();
						// $('#popup-container').append(html);
						$('#popup-container').append('<div id="modal-container-' + id + '"></div>');
						$('#modal-container-' + id).append(html);

						$("body #popup-container div form").each(function (j, form) {
							if ($(form).children("input[name=csrf]").length === 0) {
								$(form).append('<input name="csrf" class="csrf"  value="' + csrf + '" hidden>');
							}

						});

						$("body #modal-container-" + id + " .modal").attr({
							"data-modalid": id,
						});


						$("body #modal-container-" + id + " .modal").modal("show");



						$("body  .modal").on('hidden.bs.modal', function (e) {
							mid = $(this).data("modalid")
							if (typeof mid !== 'undefined' && mid !== false) {

								$(this).parents('#modal-container-' + mid).remove();
							}

						});

					},
					error: function (returnValue) { }
				});
			});
			// });



			window.onbeforeunload = function () {
				
					doswal({
						title: 'يرجى الانتظار',
						html: '<br><div class="spinner-border" style="width: 3rem; height: 3rem;" role="status"><span class="sr-only">Loading...</span></div><p class="mt-2">جار تحميل الصفحة...</p>',
						timer: 5000, // Reduced from 100 seconds to 5 seconds
						showConfirmButton: false,
						timerProgressBar: true,
						allowOutsideClick: false,
						allowEscapeKey: false,
						didOpen: () => {
							// Add custom styling for loading modal
							const popup = Swal.getPopup();
							if (popup) {
								popup.style.borderRadius = '10px';
								popup.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
							}
						}
					});
				
			};

			// Display success message if passed in URL
            const urlParams = new URLSearchParams(window.location.search);
            const successMessage = urlParams.get('success');

            if (successMessage) {
                Swal.fire({
                    position: 'center',
                    icon: 'success',
                    type: 'success',
                    title: successMessage,
                    showConfirmButton: false,
                    timer: 2500
                });
            }


		});


		$(window).on("load", function () {
			$("select.select2").select2();
		});

	

		$(document).ready(function () {

			$("select.select2").select2();
			
			


			$("input[type=number]").focus(function (event) {

				$(this).select()
			});


            $("body").on("click","a.popup",function(e){
                e.preventDefault();

				

                var url = $(this).attr('href');
                var size = $(this).data('size').split('x');

                var width = size[0];
                var height = size[1];

				const left = (screen.width - width) / 2;
				const top = (screen.height - height) / 2;


                const popup = window.open(url,"_blank",`width=${width},height=${height},scrollbars=yes,resizeable=yes,menubar=no,directories=no,toolbar=no,top=${top},left=${left}` );


				$("body").css("filter","blur(5px)");
				$("#popoverlay").show();

				const timer = setInterval(function(){
					if(popup.closed){
						clearInterval(timer);

						$("body").css("filter","none");
						$("#popoverlay").hide();
					}
				});


            })



			var elems = Array.prototype.slice.call(document.querySelectorAll('.form-check-input-switchery'));
			elems.forEach(function (html) {
				var switchery = new Switchery(html);
			});

			if (localStorage.getItem('messages')) {
        // Parse the messages back into an array
        var messages = JSON.parse(localStorage.getItem('messages'));
        var isSuccess = localStorage.getItem('success') == 'true';

        messages.forEach(function(message) {
            // Create the Bootstrap alert
            var alert = $('<div/>', {
                class: 'alert',
                role: 'alert',
                text: message
            });

            // If success, add class 'alert-success'. Otherwise, 'alert-danger'.
            if (isSuccess) {
                alert.addClass('alert-success');
            } else {
                alert.addClass('alert-danger');
            }

            // Append the alert to the '.alerts' div
            $('.alerts').append(alert);

            // Show the alert once
            alert.alert();
        });

        // Remove the messages and success status from local storage
        localStorage.removeItem('messages');
        localStorage.removeItem('success');
    }


		});

		document.addEventListener("DOMContentLoaded", function(){
			var links = document.querySelectorAll('.after-confirm');

			links.forEach(function(link){
				link.addEventListener('click', function(event){
					var userConfirmed = confirm("هل أنت متأكد من هذا الإجراء؟");

					if(!userConfirmed){
						event.preventDefault()
						//window.location.href = this.href;
					}
				})
			})
		});
	</script>
 <script src="~/js/site.js"></script>

<!-- Load notification scripts at the end of the file, just before the closing </div> tags -->
<script src="~/assets/js/desktop-notifications.js"></script>
<script src="~/assets/js/notifications-handler.js"></script>
<script src="~/assets/js/notification-debug.js"></script>

</div>
</div>


