﻿@{

    var CurrentRoute = Context.Request.Path;

}

@*ddsds*@
<div class="nav nav-tabs " id="nav-tab" role="tablist">


    @if (Model.Can("Training-admin|Affairs-Committee"))
    {
        <a style=" border-top-right-radius:15px; border-top-left-radius:15px;"
           class="nav-item nav-link @(CurrentRoute=="/Training/AffairsCommitteeApproval" ? "active" : "")"
           href="~/Training/AffairsCommitteeApproval">
            @Model._l("Training")
            <span class="badge badge-@(@Model.AffairsCommitteeTabsCounter()["Study"]>0?"danger":"light border") px-2 rounded-pill">@Model.AffairsCommitteeTabsCounter()["Study"]</span>

        </a>
    }


    
</div>