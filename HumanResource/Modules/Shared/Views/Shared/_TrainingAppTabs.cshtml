﻿@{

    var CurrentRoute = Context.Request.Path;

}
<div class="nav nav-tabs " id="nav-tab" role="tablist">
    <a style=" border-top-right-radius:15px; border-top-left-radius:15px;"
       class="nav-item nav-link @(CurrentRoute=="/Training/StudyApproval" ? "active" : "")"
       href="~/Training/StudyApproval">@Model._l("Study Approval")

        <span class="badge badge-@(@Model.TriningTabsCounter()["Study"]>0?"danger":"light border") px-2 rounded-pill">@Model.TriningTabsCounter()["Study"]</span>

   
   </a>
    
       <a style=" border-top-right-radius:15px; border-top-left-radius:15px;"
       class="nav-item nav-link @(CurrentRoute=="/Training/ViewCourses" ? "active" : "")"
       href="~/Training/ViewCourses">@Model._l("View Courses")</a>


    <a style=" border-top-right-radius:15px; border-top-left-radius:15px;"
       class="nav-item nav-link @(CurrentRoute=="/Training/TrainingAndQualificationLaw" ? "active" : "")"
       href="~/Training/TrainingAndQualificationLaw">@Model._l("Training and Qualification Law")</a>
</div>