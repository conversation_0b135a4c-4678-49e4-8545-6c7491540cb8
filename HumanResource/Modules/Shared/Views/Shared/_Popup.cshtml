﻿<!DOCTYPE html>
<html lang="@Model.Translator.language().ToLower()" dir="@(Model.Translator.language()=="AR"?"rtl":"ltr")">
    
@Html.Partial("Inc/_Head")

<body class=" @(Model?.Page?.Class ?? "")">

    <!-- /navbar content -->

    <div class="content-wrapper">

        <!-- Page header -->

        <div class="page-content">
    <div class="content pt-0">


        
<div class="page-header py-0 print-hide">

    <div class="page-header-content header-elements-inline  rounded bg px-0 alpha-gray" >

  <div class="header-elements d-none text-center text-sm-left mb-2 mb-sm-0">
	
    <a  href="#" onclick="window.opener.location.reload();window.close()" class="btn mx-1  btn-danger btn-labeled btn-labeled-right rounded-round btn-sm pr-4 pl-1" style="padding-right:10px !important;">
            <b><i class="fas fa-times  "></i></b>
				@Model._l("Close")
			</a>
    
	@{
		if (Model.Page?.Reload != null)
		{
			<button type="button" onclick="window.location.href = window.location.href" class="btn mx-1  btn-light btn-labeled btn-labeled-right rounded-round btn-sm pr-4" style="padding-right:10px !important;">
            <b><i class="fas fa-sync-alt"></i></b>
				@Model._l("Reload")
			</button>
		}
	}

	


  </div>
    </div>
    </div>
<div class="row">
    <div class="col">
		<div class="alerts"></div>
		@RenderBody()
		</div>
</div>
    </div>


    <!-- Modal --> @*tabindex="-5" aria-labelledby="exampleModalLabel" aria-hidden="true"*@
    <div class="modal fade" id="form-modal"  role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel"></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                </div>

            </div>
        </div>
    </div>


    @await RenderSectionAsync("Scripts", required: false)



@Html.Partial("Inc/_Foot")


</body>
</html>