﻿
<div class="sidebar sidebar-light  sidebar-main bg-light  sidebar-expand-md  print-hide" >
	<!-- Sidebar mobile toggler -->
	<div class="sidebar-mobile-toggler text-center ">
		<a href="#" class="sidebar-mobile-main-toggle">
			<i class="fas fa-times"></i>
		</a>
		APP
		<a href="#" class="sidebar-mobile-expand">
			<i class="icon-screen-full"></i>
			<i class="icon-screen-normal"></i>
		</a>
	</div>
	<!-- /sidebar mobile toggler -->
	<!-- Sidebar content -->
	<div class="sidebar-content" >
		
		
		<!-- Main navigation -->
		<div class="card card-sidebar-mobile">
			<ul class="nav nav-sidebar" data-nav-type="accordion">
    @foreach (var item in Model.NavMenu.Items)
    {
        <li class="nav-item nav-item-submenu @(Model.Page.Active == item.Active ? "active  nav-item-expanded nav-item-open" : "")">
            <a href="#"class="nav-link @(Model.Page.Active == item.Active ? "active" : "collapsed")">
                @Html.Raw(item.Icon)
                <span>@Model._l(item.Label)</span>
                @if (item.Badge > 0)
                {
                    <span class="badge badge-light align-self-center ml-auto">@item.Badge</span>
                }
            </a>
            <ul class="nav nav-group-sub " data-submenu-title="@item.Label">
                @foreach (var link in item.Links)
                {
                    <li class="nav-item">
                        <a class="nav-link pl-1" href="@link.Url">
                            @if (!string.IsNullOrEmpty(link.Icon))
                            {
                                <i class="@link.Icon"></i>
                            }
                            <span>@Model._l(link.Label)</span>
                            @if (link.Badge > 0)
                            {
                                <span class="badge badge-light align-self-center ml-auto">@link.Badge</span>
                            }
                        </a>
                    </li>
                }
            </ul>
        </li>
    }
</ul>

		</div>
		<!-- /main navigation -->

		
	</div>
	<!-- /sidebar content -->
</div>