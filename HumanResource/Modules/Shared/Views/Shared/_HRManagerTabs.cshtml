@{

    var CurrentRoute = Context.Request.Path;

}
<div class="nav nav-tabs " id="nav-tab" role="tablist">

    @if (Model.Can("overtime-admin|overtime-department-manager"))
    {
        <a style=" border-top-right-radius:15px; border-top-left-radius:15px;"
           class="nav-item nav-link @(CurrentRoute=="/OverTime/Department/Manager" ? "active" : "")"
           href="~/OverTime/Department/Manager">
            @Model._l("OverTime")
        </a>
    }

    @if (Model.Can("hr"))
    {
        <a style=" border-top-right-radius:15px; border-top-left-radius:15px;"
           class="nav-item nav-link @(CurrentRoute.ToString().Contains("/Employees/AttendanceReport") ? "active" : "")"
           href="~/Employees/AttendanceReport">
            <i class="fas fa-chart-bar mr-1"></i> تقرير الحضور
        </a>
    }
</div>