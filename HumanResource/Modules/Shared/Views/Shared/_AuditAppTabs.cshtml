﻿@{
    var CurrentRoute = Context.Request.Path;
}
<div class="d-flex mb-1" >

    @if (Model.Can("audit"))
    {
        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Leaves/Audit/Return" ? "primary":"")"
           href="~/Leaves/Audit/Return">
            @Model._l("قطع الاجازة")
            <span class="badge badge-@(Model.AuditTabsCounter()["LeavesReturn"]>0?"danger":"light") px-2 ">@Model.AuditTabsCounter()["LeavesReturn"]</span>

        </a>

        <a 
           class="btn py-1 btn-sm mr-1 btn-@(CurrentRoute=="/Overtime/Audit" ? "primary":"")"
           href="~/Overtime/Audit">
            @Model._l("العمل الاضافي")
            <span class="badge badge-@(Model.AuditTabsCounter()["Overtime"]>0?"danger":"light") px-2 ">@Model.AuditTabsCounter()["Overtime"]</span>

        </a>

    }

    
    
</div>