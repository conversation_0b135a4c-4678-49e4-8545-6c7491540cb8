﻿@using HumanResource.Modules.Shared.ViewModels
@model ReportViewModel

@{
    var charts = Html.Raw(Json.Serialize(ViewBag.Charts));
}



<div class="d-flex justify-content-between py-2">
    


    <h3>@Model._l("Lost hours report")</h3>

    <div>
   
        
    </div>
</div>

<div class="d-flex">
@foreach(var chart in ViewBag.Charts){

    var chartid = chart.Name.Replace(" ","-");


    
        <div>
            <canvas id="chart-@chartid" height="150" width="150"></canvas>

            <p class="text-center"><a href="#" onclick="$('input[name=totalCount]').val(@chart.DgCount);$('input[type=search]').val('@chart.Name').trigger('change').trigger('keyup')"><small>@chart.Name</small></a></p>
        </div>
    
}
</div>

<div class="card shadow">
    

    <table class="table table-sm  table-striped table-hover " id="datatable">
        <thead>
            <tr>
                <td>#</td>
                <th>@Model._("Staff")</th>
                <th>@Model._("DgDespA")</th>
                <th>@Model._("DeptDespA")</th>
                <th>@Model._("Lost hours")</th>
            </tr>
        </thead>
        <tbody>
          @foreach(var ro in Model.VReportLostHours){
            <tr>
                <td>@ro.EmpNo</td>
                <td>@ro.EmpNameA</td>
                <td>@ro.DgDespA</td>
                <td>@ro.DeptDespA</td>
                <td>@ro.Hours</td>
   
            </tr>
          }
        </tbody>
        <tfoot>
            <td></td>
            <td></td>

            <td colspan="2" class="text-end">الساعات / ساعات العمل لجميع الموظفين</td>
            <td></td>
        </tfoot>
    </table>
</div>


<p class="text-muted">** تشمل هذه الساعات الاجازات المرضية والطارئة والتغيب عن العمل وعدم التقيد بساعات العمل الرسمية. </p>
<p class="text-muted">** تم احتساب الساعات من خلال استذئذانات والتاخر والاجازات المقدمة من قبل الموظف</p>


<input type="hidden" name="totalCount" value="@ViewBag.EmpCount">

<script>


$(document).ready(function(){
        createDatatable('#datatable',true, {
            "order":[[4,"desc"]],
            footerCallback:function(row,data,start,end,display){
                var api = this.api();

                var totalf = api.column(4,{"filter":"applied"}).data().reduce(function(a,b){
                    return a+parseFloat(b);
                },0);

                @* var totalfCount = $("input[name=totalCount]").val();

                if(){
                    var totalfCount = api.rows({filter:"applied"}).count();
                } *@

                @* var total = api.column(4).data().reduce(function(a,b){
                    return a+parseFloat(b);
                },0); *@

                $(api.column(4).footer()).html(totalf +"/"+ (@ViewBag.EmpCount*7*@ViewBag.TotoalDays).toFixed(0));
            }
        });


 

        var charts = @charts;

        charts.forEach(function(item){
            var cid = item.name.replace(/ /g,"-");
            var ctx = $("#chart-"+cid);
            var c = new Chart(ctx,{
                type:"doughnut",
                data:{
                    @* labels:[item.name], *@
                    datasets:[{
                        label:item.Name,
                        data:[item.percent,100-item.percent],
                        backgroundColor:[
                            "#fffff",
                            "rgba(1,1,1,0)"
                        ]

                    }]
                },
                options:{
                  
                    plugins:{
                        datalabels:{
                            formatter:(value,ctx) => {
                                return ctx.chart.data.labels[ctx.dataIndex];
                            },
                            color:"#fff"
                        }
                    },
                    elements:{
                        center:{
                            text:item.percent.toFixed(2)+"%",
                            color:"#444",
                            sidePadding:20
                        }
                    }
                },
                plugins:[{
                    beforeDraw:function(chart){
                        var width = chart.width,
                         height = chart.height,
                         ctx = chart.ctx;

                        ctx.restore();


                        var fontSize = (height / 114).toFixed(2);
                        ctx.font = fontSize + "em ";
                        ctx.textBaseline = "middle";

                        var text = chart.options.elements.center.text,
                        textX = Math.round((width - ctx.measureText(text).width)/2),
                        textY = height /2;

                        ctx.fillText(text,textX,textY)


                        
                        ctx.save();


                        
                    }
                }]
                
            })
        })
    });


    
</script>