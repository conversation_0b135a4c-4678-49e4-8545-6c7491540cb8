﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.BC;

[Table("VACC_NAME")]
[Keyless]
public class BcAccount
{
    [Column("BUDGET_YEAR")]
    public int BudgetYear { set; get; }

    [Column("GL_ACC_NAME")]
    public string Name { set; get; }


    [Column("GL_ACC_TYPE")]
    public int Type { set; get; }

    [Column("GL_ACC_HEAD_CODE")]
    public int HeadCode { set; get; }


    [Column("GL_ACC_SUB_HEAD_CODE")]
    public int SubHeadCode { set; get; }

    [Column("GL_ACC_DET_CODE")]
    public int DetCode { set; get; }

    [Column("GL_ACC_SUB_DET_CODE")]
    public int SubDetCode { set; get; }


    [NotMapped]
    public string Account
    {
        get
        {
            return Type + "-" + HeadCode + "-" + SubHeadCode + "-" + DetCode + "-" + SubDetCode;
        }
    }


}

