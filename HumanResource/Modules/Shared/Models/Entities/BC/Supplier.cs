﻿using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.BC;


[Table("BC_VENDOR_CODE_T")]
public class Supplier
{
	[Key]
	[Column("VENDOR_CODE")]
	public int Id { get; set; }


    [Column("VENDOR_COMP_NAME")]
    public string Name { get; set; }



    [Column("VENDOR_ADDRESS")]
    public string Address { get; set; }


    [Column("VENDOR_BANK_ACC_NO")]
    public string AccountNo { get; set; }


    [Column("TELEPHONE")]
    public string Phone { get; set; }

    [Column("OWN_NAME")]
    public string Owner { get; set; }





}

