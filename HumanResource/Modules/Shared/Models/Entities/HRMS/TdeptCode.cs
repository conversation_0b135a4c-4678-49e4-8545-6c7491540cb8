﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;



namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TDEPT_CODE")]
    [Keyless]
    public class TDeptCode
    {


        [Column("UNIT_CODE")]
        public int UnitCode { get; set; }

        [Column("DG_CODE")]
        public int DgCode { get; set; }

        [Column("DEPT_CODE")]
        public int DeptCode { get; set; }

        [Column("DEPT_DESP_A")]
        public string DeptDespA { get; set; }

        [Column("BOSS")]
        public int? Manager { get; set; }

        


        [NotMapped]
        public string Name { get  { return DeptDespA; } }



    }
}
