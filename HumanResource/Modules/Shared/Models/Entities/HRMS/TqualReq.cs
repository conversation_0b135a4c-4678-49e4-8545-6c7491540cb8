﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Keyless]
    [Table("TQUAL_REQ")]
    public class TqualReq
    {
        [Column("IN_YEAR")]
        public int InYear { get; set; }

        [Column("IN_DEPT_IND")]
        public int InDeptInd { get; set; }

        [Column("IN_MAIL_NO")]
        public int InMailNo { get; set; }

        [Column("IN_DOC_SL_NO")]
        public int? InDocSlNo { get; set; }

        [Column("UNIT_CODE")]
        public int UnitCode { get; set; }

        [Column("EMP_NO")]
        public int? EmpNo { get; set; }

        [Column("STUDY_TYPE")]
        public int? StudyType { get; set; }
        public virtual TstudyType TstudyType { get; private set; }

        [Column("COUNTRY_CODE")]
        public int? CountryCode { get; set; }
        public virtual TcountryCode TcountryCode { get; private set; }

        [Column("UNIV_INST_CODE")]
        public int? UnivInstCode { get; set; }
        public virtual TunivInstCode TunivInstCode { get; private set; }

        [Column("QUAL_CODE")]
        public int? QualCode { get; set; }
        public virtual TqualCode TqualCode { get; private set; }

        [Column("SUBJ_CODE")]
        public int? SubjCode { get; set; }
        public virtual TSubjectCode TSubjectCode { get; private set; }


        [Column("TRANS_TYPE")]
        public int? TransType { get; set; }

        [Column("RESON")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string Reson { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("REQ_STAT")]
        public string ReqStat { get; set; }

        [Column("TXN_DATE", TypeName = "DATE")]
        public DateTime? TxnDate { get; set; }

        [Column("REQ_FROM_DATE", TypeName = "DATE")]
        public DateTime? ReqFromDate { get; set; }

        [Column("REQ_TO_DATE", TypeName = "DATE")]
        public DateTime? ReqToDate { get; set; }

        [Column("TICKET_REQD_FLAG")]
        public int? TichetReqdFlag { get; set; }

        [Column("APPROVAL_DATE", TypeName = "DATE")]
        public DateTime? ApprovalDate { get; set; }

        [Column("MANG_APPR_ID")]
        public int? MangAppId { get; set; }

        [Column("APPROVAL_REM")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string ApprovalRem { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("CANCEL_FLAG")]
        public int? CancelFlag { get; set; } = 0;

        [Column("USER_ID")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
       public string UserId { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("TIME_STAMP", TypeName = "DATE")]
        public DateTime? TimeStamp { get; set; } = DateTime.Now;

        [Column("COURSE_STAT")]
        public int? CourseStat { get; set; }

        [Column("MANG_APRR_STAT")]
        public int? MandAprrStat { get; set; } = 0;

        [Column("SECTION_STAT")]
        public int? SectionStat { get; set; } = 0;

        [Column("SECTION_APRR_ID")]
        public int? SectionAprrId { get; set; }

        [Column("SECTION_APP_DATE")]
        public DateTime? SectionAprrDate { get; set; }

        [Column("SECTION_APP_REM")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string SectionAprrRem { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.


        [Column("DG_MANG_APRR_ID")]
        public int? DGMangAprrId { get; set; }

        [Column("DG_MANG_APRR_DATE")]
        public DateTime? DGMangAprrDate { get; set; }

        [Column("DG_MANG_APP_REM")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string DGMangAprrRem { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("DG_MANG_STAT")]
       public int? DGMangStat { get; set; } = 0;

        [Column("AFFAIRS_COMMITTEE_ID")]
        public int? AffairsCommitteeId { get; set; }

        [Column("AFFAIRS_COMMITTEE_APRR_DATE")]
        public DateTime? AffairsCommitteeAprrDate { get; set; }

        [Column("AFFAIRS_COMMITTEE_REM")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string AffairsCommitteeAprrRem { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("AFFAIRS_COMMITTEE_STAT")]
        public int? AffairsCommitteeStat { get; set; } = 0;



        [Column("DECISION_NO")]
        public string DecisionNo { get; set; }

        [Column("DECISION_DATE")]
        public DateTime? DecisionDate { get; set; }

        [Column("DECISION_NOTE")]
        public string DecisionNote { get; set; }

        [Column("DECISION_EMP_NO")]
        public int? DecisionEmpNo { get; set; } = 0;

        [Column("INITIAL_ACCEPTANCE_FILE_ID")]
        public int? InitialAcceptanceFileId { get; set; } = 0;

        [Column("INITIAL_ACCEPTANCE_FILE_GUID")]
        public string InitialAcceptanceFileGuid { get; set; } = null;

        [Column("INITIAL_ACCEPTANCE_APPR")]
        public int? InitialAcceptanceAppr { get; set; } = 0;

        [Column("INITIAL_ACCEPTANCE_DATE")]
        public DateTime? InitialAcceptanceDate { get; set; } 

        [Column("INITIAL_ACCEPTANCE_REM")]
        public string InitialAcceptanceRem { get; set; } 

        [NotMapped]
        public IFormFile InitialAcceptanceFile { get; set; }

      



    }
}
