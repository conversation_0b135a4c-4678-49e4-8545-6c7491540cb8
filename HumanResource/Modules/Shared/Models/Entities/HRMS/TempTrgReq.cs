﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{


    [Table("TEMP_TRG_REQ")]
    public class TempTrgReq
    {


        [Key]
        [Column("REQ_NO")]
        public int ReqNo { get; set; }

        [Column("COURSE_NAME")]
        public string CourseName { get; set; }

        [Column("COURSE_START_DATE")]
        public DateTime CourseStartDate { get; set; }

        [Column("COURSE_END_DATE")]
        public DateTime CourseEndDate { get; set; }


        [Column("YEAR")]
        public int Year { set; get; } = DateTime.Now.Year;




        [Column("FEE")]
        public int Fee { set; get; }





        [Column("REQ_DATE")]
        public DateTime ReqDate { get; set; }= DateTime.Now;



        [Column("BOSSNO")]

        public string BossNo { set; get; }


        [Column("PLACE")]
        public string Place { set; get; }

        [Column("APPROVAL")]
        public int Approval { set; get; } = 0;

        [Column("APPROVAL_DATE")]
        public DateTime? ApprovalDate { set; get; }

        [Column("APPROVED_BY")]
        public string ApprovedBy { set; get; } = "";

        [Column("FILEID")]
        public int FileId { get; set; } = 0;
        
        [Column("FILE_GUID")]
        public string FileGuid { get; set; } = null;

        [NotMapped]
        public IFormFile File { get; set; }

    
    }
}
