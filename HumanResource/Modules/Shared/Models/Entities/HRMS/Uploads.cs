﻿namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

[Table("TFILES", Schema = "DRCH")]
public class Uploads
{
    [Key]
    [Column("GUID")]
    public string Guid { get; set; }

    public Uploads()
    {
        Guid = System.Guid.NewGuid().ToString();
    }

    [Required]
    [StringLength(200)]
    [Column("LABEL")]
    public string Label { get; set; }
    
    [StringLength(300)]
    [Column("NAME")]
    public string Name { get; set; }

    [Column("TIMESTAMP")]
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

