﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Keyless]
public partial class VAttendTran
{
    [Column("TRANS_DATE", TypeName = "DATE")]
    public DateTime TransDate { get; set; }

    [Required]
    [Column("EMP_NO")]
    [StringLength(10)]
    [Unicode(false)]
    public string EmpNo { get; set; }

    [Column("TIME_IN")]
    [StringLength(5)]
    [Unicode(false)]
    public string TimeIn { get; set; }

    [Column("TIME_OUT")]
    [StringLength(5)]
    [Unicode(false)]
    public string TimeOut { get; set; }

    [Column("CNT", TypeName = "NUMBER")]
    public decimal? Cnt { get; set; }

    [Column("LATE", TypeName = "NUMBER")]
    public decimal? Late { get; set; }

    [Column("EARLY", TypeName = "NUMBER")]
    public decimal? Early { get; set; }

    [Column("ONE_SIGN", TypeName = "NUMBER")]
    public decimal? OneSign { get; set; }
}
