﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TCOURSE_SUPPLIER")]
    public class TcourseSupplier
    {

        [Key]
        [Column("COURSE_SUPPLIER_CODE")]
        public int CourseSupplerCode { get; set; }

        [Column("COURSE_SUPPLIER_NAME_A")]
        public string CourseSupplierName_A { get; set; }
        [Column("COURSE_SUPPLIER_NAME_E")]
        public string CourseSupplierName_E { get; set; }
        [Column("CONTACT_PERSON_NAME_A")]
        public string ContactPersonName_A { get; set; } = "";
        [Column("SUPPLIER_ADRS1")]
        public string  SupplierAddress { get; set; }
        [Column("PHONE_NO")]
        public string PhoneNO { get; set; }
        [Column("USER_ID")]
        public string UserId { get; set; } 

        public DateTime TIME_STAMP { get; set; }   = DateTime.Now;

        public virtual ICollection<TempTrgMas> TempTrgMas { get; set; }
    }
}
