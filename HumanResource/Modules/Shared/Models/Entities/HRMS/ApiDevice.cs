﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;


[Table("API_DEVICES")]
public class ApiDevice
{

    [Key]
    [Column("IP")]
    public string Ip { get; set; }

    [Column("NAME")]
    public string Name { get; set; }

    [Column("LAST_REQUEST")]
    public DateTime? LastRequest { get; set; } = DateTime.Now;
}


