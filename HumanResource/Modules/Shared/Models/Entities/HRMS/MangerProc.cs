﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [NotMapped]
    public class MangerProc
    {
        [NotMapped]
        public int inEMPNO { get; set; }
        [NotMapped]
        public int inDG { get; set; }
        [NotMapped]
        public int inDEPT { get; set; }
        [NotMapped]
        public int inDESGN { get; set; }

        [NotMapped]
        public int? Outl_id { get; set; }
        
        [StringLength(200)]
        [NotMapped]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string Outl_name { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

    }
}
