﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TQUAL_CODE")]
    public class TqualCode
    {

        [Key]
        [Column("QUAL_CODE")] 
        public int QualCode { get; set; }

        [Column("QUAL_DESP_A")]
        public string QualDespA { get; set; }

        [Column("QUAL_DESP_E")]
        public string QualDespE { get; set; }
       
        [Column("QUAL_HIER_LEVEL")]
        public int QualHierLevel { get; set; }


        public virtual ICollection<TqualReq> TqualReqs { get; set; }
    }
}
