using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS.Mail
{
    [Table("TTRANS_TYPE")]
    public class TTransType
    {
        [Key]
        [Column("TRANS_TYPE")]
        public int TransType { get; set; }

        [Required]
        [Column("TRANS_DESP_A")]
        public string TransDespA { get; set; }

        [Required]
        [Column("TRANS_DESP_E")]
        public string TransDespE { get; set; }

        [Column("DEPT_IND")]
        public int? DeptInd { get; set; }

        [Column("FORM_NAME")]
        public string FormName { get; set; }

        [Column("FO_FLAG")]
        public int? FoFlag { get; set; }
    }
} 