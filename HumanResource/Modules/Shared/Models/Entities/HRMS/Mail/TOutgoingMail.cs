﻿using System.ComponentModel.DataAnnotations.Schema;


namespace HumanResource.Modules.Shared.Models.Entities.HRMS.Mail
{
    [Table("TOUTGOING_MAIL")]
    public class TOutgoingMail
    {
        [Column("OUT_YEAR")]
        public int InYear { get; set; }

        [Column("OUT_DEPT_IND")]
        public int InDeptNo { get; set; }

        [Column("OUT_MAIL_NO")]
        public int OutMailNo { get; set; }

        [Column("ORDER_TYPE")]
        public int OrderType { get; set; }  // 97 for overtime, 261 for leaves

        [Column("USER_ID")]
        public string UserId { get; set; }

        [Column("LET_DATE")]
        public DateTime LetDate { get; set; } = DateTime.Now;

        [Column("TIME_STAMP")]
        public DateTime TimeStamp { get; set; } = DateTime.Now;

    }
}
