﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;

[PrimaryKey("InYear", "InDeptInd", "InMailNo")]
[Table("TINCOMING_MAIL")]
[Index("InMailNo", Name = "I1TINCOMING_MAIL")]
public partial class TincomingMail
{
    [Key]
    [Column("IN_YEAR")]
    public int InYear { get; set; }

    [Key]
    [Column("IN_DEPT_IND")]
    public int InDeptInd { get; set; }

    [Key]
    [Column("IN_MAIL_NO")]
    public int InMailNo { get; set; }

    [Column("LAST_DOC_SL_NO")]
    public int? LastDocSlNo { get; set; }

    [Column("LETTER_DATE", TypeName = "DATE")]
    public DateTime LetterDate { get; set; }

    [Column("LETTER_REF_NO")]
    [StringLength(30)]
    [Unicode(false)]
    public int LetterRefNo { get; set; }

    [Column("LETTER_REM")]
    [StringLength(255)]
    [Unicode(false)]
    public string LetterRem { get; set; }

    [Column("RECD_DATE", TypeName = "DATE")]
    public DateTime RecdDate { get; set; }

    [Column("RECD_TIME", TypeName = "DATE")]
    public DateTime? RecdTime { get; set; }

    [Column("LETTER_ORIGIN")]
    [StringLength(60)]
    [Unicode(false)]
    public string LetterOrigin { get; set; }

    [Column("DOC_TYPE")]
    public int? DocType { get; set; }

    [Column("NO_ACC_DOCS")]
    public int? NoAccDocs { get; set; }

    [Column("LETTER_SUBJ")]
    [StringLength(180)]
    [Unicode(false)]
    public string LetterSubj { get; set; }

    [Column("DESTN_FILE_NO")]
    [StringLength(10)]
    [Unicode(false)]
    public string DestnFileNo { get; set; }

    [Column("LET_DESTN_UNIT_CODE")]
    public int? LetDestnUnitCode { get; set; }

    [Column("LET_DESTN_DG_CODE")]
    public int? LetDestnDgCode { get; set; }

    [Column("LET_DESTN_DEPT_CODE")]
    public int? LetDestnDeptCode { get; set; }

    [Column("LET_DESTN_SECTION_CODE")]
    public int? LetDestnSectionCode { get; set; }

    [Column("LETTER_DESTN")]
    [StringLength(120)]
    [Unicode(false)]
    public string LetterDestn { get; set; }

    [Column("USER_ID")]
    [StringLength(20)]
    [Unicode(false)]
    public string UserId { get; set; }

    [Column("TIME_STAMP", TypeName = "DATE")]
    public DateTime? TimeStamp { get; set; }

    [Column("DG_CODE")]
    public int? DgCode { get; set; }
}
