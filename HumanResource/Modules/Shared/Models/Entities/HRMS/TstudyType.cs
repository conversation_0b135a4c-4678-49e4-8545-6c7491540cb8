﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TSTUDY_TYPE")]
    public class TstudyType
    {
        [Key]
        [Column("STUDY_TYPE")]
        public int StudyType { get; set; }

        [Column("STUDY_TYPE_DESP_E")]
        public string StudyTypeDespE { get; set; }

        [Column("STUDY_TYPE_DESP_A")]
        public string StudyTypeDespA { get; set; }

        [Column("USER_ID")]
        public string UserId { get; set; }
       
        [Column("TIME_STAMP")]
        public DateTime TimeStamp { get; set; }

        [NotMapped]
        public virtual ICollection<TqualReq> TqualReqs { get; set; }
    }
}
