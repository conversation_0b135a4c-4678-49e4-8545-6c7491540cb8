﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Keyless]
[Table("REPORT_TOTAL_HOURS_LOST")]    

public class VReportLostHours
{

    [Column("EMP_NO")]
    public int EmpNo { get; set; }

    [Column("TOTAL_OUT")]
    public int Hours { get; set; } = 0;

    //[Column("DATE_MONTH")]
    //public int Month { get; set; } = 0;

    //[Column("DATE_YEAR")]
    //public int Year { get; set; } = 0;

    [Column("EMP_NAME_A")]
    public string EmpNameA { get; set; } 

    [Column("DG_CODE")]
    public int DgCode { get; set; } = 0;

    [Column("DEPT_CODE")]
    public int DeptCode { get; set; } = 0;

    [Column("DG_DESP_A")]
    public string DgDespA { get; set; } = "";

    [Column("DEPT_DESP_A")]
    public string DeptDespA { get; set; } = "";



}

