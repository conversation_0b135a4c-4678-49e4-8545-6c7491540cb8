﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Security.Principal;


namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Keyless]
    public class LeavesDtl
    {
        [Column("EMP_NO")]
        public int EmpNo { set; get; }




        [Column("FROM_TIME")]
        public DateTime FromTime { set; get; }

        [Column("TO_TIME")]
        public DateTime ToTime { set; get; }

        [Column("RELCODE")]
        public int RelId { set; get; }

        [Column("TYPE")]
        public string Type { set; get; }

        [Column("REASON")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string Reason { set; get; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.


    }
}
