﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[PrimaryKey("UnitCode", "DgCode", "DeptCode", "SectionCode")]
[Table("TSECTION_CODE")]
[Index("UnitCode", "DgCode", "DeptCode", "SectionCode", Name = "I1TSECTION_CODE", IsUnique = true)]
public partial class TsectionCode
{
    [Key]
    [Column("UNIT_CODE")]
    [Precision(2)]
    public byte UnitCode { get; set; }

    [Key]
    [Column("DG_CODE")]
    [Precision(2)]
    public byte DgCode { get; set; }

    [Key]
    [Column("DEPT_CODE")]
    [Precision(2)]
    public byte DeptCode { get; set; }

    [Key]
    [Column("SECTION_CODE")]
    [Precision(2)]
    public byte SectionCode { get; set; }

    [Required]
    [Column("SECTION_DESP_A")]
    [StringLength(60)]
    [Unicode(false)]
    public string SectionDespA { get; set; }

    [Required]
    [Column("SECTION_DESP_E")]
    [StringLength(60)]
    [Unicode(false)]
    public string SectionDespE { get; set; }

    [Column("SEC_PRINT_ORDER", TypeName = "NUMBER")]
    public decimal? SecPrintOrder { get; set; }

    //[ForeignKey("UnitCode, DgCode, DeptCode")]
    //[InverseProperty("TsectionCodes")]
    //public virtual TDeptCode TdeptCode { get; set; }

    //[ForeignKey("UnitCode, DgCode")]
    //[InverseProperty("TsectionCodes")]
    //public virtual TdgCode TdgCode { get; set; }

    //[InverseProperty("LetDestn1")]
    //public virtual ICollection<TincomingMail> TincomingMails { get; } = new List<TincomingMail>();
}
