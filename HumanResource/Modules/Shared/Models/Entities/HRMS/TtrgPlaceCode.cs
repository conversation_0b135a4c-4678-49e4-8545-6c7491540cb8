﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{

    [Table("TTRG_PLACE_CODE")]
    public class TtrgPlaceCode
    {
        [Key]
        [Column("TRG_PLACE_CODE")]
        public int TrgPlaceCode { set; get; }
        [Column("TRG_PLACE_DESP_A")]
        public string TrgPlaceDespA { set; get; }
        public virtual ICollection<TcourseCatalogue> TcourseCatalogues { get; set; }
    }
}
