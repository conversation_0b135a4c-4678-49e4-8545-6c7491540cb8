﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Table("STR_CATEGORY_CODE")]
public partial class StrCategoryCode
{
    [Key]
    [Column("CATEGORY_CODE")] //)
    public int CategoryCode { get; set; }

    //[Required]
    [Column("CATEGORY_NAME")]
    //[StringLength(80)]
    public string CategoryName { get; set; }

    [Column("CATEGORY_DEACTIVATED")]
    public int? CategoryDeactivated { get; set; }=0;

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public virtual ICollection<StrItemCode> StrItemCodes { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public virtual ICollection<StrRequestDetail> StrRequestDetail { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
}
