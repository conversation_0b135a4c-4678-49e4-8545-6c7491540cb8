﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TEMP_TRH_HIST_dtl")]
    public class TempTrgHistDtls
    {

        [Key]
        [Column("ID")]
        public int Id { get; set; }
        [Column("EMP_NO")]
        public int EmpNo { get; set; } = 0;

        [Column("COURSE_CODE")]
        public int CourseCode { get; set; }

        [Column("COURSE_SUPPLIER_CODE")]
        public int CourseSupplierCode { get; set; }

        [Column(" COURSE_START_DATE")]
        public DateTime CourseStartDate { get; set; }

        [Column("COURSE_End_DATE")]
        public DateTime CourseEndDate { get; set; }

        [Column("SUBJ_CODE")]
        public int SubjCode { get; set; } = 0;

        [Column("APPROVAL")]
        public int Approval { get; set;}
        [Column("COUNTRY_CODE")]
        public int CountryCode { get; set; }

        [Column("Fees")]
        public int Fees { get; set;}

        [Column("NoofPeople")]
        public int NoofPeople { get; set; }        
        [Column("USER_ID")]
        public string UserId { get; set; }

        public DateTime TIME_STAMP { get; set; } = DateTime.Now;
    }

}
