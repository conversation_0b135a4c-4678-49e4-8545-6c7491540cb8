﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TCOURSE_CAT_CODE")]

    public class TcourseCatCode
    {

        [Key]
        [Column("COURSE_CAT_CODE")]
        public int CourseCatCode { get; set; }

      
        [Column("COURSE_CAT_DESP_A")]
        public string CourseCatDesp { get; set; }
        public virtual ICollection<TcourseCatalogue> TcourseCatalogues { get; set; }
    }
}
