﻿using System.ComponentModel.DataAnnotations.Schema;
namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{

    [Table("TRAINIG_COMM")]
    public class TrainingComm
    {
        [Column("ID")]
        public int Id { get; set; }
        [Column("COURSEID")]
        public int CourseId { get; set; }
        public virtual TcourseCatalogue TcourseCatalogue { get; set; }
        [Column("SUPPLIERID")]
        public int SupplierId { get; set; }
        [Column("PLACEID")]
        public int PlaceId { get; set; }
        [Column("DFROM")]
        public DateTime Dfrom { get; set; }
        [Column("DTO")]
        public DateTime Dto { get; set; }
        [Column("COST")]
        public int Cost { get; set; }
        [Column("TOTAL")]
        public int Total { get; set; }
        [Column("FILENO")]
        public int FileNo { get; set; }
        [Column("FILE_GUID")]
        public string FileGuid { get; set; }
        [Column("TCREATEDBY")]
        public string  TcreatedBy { get; set; }
        [Column("FCREATEDBY")]
        public string  FcreatedBy { get; set; }
        [Column("COMM")]
        public int CommNo { get; set; }
        [Column("ACCOUNTNO")]
        public int AccountNo { get; set; } =2-101-33-16;
        [Column("VENDORNO")]
        public int VendorNo { get; set; }
        [Column("BUDGETCODE")]
        public int BudgetCode { get; set; }
        [Column("SALNO")]
        public int SalNo { get; set; }
        public virtual TempTrgMas TempTrgMas { get; set; }
        [Column("ISSEND")]
        public int IsSend { get; set; } = 0;
        [Column("FILEPATH")]
        public string FilePath { get; set; }


    }
}
