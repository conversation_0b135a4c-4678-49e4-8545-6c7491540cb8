﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[PrimaryKey("ReqNo", "ReqDetailNo")]
[Table("STR_REQUEST_DETAIL")]
[Index("CategoryCode", Name = "IX_StrReqDetails_CategoryCode")]
[Index("ItemCode", Name = "IX_StrReqDetails_ItemCode")]
public partial class StrRequestDetail
{
    [Key]
    [Column("REQ_DETAIL_NO")]
    public int ReqDetailNo { get; set; }

    [Key]
    [Column("REQ_NO")]
    [ForeignKey("ReqNo")]
    public int ReqNo { get; set; }
    public virtual StrReqMastr StrReqMastr { get; set; }

    [ForeignKey("StrCategory")]
    [DisplayName("Category Code")]
    [Column("CATEGORY_CODE")] //
    //[Required(ErrorMessage = "This Category Code Field is required.")]
    public int CategoryCode { get; set; }
    public virtual StrCategoryCode StrCategory { get; private set; }
  
    [ForeignKey("StrItem")]
    [DisplayName("Item Code")]
    [Column("ITEM_CODE")]
    //[Required(ErrorMessage = "This Item Code Field is required.")]
    public int? ItemCode { get; set; }
    public virtual StrItemCode StrItem { get; private set; }


    [Column("DIRECT_TO_PUR")]
    public int? DirectToPur { get; set; }

    [Column("ITEM_QUANTITY")]
    public int? ItemQuantity { get; set; }

    [Column("ITEM_REMARK")]
    [StringLength(200)]
    public string ItemRemark { get; set; }

    [Column("ITEM_DELIVERY")]
    public int? ItemDelivery { get; set; }

    [Column("ITEM_DELIVERY_DATE")]
    [Precision(7)]
    public DateTime? ItemDeliveryDate { get; set; }

    [Column("CANCEL_FLAGE")]
    public int? CancelFlage { get; set; }

    [Column("STR_MANAGER_APPR")]
    public int? StrManagerAppr { get; set; }

    [Column("STR_MANAGER_APPR_RESON")]
    [StringLength(150)]
    public string StrManagerApprReson { get; set; }

    [Column("ITEM_QUT_RECEIVED")]
    public int? ItemQutReceived { get; set; }

    [Column("STR_APPR_DATE")]
    [Precision(7)]
    public DateTime? StrApprDate { get; set; }

    [Column("IS_ITEM_RETRIEVE")]
    public int? IsItemRetrieve { get; set; }

    [Column("QUT_RETRIEVE")]
    public int? QutRetrieve { get; set; }

    [Column("RETRIEV_REASON")]
    [StringLength(200)]
    public string RetrievReason { get; set; }

  
}
