﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TQUAL_STUDY_REPORT")]
    [PrimaryKey("EmpNo", "Year", "Semester")]
    public class TqualStudyReport
    {
        [Key]
        [Column("EMP_NO")]
        public int? EmpNo { get; set; }
        [Key]
        [Column("YEAR")]
        public int Year { get; set; }
        [Key]
        [Column("SEMESTER")]
        public int Semester { get; set; }

        [Column("RESULT")]
        public decimal Result { get; set; }
        [Column("GRADE")]
        public string Grade { get; set; }



    }
}
