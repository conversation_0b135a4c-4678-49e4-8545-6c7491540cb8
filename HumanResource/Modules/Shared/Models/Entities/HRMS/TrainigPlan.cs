﻿using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{

    [Table("TRAINIG_PLAN")]
    public class TrainigPlan
    {
        [Column("ID")]
        public int Id { get; set; }
        [Column("SPID")]
        public int spId { get; set; }
        public virtual SpecialiedSkill SpecialiedSkill { get; set; }
        [Column("TRG")]
        public int Trg { get; set; }
        [Column("EMPID")]
        public int EmpId { get; set; }
        [Column("SKILLID")]

        public int SkillId { get; set;}
        [Column("PLACEID")]
        public int? PlaceId { get; set;}
        public virtual TcityCode TcityCode { get; set; }

        [Column("TIME")]
        public DateTime time { get; set; }
        [Column("FEES")]
        public int Fees { get; set; }
        [Column("CREATEDBY")]
        public string CreatedBy { get; set; }

    }
}
