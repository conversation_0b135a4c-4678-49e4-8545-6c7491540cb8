﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Table("CON_PAYROLL_DTLS")]
[Keyless]
public class ConPayrollDtl
{

    [Column("EMP_NO")]
    public int EmpNo { get; set; }

    [Column("PAY_YEAR")]
    public int PayYear { get; set; }

    [Column("PAY_MONTH")]
    public int PayMonth { get; set; }



    [Column("EARN_DED_TYPE")]
    public int EarnDepType { get; set; }

    [Column("EARN_DED_CODE")]
    public int EarnDepCode { get; set; }

    [Column("START_DATE")]
    public DateTime? StartDate { get; set; }

    [Column("END_DATE")]
    public DateTime? EndDate { get; set; }

    [Column("CONTRACT_NO")]
    public string ContractNo { get; set; }

    [Column("AMOUNT")]
    public float? Amount { get; set; }


}

