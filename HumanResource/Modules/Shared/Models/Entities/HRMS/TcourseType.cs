﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TCOURSE_TYPE")]
    public class TcourseType
    {
       
        [Column ("COURSE_TYPE_DESP_A")]
       public string  CourseTypeDesp{ set; get; }

        [Key]
        [Column("COURSE_TYPE")]
        public int CourseType { set; get; }

        public virtual ICollection<TcourseCatalogue> TcourseCatalogues { get; set; }
    }
}
