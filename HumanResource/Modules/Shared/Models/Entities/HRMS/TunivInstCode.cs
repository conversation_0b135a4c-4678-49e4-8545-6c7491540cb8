﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TUNIV_INST_CODE")]
    public class TunivInstCode
    {
        [Key]
        [Column("UNIV_INST_CODE")]
        public int UnivInstCode { get; set; }

        [Column("UNIV_INST_DESP_A")]
        public string UnivInstDespA { get; set; }

        [Column("UNIV_INST_DESP_E")]
        public string UnivInstDespE { get; set; }

        [Column("UNIV_INST_PLACE")]
        public string UnivInstPlace { get; set; }

        [Column("COUNTRY_CODE")]
        public int CountryCode { get; set; }

        public virtual ICollection<TqualReq> TqualReqs { get; set; }

    }
}
