using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS.Careers
{
    [Table("TEMPLOYEE_CAREER")]
    [PrimaryKey("UnitCode", "EmpNo", "OrderYear", "OrderDeptInd", "OrderSlNo")]
    public class TemployeeCareer
    {
        [Column("UNIT_CODE")]
        public short UnitCode { get; set; }

        [Column("EMP_NO")]
        public int EmpNo { get; set; }

        [Column("ORDER_YEAR")]
        public short OrderYear { get; set; }

        [Column("ORDER_DEPT_IND")]
        public short OrderDeptInd { get; set; }

        [Column("ORDER_SL_NO")]
        public int OrderSlNo { get; set; }

        [Column("ORDER_DATE")]
        public DateTime? OrderDate { get; set; }

        [Column("ORDER_TYPE")]
        public short? OrderType { get; set; }

        [Column("P_UNIT_CODE")]
        public short? PUnitCode { get; set; }

        [Column("DG_CODE")]
        public short? DgCode { get; set; }

        [Column("DEPT_CODE")]
        public short? DeptCode { get; set; }

        [Column("SECTION_CODE")]
        public short? SectionCode { get; set; }

        [Column("DESGN_CODE")]
        public short? DesgnCode { get; set; }

        [Column("CAT_CODE")]
        public byte? CatCode { get; set; }

        [Column("SUB_CAT_CODE")]
        public byte? SubCatCode { get; set; }

        [Column("GRADE_RANK_CODE")]
        public short? GradeRankCode { get; set; }

        [Column("CONTRACT_TYPE")]
        public byte? ContractType { get; set; }

        [Column("FROM_DATE")]
        public DateTime? FromDate { get; set; }

        [Column("TO_DATE")]
        public DateTime? ToDate { get; set; }

        [Column("UPDATE_FLAG")]
        public byte? UpdateFlag { get; set; }



    }
} 