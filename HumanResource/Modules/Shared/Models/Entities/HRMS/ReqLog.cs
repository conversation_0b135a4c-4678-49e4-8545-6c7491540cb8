﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;


namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Keyless]
[Table("TREQ_LOGS")]
public class ReqLog
{

    [Column("ID")]
    public string Id { get; set; }

    [Column("USER_ID")]
    public int UserId { get; set; }

    [Column("TIMESTAMP")]
    public DateTime? TimeStamp { get; set; } = DateTime.Now;

    [Column("REM")]
    public string Rem { get; set; } = "";

    [Column("TYPE")]
    public string Type { get; set; } = "";

}

