﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Table("STR_ITEM_CODE")]
[Index("CategoryCode", Name = "IX_StrCodes_CategoryCode")]
public partial class StrItemCode
{
    [Key]
    [Column("ITEM_CODE")]
    public int ItemCode { get; set; }
   

    [Column("ITEM_NAME")]
    [StringLength(100)]
    public string ItemName { get; set; }

    [Column("ITEM_DESC")]
    [StringLength(300)]
    public string ItemDesc { get; set; }

    [Column("ITEM_BRAND")]
    [StringLength(100)]
    public string ItemBrand { get; set; }

    [Column("ITEM_MODEL")]
    [StringLength(100)]
    public string ItemModel { get; set; }

    [Column("CATEGORY_CODE")] //
    [ForeignKey("StrCategory")]
    [DisplayName("Category Name")]
    [Required(ErrorMessage = "This Category Name Field is required.")]
    public int? CategoryCode { get; set; }
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public virtual StrCategoryCode StrCategory { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

    [Column("TOTAL_QUA_AVA")]
    public int? TotalQuaAva { get; set; }

    [Column("MAX_QAU")]
    public int? MaxQau { get; set; }

    [Column("MIN_QAU")]
    public int? MinQau { get; set; }

    [Column("NO_TO_REQ")]
    public int? NoToReq { get; set; }

    [Column("ITEM_DETDEACTIVATED")]
    public int? ItemDetdeactivated { get; set; } = 0;

    [NotMapped]
    public int IsDeleted { get; set; } = 0;

    //public class QauLessThanAttribute : ValidationAttribute
    //{
    //    private readonly string _comparisonProperty;

    //    public QauLessThanAttribute(string comparisonProperty)
    //    {
    //        _comparisonProperty = comparisonProperty;
    //    }

    //    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    //    {
    //        ErrorMessage = ErrorMessageString;
    //        if (value == null)
    //            return new ValidationResult("This Minimum Quantity Field is required.");

    //        var currentValue = (int)value!;

    //        var property = validationContext.ObjectType.GetProperty(_comparisonProperty);

    //        if (property == null)
    //            return new ValidationResult("Property with this name not found");

    //        var comparisonValue = (int)property.GetValue(validationContext.ObjectInstance);

    //        if (currentValue > comparisonValue)
    //            return new ValidationResult(ErrorMessage);

    //        return ValidationResult.Success;
    //    }
    //}


}
