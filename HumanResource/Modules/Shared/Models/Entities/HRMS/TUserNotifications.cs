﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;


[Table("TUSER_NOTIFICATIONS")]
public class TUserNotifications
{

    [Key]
    [Column("GUID")]
    public string Guid { get; set; }

    public TUserNotifications()
    {
        Guid = System.Guid.NewGuid().ToString();
    }



    [Column("EMP_NO")]
    public int? EmpNo { get; set; } = null;

    [Column("TITLE")]
    public string Title { get; set; } = null;

    [Column("TEXT")]
    public string Text { get; set; } = "";

    [Column("URL")]
    public string Url { get; set; } = "";

    [Column("ICON")]
    public string Icon { get; set; } = "";

    [Column("READ_FLAG")]
    public int ReadFlag { get; set; } = 0;

    [Column("TIMESTAMP")]
    public DateTime Timestamp { get; set; } = DateTime.Now;


}
