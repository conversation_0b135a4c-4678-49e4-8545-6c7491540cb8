﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("EMPIMPACT")]
    public class EmpImpact
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }
        [Column("NAME")]
        public string Name { get; set; }


   public virtual ICollection<SpecialiedSkill> SpecialiedSkills { get; set; }
    }
}
