﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[PrimaryKey("UnitCode", "DgCode")]
[Table("TDG_CODE")]
[Index("UnitCode", "DgCode", Name = "I1DG_CODE", IsUnique = true)]
public partial class TdgCode
{
    [Key]
    [Column("UNIT_CODE")]
    [Precision(2)]
    public byte UnitCode { get; set; }

    [Key]
    [Column("DG_CODE")]
    [Precision(2)]
    public byte DgCode { get; set; }

    [Required]
    [Column("DG_DESP_A")]
    [StringLength(60)]
    [Unicode(false)]
    public string DgDespA { get; set; }

    [Required]
    [Column("DG_DESP_E")]
    [StringLength(50)]
    [Unicode(false)]
    public string DgDespE { get; set; }

    [Required]
    [Column("DG_SHORT_DESP")]
    [StringLength(60)]
    [Unicode(false)]
    public string DgShortDesp { get; set; }

    [Column("DIWAN_BUDGET_CODE")]
    [Precision(5)]
    public short? DiwanBudgetCode { get; set; }

    [Column("VALID_FLAG")]
    [Precision(1)]
    public bool? ValidFlag { get; set; }

    //[InverseProperty("TdgCode")]
    //public virtual ICollection<TDeptCode> TdeptCodes { get; } = new List<TDeptCode>();

    //[InverseProperty("LetDestn")]
    //public virtual ICollection<TincomingMail> TincomingMails { get; } = new List<TincomingMail>();

    //[InverseProperty("TdgCode")]
    //public virtual ICollection<TsectionCode> TsectionCodes { get; } = new List<TsectionCode>();
}
