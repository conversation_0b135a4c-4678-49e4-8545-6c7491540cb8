﻿

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HumanResource.Modules.Employees.Models.Entities;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;



[Table("Form1")]
public class Form1
{

    public virtual List<Form1Data> Form1Data { get; set; }

    [NotMapped]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public virtual VempDtl Staff { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

    [Key]
    public int Id { get; set; }

    [Required]
    public int StaffId { get; set; }


    public int DeptCode { get; set; } = 0;

    [Required]
    public int ManagerId { get; set; } = 0;
    public int ManagerApproval { get; set; } = 0;
    public int ManagerApprovalBy { get; set; } = 0;
    public DateTime? ManagerApprovalAt { get; set; } = null;

    public int ManagerDeclined { get; set; } = 0;
    public int ManagerDeclinedBy { get; set; } = 0;
    public string ManagerDeclinedNote { get; set; } = "";
    public DateTime? ManagerDeclinedAt { get; set; } = null;

    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? UpdatedAt { get; set; } = null;
    public int UpdatedBy { get; set; } = 0;

    public string Skill1 { get; set; } = "";
    public string Skill2 { get; set; } = "";
    public string Skill3 { get; set; } = "";
    public string Skill4 { get; set; } = "";

    [Range(minimum:0, maximum:40)]
    public int Experience  { get; set; } = 0;

    public string Traits { get; set; } = "";

}

[Table("Form1Data")]
public class Form1Data
{


    public virtual ICollection<Form1> Form1 { get; set; }

    [Key]
    public int Id { get; set; }

    [Required]
    public int FormId { get; set; }

    [Required]
    public string Task { get; set; } = "";


    [Required]
    public string Option1 { get; set; } = "";

    [Required]
    public string Option2 { get; set; } = "";

    [Required]
    public string Option3 { get; set; } = "";



}

