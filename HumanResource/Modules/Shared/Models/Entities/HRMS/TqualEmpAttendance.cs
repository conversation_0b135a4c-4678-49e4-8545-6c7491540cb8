﻿using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TQUAL_EMP_ATTENDANCE")]
    public class TqualEmpAttendance
    {
        [Column("ID")]
        public int Id { get; set;}


        [Column("EMP_NO")]
        public int? EmpNo { get; set; }

        [Column("FROM_DATE", TypeName = "DATE")]
        public DateTime FromDate { get; set; }

        [Column("TO_DATE", TypeName = "DATE")]
        public DateTime ToDate { get; set; }

        [Column("DAY")]
        public int Day { get; set; }

        [Column("TIME_FROM", TypeName = "TIME")]
        public DateTime TimeFrom { get; set; }

        [Column("TIME_TO", TypeName = "TIME")]
        public DateTime TimeTo { get; set; }

    }
}
