﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HumanResource.Modules.Employees.Models.Entities;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("SPECIALIEDSKILL")]
    public class SpecialiedSkill
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; } = 0;
        [Column("EMPID")]
        public int? EmpId { get; set; }
        [Column("SKILLID")]
        public int SkillId { get; set; }
        public virtual EmpSkill EmpSkill { get; set; }

        [Column("IMPACTID")]
        public int ImpactId { get; set; }
        public virtual EmpImpact EmpImpact { get; set; }

        [Column("TIME")]
        public DateTime Time { get; set; }
        [Column("APPROVE")]
        public int Approve { get; set; } = 0;
        [Column("APPROVEDBY")]
        public string  ApprovedBy { get; set; }
        [Column("MANAGERID")]
        public int ManagerId { get; set; }
        [Column("DGID")]
        public int? DgId { get; set; }
        public virtual VempDtl VempDtl { get; set; }
        public virtual ICollection<TrainigPlan> TrainigPlans { get; set; }
    }
}
