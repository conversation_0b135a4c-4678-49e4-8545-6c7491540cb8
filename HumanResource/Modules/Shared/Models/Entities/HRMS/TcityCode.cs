﻿using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TCITY_CODE")]
    public class TcityCode
    {
        [Column("CITY_CODE")]
        public int Id { get; set; }
        [Column("CITY_DESP_A")]
        public string CityDespa{ get; set; }
        [Column("COUNTRY_CODE")]
        public string CountryCode { get; set; }

        public virtual ICollection<TrainigPlan> TrainigPlans { get; set; }
    }
}
