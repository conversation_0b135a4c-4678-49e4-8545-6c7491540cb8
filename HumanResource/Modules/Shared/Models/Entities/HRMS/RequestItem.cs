﻿namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;


[Table("TREQUEST_ITEM")]
public class RequestItem
{

    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Column("REL_ID")]
    public int RelId { get; set; } = 0;


    [Column("REL_TYPE")]
    public string RelType { get; set; } = "";


    [Column("REQUEST_ID")]
    public int RequestId { get; set; } = 0;

    [Column("ITEM_ID")]
    public int ItemId { get; set; } = 0;


    [Column("NAME")]
    [Required]
    public string Name { set; get; }


    [Column("NOTE")]
    [StringLength(500)]
    public string Note { get; set; } = "";


    [Column("QUANTITY")]
    [Required]
    public double Quantity { get; set; }


    [Column("TAX")]
    public double Tax { get; set; } = 0;

    [Column("COST")]
    public double Cost { get; set; } = 0;

    [Column("UNIT")]
    public string Unit { get; set; } = "";


    [Column("CREATED_BY")]
    public int CreatedBy { get; set; }

    [Column("CREATED_AT")]
    public DateTime CreatedAt { get; set; } = DateTime.Now;


    [Column("UPDATED_BY")]
    public int UpdatedBy { get; set;} = 0;

    [Column("UPDATED_AT")]
    public DateTime? UpdatedAt { get; set; } = null;


    [Column("TECHNICAL_NOTE")]
    public string TechnicalNote { get; set; } = "";

    [Column("TECHNICAL_DEPT_CODE")]
    public int TechnicalDepartmentCode { get; set; } = 0;

    [Column("TECHNICAL_NOTE_BY")]
    public int TechnicalNoteBy { get; set; } = 0;

    [Column("TECHNICAL_NOTE_DATE")]
    public DateTime? TechnicalNoteDate { get; set; } = null;

    [Column("TECHNICAL_NOTE_STAT")]
    public int TechnicalStat { get; set; } = 0;

    [Column("TECHNICAL_NOTE_REQUIRED")]
    public int TechnicalRequired { get; set; } = 0;

    


    // DG
    [Column("DG_APPROVAL_REQUIRED")]
    public int DgApprovalRequired { get; set; } = 0;

    [Column("DG_NO")]
    public int DgNo { get; set; } = 0;

    [Column("DG_ACTION_AT")]
    public DateTime? DgActionAt { get; set; } = null;

    [Column("DG_APPROVAL_STAT")]
    public int DgApprovalStat { get; set; }  = 0;

    [Column("DG_NOTE")]
    public string DgNote { get; set; } = "";


    [Column("PURCHASES_STAT")]
    public int PurchasesStat  { get; set; } = 0;

    [Column("DELIVERD_QTY")]
    public double DeliveredQty { get; set; } = 0;

    [Column("DELIVERY_STAT")]
    public int DeliveryStat { get; set; } = 0;

    [Column("INVENTORY_NOTE")]
    public string InventoryNote { get; set; } = "";

    [Column("STATUS")]
    public string Status { get; set; } = "";


    [Column("ORIGIN_ID")]
    public int OriginId { get; set; } = 0;

    [Column("ORIGIN_TYPE")]
    public string OriginType { get; set; } = "";

    [Column("FINANCE_MANAGER_STAT")]
    public int FinanceManagerStat { get; set; } = 0;

    [Column("CANCEL_FLAG")]
    public int CancelFlag { get; set; } = 0;
}

