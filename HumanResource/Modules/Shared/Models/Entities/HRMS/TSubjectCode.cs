﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TSUBJECT_CODE")]
    public class TSubjectCode
    {
        [Key]
        [Column("SUBJ_CODE")]
        public int SubjCode { get; set; }

        [Column("SUBJ_DESP_A")]
        public string SubjDespA { get; set; }

        [Column("SUBJ_DESP_E")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string SubjDespE { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("SCIENCE_ARTS_TYPE")]
        public int? ScienceArtsType { get; set; }

        [NotMapped]
        public virtual ICollection<TqualReq> TqualReqs { get; set; }

        public virtual ICollection<TcourseCatalogue> TcourseCatalogue { get; set; }
    }
}
