﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{

    [Table("TEMP_TRG_MAS")]
    public class TempTrgMas
    {
        [Key]
        [Column("SAL_NO")]
        public int SalNo { set; get; }

     

        [Column("COURSE_CODE")]
        public int CourseCode { set; get; }
        public virtual TcourseCatalogue TcourseCatalogue { get; set; }

        [Column("COURSE_START_DATE")]
        public DateTime  CourseStartDate { set; get; }


        [Column("COURSE_END_DATE")]
        public DateTime CourseEndDate  { set; get; }


        [Column("YEAR")]
        public int Year { set; get; } = DateTime.Now.Year;


        [Column("APPROVAL")]
        public int Approval { set; get; }



        [Column("FEES")]
        public int Fees { set; get; }

        [Column("NOOFPEOPLE")]
        public int NoofPeople { set; get; }


        [Column("COUNTRY_CODE")]
        public int CountryCode { get; set; }
        public virtual TcountryCode TcountryCode { get; set; }

        [Column("COURSE_SUPPLIER_CODE")]
        public int CourseSupplierCode { get; set; }
        public virtual TcourseSupplier TcourseSupplier { get; set; }

        [Column("USER_ID")]
        public string UserId { get; set; }

        [Column("TIME_STAMP")]
        public DateTime? TimeStamp { get; set; } = DateTime.Now;
        [Column("ISSEND")]
        public int IsSend { set; get; }=0;

        public virtual ICollection<TempTrgHist> TempTrgHist { get; set; }
        public virtual ICollection<TrainingComm> TrainingComm { get; set; }
    }
}
