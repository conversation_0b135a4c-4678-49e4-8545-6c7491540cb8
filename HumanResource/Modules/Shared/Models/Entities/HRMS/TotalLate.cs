﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Keyless]
    public class TotalLate
    {
        [Column("EMP_NO")]
        public int? EmpNo { get; set; }

        [Column("DATE_YEAR")]
        public int? DateYear { get; set; }

        [Column("DATE_MONTH")]
        public int? DateMonth { get; set; }

        [Column("HOURS")]
        public int? Hours { get; set; }

        [Column("MINUTES")]
        public int? Minutes { get; set; }

    }
}
