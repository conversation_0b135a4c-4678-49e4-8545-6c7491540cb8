﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TCOUNTRY_CODE")]
    public class TcountryCode
    {

        [Key]
        [Column("COUNTRY_CODE")]
        public int CountryCode { get; set; }

        [Column("REGION_CODE")]
        public int? ReginCode { get; set; }

        [Column("COUNTRY_DESP_A")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string CountryDespA { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("COUNTRY_DESP_E")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string CountryDespE { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("NATIONALITY_A_M")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string NationaltyAM { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("NATIONALITY_A_F")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string NationaltyAF { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("NATIONALITY_E")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string NationaltyE { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("VISA_FLAG")]
        public int? VisaFlag { get; set; }

        [Column("PAY_SLIP_LANG")]
        public int? PaySlipLang { get; set; }

        public virtual ICollection<TqualReq> TqualReqs { get; set; }

        public virtual ICollection<TempTrgMas> TempTrgMas { get; set; }


   
    }
}
