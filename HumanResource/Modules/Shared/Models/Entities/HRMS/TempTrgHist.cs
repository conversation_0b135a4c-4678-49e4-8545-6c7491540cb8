﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using HumanResource.Modules.Employees.Models.Entities;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TEMP_TRG_HIST")]
    public class TempTrgHist
    {


        [Column("ID")]
        public int? Id { get; set; } 

        [Column("SAL_NO")]
        public int? SalNo { get; set; }
        public virtual TempTrgMas TempTrgMas { get; set; }

        [Key]
        [Column("EMP_NO")]
        public int EmpNo { get; set; }
        public virtual VempDtl VempDtl { get; set; }


        [Key]
        [Column("COURSE_CODE")]
        public int CourseCode { get; set; }
        public virtual TcourseCatalogue TcourseCatalogue { get; set; }
        [Column("UNIV_INST_CODE")]
        public int? UnivInstCode { get; set; }


        [Column("COURSE_SUPPLIER_CODE")]
        public int? CourseSupplierCode { get; set; }

        [Key]
        [Column("COURSE_START_DATE")]
        public DateTime CourseStartDate  { get; set; }

        [Key]
        [Column("COURSE_END_DATE")]
        public DateTime CourseEndDate  { get; set; }


        [Key]
        [Column("SUBJ_CODE")]
        
        public int SubjCode { get; set; }

        [Column("USER_ID")]
        public string UserId { get; set; }

        [Column("TIME_STAMP")]
        public DateTime? TimeStamp { get; set; } = DateTime.Now;

      
        [Column("UNIT_CODE")]
        public int UnitCode { get; set; } = 1;

        [NotMapped]
        public int CountryCode { get; set; }
     



    }

}
