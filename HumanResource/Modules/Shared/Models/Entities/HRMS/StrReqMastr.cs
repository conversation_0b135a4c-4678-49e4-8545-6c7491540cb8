﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Table("STR_REQ_MASTR")]
public partial class StrReqMastr
{
    [Key]
    [Column("REQ_NO")]
    public int ReqNo { get; set; }

    [Column("REQ_DATE", TypeName = "DATE")]
    public DateTime? ReqDate { get; set; }

    [Column("EMP_NO")]
    public int? EmpNo { get; set; }


    
    [Column("MANAGR_APPR")]
    public int? ManagrAppr { get; set; } 

    [Column("MANAGR_APPR_FLAG")]
    public int? ManagrApprFlag { get; set; }

    [Column("MANAGR_APPR_DATE", TypeName = "DATE")]
    public DateTime? ManagrApprDate { get; set; }

    [Column("STR_MANAGR_APPR")]
    public int? StrManagrAppr { get; set; }

    [Column("STR_MANAGRAPPRFLAG")]
    public int? StrManagrapprflag { get; set; }

    [Column("STRMANAGR_APPR_DATE", TypeName = "DATE")]
    public DateTime? StrmanagrApprDate { get; set; }

    [Column("DIRECTOR_APPROVAL")]
    public int? DirectorApproval { get; set; }

    [Column("DIRECTOR_APPROVAL_DATE", TypeName = "DATE")]
    public DateTime? DirectorApprovalDate { get; set; }

    [Column("CANCEL_FLAGE")]
    public int? CancelFlage { get; set; }

    [Column("USER_ID")]
    public int? UserId { get; set; }

    [Column("DELIVERY_APPR")]
    public int? DeliveryAppr { get; set; }

  
    public virtual List<StrRequestDetail> StrRequestDetail { get; set; } = new List<StrRequestDetail>();
}
