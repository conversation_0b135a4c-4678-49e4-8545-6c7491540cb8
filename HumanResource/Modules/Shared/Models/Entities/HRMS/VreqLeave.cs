﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS;

[Keyless]
public partial class VreqLeave
{
    [Column("IN_YEAR")]
    [Precision(2)]
    public byte? InYear { get; set; }

    [Column("IN_DEPT_IND")]
    [Precision(2)]
    public byte? InDeptInd { get; set; }

    [Column("IN_MAIL_NO")]
    [Precision(5)]
    public short? InMailNo { get; set; }

    [Column("IN_DOC_SL_NO")]
    [Precision(4)]
    public byte? InDocSlNo { get; set; }

    [Column("UNIT_CODE", TypeName = "NUMBER")]
    public decimal? UnitCode { get; set; }

    [Column("EMP_NO", TypeName = "NUMBER")]
    public decimal? EmpNo { get; set; }

    [Column("LEAVE_TYPE", TypeName = "NUMBER")]
    public decimal? LeaveType { get; set; }

    [Column("ORDER_YEAR", TypeName = "NUMBER")]
    public decimal? OrderYear { get; set; }

    [Column("ORDER_DEPT_IND", TypeName = "NUMBER")]
    public decimal? OrderDeptInd { get; set; }

    [Column("ORDER_SL_NO", TypeName = "NUMBER")]
    public decimal? OrderSlNo { get; set; }

    [Column("ORDER_TYPE", TypeName = "NUMBER")]
    public decimal? OrderType { get; set; }

    [Column("ORDER_DATE", TypeName = "DATE")]
    public DateTime? OrderDate { get; set; }

    [Column("REQ_STAT")]
    [Precision(2)]
    public byte? ReqStat { get; set; }

    [Column("CANCEL_FLAG", TypeName = "NUMBER")]
    public decimal? CancelFlag { get; set; }

    [Column("AUDIT_STAT", TypeName = "NUMBER")]
    public decimal? AuditStat { get; set; }

    [Column("TXN_DATE", TypeName = "DATE")]
    public DateTime? TxnDate { get; set; }
}
