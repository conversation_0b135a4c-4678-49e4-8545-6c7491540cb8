﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
    [Table("TQUAL_PLAN")]
    public class TqualPlan
    {
        [Key]
        [Column("PLANID")]
        public int PlanId { get; set; }

        [Column("YEAR")]
        public int Year { get; set; }

        [Column("UNIT_CODE")]
        public int UnitCode { get; set; }

        [Column("DG_CODE")]
        public int DgCode { get; set; }

        [Column("QUAL_CODE")]
        public int QualCode { get; set; }

        [Column("SUBJ_CODE")]
        public int SubjCode { get; set; }

        [Column("NO_OF_CHANCES")]
        public int NoOfChanges { get; set; }

        [Column("EXPECTED_COST")]
        public int ExpectedCost { get; set; }

        [Column("USER_ID")]
        public string UserId { get; set; }

        [Column("TIME_STAMP")]
        public DateTime TimeStamp { get; set; }


    }
}
