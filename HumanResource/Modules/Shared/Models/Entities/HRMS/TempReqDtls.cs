﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net.NetworkInformation;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{


    [Table("TEMP_REQ_DTLS")]
    public class TempReqDtls


    {
   
        [Column("ID")]
        public int Id { get; set; }

        [Column("REQNO")]
        public int? ReqNo { get; set; }
   


        [Column("EMPNO")]
        public int EmpNo { get; set; }

        [Column("STATUS")]
        public int Status { get; set; } = 0;

        [Column("START_DATE")]
        public DateTime StartDate { get; set; }

        [Column("END_DATE")]
        public DateTime EndDate { get; set; }
    }
}
