﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.HRMS
{
 
       [Table("TCOURSE_CATALOGUE")]
    public class TcourseCatalogue
       
    {
        [Key]
        [Column("COURSE_CODE")]
        public int CourseCode { get; set; }

        [Column("COURSE_NAME_A")]
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string CourseNameA { get; set; }
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        [Column("COURSE_NAME_E")]
        public string CourseNameE { get; set; }
        [Column("SUBJ_CODE")]
        public int? SubjCode { get; set; }
        public virtual TSubjectCode TSubjectCode { get; set; }

        [Column("COURSE_CAT_CODE")]
        public int? CourseCatCode { get; set; }
        public virtual TcourseCatCode TcourseCatCode { get; set; }

        [Column("COURSE_TYPE")]
        public int? CourseType { get; set; }
        public virtual TcourseType TcourseType { get; set; }

        [Column("CLASS_ONLINE")]
        public string ClassOnline { get; set; }

        [Column("FEES")]
       public string Fees { get; set; }

        [Column("TRG_PLACE_CODE")]
        public int? TrgPlaceCode { set; get; }
        public virtual TtrgPlaceCode TtrgPlaceCode { get; set; }

        [Column("USER_ID")]

        public string UserId { get; set; }
 
        [Column("TIME_STAMP")]
        public DateTime? TimeStamp { get; set; } = DateTime.Now;

        public virtual ICollection<TempTrgMas> TempTrgMas { get; set; }
        public virtual ICollection<TempTrgHist> TempTrgHist { get; set; }
        public virtual ICollection<TrainingComm> TrainingComm { get; set; }
    }

}
