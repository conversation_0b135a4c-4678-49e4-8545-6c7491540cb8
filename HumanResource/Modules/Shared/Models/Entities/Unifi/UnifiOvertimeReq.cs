﻿using System.ComponentModel.DataAnnotations.Schema;

namespace HumanResource.Modules.Shared.Models.Entities.Unifi
{
    public class UnifiOvertimeReqMas
    {

        public UnifiOvertimeReqMas()
        {
            

            if (EmpNo >= 1000)
            {
                Rel = "Con";
            }
            else
            {
                Rel = "Emp";
            }
        }


        public int InYear { get; set; }

        public int InDeptInd { get; set; }

        public int InMailNo { get; set; }

        public int? InDocSlNo { get; set; }

        //public int UnitCode { get; set; }

        public int? EmpNo {
            get { return _EmpNo;  }
            set {
                
                _EmpNo = value.Value;

                if (_EmpNo >= 1000)
                {
                    Rel = "Con";
                }
                else
                {
                    Rel = "Emp";
                }
            }
        }

        public int _EmpNo;

        public int OrderType { get; set; } = 97;

        public int? ReqStatus { get; set; }  // see Tstat_code

        public DateTime TxnDate { get; set; } = DateTime.Now;

        public int? OrderYear { get; set; } = 1;
        public int? OrderMonth { get; set; } = 1;

        public int? OrderDeptInd { get; set; } = 1;

        public int OrderSlNo { get; set; } = 988;

        public DateTime? OrderDate { get; set; } = DateTime.Now;

        public int? SignAuthCode { get; set; } = null; // رئيس القسم

        public int? SignByCode { get; set; } = 1; // الشيخ

        public DateTime? SignDate { get; set; } = null;

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string SignRem { get; set; } = "-";
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        public int? CancelFlag { get; set; }

        public int? EstimatedFlag { get; set; }

        public int? AuditStat { get; set; } = 0;

        public DateTime? AuditDate { get; set; } = null;

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string AuditRem { get; set; } = null;
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string AuditUserId { get; set; } = null;
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        public DateTime? AuditTimeStamp { get; set; } = null;

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string UserId { get; set; } = null; // قسم الاجازات
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        public DateTime? TimeStamp { get; set; } = DateTime.Now;

        public int? HrManagerSign { get; set; } = 0; // مدير الموارد البشرة

        public DateTime? HrManagerSignDate { get; set; } = null;

        public int? ManagerNo { get; set; } 
        public float? ExtraAmountSum { get; set; }
        public float? ExtraSum { get; set; } 
#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string OtStatus { get; set; }

        public int FinanceManager { get; set; } = 0;
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string FileGuid { get; set; } = null;

        
        [NotMapped]
        public string Rel { get; set; }

        
    }

    public partial class UnifiOvertimeReqDtls
    {
        public int InYear { get; set; }
        public int InDeptInd { get; set; }
        public int InMailNo { get; set; }
        public int InDocSlNo { get; set; }
        public int? UnitCode { get; set; } = 1;
        public int EmpNo { get; set; }
        public DateTime OtDate { get; set; }
        public double OtFromTime { get; set; }

        public double OtToDate { get; set; }

        public int PayStat { get; set; } = 0;

        public int? PayYear { get; set; } = null;

        public int? PayMonth { get; set; } = null;

        public int? RunNo { get; set; } = null;

        public DateTime? CommitmentDate { get; set; } = null;

        public int? CommitmentNo { get; set; } = null;

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string UserId { get; set; } = null; // قسم الاجازات
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        public DateTime TimeStamp { get; set; } = DateTime.Now;

        public float OtDuration { get; set; } = 0;

        public float OtAmount { get; set; } = 0;

        public float? OtRate { get; set; } = 0;

        public int HolydayFlag { get; set; } = 0;

        public float BasicAtm { get; set; } = 0;

        public int OvertimeExceedFlag { get; set; } = 0;

        public float ComputedAmount { get; set; } = 0;



    }

    public partial class UnifiOvertimeReqPreDtls
    {
        public int InYear { get; set; }
        public int InDeptInd { get; set; }
        public int InMailNo { get; set; }
        public int InDocSlNo { get; set; }
        public int UnitCode { get; set; }
        public int EmpNo { get; set; }
        public DateTime OtDate { get; set; }
        public double OtFromTime { get; set; }

        public double OtToDate { get; set; }

        public int payStat { get; set; } = 0;

        public int payYear { get; set; } = 0;

        public int PayMonth { get; set; } = 0;

        public int? RunNo { get; set; } = null;

        public DateTime? CommitmentDate { get; set; } = null;

        public int? CommitmentNo { get; set; } = null;

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
        public string UserId { get; set; } = null; // قسم الاجازات
#pragma warning restore CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.

        public DateTime TimeStamp { get; set; } = DateTime.Now;

        public float OtDuration { get; set; } = 0;

        public float OtAmount { get; set; } = 0;

        public float? OtRate { get; set; } = 0;

        public int HolydayFlag { get; set; } = 0;

        public float BasicAtm { get; set; } = 0;

        public int OvertimeExceedFlag { get; set; } = 0;

        public float ComputedAmount { get; set; } = 0;

        public bool IsApproved { get; set; } 



    }
}
