﻿using HumanResource.Modules.Leaves.Models.Entities;
using System.ComponentModel.DataAnnotations.Schema;


namespace HumanResource.Modules.Shared.Models.Entities.Unifi;

public class UnifiLeavesAplTx
{
    public int InYear { get; set; }
    public int InDeptInd { get; set; }//s
    public int InMailNo { get; set; } //s
    public int InDocSlNo { get; set; } //s
   
    public int LeaveCode { get; set; }
    public virtual TleaveCode TleaveCode  { get; set; }
    public int? LeaveType { get; set; }
    public virtual TleaveType TleaveType { get; set; }
    
    public DateTime LeaveStartDate { get; set; }
    public DateTime LeaveEndDate { get; set; }
    public decimal? LeaveBalOnStartDate { get; set; }
    public int? ReqStat { get; set; }
    public int? PayStat { get; set; } = 0;
    public DateTime? DutyResumeDate { get; set; }
    public int CancelFlag { get; set; } = 0;
    public int? ManagerApproval { get; set; }
    public DateTime? ApprovalDate { get; set; }
    public string ApprovalRem { get; set; }
    public int? OrderYear { get; set; }
    public int? OrderDeptInd { get; set; }
    public int? OrderSlNo { get; set; }
    public DateTime? OrderDate { get; set; }
    public int? SignAuthCode { get; set; }
    public string SignRem { get; set; }
    public DateTime? SignDate { get; set; }
    public int? SignByCode { get; set; }
    public int? AuditStat { get; set; }=0;
    public DateTime? AuditDate { get; set; }
    public string AuditRem { get; set; }
    public string AuditUserId { get; set; }
    public DateTime? AuditTimeStamp { get; set; }
    public DateTime? TxnDate { get; set; }
    public int? OrderType { get; set; }
    public string UserId { get; set; }
    public DateTime? TimeStamp { get; set; }


    public int EmpNo {
        get { return _EmpNo;  }
        set {
            
            _EmpNo = value;

            if (_EmpNo >= 1000)
            {
                Rel = "Con";
            }
            else
            {
                Rel = "Emp";
            }
        }
    }

    public int? ReqStatCode { get; set; } 

    public int _EmpNo;


    [NotMapped]
    public string Rel { get; set; }

}

