﻿using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;

namespace HumanResource.Modules.Shared.Models.DTOs;


public class DgReport
{

    public List<DgRow> Dgs { get; set; } = new List<DgRow>();
    public List<DeptRow> Depts { get; set; } = new List<DeptRow>();
    public TdgCode Dg { get; set; } = new TdgCode();
    public TDeptCode Dept { get; set; } = new TDeptCode();


    public class DgRow
    {
        public TdgCode Dg { get; set; } = new TdgCode();
        public List<TDeptCode> Depts { get; set; } = new List<TDeptCode> { new TDeptCode() };
        public List<VempDtl> Employees { get; set; } = new List<VempDtl> { new VempDtl() };
        public List<VempDtl> EmployeesMale { get; set; } = new List<VempDtl> { new VempDtl() };
        public List<VempDtl> EmployeesFemale { get; set; } = new List<VempDtl>();
        public Dictionary<string, decimal> Decimal { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, float> Float { get; set; } = new Dictionary<string, float>();
        public Dictionary<string, int> Counts { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, string> Values { get; set; } = new Dictionary<string, string>();
    }

    public class DeptRow
    {
        public TdgCode Dg { get; set; } = new TdgCode();
        public  TDeptCode Dept { get; set; } = new TDeptCode();
        public List<VempDtl> Employees { get; set; } = new List<VempDtl>();
        public List<VempDtl> EmployeesMale { get; set; } = new List<VempDtl>();
        public List<VempDtl> EmployeesFemale { get; set; } = new List<VempDtl>();
        public Dictionary<string, decimal> Decimal { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, float> Float { get; set; } = new Dictionary<string, float>();
        public Dictionary<string, int> Counts { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, string> Values { get; set; } = new Dictionary<string, string>();
    }

    public class EmpRow
    {
        public TdgCode Dg { get; set; } = new TdgCode();
        public TDeptCode Dept { get; set; } = new TDeptCode();
        public VempDtl Employee { get; set; } = new VempDtl();

        public Dictionary<string, decimal> Decimal { get; set; } = new Dictionary<string, decimal>();
        public Dictionary<string, float> Float { get; set; } = new Dictionary<string, float>();
        public Dictionary<string, int> Counts { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, string> Values { get; set; } = new Dictionary<string, string>();
    }


}






