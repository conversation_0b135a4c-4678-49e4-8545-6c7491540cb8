﻿using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;

namespace HumanResource.Modules.Shared.ViewModels;

public class ReportViewModel : BaseViewModel
{

#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly hrmsContext _context;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly hrmsContext _db;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public readonly IHttpContextAccessor _http;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    private AppHelper _h;
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword

    public ReportViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)
        : base(context, httpContextAccessor, helper)
    {

        _context = context;
        _db = context;
        _http = httpContextAccessor;
        _h = helper;
    }


    public List<VReportLostHours> VReportLostHours { get; set; }

    public List<LostHoursDT> LostHoursDTRows { get; set; }

    public class LostHoursDT
    {
        public int? EmpNo { get; set; }
        public string Name { get; set; }
        public string EmpNameA { get; set; }
        public int? DgCode { get; set; }

        public string DgDespA { get; set; }
        public int? DeptCode { get; set; }
        public string DeptDespA { get; set; }
        public int Hours { get; set; } = 0;
        public int Month { get; set; } = 0;
        public int Year { get; set; } = 0;

    }
}

