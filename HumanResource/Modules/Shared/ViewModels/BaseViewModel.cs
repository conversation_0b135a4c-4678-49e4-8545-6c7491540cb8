﻿using System.Text.Json;
using HumanResource.Language;
using System.Security.Claims;
using System.Net;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Core.Data;
using HumanResource.Core.Helpers;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.DTOs;
using HumanResource.Core.UI.Models;
using HumanResource.Modules.Execuses.Models.Enums;

namespace HumanResource.Modules.Shared.ViewModels;
public class BaseViewModel
{

    public readonly hrmsContext _context;
    public readonly hrmsContext _db;
    public readonly bcContext _bc;
    public readonly IHttpContextAccessor _http;
    public AppHelper _h;

    public string AppEnvironment { get; set; } = "";

    public BaseViewModel(
        hrmsContext context,
        bcContext bccontext,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)

    {

        _context = context;
        _db = context;
        _bc = bccontext;
        _http = httpContextAccessor;
        _h = helper;

        Profile = _h.Auth();
        Auth = Profile;

        // NavMenu = new NavMenu(_db, _h);


        Url = new UrlBuilder(_http);

        AppEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

    }

    public BaseViewModel(
        hrmsContext context,
        IHttpContextAccessor httpContextAccessor, AppHelper helper)

    {

        _context = context;
        _db = context;
        _http = httpContextAccessor;
        _h = helper;
        AppEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        Profile = _h.Auth();
        Auth = Profile;

        // NavMenu = new NavMenu(_db, _h);


        Url = new UrlBuilder(_http);

        // ADD NOTIFICATIONS 

        var f = _db.Form1.Where(f => f.StaffId == Profile.EmpNo.Value).FirstOrDefault();

        if (f == null)
        {
            //Notifications.Add(new Notification
            //{
            //    Title = _("Form1Title"),
            //    Text = _("استمارة تحليل مهاهم الموظف"),
            //    Url = "/Forms/My/Form1/",
            //});
        }


        
    }




    public VempDtl Profile { get; set; }

    protected ClaimsIdentity Identity { get; private set; }
    public Rights Rights { get; set; } = new Rights();
    public UrlBuilder Url { get; set; }
    public PageModel Page { get; set; } = new PageModel();

    public string PageClass { get; set; } = "";
    public Translator Translator { get; set; } = new Translator();
    public VempDtl Auth { get; set; }

    public AppHelper Helper { get; set; }

    public List<string> Success { get; set; } = new List<string>();
    public List<string> Errors { get; set; } = new List<string>();


    public List<VempDtl> StaffList { get; set; }
    public List<VempDtl> VempDtls { get; set; }  
    public VempDtl Staff { get; set; }
    public List<UserTask> UserTasks { get; set; } = new List<UserTask>();

    public DgReport DgReport { get; set; }

    public Dictionary<string,int> ManagerTabsCounter()
    {


        return new Dictionary<string, int>
        {
            ["Execuses"] = _db.Absents
                .Where(m => m.MangerNo == Profile.EmpNo &&  m.Status == (int)AbsentStatus.Pending).Count(),
            ["Leaves"] = _h.Leave().getCountForManager(Profile.EmpNo.Value),
            ["Transports"] = _db.CarsRequests.Where(
            r => r.DepManagerId == Profile.EmpNo
            && r.Deleted01 != 1
            && _context.VempDtls.Any(s => s.EmpNo == r.StaffId)
            ).Count(),
            ["Training"]=_db.TqualReqs.Where(x=>x.MangAppId == Profile.EmpNo.Value && x.CancelFlag==0 && x.ApprovalDate == null).Count(),
            ["OverTime"] = _db.OvertimeReqMas.Where(f => f.ManagerNo == Profile.EmpNo.Value  && f.ReqStatus == 1 && f.CancelFlag!=0).Count(),
        };
    }

    public Dictionary<string, int> DGManagerTabsCounter()
    {
        return new Dictionary<string, int>
        {
            ["Execuses"] = _db.Absents.Where(x => x.MangerApproval == (int)AbsentStatus.Approved  &&  x.Status == (int)AbsentStatus.NeedsDGApproval).Count(),
            ["Studys"] = _db.TqualReqs.Where(x =>x.MandAprrStat==1 && x.SectionStat == 1 && x.DGMangStat== null || x.DGMangStat ==0  && x.CancelFlag == 0).Count(),
            ["Overtime"] = _db.OvertimeReqMas.Where(ro => ro.EstimatedFlag == 1 && ro.CancelFlag != 1 && ro.ReqStatus == 30 && ro.EmpNo > 1).Count(),
            ["Inventory"] = _db.RequestItem.Where(x =>x.DgApprovalRequired==1 && x.CancelFlag == 0).Count(),
            ["Leaves"] = _h.Leave().getCountForDg(),
        };
    }

    public Dictionary<string, int> AuditTabsCounter()
    {
        return new Dictionary<string, int>
        {
            
            ["Overtime"] = _db.OvertimeReqMas.Where(x => (x.ReqStatus == 40 || x.ReqStatus == 50)  && x.CancelFlag != 1 && x.EmpNo > 1).Count(),
            ["LeavesReturn"] = _db.TleaveReturnTxs.Where(x => x.ReqStat == 1 && x.CancelFlag != 1).Count(),
        };
    }

    public Dictionary<string, int> TriningTabsCounter()
    {
        return new Dictionary<string, int>
        {
            ["Study"] = _db.TqualReqs.Where(x => x.MandAprrStat == 1 && x.SectionStat == null || x.SectionStat ==0 && x.CancelFlag == 0).Count(),
        };
    }

    public Dictionary<string, int> AffairsCommitteeTabsCounter()
    {
        return new Dictionary<string, int>
        {
            ["Study"] = _db.TqualReqs.Where(x => x.MandAprrStat == 1 && x.DGMangStat ==1  && (x.AffairsCommitteeStat == null || x.AffairsCommitteeStat == 0) && x.CancelFlag == 0).Count(),
        };
    }

    public bool Can(string role)
    {
        return _h.Can(role);
    }

    public bool Can(Right right)
    {
        return _h.Can(right);
    }

    public string ListToJson<T>(List<T> list)
    {
        return JsonSerializer.Serialize(list);

    }



    public bool can(string role)
    {
        return Helper.Can(role);
    }

    public bool Can(IEnumerable<Right> rights)
    {
        return _h.Can(rights);
    }

    // format date
    public string _d(DateTime? date)
    {

        if (date == null)
        {
            return "";
        }

        return _h.Format.Date(date);
    }


    public string _dt(DateTime? date)
    {
        return _h.Format.DateTime(date);
    }

    public string _ddd(DateTime? date)
    {
        return _h.Format.DateWithDay(date);
    }

    // format date time
    public string _t(DateTime? date)
    {
        return _h.Format.Time(date);
    }

    //format value to 3 digts
    public string _amount(float amount)
    {
        return _h.Format.Amount(amount);
    }

    public string _amount(float? amount)
    {
        if (amount == null)
            return "0";

        return _h.Format.Amount(amount.Value);
    }

    public string _amount(double amount)
    {
        return _h.Format.Amount(amount);
    }

    public string _amount(int amount)
    {
        return _h.Format.Amount(amount);
    }

    public string _Balance(decimal Balance)
    {
        return Balance.ToString("F2");
    }

    // translate
    public string _l(string lable)
    {
        return Translator.Tr(lable);
    }

    public string _(string lable)
    {
        return Translator.Tr(lable);
    }

    public string tr(string lable)
    {
        return Translator.Tr(lable);
    }


    public string Ec(string input)
    {
        return input;
#pragma warning disable CS0162 // Unreachable code detected
        return _h.Ec(input);
#pragma warning restore CS0162 // Unreachable code detected
    }

    public string Ec(int input)
    {
        string inputString = input.ToString();
        return inputString;
#pragma warning disable CS0162 // Unreachable code detected
        return _h.Ec(inputString);
#pragma warning restore CS0162 // Unreachable code detected
    }

    public string Dc(string input)
    {

        return input;
#pragma warning disable CS0162 // Unreachable code detected
        return _h.Dc(input);
#pragma warning restore CS0162 // Unreachable code detected
    }

    public int DcInt(string input)
    {
        return Convert.ToInt32(input);
#pragma warning disable CS0162 // Unreachable code detected
        if (IsUrlEncoded(input))
        {
            input = WebUtility.UrlDecode(input);
        }
#pragma warning restore CS0162 // Unreachable code detected

        string dc = _h.Dc(input);
        return Convert.ToInt32(dc);
    }

    public bool IsUrlEncoded(string str)
    {

        if (string.IsNullOrEmpty(str)) return false;

        string decoded = WebUtility.UrlDecode(str);

        if (decoded == str)
        {
            return false;
        }

        if (decoded != str)
        {
            return false;
        }

#pragma warning disable CS0168 // Variable is declared but never used
        try
        {
            decoded = WebUtility.UrlDecode(str);
            return decoded != str;
        }
        catch (Exception ex)
        {
            return false;
        }
#pragma warning restore CS0168 // Variable is declared but never used


    }


    public string renderSatus(int? input )
    {
        return _h.renderSatus(input);
    }

    public string renderSatus(int? input, int[] Alerts)
    {
        return _h.renderSatus(input, Alerts);
    }

    public List<_EmptRequestReport> _EmptRequestReport { get; set; }


}

public class _EmptRequestReport
{
    public VempDtl Employee { get; set; }
    public int Count { get; set; }
}