using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Shared.Providers
{
    /// <summary>
    /// Navigation provider for the Reports module
    /// </summary>
    public class ReportsNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Reports";
        public int Priority => 60;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Reports Group navigation items
            var reportsNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "reports",
                    Label = "التقارير",
                    Active = "reports",
                    Icon = "<i class=\"far fa-file-chart-line\"></i>",
                    Rights = new List<Right> { Right.LostHours },
                    Priority = 10,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "losthours",
                            Label = "الساعات المفقودة",
                            Rights = new List<Right> { Right.LostHours },
                            Url = "/Report/Losthours"
                        }
                    }
                }
            };

            // Add navigation items to their respective groups
            navigation[NavigationGroup.Reports] = reportsNavItems;

            return navigation;
        }
    }
} 