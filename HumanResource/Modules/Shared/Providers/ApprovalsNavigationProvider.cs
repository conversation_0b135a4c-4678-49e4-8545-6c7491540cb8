using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;
using HumanResource.Core.Data;

namespace HumanResource.Modules.Shared.Providers
{
    /// <summary>
    /// Navigation provider for general Approvals module
    /// </summary>
    public class ApprovalsNavigationProvider : INavigationProvider
    {
        public string ModuleName => "Approvals";
        public int Priority => 35;

        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync()
        {
            var navigation = new Dictionary<NavigationGroup, List<NavItem>>();

            // Approvals Group navigation items
            // Approval navigation items
            var approvalNavItems = new List<NavItem>
            {
                new NavItem
                {
                    Name = "ManagerApprovals",
                    Label = "اعتماد المسؤول المباشر",
                    Active = "ManagerApprovals",
                    Icon = "<i class=\"fas fa-file-check\"></i>",
                    Rights = new List<Right> { Right.DepartmentManager },
                    Priority = 10,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "ManagerApprovals",
                            Label = "اعتماد المسؤول المباشر",
                            Url = "/Leaves/Manager"
                        }
                    }
                },
                new NavItem
                {
                    Name = "DgApprovals",
                    Label = "اعتماد المدير العام",
                    Active = "DgApprovals",
                    Icon = "<i class=\"fas fa-file-check\"></i>",
                    Rights = new List<Right> { Right.AbsentDGeneral, Right.OvertimeDG, Right.Attendence, Right.TransportsDGeneral, Right.InventoryDGeneral, Right.AffairsCommittee },
                    Priority = 20,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "DirectorGeneral",
                            Label = "اعتماد المدير العام",
                            Url = "/Account/Dg"
                        }
                    }
                },
                new NavItem
                {
                    Name = "Audit",
                    Label = "التدقيق",
                    Active = "Audit",
                    Icon = "<i class=\"fas fa-file-check\"></i>",
                    Rights = new List<Right> { Right.Audit },
                    Priority = 30,
                    Links = new List<NavLink>
                    {
                        new NavLink
                        {
                            Name = "Audit",
                            Label = "التدقيق",
                            Url = "/Leaves/Audit/Return"
                        }
                    }
                }
            };
            // Add navigation items to their respective groups
            navigation[NavigationGroup.Approvals] = approvalNavItems;

            return navigation;
        }
    }
} 