using HumanResource.Core.UI.Services;

namespace HumanResource.Core.UI.Extensions
{
    /// <summary>
    /// Extension methods for registering UI services
    /// </summary>
    public static class UIServiceExtensions
    {
        /// <summary>
        /// Registers the shared UI services
        /// </summary>
        public static IServiceCollection AddUIServices(this IServiceCollection services)
        {
            // Register core UI services
            services.AddScoped<NavigationService>();
            services.AddScoped<BadgeCacheService>();
            
            // Register memory cache if not already registered
            services.AddMemoryCache();

            return services;
        }
    }
} 