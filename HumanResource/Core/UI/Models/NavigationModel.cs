using HumanResource.Core.Data;
using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Core.UI.Models
{
    /// <summary>
    /// Represents a group of navigation items
    /// </summary>
    public class NavGroup
    {
        /// <summary>
        /// Display name of the navigation group
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// Required permissions to see this group
        /// </summary>
        public List<Right> Rights { get; set; } = new List<Right>();

        /// <summary>
        /// Navigation items in this group
        /// </summary>
        public List<NavItem> NavItems { get; set; } = new();

        /// <summary>
        /// Module that owns this navigation group
        /// </summary>
        public string ModuleName { get; set; } = "";

        /// <summary>
        /// Priority for ordering groups (lower = higher priority)
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// Navigation group enum value
        /// </summary>
        public NavigationGroup NavigationGroup { get; set; } = NavigationGroup.Home;
    }

    /// <summary>
    /// Represents a navigation item within a group
    /// </summary>
    public class NavItem
    {
        /// <summary>
        /// Internal name/identifier for the navigation item
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// Required permissions to see this item
        /// </summary>
        public List<Right> Rights { get; set; } = new List<Right>();

        /// <summary>
        /// Display label for the navigation item
        /// </summary>
        public string Label { get; set; } = "";

        /// <summary>
        /// Icon HTML for the navigation item
        /// </summary>
        public string Icon { get; set; } = "";

        /// <summary>
        /// Badge count to display
        /// </summary>
        public int Badge { get; set; } = 0;

        /// <summary>
        /// Active state identifier
        /// </summary>
        public string Active { get; set; } = "";

        /// <summary>
        /// List of navigation links under this item
        /// </summary>
        public List<NavLink> Links { get; set; } = new();

        /// <summary>
        /// Whether this item is hidden
        /// </summary>
        public bool Hidden { get; set; } = false;

        /// <summary>
        /// Module that owns this navigation item
        /// </summary>
        public string ModuleName { get; set; } = "";

        /// <summary>
        /// Priority for ordering items within a group (lower = higher priority)
        /// </summary>
        public int Priority { get; set; } = 100;
    }

    /// <summary>
    /// Represents a navigation link within an item
    /// </summary>
    public class NavLink
    {
        /// <summary>
        /// Internal name/identifier for the navigation link
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// Required permissions to see this link
        /// </summary>
        public List<Right> Rights { get; set; } = new List<Right>();

        /// <summary>
        /// Display label for the navigation link
        /// </summary>
        public string Label { get; set; } = "";

        /// <summary>
        /// Icon HTML for the navigation link
        /// </summary>
        public string Icon { get; set; } = "";

        /// <summary>
        /// Badge count to display
        /// </summary>
        public int Badge { get; set; } = 0;

        /// <summary>
        /// URL for the navigation link
        /// </summary>
        public string Url { get; set; } = "";

        /// <summary>
        /// Whether this link is hidden
        /// </summary>
        public bool Hidden { get; set; } = false;
    }
} 