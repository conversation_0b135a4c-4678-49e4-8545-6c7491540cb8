using HumanResource.Core.UI.Interfaces;

namespace HumanResource.Core.UI.Models
{
    /// <summary>
    /// Represents page metadata and settings
    /// </summary>
    public class PageModel : IPageModel
    {
        /// <inheritdoc/>
        public string Layout { get; set; } = "_layout";

        /// <inheritdoc/>
        public string Class { get; set; } = "";

        /// <inheritdoc/>
        public string Title { get; set; } = "HRMS";

        /// <inheritdoc/>
        public string Active { get; set; } = "";

        /// <inheritdoc/>
        public bool Reload { get; set; } = true;

        /// <inheritdoc/>
        public string? Back { get; set; } = null;

        /// <inheritdoc/>
        public List<Breadcrumb> Breadcrumb { get; set; } = new();

        /// <inheritdoc/>
        public Filter Filter { get; set; } = new();

        /// <inheritdoc/>
        public string ModuleName { get; set; } = "";

        /// <inheritdoc/>
        public Dictionary<string, object> ViewData { get; set; } = new();
    }

    /// <summary>
    /// Represents filter settings for a page
    /// </summary>
    public class Filter
    {
        /// <summary>
        /// Start date for filtering
        /// </summary>
        public DateTime? DateFrom { get; set; } = null;

        /// <summary>
        /// End date for filtering
        /// </summary>
        public DateTime? DateTo { get; set; } = null;

        /// <summary>
        /// Search term for filtering
        /// </summary>
        public string SearchTerm { get; set; } = "";

        /// <summary>
        /// Custom filters specific to the page/module
        /// </summary>
        public Dictionary<string, string> CustomFilters { get; set; } = new();
    }

    /// <summary>
    /// Represents a breadcrumb navigation item
    /// </summary>
    public class Breadcrumb
    {
        /// <summary>
        /// Display label for the breadcrumb item
        /// </summary>
        public string Label { get; set; } = "";

        /// <summary>
        /// URL for the breadcrumb item
        /// </summary>
        public string Url { get; set; } = "";

        /// <summary>
        /// Whether this breadcrumb item is the current active page
        /// </summary>
        public bool IsActive { get; set; } = false;
    }
} 