namespace HumanResource.Core.UI.Interfaces
{
    /// <summary>
    /// Interface for modules to provide badge counts for navigation items
    /// </summary>
    public interface IBadgeProvider
    {
        /// <summary>
        /// Gets the badge counts for this module's navigation items
        /// Key format: "ModuleName.ItemName"
        /// </summary>
        Task<Dictionary<string, int>> GetBadgesAsync();

        /// <summary>
        /// The name of the module providing badges
        /// </summary>
        string ModuleName { get; }
    }
} 