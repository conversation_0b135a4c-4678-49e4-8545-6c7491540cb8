using HumanResource.Core.Data;
using HumanResource.Core.UI.Models;

namespace HumanResource.Core.UI.Interfaces
{
    public enum NavigationGroup
    {
        Home = 0,
        HR = 1,
        Finance = 2,
        Approvals = 3,
        Inventory = 4,
        Administration = 5,
        SelfService = 6,
        Reports = 7
    }

    /// <summary>
    /// Group configuration for navigation with rights and display info
    /// </summary>
    public class NavigationGroupConfig
    {
        public NavigationGroup Group { get; set; }
        public string DisplayName { get; set; }
        public List<Right> Rights { get; set; }
        public int Priority { get; set; }

        public static Dictionary<NavigationGroup, NavigationGroupConfig> DefaultConfigs = new()
        {
            {
                NavigationGroup.Home,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.Home,
                    DisplayName = "",
                    Rights = new List<Right>(),
                    Priority = 0
                }
            },
            {
                NavigationGroup.HR,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.HR,
                    DisplayName = "دائرة الموارد البشرية",
                    Rights = new List<Right>(),
                    Priority = 1
                }
            },
            {
                NavigationGroup.Finance,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.Finance,
                    DisplayName = "المالية",
                    Rights = new List<Right>(),
                    Priority = 2
                }
            },
            {
                NavigationGroup.Approvals,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.Approvals,
                    DisplayName = "الاعتمادات",
                    Rights = new List<Right>(),
                    Priority = 3
                }
            },
            {
                NavigationGroup.Inventory,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.Inventory,
                    DisplayName = "المخازن والنقليات",
                    Rights = new List<Right>(),
                    Priority = 4
                }
            },
            {
                NavigationGroup.Administration,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.Administration,
                    DisplayName = "الادارة",
                    Rights = new List<Right> { Right.Admin },
                    Priority = 5
                }
            },
            {
                NavigationGroup.SelfService,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.SelfService,
                    DisplayName = "الخدمات الذاتية",
                    Rights = new List<Right>(), // Available to all authenticated users
                    Priority = 6
                }
            },
            {
                NavigationGroup.Reports,
                new NavigationGroupConfig
                {
                    Group = NavigationGroup.Reports,
                    DisplayName = "التقارير",
                    Rights = new List<Right>(),
                    Priority = 7
                }
            }
        };
    }

    /// <summary>
    /// Interface for modules to provide their navigation items
    /// </summary>
    public interface INavigationProvider
    {
        /// <summary>
        /// Gets the navigation items for this module organized by navigation groups
        /// </summary>
        Task<Dictionary<NavigationGroup, List<NavItem>>> GetNavigationAsync();

        /// <summary>
        /// The name of the module providing navigation
        /// </summary>
        string ModuleName { get; }

        /// <summary>
        /// Priority for ordering navigation items within groups (lower = higher priority)
        /// </summary>
        int Priority { get; }
    }
} 