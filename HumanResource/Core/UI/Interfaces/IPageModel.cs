using HumanResource.Core.UI.Models;

namespace HumanResource.Core.UI.Interfaces
{
    /// <summary>
    /// Interface for page models to provide consistent page metadata
    /// </summary>
    public interface IPageModel
    {
        /// <summary>
        /// The layout to use for the page
        /// </summary>
        string Layout { get; set; }

        /// <summary>
        /// CSS classes to apply to the page
        /// </summary>
        string Class { get; set; }

        /// <summary>
        /// Page title
        /// </summary>
        string Title { get; set; }

        /// <summary>
        /// Active navigation item identifier
        /// </summary>
        string Active { get; set; }

        /// <summary>
        /// Whether the page should auto-reload
        /// </summary>
        bool Reload { get; set; }

        /// <summary>
        /// Back button URL
        /// </summary>
        string? Back { get; set; }

        /// <summary>
        /// Breadcrumb navigation items
        /// </summary>
        List<Breadcrumb> Breadcrumb { get; set; }

        /// <summary>
        /// Filter settings for the page
        /// </summary>
        Filter Filter { get; set; }

        /// <summary>
        /// Module name that owns this page
        /// </summary>
        string ModuleName { get; set; }

        /// <summary>
        /// Additional view data
        /// </summary>
        Dictionary<string, object> ViewData { get; set; }
    }
} 