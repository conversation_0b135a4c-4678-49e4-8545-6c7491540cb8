using HumanResource.Modules.Shared.Models.DTOs;

namespace HumanResource.Core.UI.Interfaces
{
    /// <summary>
    /// Interface for modules to provide their user tasks
    /// </summary>
    public interface ITaskProvider
    {
        /// <summary>
        /// Gets the user tasks for this module
        /// </summary>
        Task<List<UserTask>> GetTasksAsync();

        /// <summary>
        /// The name of the module providing tasks
        /// </summary>
        string ModuleName { get; }
    }
} 