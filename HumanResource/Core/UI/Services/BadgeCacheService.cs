using HumanResource.Core.UI.Interfaces;
using Microsoft.Extensions.Caching.Memory;

namespace HumanResource.Core.UI.Services
{
    /// <summary>
    /// Service for caching badge calculations to improve performance
    /// </summary>
    public class BadgeCacheService
    {
        private readonly IMemoryCache _cache;
        private readonly IEnumerable<IBadgeProvider> _badgeProviders;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5); // Cache badges for 5 minutes

        public BadgeCacheService(IMemoryCache cache, IEnumerable<IBadgeProvider> badgeProviders)
        {
            _cache = cache;
            _badgeProviders = badgeProviders;
        }

        /// <summary>
        /// Gets badges with caching to reduce database queries
        /// </summary>
        public async Task<Dictionary<string, int>> GetCachedBadgesAsync(int empNo)
        {
            string cacheKey = $"badges_{empNo}";
            
            if (_cache.TryGetValue(cacheKey, out Dictionary<string, int> cachedBadges))
            {
                return cachedBadges;
            }

            // Calculate fresh badges
            var allBadges = new Dictionary<string, int>();
            
            foreach (var provider in _badgeProviders)
            {
                try
                {
                    var badges = await provider.GetBadgesAsync();
                    foreach (var badge in badges)
                    {
                        allBadges[badge.Key] = badge.Value;
                    }
                }
                catch (Exception ex)
                {
                    // Log error but don't fail entire badge calculation
                    Console.WriteLine($"Error loading badges from {provider.ModuleName}: {ex.Message}");
                }
            }

            // Cache the results
            _cache.Set(cacheKey, allBadges, _cacheExpiry);
            
            return allBadges;
        }

        /// <summary>
        /// Invalidates badge cache for a specific user
        /// </summary>
        public void InvalidateUserBadges(int empNo)
        {
            string cacheKey = $"badges_{empNo}";
            _cache.Remove(cacheKey);
        }

        /// <summary>
        /// Invalidates all badge caches
        /// </summary>
        public void InvalidateAllBadges()
        {
            // Note: IMemoryCache doesn't have a clear all method
            // In a production environment, consider using IDistributedCache with Redis
            // For now, we rely on cache expiry
        }
    }
} 