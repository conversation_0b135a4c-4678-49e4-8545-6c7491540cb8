using HumanResource.Core.UI.Interfaces;
using HumanResource.Modules.Shared.Models.DTOs;

namespace HumanResource.Core.UI.Services
{
    /// <summary>
    /// Service for aggregating user tasks from all task providers
    /// </summary>
    public class TaskService
    {
        private readonly IEnumerable<ITaskProvider> _taskProviders;

        public TaskService(IEnumerable<ITaskProvider> taskProviders)
        {
            _taskProviders = taskProviders;
        }

        /// <summary>
        /// Gets all user tasks from all providers
        /// </summary>
        public async Task<List<UserTask>> GetAllTasksAsync()
        {
            var allTasks = new List<UserTask>();

            foreach (var provider in _taskProviders)
            {
                try
                {
                    var tasks = await provider.GetTasksAsync();
                    allTasks.AddRange(tasks);
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail the entire task loading
                    Console.WriteLine($"Error loading tasks from {provider.ModuleName}: {ex.Message}");
                }
            }

            // Sort tasks by priority (descending - higher priority first)
            return allTasks.OrderByDescending(t => t.Priority).ToList();
        }

        /// <summary>
        /// Gets tasks from a specific module
        /// </summary>
        public async Task<List<UserTask>> GetTasksFromModuleAsync(string moduleName)
        {
            var provider = _taskProviders.FirstOrDefault(p => p.ModuleName == moduleName);
            if (provider == null)
            {
                return new List<UserTask>();
            }

            try
            {
                return await provider.GetTasksAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading tasks from {provider.ModuleName}: {ex.Message}");
                return new List<UserTask>();
            }
        }
    }
} 