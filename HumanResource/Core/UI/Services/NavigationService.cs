using HumanResource.Core.Helpers;
using HumanResource.Core.UI.Interfaces;
using HumanResource.Core.UI.Models;

namespace HumanResource.Core.UI.Services
{
    /// <summary>
    /// Service for managing navigation across all modules
    /// </summary>
    public class NavigationService
    {
        private readonly IEnumerable<INavigationProvider> _navigationProviders;
        private readonly IEnumerable<IBadgeProvider> _badgeProviders;
        private readonly AppHelper _appHelper;

        public NavigationService(
            IEnumerable<INavigationProvider> navigationProviders,
            IEnumerable<IBadgeProvider> badgeProviders,
            AppHelper appHelper)
        {
            _navigationProviders = navigationProviders;
            _badgeProviders = badgeProviders;
            _appHelper = appHelper;
        }

        /// <summary>
        /// Gets the complete navigation structure with badges and permissions applied
        /// </summary>
        public async Task<List<NavGroup>> GetNavigationAsync()
        {
            var groupedNavigation = new Dictionary<NavigationGroup, List<NavItem>>();
            var badges = await GetAllBadgesAsync();
            
            foreach (var provider in _navigationProviders.OrderBy(p => p.Priority))
            {
                try
                {
                    var providerNavigation = await provider.GetNavigationAsync();
                    
                    foreach (var kvp in providerNavigation)
                    {
                        var navigationGroup = kvp.Key;
                        var navItems = kvp.Value;
                        
                        // Process each navigation item
                        foreach (var item in navItems)
                        {
                            // Set module name if not already set
                            if (string.IsNullOrEmpty(item.ModuleName))
                            {
                                item.ModuleName = provider.ModuleName;
                            }

                            // Apply badges
                            ApplyBadges(item, badges, provider.ModuleName);
                            
                            // Filter by permissions
                            FilterItemByPermissions(item);
                            
                            // Only add items that have visible links
                            if (item.Links.Any(link => !link.Hidden))
                            {
                                if (!groupedNavigation.ContainsKey(navigationGroup))
                                {
                                    groupedNavigation[navigationGroup] = new List<NavItem>();
                                }
                                
                                groupedNavigation[navigationGroup].Add(item);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail the entire navigation
                    // TODO: Add proper logging
                    Console.WriteLine($"Error loading navigation from {provider.ModuleName}: {ex.Message}");
                }
            }

            // Convert to NavGroup format - only include groups that have items
            return ConvertToNavGroups(groupedNavigation);
        }

        /// <summary>
        /// Converts grouped navigation to NavGroup format
        /// </summary>
        private List<NavGroup> ConvertToNavGroups(Dictionary<NavigationGroup, List<NavItem>> groupedNavigation)
        {
            var result = new List<NavGroup>();
            
            foreach (var kvp in groupedNavigation.OrderBy(g => NavigationGroupConfig.DefaultConfigs[g.Key].Priority))
            {
                var navigationGroup = kvp.Key;
                var navItems = kvp.Value;
                
                // Only include groups that have visible items
                if (navItems.Any())
                {
                    var config = NavigationGroupConfig.DefaultConfigs[navigationGroup];
                    
                    var navGroup = new NavGroup
                    {
                        Name = config.DisplayName,
                        Rights = config.Rights,
                        Priority = config.Priority,
                        NavigationGroup = navigationGroup,
                        NavItems = navItems.OrderBy(item => item.Priority).ToList()
                    };
                    
                    result.Add(navGroup);
                }
            }
            
            return result;
        }

        /// <summary>
        /// Gets all badges from all providers with optional caching
        /// </summary>
        private async Task<Dictionary<string, int>> GetAllBadgesAsync()
        {
            var allBadges = new Dictionary<string, int>();
            
            foreach (var provider in _badgeProviders)
            {
                try
                {
                    var badges = await provider.GetBadgesAsync();
                    foreach (var badge in badges)
                    {
                        allBadges[badge.Key] = badge.Value;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail the entire navigation
                    // TODO: Add proper logging
                    Console.WriteLine($"Error loading badges from {provider.ModuleName}: {ex.Message}");
                }
            }

            return allBadges;
        }

        /// <summary>
        /// Applies badge counts to navigation items
        /// </summary>
        private void ApplyBadges(NavItem item, Dictionary<string, int> badges, string moduleName)
        {
            // Check for item-level badges
            var itemBadgeKey = $"{moduleName}.{item.Name}";
            if (badges.ContainsKey(itemBadgeKey))
            {
                item.Badge = badges[itemBadgeKey];
            }

            // Check for link-level badges
            foreach (var link in item.Links)
            {
                var linkBadgeKey = $"{moduleName}.{link.Name}";
                if (badges.ContainsKey(linkBadgeKey))
                {
                    link.Badge = badges[linkBadgeKey];
                }
            }

            // If any links have badges, roll up to item level
            if (item.Badge == 0 && item.Links.Any(l => l.Badge > 0))
            {
                item.Badge = item.Links.Sum(l => l.Badge);
            }
        }

        /// <summary>
        /// Filters navigation item based on user permissions
        /// </summary>
        private void FilterItemByPermissions(NavItem item)
        {
            // Filter item level permissions
            if (item.Rights.Any() && !_appHelper.Can(item.Rights))
            {
                item.Hidden = true;
                return;
            }

            // Filter link level permissions
            foreach (var link in item.Links)
            {
                if (link.Rights.Any() && !_appHelper.Can(link.Rights))
                {
                    link.Hidden = true;
                }
            }

            // If all links are hidden, hide the item
            if (item.Links.All(l => l.Hidden))
            {
                item.Hidden = true;
            }
        }

        /// <summary>
        /// Gets navigation for a specific module
        /// </summary>
        public async Task<Dictionary<NavigationGroup, List<NavItem>>> GetModuleNavigationAsync(string moduleName)
        {
            var provider = _navigationProviders.FirstOrDefault(p => p.ModuleName == moduleName);
            if (provider == null)
            {
                return new Dictionary<NavigationGroup, List<NavItem>>();
            }

            var providerNavigation = await provider.GetNavigationAsync();
            var badges = await GetAllBadgesAsync();

            // Filter and process navigation items
            var result = new Dictionary<NavigationGroup, List<NavItem>>();
            
            foreach (var kvp in providerNavigation)
            {
                var navigationGroup = kvp.Key;
                var navItems = kvp.Value;
                
                var processedItems = new List<NavItem>();
                
                foreach (var item in navItems)
                {
                    ApplyBadges(item, badges, moduleName);
                    FilterItemByPermissions(item);
                    
                    if (item.Links.Any(link => !link.Hidden))
                    {
                        processedItems.Add(item);
                    }
                }
                
                if (processedItems.Any())
                {
                    result[navigationGroup] = processedItems;
                }
            }

            return result;
        }

        /// <summary>
        /// Gets all available modules
        /// </summary>
        public List<string> GetAvailableModules()
        {
            return _navigationProviders.Select(p => p.ModuleName).Distinct().ToList();
        }

        /// <summary>
        /// Gets navigation for a specific navigation group
        /// </summary>
        public async Task<List<NavItem>> GetNavigationForGroupAsync(NavigationGroup group)
        {
            var groupNavigation = new List<NavItem>();
            var badges = await GetAllBadgesAsync();
            
            foreach (var provider in _navigationProviders.OrderBy(p => p.Priority))
            {
                try
                {
                    var providerNavigation = await provider.GetNavigationAsync();
                    
                    if (providerNavigation.ContainsKey(group))
                    {
                        var navItems = providerNavigation[group];
                        
                        foreach (var item in navItems)
                        {
                            ApplyBadges(item, badges, provider.ModuleName);
                            FilterItemByPermissions(item);
                            
                            if (item.Links.Any(link => !link.Hidden))
                            {
                                groupNavigation.Add(item);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading navigation from {provider.ModuleName}: {ex.Message}");
                }
            }
            
            return groupNavigation.OrderBy(item => item.Priority).ToList();
        }
    }
} 