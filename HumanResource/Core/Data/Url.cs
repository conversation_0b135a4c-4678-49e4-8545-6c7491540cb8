﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using System;
using System.Collections.Specialized;
using System.Security.Policy;
using System.Web;

namespace HumanResource.Core.Data;
public class UrlBuilder
{
    private UriBuilder uriBuilder;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public UrlBuilder(string url)
    {
     
            uriBuilder = new UriBuilder(url);
        
    }

    public UrlBuilder(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
        string currentUrl = _httpContextAccessor.HttpContext.Request.GetDisplayUrl();
        uriBuilder = new UriBuilder(currentUrl);
    }



    public string Add(Dictionary<string, string> queryParams)
    {
        var query = HttpUtility.ParseQueryString(uriBuilder.Query);
        foreach (var param in queryParams)
        {
            query[param.Key] = param.Value;
        }
        uriBuilder.Query = query.ToString();

        return uriBuilder.ToString();
    }





    public void Remove(string[] keys)
    {
        var query = HttpUtility.ParseQueryString(uriBuilder.Query);
        foreach (var key in keys)
        {
            query.Remove(key);
        }
        uriBuilder.Query = query.ToString();
    }

    public string Get()
    {
        return uriBuilder.ToString();
    }
}
