﻿namespace HumanResource.Core.Data
{
    // Enum-based Rights System
    public enum Right
    {
        // Transport Rights
        TransportsAdmin,
        TransportsDepartment,
        TransportsDGeneral,
        
        // Inventory Rights
        InventoryAdmin,
        InventoryDepartment,
        InventoryDGeneral,
        
        // Finance Rights
        FinanceDepartment,
        FinanceManager,
        PurchasesDepartment,
        PurchasesAdmin,
        
        // Overtime Rights
        OvertimeAdmin,
        OvertimeDepartment,
        OvertimeHrManager,
        OvertimeDG,
        OvertimeReport,
        
        // Leaves Rights
        LeavesDepartment,
        LeavesDepartmentManager,
        LeavesHrManager,
        LeavesDG,
        LeavesAdmin,
        
        // Attendance Rights
        Attendence,
        
        // HR Rights
        Hr,
        
        // Audit Rights
        Audit,
        
        // Other Rights
        DepartmentManager,
        UsersAdmin,
        Admin,
        AbsentDGeneral,
        AbsentAdmin,
        GeneralForms,
        LostHours,
        TrainingAdmin,
        TrainingDepartment,
        TrainingDG,
        AffairsCommittee,
        HrReports
    }

    // Extension methods for enum conversion
    public static class RightExtensions
    {
        private static readonly Dictionary<Right, string> _enumToString = new Dictionary<Right, string>
        {
            { Right.TransportsAdmin, "transports-admin" },
            { Right.TransportsDepartment, "transports-department" },
            { Right.TransportsDGeneral, "transports-dgeneral" },
            { Right.InventoryAdmin, "inventory-admin" },
            { Right.InventoryDepartment, "inventory-department" },
            { Right.InventoryDGeneral, "inventory-dgeneral" },
            { Right.FinanceDepartment, "finance-department" },
            { Right.FinanceManager, "finance-manager" },
            { Right.PurchasesDepartment, "purchases-department" },
            { Right.PurchasesAdmin, "purchases-admin" },
            { Right.OvertimeAdmin, "overtime-admin" },
            { Right.OvertimeDepartment, "overtime-department" },
            { Right.OvertimeHrManager, "overtime-hr-manager" },
            { Right.OvertimeDG, "overtime-dg" },
            { Right.OvertimeReport, "overtime-report" },
            { Right.LeavesDepartment, "leaves-department" },
            { Right.LeavesDepartmentManager, "leaves-department-manager" },
            { Right.LeavesHrManager, "leaves-hr-manager" },
            { Right.LeavesDG, "leaves-dg" },
            { Right.LeavesAdmin, "leaves-admin" },
            { Right.Attendence, "attendence" },
            { Right.Hr, "hr" },
            { Right.Audit, "audit" },
            { Right.DepartmentManager, "department-manager" },
            { Right.UsersAdmin, "users-admin" },
            { Right.Admin, "admin" },
            { Right.AbsentDGeneral, "Absent-dgeneral" },
            { Right.AbsentAdmin, "Absent-admin" },
            { Right.GeneralForms, "gforms" },
            { Right.LostHours, "losthours" },
            { Right.TrainingAdmin, "Training-admin" },
            { Right.TrainingDepartment, "Training-department" },
            { Right.TrainingDG, "Training-DG" },
            { Right.AffairsCommittee, "Affairs-Committee" },
            { Right.HrReports, "hr-reports" }
        };

        private static readonly Dictionary<string, Right> _stringToEnum = 
            _enumToString.ToDictionary(kvp => kvp.Value, kvp => kvp.Key);

        /// <summary>
        /// Converts a Right enum to its string representation
        /// </summary>
        public static string ToStringValue(this Right Right)
        {
            return _enumToString.TryGetValue(Right, out var value) ? value : Right.ToString();
        }

        /// <summary>
        /// Converts a string to Right enum
        /// </summary>
        public static Right? ToRight(this string rightString)
        {
            return _stringToEnum.TryGetValue(rightString, out var Right) ? Right : null;
        }

        /// <summary>
        /// Converts multiple Right enums to pipe-separated string
        /// </summary>
        public static string ToStringValue(this IEnumerable<Right> Rights)
        {
            return string.Join("|", Rights.Select(r => r.ToStringValue()));
        }

        /// <summary>
        /// Converts pipe-separated string to Right enums
        /// </summary>
        public static IEnumerable<Right> ToRights(this string rightString)
        {
            if (string.IsNullOrEmpty(rightString))
                return Enumerable.Empty<Right>();

            return rightString.Split('|', StringSplitOptions.RemoveEmptyEntries)
                             .Select(r => r.ToRight())
                             .Where(r => r.HasValue)
                             .Select(r => r.Value);
        }
    }

    public class Rights
    {
        public List<RightsGroupModel> List { get; set; } = new List<RightsGroupModel> {

            new RightsGroupModel
            {
                Name = "النقليات",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="النقليات (ادارة)", Name="transports-admin", EnumValue = Right.TransportsAdmin},
                    new RightModel {Label="النقليات (قسم)", Name="transports-department", EnumValue = Right.TransportsDepartment},
                    new RightModel {Label="النقليات (مدير عام)", Name="transports-dgeneral", EnumValue = Right.TransportsDGeneral},
                }
            },

            new RightsGroupModel
            {
                Name = "المخازن",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="المخازن (ادارة)", Name="inventory-admin", EnumValue = Right.InventoryAdmin},
                    new RightModel {Label="المخازن (قسم)", Name="inventory-department", EnumValue = Right.InventoryDepartment},
                    new RightModel {Label="المخازن (مدير عام)", Name="inventory-dgeneral", EnumValue = Right.InventoryDGeneral},
                }
            },

            new RightsGroupModel
            {
                Name = "المالية",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="قسم المالية", Name="finance-department", EnumValue = Right.FinanceDepartment},
                    new RightModel {Label="مدير المالية", Name="finance-manager", EnumValue = Right.FinanceManager},
                    new RightModel {Label="قسم المشتريات", Name="purchases-department", EnumValue = Right.PurchasesDepartment},
                    new RightModel {Label="ادارة المشتريات", Name="purchases-admin", EnumValue = Right.PurchasesAdmin},
                }
            },

            new RightsGroupModel
            {
                Name = "العمل الاضافي",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="Over time admin", Name="overtime-admin", EnumValue = Right.OvertimeAdmin},
                    new RightModel {Label="العمل الاضافي (قسم)", Name="overtime-department", EnumValue = Right.OvertimeDepartment},
                    new RightModel {Label="العمل الاضافي (مدير قسم)", Name="overtime-hr-manager", EnumValue = Right.OvertimeHrManager},
                    new RightModel {Label="العمل الاضافي (مدير عام)", Name="overtime-dg", EnumValue = Right.OvertimeDG},
                    new RightModel {Label="العمل الاضافي (تقرير)", Name="overtime-report", EnumValue = Right.OvertimeReport},
                }
            },

            new RightsGroupModel
            {
                Name = "الاجازات",
                Rights = new List<RightModel>
                {
                    new RightModel {Label=" الاجازات (قسم)", Name="leaves-department", EnumValue = Right.LeavesDepartment},
                    new RightModel {Label=" الاجازات (رئيس قسم)", Name="leaves-department-manager", EnumValue = Right.LeavesDepartmentManager},
                    new RightModel {Label=" الاجازات (مدير قسم)", Name="leaves-hr-manager", EnumValue = Right.LeavesHrManager},
                    new RightModel {Label="الاجازات (مدير عام)", Name="leaves-dg", EnumValue = Right.LeavesDG},
                    new RightModel {Label="Leaves admin", Name="leaves-admin", EnumValue = Right.LeavesAdmin},
                }
            },

            new RightsGroupModel
            {
                Name = "الحضور",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="الحضور", Name="attendence", EnumValue = Right.Attendence},
                }
            },

            new RightsGroupModel
            {
                Name = "التعيينات",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="التعيينات", Name="hr", EnumValue = Right.Hr},
                }
            },

            new RightsGroupModel
            {
                Name = "التدقيق",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="التدقيق", Name="audit", EnumValue = Right.Audit},
                }
            },

            new RightsGroupModel
            {
                Name = "اخرى",
                Rights = new List<RightModel>
                {
                    new RightModel {Label="رئيس قسم", Name="department-manager", EnumValue = Right.DepartmentManager},
                    new RightModel {Label="Users admin", Name="users-admin", EnumValue = Right.UsersAdmin},
                    new RightModel {Label="ادارة", Name="admin", EnumValue = Right.Admin},
                    new RightModel {Label="Absent D General", Name="Absent-dgeneral", EnumValue = Right.AbsentDGeneral},
                    new RightModel {Label="Absent admin", Name="Absent-admin", EnumValue = Right.AbsentAdmin},
                    new RightModel {Label="General forms", Name="gforms", EnumValue = Right.GeneralForms},
                    new RightModel {Label="Lost hours report", Name="losthours", EnumValue = Right.LostHours},
                    new RightModel {Label="Training Admin", Name="Training-admin", EnumValue = Right.TrainingAdmin},
                    new RightModel {Label="Training Department", Name="Training-department", EnumValue = Right.TrainingDepartment},
                    new RightModel {Label="Training DG", Name="Training-DG", EnumValue = Right.TrainingDG},
                    new RightModel {Label="Affairs Committee", Name="Affairs-Committee", EnumValue = Right.AffairsCommittee},
                    new RightModel {Label="تقارير الموارد البشرية", Name="hr-reports", EnumValue = Right.HrReports},
                }
            },
        };

        /// <summary>
        /// Get all rights as enum values
        /// </summary>
        public IEnumerable<Right> AllRights => List.SelectMany(g => g.Rights).Select(r => r.EnumValue);

        /// <summary>
        /// Get rights by group name
        /// </summary>
        public IEnumerable<Right> GetRightsByGroup(string groupName)
        {
            return List.FirstOrDefault(g => g.Name == groupName)?.Rights.Select(r => r.EnumValue) ?? Enumerable.Empty<Right>();
        }

        /// <summary>
        /// Find right by enum value
        /// </summary>
        public Right? FindRight(Right Right)
        {
            return List.SelectMany(g => g.Rights).FirstOrDefault(r => r.EnumValue == Right)?.EnumValue;
        }

        /// <summary>
        /// Find right by string name
        /// </summary>
        public Right? FindRight(string Right)
        {
            return List.SelectMany(g => g.Rights).FirstOrDefault(r => r.Name == Right)?.EnumValue;
        }
    }

    public class RightModel
    {
        public string Name { get; set; } = ""; 
        public string Label { get; set; } = "";
        public Right EnumValue { get; set; }
    }

    public class RightsGroupModel
    {
        public string Name { get; set; } = "";
        public List<RightModel> Rights { get; set; } = new List<RightModel>();

        /// <summary>
        /// Get rights in this group as enum values
        /// </summary>
        public IEnumerable<Right> RightEnums => Rights.Select(r => r.EnumValue);
    }
}
