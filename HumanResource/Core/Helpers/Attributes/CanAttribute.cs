﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using HumanResource.Core.Data;

namespace HumanResource.Core.Helpers.Attributes;

[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, Inherited = true, AllowMultiple = true)]
public class CanAttribute : Attribute, IAuthorizationFilter
{
    private readonly string _rights;
    private AppHelper _helper;

    public CanAttribute(string rights)
    {
        if (string.IsNullOrEmpty(rights))
        {
            throw new ArgumentNullException("Rights cannot be empty", nameof(rights));
        }

        _rights = rights;
    }

    /// <summary>
    /// Constructor for single enum right
    /// </summary>
    public CanAttribute(Right rightName)
    {
        _rights = rightName.ToStringValue();
    }

    /// <summary>
    /// Constructor for multiple enum rights
    /// </summary>
    public CanAttribute(params Right[] rightNames)
    {
        if (rightNames == null || rightNames.Length == 0)
        {
            throw new ArgumentNullException("Rights cannot be empty", nameof(rightNames));
        }

        _rights = rightNames.ToStringValue();
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {
        _helper = context.HttpContext.RequestServices.GetService<AppHelper>();

        if (!_helper.Can(_rights))
        {
            var request = context.HttpContext.Request;

            bool isAjax = request.Headers["X-Requested-With"] == "XMLHttpRequest";

            if (isAjax)
            {
                context.Result = new JsonResult(new
                {
                    success = false,
                    message = new List<string> { "لا تتوفر لديك صلاحية لهذا الطلب" },
                })
                {
                    StatusCode = StatusCodes.Status200OK
                };
            }
            else
            {
                context.Result = new ForbidResult();
            }

            return;
        }
    }
}
