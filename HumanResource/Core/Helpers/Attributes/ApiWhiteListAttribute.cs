﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using HumanResource.Core.Contexts;

namespace HumanResource.Core.Helpers.Attributes;


[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, Inherited = true, AllowMultiple = true)]
public class ApiWhiteListAttribute : Attribute, IAuthorizationFilter
{

    public ApiWhiteListAttribute()
    {
       
    }

    public void OnAuthorization(AuthorizationFilterContext context)
    {

        var clientIp = context.HttpContext.Connection.RemoteIpAddress?.ToString();

        var _db =  context.HttpContext.RequestServices.GetService<hrmsContext>();


        if (string.IsNullOrEmpty(clientIp) )
        {
            context.Result = new JsonResult(new
            {
                success = false,
                message = new List<string> { "لا تتوفر لديك صلاحية لهذا الطلب"  },
            })
            {
                StatusCode = StatusCodes.Status403Forbidden
            };

            return;
        }

        var device =  _db.ApiDevices.Where(r => r.Ip == clientIp).FirstOrDefault();

        if (device == null)
        {
            context.Result = new JsonResult(new
            {
                success = false,
                message = new List<string> { "لا تتوفر لديك صلاحية لهذا الطلب" + " [" + clientIp + "]" },
            })
            {
                StatusCode = StatusCodes.Status403Forbidden
            };

            return;
        }

    }
}
