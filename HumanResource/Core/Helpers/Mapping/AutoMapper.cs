﻿namespace HumanResource.Core.Helpers.Mapping;

public class AutoMapper
{
    public static TTarget MapProperties<TSource, TTarget>(TSource source)
        where TSource : class
        where TTarget : class, new()
    {

        var target = new TTarget();
        var sourceProperties = typeof(TSource).GetProperties();
        var targetProperties = typeof(TTarget).GetProperties();

        foreach (var sourceProperty in sourceProperties)
        {
            var targetPropery = targetProperties
                .FirstOrDefault(p => p.Name == sourceProperty.Name && p.PropertyType == sourceProperty.PropertyType);

            if (targetPropery != null)
            {

                var value = sourceProperty.GetValue(source);

                if (value != null || Nullable.GetUnderlyingType(targetPropery.PropertyType) != null)
                {
                    targetPropery.SetValue(target, value);
                }
                else
                {
                    continue;
                }
            }
        }

        return target;
    }

    public static List<TTarget> MapList<TSource, TTarget>(List<TSource> sourceList)
        where TSource : class
        where TTarget : class, new()
    {
        var targetList = new List<TTarget>();

        foreach (var sourceItem in sourceList)
        {
            targetList.Add(MapProperties<TSource, TTarget>(sourceItem));
        }

        return targetList;
    }
}


