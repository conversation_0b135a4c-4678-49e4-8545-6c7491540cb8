﻿using HumanResource.Modules.Settings.Services;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Shared.Models.Entities.HRMS;

namespace HumanResource.Core.Helpers.Utilities;


public class StorageHelper
{
    private readonly hrmsContext _db;
    private readonly SettingsService _settings;
    private string _baseUploadDirectory;

    // Define allowed file types
    public enum FileType
    {
        Leaves,
        Contracts,
        Certificates,
        Photos,
        Documents,
        Others,
        System,
        Overtimes
    }

    public StorageHelper(hrmsContext context)
    {
        _db = context;
        _settings = new SettingsService(context);
        
        // Get upload directory from settings or use default
        _baseUploadDirectory = _settings.Get("system.upload_dir", @"C:\hrmsUploads");
    }



    public async Task<string> UploadAsync(IFormFile file, int? employeeId = null, FileType fileType = FileType.Others)
    {
        string directoryPath;
        
        if (employeeId.HasValue && employeeId.Value > 0)
        {
            // For employee files: upload_dir/[employee_id]/[file_type]/;
            string fileTypeFolder = fileType.ToString().ToLower();
            
            directoryPath = Path.Combine(_baseUploadDirectory, employeeId.Value.ToString(), fileTypeFolder);
        }
        else
        {
            // For system files: upload_dir/system/
            directoryPath = Path.Combine(_baseUploadDirectory, "system");
        }

        // Create directory if it doesn't exist
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }

        // Generate unique filename with timestamp
        string fileName = DateTime.Now.ToString("yyyyMMddHHmmssffff") + Path.GetExtension(file.FileName);
        var filePath = Path.Combine(directoryPath, fileName);

        // Save the file
        using (var stream = new FileStream(filePath, FileMode.Create))
        {
            file.OpenReadStream().Position = 0;
            await file.CopyToAsync(stream);
        }

        // Create relative path for storage in database
        string relativePath = filePath.Replace(_baseUploadDirectory, "").TrimStart('\\', '/');

        // Save file information to database
        var upload = new Uploads
        {
            Guid = Guid.NewGuid().ToString(),
            Label = file.FileName,
            Name = relativePath,
            Timestamp = DateTime.Now
        };

        _db.Uploads.Add(upload);
        await _db.SaveChangesAsync();

        return upload.Guid;
    }

    public async Task<string> UploadAsync(IFormFile file, int? employeeId = null, string fileType = "Others")
    {

        return await UploadAsync(file, employeeId, (FileType)Enum.Parse(typeof(FileType), fileType));
    }

    public async Task<bool> Delete(string guid)
    {
        var file = _db.Uploads.Find(guid);
        
        if (file == null)
            return false;
            
        string fullPath = Path.Combine(_baseUploadDirectory, file.Name);
        
        if (File.Exists(fullPath))
        {
            File.Delete(fullPath);
        }
        
        _db.Uploads.Remove(file);
        await _db.SaveChangesAsync();
        
        return true;
    }

    public string Get(string guid)
    {
        var file = _db.Uploads.Find(guid);

        if (file == null)
            return "";

        return "/File/Get/" + file.Guid;
    }

    public string GetPhysicalPath(string guid)
    {
        var file = _db.Uploads.Find(guid);

        if (file == null)
            return "";

        return Path.Combine(_baseUploadDirectory, file.Name);
    }
}