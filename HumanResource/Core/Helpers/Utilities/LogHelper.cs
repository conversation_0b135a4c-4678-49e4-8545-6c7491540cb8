﻿using HumanResource.Core.Contexts;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;

namespace HumanResource.Core.Helpers.Utilities;

public  class LogHelper
{

    hrmsContext _db;
    VempDtl _emp;
    public LogHelper(hrmsContext context, VempDtl emp)
    {
        _db = context;
        _emp = emp;

    }

    public void Log(string code, string type, string description, params (string Key, object Value)[] variables)
    {
        string formatedVariables = FormatedVariables(variables);
        string fullDescription = $"{description}. {formatedVariables}";

        SaveLog(code, type, fullDescription);

    }


    private string FormatedVariables((string Key,object Value)[] variables)
    {
        return string.Join(", ", variables.Select(v => $"{v.Key}: {v.Value}"));
    }

    public void SaveLog(string code, string type, string description)
    {

        var reqLog = new ReqLog
        {
            Id = code,
            Type = type,
            Rem = description,
            UserId = _emp.EmpNo.Value,
        };


        _db.ReqLogs.Add(reqLog);
        _db.SaveChanges();

    }


    public List<ReqLog> Get(string code, string type)
    {

        var logs =  _db.ReqLogs.Where(r=>r.Id == code && r.Type == type).OrderByDescending(r=>r.TimeStamp).ToList();

        return logs;
    }

}
