﻿using System;
using System.IO;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace HumanResource.Core.Helpers;

public static class CryptoHelper
{


    private static readonly string _key = "HrmsSecureKey1324321999AA";
    private static readonly byte[] Salt = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16 };



    public static string Encrypt(string input)
    {

#pragma warning disable CS0168 // Variable is declared but never used
        try
        {
            using (Aes encryptor = Aes.Create())
            {
                Rfc2898DeriveBytes pdb = new Rfc2898DeriveBytes(_key, Salt);
                encryptor.Key = pdb.GetBytes(32);
                encryptor.IV = pdb.GetBytes(16);


                byte[] encrypted;

                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor.CreateEncryptor(), CryptoStreamMode.Write))
                    {
                        byte[] clearByte = Encoding.Unicode.GetBytes(input);
                        cs.Write(clearByte, 0, clearByte.Length);
                    }

                    encrypted = ms.ToArray();
                }

                string encoded = Convert.ToBase64String(encrypted);
                //encoded = encoded.TrimEnd('=');

                //return (encoded);


                return WebUtility.UrlEncode(encoded);


            }
        }
        catch (Exception ex)
        {
            return input.ToString();
        }
#pragma warning restore CS0168 // Variable is declared but never used


    }

    public static string Decrypt(string cipher)
    {
#pragma warning disable CS0168 // Variable is declared but never used
        try
        {
#pragma warning disable CS1717 // Assignment made to same variable
            cipher = cipher;
#pragma warning restore CS1717 // Assignment made to same variable
            //cipher = cipher.PadRight(cipher.Length + (4 - cipher.Length % 4) % 4, '=');


            byte[] decrypted;

            using (Aes encryptor = Aes.Create())
            {
                Rfc2898DeriveBytes pdb = new Rfc2898DeriveBytes(_key, Salt);
                encryptor.Key = pdb.GetBytes(32);
                encryptor.IV = pdb.GetBytes(16);

                using (MemoryStream ms = new MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor.CreateDecryptor(), CryptoStreamMode.Write))
                    {
                        byte[] cipherByte = Convert.FromBase64String(cipher);
                        cs.Write(cipherByte, 0, cipherByte.Length);
                    }

                    decrypted = ms.ToArray();
                }
            }

            return Encoding.Unicode.GetString(decrypted);

        }
        catch (Exception ex)
        {
            return null;
        }
#pragma warning restore CS0168 // Variable is declared but never used
    }

    public static string ToE(string input)
    {
        StringBuilder sb = new StringBuilder();

        foreach (char c in input)
        {
            if (c >= 'A' && c <= 'Z')
            {
                sb.Append((char)(c + 23));
            }
            else if (c >= 'a' && c <= 'z' || c >= 'O' && c <= '9')
            {
                sb.Append(c);
            }

        }



        return sb.ToString();

    }






}

