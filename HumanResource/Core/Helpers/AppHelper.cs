﻿using Microsoft.CodeAnalysis;
using Microsoft.EntityFrameworkCore;
using Oracle.ManagedDataAccess.Client;
using System.Data;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore.Metadata;
using Oracle.ManagedDataAccess.Types;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using HumanResource.Modules.Leaves.Services;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Employees.Services;
using HumanResource.Modules.Settings.Services;
using HumanResource.Modules.Shared.Services;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;
using HumanResource.Core.Helpers.Utilities;
using HumanResource.Core.Contexts;
using HumanResource.Core.Data;

namespace HumanResource.Core.Helpers;

public class AppHelper
{

    private readonly hrmsContext _context;
    private readonly hrmsContext _db;
    private readonly MailService _mail;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly SettingsService _settings;
    private readonly IFormattingService _format;
    private int _EmpNo = 0;

    // note from safa

    public AppHelper(hrmsContext context, IHttpContextAccessor httpContextAccessor, MailService mail)
    {
        _context = context;
        _db = context;
        _mail = mail;
        _settings = new SettingsService(context);
        _format = new FormattingService();
        _httpContextAccessor = httpContextAccessor;



        //_EmpNo = Int16.Parse(NumberName);
        _EmpNo = 213; //default user for testing

        if (_EmpNo == 213)
        {
            //_EmpNo = 213;
            //_EmpNo = 58;
            //_EmpNo = 341;

        }
    }

    private ClaimsIdentity Identity
    {
        get
        {
            return _httpContextAccessor.HttpContext.User.Identity as ClaimsIdentity;
        }
    }


    public VempDtl Auth()
    {
        // Check if user data is in session cache
        var httpContext = _httpContextAccessor.HttpContext;
        string cacheKey = $"UserAuth_{_EmpNo}";

        if (httpContext.Session.TryGetValue(cacheKey, out byte[] cachedData))
        {
            // Deserialize user data from session
            string jsonData = System.Text.Encoding.UTF8.GetString(cachedData);
            return JsonConvert.DeserializeObject<VempDtl>(jsonData);
        }

        // If not in cache, get from database
        var emp = _context.VempDtls.
           Where(m => m.EmpNo == _EmpNo)
          .Select(x => new VempDtl
          {
              NatId = x.NatId,
              EmpNo = x.EmpNo,
              EmpNameA = x.EmpNameA,
              EmailId = x.EmailId,
              DeptDespA = x.DeptDespA,
              DgDespA = x.DgDespA,
              DeptCode = x.DeptCode,
              DesgCode = x.DesgCode,
              DesgnHierLevel = x.DesgnHierLevel,
              ProfileImage = x.ProfileImage
          })
           .FirstOrDefault();

        // Store in session cache
        if (emp != null)
        {
            string jsonData = JsonConvert.SerializeObject(emp);
            httpContext.Session.Set(cacheKey, System.Text.Encoding.UTF8.GetBytes(jsonData));
        }

        return emp;
#pragma warning restore CS0168 // Variable is declared but never used
    }

#pragma warning disable CS8632 // The annotation for nullable reference types should only be used in code within a '#nullable' annotations context.
    public bool Can(string rightsRaw)
    {
        // Early return for null or empty rights
        if (string.IsNullOrEmpty(rightsRaw))
        {
            return true;
        }

        // Get or create user rights from cache
        var httpContext = _httpContextAccessor.HttpContext;
        string cacheKey = $"UserRights_{_EmpNo}";
        HashSet<string> roleRights;

        if (httpContext.Session.TryGetValue(cacheKey, out byte[] cachedData))
        {
            // Deserialize rights from session
            string jsonData = System.Text.Encoding.UTF8.GetString(cachedData);
            roleRights = JsonConvert.DeserializeObject<HashSet<string>>(jsonData);
        }
        else
        {
            // Get user roles from database
            var empRoles = _db.RoleEmps.Where(r => r.EmpNo == _EmpNo).ToList();
            if (empRoles.Count == 0)
            {
                return false;
            }

            // Build user rights
            roleRights = new HashSet<string>();
            foreach (var empRole in empRoles)
            {
                var role = _db.Roles.Find(empRole.RoleCode);
                if (role?._Rights != null)
                {
                    foreach (var right in role._Rights)
                    {
                        roleRights.Add(right);
                    }
                }
            }

            // Add department-manager right for users with DesgnHierLevel <= 6
            if (Auth().DesgnHierLevel <= 6)
            {
                roleRights.Add("department-manager");
            }

            // Cache the rights
            string jsonData = JsonConvert.SerializeObject(roleRights);
            httpContext.Session.Set(cacheKey, System.Text.Encoding.UTF8.GetBytes(jsonData));
        }

        // Check if user has any of the required rights
        var requestedRights = rightsRaw.Split('|');
        foreach (var right in requestedRights)
        {
            if (roleRights.Contains(right))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Check if user has the specified right using enum
    /// </summary>
    public bool Can(Right rightName)
    {
        return Can(rightName.ToStringValue());
    }

    /// <summary>
    /// Check if user has any of the specified rights using enums
    /// </summary>
    public bool Can(params Right[] rightNames)
    {
        if (rightNames == null || rightNames.Length == 0)
        {
            return true;
        }

        var rightsString = rightNames.ToStringValue();
        return Can(rightsString);
    }

    /// <summary>
    /// Check if user has any of the specified rights using enums
    /// </summary>
    public bool Can(IEnumerable<Right> rightNames)
    {
        if (rightNames == null || !rightNames.Any())
        {
            return true;
        }

        var rightsString = rightNames.ToStringValue();
        return Can(rightsString);
    }

    /// <summary>
    /// Get all rights the current user has as enum values
    /// </summary>
    public IEnumerable<Right> GetUserRights()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        string cacheKey = $"UserRights_{_EmpNo}";
        HashSet<string> roleRights;

        if (httpContext.Session.TryGetValue(cacheKey, out byte[] cachedData))
        {
            // Deserialize rights from session
            string jsonData = System.Text.Encoding.UTF8.GetString(cachedData);
            roleRights = JsonConvert.DeserializeObject<HashSet<string>>(jsonData);
        }
        else
        {
            // Force cache creation by calling the existing Can method
            Can(""); // This will populate the cache

            // Now get from cache
            if (httpContext.Session.TryGetValue(cacheKey, out cachedData))
            {
                string jsonData = System.Text.Encoding.UTF8.GetString(cachedData);
                roleRights = JsonConvert.DeserializeObject<HashSet<string>>(jsonData);
            }
            else
            {
                return Enumerable.Empty<Right>();
            }
        }

        return roleRights.SelectMany(r => r.ToRights());
    }

    /// <summary>
    /// Check if user has all of the specified rights using enums
    /// </summary>
    public bool HasAllRights(params Right[] rightNames)
    {
        if (rightNames == null || rightNames.Length == 0)
        {
            return true;
        }

        return rightNames.All(r => Can(r));
    }

    /// <summary>
    /// Check if user has all of the specified rights using enums
    /// </summary>
    public bool HasAllRights(IEnumerable<Right> rightNames)
    {
        if (rightNames == null || !rightNames.Any())
        {
            return true;
        }

        return rightNames.All(r => Can(r));
    }

    public VempDtl StaffData(int? Id)
    {

        var emp = _context.VempDtls
        .Select(v => new VempDtl
        {
            EmpNo = v.EmpNo,
            EmpNameA = v.EmpNameA,
            DgCode = v.DgCode,
            DgDespA = v.DgDespA,
            DeptCode = v.DeptCode,
            DeptDespA = v.DeptDespA,
            DesgnCode = v.DesgnCode,
            DesgCode = v.DesgCode,
            DesgType = v.DesgType,
            EmailId = v.EmailId,
            NatId = v.NatId,
            GradRank = v.GradRank,
            BirthDate = v.BirthDate,
            AppointDate = v.AppointDate,
            DesgnHierLevel = v.DesgnHierLevel,
            GscAppointDate = v.GscAppointDate,
            ProfileImage = v.ProfileImage,
            SexInfo = v.SexInfo,
            CatCode = v.CatCode,
            ProfileImageUrl = v.ProfileImageUrl
        })
        .Where(m => m.EmpNo == Id)
        .FirstOrDefault();

        if (emp == null)
        {
            return new VempDtl();
        }

        return emp;
    }

    public List<VempDtl> Managers(int? Id)
    {

        using (var ctx = new hrmsContext())
        using (var cmd = ctx.Database.GetDbConnection().CreateCommand())
        {
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.CommandText = "MANGERAPPROVAL";

            var EMPNOParam = new OracleParameter("inEMPNO", OracleDbType.Int32, Id, ParameterDirection.Input);
            var DGParam = new OracleParameter("inDG", OracleDbType.Int32, StaffData(Id).DgCode, ParameterDirection.Input);
            var DEPTParam = new OracleParameter("inDEPT", OracleDbType.Int32, StaffData(Id).DeptCode, ParameterDirection.Input);
            var DESGNParam = new OracleParameter("inDESGN", OracleDbType.Int32, StaffData(Id).DesgnHierLevel, ParameterDirection.Input);
            var l_idParam = new OracleParameter("Outl_id", OracleDbType.RefCursor, ParameterDirection.Output);

            cmd.Parameters.AddRange(new[] { EMPNOParam, DGParam, DEPTParam, DESGNParam, l_idParam });
            cmd.Connection.Open();
            var result = cmd.ExecuteNonQuery();
            OracleDataReader dr = ((OracleRefCursor)cmd.Parameters[4].Value).GetDataReader();

            List<VempDtl> Mangers = new List<VempDtl>();
            while (dr.Read())
            {
                Mangers.Add(StaffData(Convert.ToInt32(dr["EmpNo"])));

            }

            cmd.Connection.Close();


            return Mangers;

        }
    }



    public EmployeeService Employee()
    {
        return new EmployeeService(_db);
    }

    public LeaveService Leave()
    {
        return new LeaveService(_db, _mail);
    }

    public MailService Mail()
    {
        return _mail;
    }

    public UserNotificationService Notify()
    {
        return new UserNotificationService(_db);
    }


    public string Ec(string input)
    {
        return CryptoHelper.Encrypt(input);
    }

    public string Dc(string input)
    {
        return CryptoHelper.Decrypt(input);
    }

    public async Task<string> UploadAsync(IFormFile file, int? employeeId = null)
    {
        return await Storage().UploadAsync(file, employeeId, StorageHelper.FileType.Others);
    }

    public async Task<string> UploadAsync(IFormFile file, int? employeeId, StorageHelper.FileType fileType)
    {
        return await Storage().UploadAsync(file, employeeId, fileType);
    }

    public async Task<string> UploadAsync(IFormFile file, int? employeeId, string fileType)
    {
        return await Storage().UploadAsync(file, employeeId, fileType);
    }

    public async Task<string> UploadSystemFile(IFormFile file)
    {
        return await Storage().UploadAsync(file, null, StorageHelper.FileType.System);
    }

    public bool IsHoliday(DateTime date)
    {

        if (date.DayOfWeek == DayOfWeek.Friday || date.DayOfWeek == DayOfWeek.Saturday)
        {
            return true;
        }


        if (_context.Tholidays.Any(holiday => holiday.Holiday == date))
        {
            return true;
        }

        return false;
    }


    public StorageHelper Storage()
    {
        return new StorageHelper(_context);
    }


    public string GetFile(string guid)
    {
        return Storage().Get(guid);
    }


    public LogHelper Logger()
    {
        return new LogHelper(_context, Auth());
    }

    public void Log(string code, string type, string description, params (string Key, object Value)[] variables)
    {
        Logger().Log(code, type, description, variables);
    }

    public void Log(int code, string type, string description, params (string Key, object Value)[] variables)
    {

        var scode = code.ToString();

        Logger().Log(scode, type, description, variables);
    }

    public List<ReqLog> GetLogs(string code, string type)
    {
        return Logger().Get(code, type);
    }

    public List<ReqLog> GetLogs(int code, string type)
    {

        var scode = code.ToString();

        return Logger().Get(scode, type);
    }

    public string FormatHour(float hour)
    {
        return Format.Hours(hour);
    }

    public string FormatHour(float? hour)
    {
        return Format.Hours(hour);
    }


    public TincomingMail createIncome(string LetterSubj)
    {

        return null;

    }


    public string renderSatus(int? StatusCode)
    {


        var status = _context.ReqStatCode.Find(StatusCode);

        if (status == null)
        {
            return "Error " + StatusCode;
        }

        return status.NameA;
    }

    public string renderSatus(int? StatusCode, int[] Alerts)
    {


        var status = _context.ReqStatCode.Find(StatusCode);

        if (status == null)
        {
            return "Error " + StatusCode;
        }

        if (Alerts.Contains(StatusCode.Value))
        {
            return "<span class='badge badge-danger rounded-pill px-3'> " + status.NameA + " </span>";
        }

        return status.NameA;
    }

    public SettingsService Settings => _settings;

    public IFormattingService Format => _format;

    /// <summary>
    /// Clear all cached data for the current user (authentication and rights)
    /// </summary>
    public void ClearUserCache()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Session != null)
        {
            string authCacheKey = $"UserAuth_{_EmpNo}";
            string rightsCacheKey = $"UserRights_{_EmpNo}";
            
            httpContext.Session.Remove(authCacheKey);
            httpContext.Session.Remove(rightsCacheKey);
        }
    }

    /// <summary>
    /// Clear cached authentication data for the current user
    /// </summary>
    public void ClearAuthCache()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Session != null)
        {
            string cacheKey = $"UserAuth_{_EmpNo}";
            httpContext.Session.Remove(cacheKey);
        }
    }

    /// <summary>
    /// Clear cached rights data for the current user
    /// </summary>
    public void ClearRightsCache()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Session != null)
        {
            string cacheKey = $"UserRights_{_EmpNo}";
            httpContext.Session.Remove(cacheKey);
        }
    }

    /// <summary>
    /// Clear cached data for a specific user by employee number
    /// </summary>
    /// <param name="empNo">Employee number to clear cache for</param>
    public void ClearUserCache(int empNo)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.Session != null)
        {
            string authCacheKey = $"UserAuth_{empNo}";
            string rightsCacheKey = $"UserRights_{empNo}";
            
            httpContext.Session.Remove(authCacheKey);
            httpContext.Session.Remove(rightsCacheKey);
        }
    }
}

