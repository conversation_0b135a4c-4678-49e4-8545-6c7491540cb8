﻿using Microsoft.AspNetCore.Mvc;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace HumanResource.Core.Helpers.Extinctions;

public static class ResponseHelper
{

    public static JsonResult Json<T>(
        bool success,
        T? data = default,
        List<string> message = null,
        string action = ""
    )
    {
        var response = new Response<T>
        {
            Success = success,
            Message = message,
            Action = action,
            Data = data
        };

        return new JsonResult(response);
    }


    public static JsonResult Json(
        bool success,
        List<string> message = null,
        string action = ""
    )
    {
        var response = new Response
        {
            Success = success,
            Message = message ,
            Action = action,

        };

        return new JsonResult(response);
    }



    public static Response Result(
        //this Controller controller,
        bool success,
        List<string> message = null,
        string action = ""
    )
    {
        return new Response
        {
            Success = success,
            Message = message,
            Action = action,
        };
    }



    public static Response<T> Result<T>(
        //this Controller controller,
        bool success,
        List<string> message = null,
        string action = "",
        T? data = default
    )
    {
        return new Response<T>
        {
            Success = success,
            Message = message ,
            Action = action,
            Data = data
        };
    }

}

public class Response
{
    public bool Success { get; set; }
    public List<string> Message { get; set; } = new List<string>();
    public string Action { get; set; } = "";

}


public class Response<T>
{
    public bool Success { get; set; }
    public List<string> Message { get; set; } = new List<string>();
    public T? Data { get; set; }
    public string Action { get; set; } = "";

}