﻿
using HumanResource.Modules.Assets.Models.Entities;
using HumanResource.Modules.Attendence.Models.Entities;
using HumanResource.Modules.Employees.Models.Entities;
using HumanResource.Modules.Execuses.Models.Entities;
using HumanResource.Modules.Inventory.Models.Entities;
using HumanResource.Modules.Leaves.Models.Entities;
using HumanResource.Modules.Overtime.Models.Entities;
using HumanResource.Modules.Purchases.Models.Entities;
using HumanResource.Modules.Transports.Models.Entities;
using HumanResource.Modules.Settings.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities;
using HumanResource.Modules.Shared.Models.Entities.HRMS;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Careers;
using HumanResource.Modules.Shared.Models.Entities.HRMS.Mail;

using Microsoft.EntityFrameworkCore;


namespace HumanResource.Core.Contexts;

public partial class hrmsContext : DbContext
{
    public hrmsContext()
    {
    }
    
    public hrmsContext(DbContextOptions<hrmsContext> options)
        : base(options)
    {
    }


    public virtual DbSet<OvertimePay> OvertimePays { get; set; }
    public virtual DbSet<VleaveHistory> VleaveHistories { get; set; }


    public virtual DbSet<StrCategoryCode> StrCategoryCodes { get; set; }
    public virtual DbSet<StrItemCode> StrItemCodes { get; set; }
    public virtual DbSet<StrReqMastr> StrReqMastrs { get; set; }
    public virtual DbSet<StrRequestDetail> StrRequestDetails { get; set; }
    public virtual DbSet<VempDtl> VempDtls { get; set; }
    public virtual DbSet<ConEmpMas> ConEmpMas { get; set; }
    public virtual DbSet<EmployeeMas> EmployeeMas { get; set; }
    public virtual DbSet<VempQual> VempQuals { get; set; }
    public virtual DbSet<TotalExc> TotalExcs { get; set; }
    public virtual DbSet<TotalLate> TotalLates  { get; set; }
    public virtual DbSet<Tholidays> Tholidays { get; set; }

    public virtual DbSet<VempTrg> VempTrgs { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<Uploads> Uploads { get; set; }
    public virtual DbSet<TDeptCode> TDeptCode { get; set; }
    public virtual DbSet<TdgCode> TdgCodes { get; set; }
    public virtual DbSet<TsectionCode> TsectionCodes { get; set; }

    public virtual DbSet<EmpDtlsUpdate> EmpDtlsUpdates  { get; set; }

    public virtual DbSet<TEmpEarning> EmpEarnings { get; set; }
    public virtual DbSet<VEmpEarningsAll> VEmpEarningsAll { get; set; }
    public virtual DbSet<ConPayrollDtl> ConPayrollDtls { get; set; }


    // TRANSPORTS 

    public virtual DbSet<Cars> Cars { get; set; }
    public virtual DbSet<CarsServices> CarsServices { get; set; }
    public virtual DbSet<CarsGasDtl> CarsGasDtl { get; set; }
    public virtual DbSet<CarsViolations> CarsViolations { get; set; }
    public virtual DbSet<CarsRequests> CarsRequests { get; set; }



    // LEAVES 
    public virtual DbSet<FPRecords> FPRecords { get; set; }
    public virtual DbSet<LeavesDtl> LeavesDtl { get; set; }
    public virtual DbSet<Absent> Absents { get; set; }
    public virtual DbSet<VAttendTran> VAttendTrans { get; set; }
    public virtual DbSet<TdispTrnLeaveDtl> TdispTrnLeaveDtls { get; set; }


    // PURCHASES 

    public virtual DbSet<RequestItem> RequestItem { get; set; }
    public virtual DbSet<PurchasesRequest> PurchasesRequest { get; set; }
    //public virtual DbSet<PurchasesSupplier> PurchasesSupplier { get; set; }
    public virtual DbSet<PurchaseQuotation> PurchaseQuotations { get; set; }
    public virtual DbSet<Purchases> Purchases { get; set; }

    // INVENTORY

    public virtual DbSet<InventoryCategory> InventoryCategory { get; set; }
    public virtual DbSet<InventoryItem> InventoryItem { get; set; }
    public virtual DbSet<InventoryRequest> InventoryRequest { get; set; }
    public virtual DbSet<InventoryLog> InventoryLog { get; set; }

    public virtual DbSet<AssetItem> AssetItem { get; set; }


    // LEAVeS
    public virtual DbSet<ConLeaveAplTx> ConLeaveAplTxs { get; set; }
    public virtual DbSet<ConLeaveBal> ConLeaveBals { get; set; }
    public virtual DbSet<TleaveAplDtl> TleaveAplDtls { get; set; }
    public virtual DbSet<TleaveAplTx> TleaveAplTxs { get; set; }
    public virtual DbSet<TleaveBal> TleaveBals { get; set; }
    public virtual DbSet<TleaveBalLog> TleaveBalLogs { get; set; }
    public virtual DbSet<TleaveBalAdjustmentTx> TleaveBalAdjustmentTxs { get; set; }
    public virtual DbSet<TleaveReturnTx> TleaveReturnTxs { get; set; }
    public virtual DbSet<TleaveBalAsofdate> TleaveBalAsofdates { get; set; }
    public virtual DbSet<TleaveEApp> TleaveEApps { get; set; }
    public virtual DbSet<VreqLeave> VreqLeaves { get; set; }
    public virtual DbSet<TleaveCode> TleaveCodes { get; set; }
    public virtual DbSet<TleaveType> TleaveTypes { get; set; }
    public virtual DbSet<TleaveEligibility> TleaveEligibilities { get; set; }


    // MAIL

    public virtual DbSet<TincomingMail> TincomingMails { get; set; }
    public virtual DbSet<TIncomingDoc> TIncomingDoc { get; set; }
    public virtual DbSet<TLastMailNo> TLastMailNo { get; set; }
    public virtual DbSet<TOutgoingMail> TOutgoingMail { get; set; }
    public virtual DbSet<TOrderType> TOrderType { get; set; }
    public virtual DbSet<TTransType> TTransType { get; set; }


    //Training
    public virtual DbSet<TqualCode> TqualCodes { get; set; }
    public virtual DbSet<TunivInstCode> TunivInstCodes { get; set; }
    public virtual DbSet<TqualPlan> TqualPlans { get; set; }
    public virtual DbSet<TqualReq> TqualReqs { get; set; }
    public virtual DbSet<TSubjectCode> TSubjectCodes { get; set; }
    public virtual DbSet<TstudyType> TstudyTypes { get; set; }
    public virtual DbSet<EmpMaxOualDet> EmpMaxOualDets { get; set; }
    public virtual DbSet<TcountryCode> TcountryCode { get; set; }
    public virtual DbSet<TqualEmpAttendance> TqualEmpAttendances  { get; set; }
    public virtual DbSet<TqualStudyReport> TqualStudyReports { get; set; }

    
    public virtual DbSet<TcourseSupplier> TcourseSuppliers { get; set; }
    public virtual DbSet<TempTrgHist> TempTrgHist { get; set; }

    public virtual DbSet<TcourseCatalogue> TcourseCatalogue { get; set; }
    public virtual DbSet<TcourseType> TcourseType { get; set; }

    public virtual DbSet<TtrgPlaceCode> TtrgPlaceCode { get; set; }
    public virtual DbSet<TcourseCatCode> TcourseCatCode { get; set; }

    public virtual DbSet<TcountryCode> TcountryCodes{ get; set; }


    public virtual DbSet<VempDtl> VempDtl { get; set; }

    public virtual DbSet<TempTrgMas> TempTrgMas { get; set; }


    public virtual DbSet<TempTrgReq> TempTrgReq { get; set; }
    public virtual DbSet<TempReqDtls> TempReqDtls { get; set; }

    public virtual DbSet<Years> Years { get; set; }
    public virtual DbSet<EmpSkill> EmpSkills { get; set; }
    public virtual DbSet<EmpImpact> EmpImpacts { get; set; }

    public virtual DbSet<SpecialiedSkill> SpecialiedSkills { get; set; }

    public virtual DbSet<TrainigPlan> TrainigPlans { get; set; }
    public virtual DbSet<TcityCode> TcityCodes { get; set; }
    public virtual DbSet<TrainingComm> TrainingComms { get; set; }

    public virtual DbSet<VendorTraining> VendorTraining { get; set; }
    public virtual DbSet<TrainingField> TrainingFields { get; set; }

    /// ////////////////////////////////////

    // ASSETS
    public  virtual DbSet<AssetRequest> AssetRequest { get; set; }
    public virtual DbSet<AssetDtls> AssetDtls { get; set; }
    public virtual DbSet<RequestStatus> RequestStatus { get; set; }
    public virtual DbSet<AssetLocation> AssetLocation { get; set; }
    public virtual DbSet<AssetTransferHistory> AssetTransferHistory { get; set; }
    // FROMS

    public virtual DbSet<Form1> Form1 { get; set; }
    public virtual DbSet<Form1Data> Form1Data { get; set; }


    // REPORT 

    public virtual DbSet<VReportLostHours> VReportLostHours { get; set; }
     
    // OVERTIME
    public virtual DbSet<OvertimeReqMas> OvertimeReqMas { get; set; }
    public virtual DbSet<OvertimeReqDtls> OvertimeReqDtls { get; set; }
    public virtual DbSet<OvertimeReqPreDtls> OvertimeReqPreDtls { get; set; }

    public virtual DbSet<ConOvertimeReqMas> ConOvertimeReqMas { get; set; }
    public virtual DbSet<ConOvertimeReqDtls> ConOvertimeReqDtls { get; set; }
    public virtual DbSet<ConOvertimeReqPreDtls> ConOvertimeReqPreDtls { get; set; }

    public virtual DbSet<OvertimeComment> OvertimeComments { get; set; } // shared for EMP and CON

    public virtual DbSet<EmpWorkHours> EmpWorkHours { get; set; }
    public virtual DbSet<TEmpWorkShift> TEmpWorkShifts { get; set; }



    //Status 

    public virtual DbSet<ReqStatCode> ReqStatCode { get; set; }
    public virtual DbSet<ReqLog> Reqlogs { get; set; }

    // ROLES
    public virtual DbSet<Role> Roles { get; set; }
    public virtual DbSet<RoleEmp> RoleEmps { get; set; }

    public virtual DbSet<ReqLog> ReqLogs { get; set; }


    //API
    public virtual DbSet<ApiDevice> ApiDevices { get; set; }

    //
        public virtual DbSet<TUserNotifications> TUserNotifications { get; set; }    public virtual DbSet<TleaveCreditTx> TleaveCreditTxs { get; set; }    public virtual DbSet<TemployeeCareer> TemployeeCareers { get; set; }    public virtual DbSet<TgradeRankCode> TgradeRankCodes { get; set; }

    // Attendance
    public virtual DbSet<AttendanceWarning> AttendanceWarnings { get; set; }
    
    // Employee Warnings
    public virtual DbSet<EmployeeWarning> EmployeeWarnings { get; set; }

    // Settings
    public virtual DbSet<Settings> Settings { get; set; }   


    private Connections connections = new Connections();
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
     => optionsBuilder.UseOracle(connections.getConnection("hrms"), options => 
     {
         options.UseOracleSQLCompatibility("11");
         options.CommandTimeout(60);
  
     });


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("DRCH");

        modelBuilder.Entity<TUserNotifications>(entity =>
        {
            entity.HasKey(e => e.Guid).HasName("PK_GUID");
        });

        modelBuilder.Entity<TleaveBalAdjustmentTx>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InMailNo, e.InDocSlNo }).HasName("TLEAVE_BAL_ADJUSTMENT_TX_PK");
        });

        modelBuilder.Entity<TleaveReturnTx>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InMailNo, e.InDocSlNo }).HasName("TLEAVE_RETURN_TX_PK");
        });



        modelBuilder.Entity<Uploads>(entity =>
        {
            entity.HasKey(e => e.Guid).HasName("PK_GUID");
        });

        modelBuilder.Entity<TEmpWorkShift>(entity =>
        {
            entity.HasKey(e => e.Guid).HasName("PK_GUID");
        });

        modelBuilder.Entity<EmpDtlsUpdate>(entity =>
        {
            entity.HasKey(e => e.EmpNo).HasName("PK_EmpNo");
        });

        modelBuilder.Entity<EmployeeMas>(entity =>
        {
            entity.HasKey(e => e.EmpNo);
        });

        modelBuilder.Entity<Settings>(entity =>
        {
            entity.HasKey(e => e.Key).HasName("PK_Settings");
        });

        modelBuilder.Entity<AttendanceWarning>(entity =>
        {
            entity.HasKey(e => new { e.EmpNo, e.AttendanceDate }).HasName("PK_AttendanceWarning");
        });

        modelBuilder.Entity<EmployeeWarning>(entity =>
        {
            entity.HasKey(e => e.Guid).HasName("EMPLOYEE_WARNINGS_PK");
        });

        modelBuilder.Entity<ConEmpMas>(entity =>
        {
            entity.HasKey(e => e.EmpNo);
        });

        modelBuilder.Entity<RoleEmp>(entity =>
        {

            entity.HasKey(e => new { e.EmpNo, e.RoleCode });
            entity.HasOne(d => d.Role).WithMany(p => p.RoleEmps).HasForeignKey(x => x.RoleCode);
            entity.HasOne(d => d.EmpDtls).WithMany(p => p.RoleEmps).HasForeignKey(x => x.EmpNo);

        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => new {  e.Code });
       

        });

        modelBuilder.Entity<ReqLog>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.Type,e.TimeStamp,e.UserId });
        });

        modelBuilder.Entity<ApiDevice>(entity =>
        {
            entity.HasKey(e => new { e.Ip });
        });


        modelBuilder.Entity<VempDtl>(entity =>
        {
            entity.ToView("VEMP_DTLS");
        });

        modelBuilder.Entity<EmpWorkHours>(entity =>
        {
            entity.ToView("VEMP_WORK_HOURS");
        });

        modelBuilder.Entity<TqualReq>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo });
            entity.HasOne(d => d.TstudyType).WithMany(p => p.TqualReqs).HasForeignKey(x => x.StudyType);
            entity.HasOne(d => d.TcountryCode).WithMany(p => p.TqualReqs).HasForeignKey(x => x.CountryCode);
            entity.HasOne(d => d.TunivInstCode).WithMany(p => p.TqualReqs).HasForeignKey(x => x.UnivInstCode);
            entity.HasOne(d => d.TqualCode).WithMany(p => p.TqualReqs).HasForeignKey(x => x.QualCode);
            entity.HasOne(d => d.TSubjectCode).WithMany(p => p.TqualReqs).HasForeignKey(x => x.SubjCode);

        });

        modelBuilder.Entity<TqualCode>(entity =>
        {
            entity.HasKey(e => e.QualCode);
      });


     modelBuilder.Entity<TunivInstCode>(entity =>
        {
            entity.HasKey(e => e.UnivInstCode);
        });

        modelBuilder.Entity<TqualPlan>(entity =>
        {
            entity.HasKey(e => e.QualCode);

        });
        modelBuilder.Entity<TSubjectCode>(entity =>
        {
            entity.HasKey(e => e.SubjCode);
        });

        modelBuilder.Entity<TstudyType>(entity =>
        {
            entity.HasKey(e => e.StudyType);
        });

        modelBuilder.Entity<StrCategoryCode>(entity =>
        {
            entity.HasKey(e => e.CategoryCode).HasName("PK_StrCategoryCodes");
        });

        modelBuilder.Entity<StrItemCode>(entity =>
        {
            entity.HasKey(e => e.ItemCode).HasName("PK_StrItemCodes");

            entity.HasOne(d => d.StrCategory).WithMany(p => p.StrItemCodes)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_StrItemCodes_CategoryCode");
        });

        modelBuilder.Entity<StrReqMastr>(entity =>
        {
            entity.HasKey(e => e.ReqNo).HasName("PK_StrReqMstrs");
        });

        modelBuilder.Entity<TqualCode>(entity =>
        {
            entity.HasKey(e => e.QualCode).HasName("PK_QualCode");
        });

        modelBuilder.Entity<TunivInstCode>(entity =>
        {
            entity.HasKey(e => e.UnivInstCode).HasName("PK_UnivInstCode");
        });

        modelBuilder.Entity<TqualStudyReport>(entity =>
        {
            entity.HasKey(e => new { e.EmpNo, e.Semester, e.Year }).HasName("StudyReport_PK");
        });


        modelBuilder.Entity<StrRequestDetail>(entity =>
        {
            entity.HasKey(e => new { e.ReqNo, e.ReqDetailNo }).HasName("PK_StrRequestDetails");

            entity.HasOne(d => d.StrCategory).WithMany(p => p.StrRequestDetail)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_StrReqtDetails_CategoryCode");



            entity.HasOne(d => d.StrReqMastr).WithMany(p => p.StrRequestDetail).HasConstraintName("FK_StrReqDetails_ReqNo");
        });

        modelBuilder.Entity<TqualEmpAttendance>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_TqualEmpAttendance");
        });

        modelBuilder.Entity<FPRecords>(entity =>
        {
            entity.ToView("V_ORIGIN_TRANS");

        });

        modelBuilder.Entity<LeavesDtl>(entity =>
        {
            entity.ToView("VLEAVE_DTL");

        });

        modelBuilder.Entity<EmpMaxOualDet>(entity =>
        {
            entity.ToView("EMP_MAX_QUAL_DET");

        });

        

        modelBuilder.Entity<TotalLate>(entity =>
        {
            entity.ToView("TOTAL_LATE");

        });

        modelBuilder.Entity<TotalExc>(entity =>
        {
            entity.ToView("TOTAL_EXC");

        });


        modelBuilder.Entity<TDeptCode>(entity =>
        {
            entity.HasKey(e => new { e.UnitCode, e.DgCode, e.DeptCode }).HasName("DEPT_PK");

            //entity.HasOne(d => d.DgCode).WithMany(p => p.TDeptCode)
            //    .OnDelete(DeleteBehavior.ClientSetNull)
            //    .HasConstraintName("DEPT_DG_FK");
        });

        modelBuilder.Entity<TdgCode>(entity =>
        {
            entity.HasKey(e => new { e.UnitCode, e.DgCode }).HasName("DG_PK");
        });
        modelBuilder.Entity<TsectionCode>(entity =>
        {
            entity.HasKey(e => new { e.UnitCode, e.DgCode, e.DeptCode, e.SectionCode }).HasName("SECTN_PK");

        });
        modelBuilder.Entity<VempQual>(entity =>
        {
            entity.ToView("VEMP_QUAL");
        });

        modelBuilder.Entity<VempTrg>(entity =>
        {
            entity.ToView("VEMP_TRG");
        });

        modelBuilder.Entity<VleaveHistory>(entity =>
        {
            entity.ToView("VLEAVE_HISTORY");
        });

        modelBuilder.Entity<OvertimePay>(entity =>
        {
            entity.ToView("OVERTIME_PAY");
        });

        modelBuilder.Entity<VAttendTran>(entity =>
        {
            entity.ToView("V_ATTEND_TRANS");
        });

        modelBuilder.Entity<TdispTrnLeaveDtl>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo }).HasName("TLEAVE_RETURN_TX_PK");
        });

        modelBuilder.Entity<Absent>(entity =>
        {
            entity.HasKey(e => e.ReqNo).HasName("PK_Absents");
        });



        modelBuilder.Entity<ConLeaveBal>(entity =>
        {
            entity.Property(e => e.CentreCode).HasDefaultValueSql("1");
            entity.HasKey(e => new { e.EmpNo, e.LeaveCode}).HasName("CON_LV_APL_BAL_PK");
        });


  
        modelBuilder.Entity<TleaveAplDtl>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo, e.SlNo }).HasName("LV_APL_DTLS_PK");
        });

        modelBuilder.Entity<TleaveAplTx>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo }).HasName("LV_APL_Tx_PK");
            entity.HasOne(d => d.TleaveCode).WithMany(p => p.TleaveAplTxs).HasForeignKey(x => x.LeaveCode);
            entity.HasOne(d => d.TleaveType).WithMany(p => p.TleaveAplTxes).HasForeignKey(x => x.LeaveType);
        });

        modelBuilder.Entity<ConLeaveAplTx>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo }).HasName("CON_LV_APL_TX_PK");
            entity.HasOne(d => d.TleaveCode).WithMany(p => p.ConLeaveAplTxs).HasForeignKey(x => x.LeaveCode);
            entity.HasOne(d => d.TleaveType).WithMany(p => p.ConLeaveAplTxs).HasForeignKey(x => x.LeaveType);
        });

        modelBuilder.Entity<TleaveBal>(entity =>
        {
            entity.HasKey(e => new { e.UnitCode, e.EmpNo, e.LeaveCode }).HasName("LV_LEAVE_BAL_PK");
        });

        modelBuilder.Entity<TleaveBalLog>(entity =>
        {
            entity.HasKey(e => new { e.UnitCode, e.EmpNo, e.LeaveCode, e.RefDate }).HasName("TLEAVE_BAL_LOG_PK");
        });

        modelBuilder.Entity<TleaveEApp>(entity =>
        {
            entity.HasKey(e => e.AppNo).HasName("SYS_C00325935");

            entity.Property(e => e.AppNo).ValueGeneratedNever();
        });

        modelBuilder.Entity<TleaveCode>(entity =>
        {
            entity.HasKey(e => e.LeaveCode).HasName("LEAVE_PK");
        });

        modelBuilder.Entity<TleaveType>(entity =>
        {
            entity.HasKey(e => e.LeaveType).HasName("LEAVE_TYPE_PK");
        });

        modelBuilder.Entity<TleaveEligibility>(entity =>
        {
            entity.HasKey(e => e.LeaveCode).HasName("LEAVE_ELIGIBILITY_PK");
            entity.HasOne(d => d.TleaveCode)
                  .WithOne()
                  .HasForeignKey<TleaveEligibility>(x => x.LeaveCode);
        });

        modelBuilder.Entity<TemployeeCareer>(entity =>
        {
            entity.HasKey(e => new { e.UnitCode, e.EmpNo, e.OrderYear, e.OrderDeptInd, e.OrderSlNo }).HasName("TEMPLOYEE_CAREER_PK");
        });

        // Add configuration for TleaveCreditTx
        modelBuilder.Entity<TleaveCreditTx>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InMailNo, e.InDocSlNo }).HasName("TLEAVE_CREDIT_TX_PK");
        });

        ///////////TRAINING///////////////

        modelBuilder.Entity<TempTrgHist>(entity =>
        {
            entity.HasKey(e => new {  e.EmpNo, e.CourseCode, e.CourseStartDate, e.CourseEndDate, e.SubjCode }).HasName("ID");
            entity.HasOne(d => d.TcourseCatalogue).WithMany(p => p.TempTrgHist).HasForeignKey(x => x.CourseCode);
            entity.HasOne(d => d.TempTrgMas).WithMany(p => p.TempTrgHist).HasForeignKey(x => x.SalNo);
            entity.HasOne(d => d.VempDtl).WithMany(p => p.TempTrgHist).HasForeignKey(x => x.EmpNo);

          

        });

        modelBuilder.Entity<TcourseSupplier>(entity =>
        {
            entity.HasKey(e => e.CourseSupplerCode);
        });


        modelBuilder.Entity<Years>(entity =>
        {
            entity.HasKey(e => e.Id);
        });


  

        modelBuilder.Entity<TcourseCatCode>(entity =>
        {
            entity.HasKey(e => e.CourseCatCode);
        });

        modelBuilder.Entity<TcourseType>(entity =>
        {
            entity.HasKey(e => e.CourseType);
        });

        modelBuilder.Entity<TtrgPlaceCode>(entity =>
        {
            entity.HasKey(e => e.TrgPlaceCode);
        });
        modelBuilder.Entity<VempDtl>(entity =>
        {
            entity.HasKey(e => e.EmpNo);
        }); 


        modelBuilder.Entity<TcountryCode>(entity =>
        {
            entity.HasKey(e => e.CountryCode);
        });



        modelBuilder.Entity<TempTrgMas>(entity =>
        {
            entity.HasKey(e => e.SalNo);
            entity.HasOne(d => d.TcourseCatalogue).WithMany(p => p.TempTrgMas).HasForeignKey(x => x.CourseCode);
            entity.HasOne(d => d.TcountryCode).WithMany(p => p.TempTrgMas).HasForeignKey(x => x.CountryCode);
            entity.HasOne(d => d.TcourseSupplier).WithMany(p => p.TempTrgMas).HasForeignKey(x => x.CourseSupplierCode);
           
            //entity.HasOne(d => d.TempTrgHist).WithMany(p => p.TempTrgMas).HasForeignKey(x => x.SalNo); 
        });


        modelBuilder.Entity<TempTrgReq>(entity =>
        {
            entity.HasKey(e => e.ReqNo);
           
        });

        modelBuilder.Entity<TempReqDtls>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ID");
        

        });



        modelBuilder.Entity<TcourseCatalogue>(entity =>
        {
            entity.HasKey(e => e.CourseCode);
            entity.HasOne(d => d.TSubjectCode).WithMany(p => p.TcourseCatalogue).HasForeignKey(x => x.SubjCode);
            entity.HasOne(d => d.TcourseCatCode).WithMany(p => p.TcourseCatalogues).HasForeignKey(x => x.CourseCatCode);
            entity.HasOne(d => d.TcourseType).WithMany(p => p.TcourseCatalogues).HasForeignKey(x => x.CourseType);
            entity.HasOne(d => d.TtrgPlaceCode).WithMany(p => p.TcourseCatalogues).HasForeignKey(x => x.TrgPlaceCode);
        });

        modelBuilder.Entity<Tholidays>(entity =>
        {
            entity.HasKey(e => new { e.Holiday }).HasName("HOLIDAY");
        });

        modelBuilder.Entity<OvertimeComment>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo, e.TimeStamp }).HasName("COV_APL_Tx_PK");


        });

        modelBuilder.Entity<ConOvertimeReqMas>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo }).HasName("COV_APL_Tx_PK");
     

        });


        modelBuilder.Entity<ConOvertimeReqMas>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo }).HasName("COV_APL_Tx_PK");
        });


        modelBuilder.Entity<ConOvertimeReqDtls>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo, e.OtDate }).HasName("COV_APL_Dtl_PK");
        });

        modelBuilder.Entity<ConOvertimeReqPreDtls>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo, e.OtDate }).HasName("COV_APL_Dtl_PK");
        });

        modelBuilder.Entity<OvertimeReqMas>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo }).HasName("OV_APL_Tx_PK");
        });
        modelBuilder.Entity<OvertimeReqDtls>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo, e.OtDate }).HasName("OV_APL_Dtl_PK");
        });

        modelBuilder.Entity<OvertimeReqPreDtls>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.InDocSlNo, e.OtDate }).HasName("OV_APL_Dtl_PK");
        });


        modelBuilder.Entity<EmpImpact>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ID"); ;
        });
        modelBuilder.Entity<EmpSkill>(entity =>
        {
            entity.HasKey(e => e.Id);
        });

        modelBuilder.Entity<SpecialiedSkill>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(d => d.EmpImpact).WithMany(p => p.SpecialiedSkills).HasForeignKey(x => x.ImpactId);
            entity.HasOne(d => d.EmpSkill).WithMany(p => p.SpecialiedSkills).HasForeignKey(x => x.SkillId);
            entity.HasOne(d => d.VempDtl).WithMany(p => p.SpecialiedSkills).HasForeignKey(x => x.DgId);
        });
        modelBuilder.Entity<TcityCode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ID"); ;
        });
        modelBuilder.Entity<TrainigPlan>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(d => d.TcityCode).WithMany(p => p.TrainigPlans).HasForeignKey(x => x.PlaceId);
            entity.HasOne(d => d.SpecialiedSkill).WithMany(p => p.TrainigPlans).HasForeignKey(x => x.spId);


        });

        modelBuilder.Entity<TrainingComm>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasOne(d => d.TempTrgMas).WithMany(p => p.TrainingComm).HasForeignKey(x => x.SalNo);
            entity.HasOne(d => d.TcourseCatalogue).WithMany(p => p.TrainingComm).HasForeignKey(x => x.CourseId);
        });

        modelBuilder.Entity<VendorTraining>(entity =>
        {
            entity.HasKey(e => e.Id);
            
        });
        modelBuilder.Entity<TrainingField>(entity =>
        {
            entity.HasKey(e => e.Id);

        });

        modelBuilder.Entity<AssetItem>(entity =>
        {

            entity.HasKey(e => new { e.ItemId });
            entity.HasOne(d => d.Category).WithMany(p => p.AssetItem).HasForeignKey(x => x.CatId);
           

        });



        ///////////////////////////////////////////////////////////
        ///
        //ASSETS
        //modelBuilder.Entity<AssetRequest>(entity =>
        //{
        //    entity.HasKey(e => e.ReqId);
        //    entity.HasOne(d => d.ReqStat).WithMany(p => p.RequestStatus).HasForeignKey(x => x.RsId);


        //});

        modelBuilder.Entity<VreqLeave>(entity =>
        {
            entity.ToView("VREQ_LEAVE");
        });

        modelBuilder.Entity<TincomingMail>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo }).HasName("INCOMING_MAIL_PK");
        });

        modelBuilder.Entity<TOutgoingMail>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptNo, e.OutMailNo, e.OrderType }).HasName("INCOMING_MAIL_PK");
        });

        modelBuilder.Entity<TIncomingDoc>(entity =>
        {
            entity.HasKey(e => new { e.InYear, e.InDeptInd, e.InMailNo, e.TransType}).HasName("INCOMING_MAIL_PK");
        });

        modelBuilder.Entity<TLastMailNo>(entity =>
        {
            entity.HasKey(e => new { e.DocYear, e.DeptInd }).HasName("INCOMING_MAIL_PK");
        });

        modelBuilder.Entity<TOrderType>(entity =>
        {
            entity.HasKey(e => new { e.OrderType }).HasName("ORDER_TYPE");
        });

        modelBuilder.HasSequence("AUTH_SEQ");
        modelBuilder.HasSequence("EXCUSE_FOR_SEC_SEQ");
        modelBuilder.HasSequence("SEQ_EX");
        modelBuilder.HasSequence("SEQ1");
        modelBuilder.HasSequence("SEQ11");
        modelBuilder.HasSequence("SEQ2");
        modelBuilder.HasSequence("SEQ7");
        modelBuilder.HasSequence("SQ_Absents");
        modelBuilder.HasSequence("SQ_Cars");
        modelBuilder.HasSequence("SQ_CarsGasDtl");
        modelBuilder.HasSequence("SQ_CarsRequests");
        modelBuilder.HasSequence("SQ_CarsServices");
        modelBuilder.HasSequence("SQ_CarsViolations");
        modelBuilder.HasSequence("SQ_StrCategoryCodes");
        modelBuilder.HasSequence("SQ_StrItemCodes");
        modelBuilder.HasSequence("SQ_StrReqMstrs");
        modelBuilder.HasSequence("SQ_UserRoles");
        modelBuilder.HasSequence("SRW_NEXT_APPID");

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
