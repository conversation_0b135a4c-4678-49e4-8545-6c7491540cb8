﻿using Microsoft.AspNetCore.Hosting;
using System;

namespace HumanResource.Core.Contexts;

public class Connections
{


    private string _bcStringProduction { get; } = "Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = orcl)));User Id=***2010_4;Password=***";
    private string _bcStringTest { get; } = "Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = orcl)));User Id=***2010_4;Password=***";


    private string _hrmsStringProduction { get; } = "Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = orcl)));User Id=DRCH;Password=*******";
    private string _hrmsStringTest { get; } = "Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = localhost)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = orcl)));User Id=DRCH;Password=*******";

    public IHttpContextAccessor _http;

    private string _env = "Development"; // Development , Production 

    public Connections(
       
        IHttpContextAccessor httpContextAccessor)
    {

        _http  = httpContextAccessor;

    }

    public Connections()
    {



    }

    public string getConnection(string type)
    {

        //_env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

        if(_env == "Production")
        {
            if (type == "hrms") 
            {
                return _hrmsStringProduction;
            }

            if (type == "bc")
            {
                return _bcStringProduction;
            }  
        }

        if (_env == "Development")
        {
            if (type == "hrms")
            {
                return _hrmsStringTest;
            }

            if (type == "bc")
            {
                return _bcStringTest;
            }
        }

         

        return "";
    }

}