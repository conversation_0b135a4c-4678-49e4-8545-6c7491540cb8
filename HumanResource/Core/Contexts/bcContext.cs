﻿using System;
using System.Collections.Generic;
using HumanResource.Modules.Shared.Models.Entities.BC;
using Microsoft.EntityFrameworkCore;
using HumanResource.Modules.Shared.Models.Entities.BC;

namespace HumanResource.Core.Contexts;

public partial class bcContext : DbContext
{

    public bcContext(DbContextOptions<bcContext> options)
        : base(options)
    {
    }


    public virtual DbSet<Supplier> Supplier { get; set; }

    public virtual DbSet<BcAccount> BcAccount { get; set; }

    private Connections connections = new Connections();
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)

        => optionsBuilder.UseOracle(connections.getConnection("bc"));
        //=> optionsBuilder.UseOracle("Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = gsc-db002)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = GSCDB19C)));User Id=***2010_4;Password=***");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema("DRC2010_4");

        modelBuilder.Entity<BcAccount>(entity =>
        {
            entity.ToView("VACC_NAME");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
