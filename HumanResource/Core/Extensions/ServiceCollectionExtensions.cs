using HumanResource.Core.UI.Services;
using HumanResource.Core.Contexts;
using HumanResource.Modules.Account.Configuration;
using HumanResource.Modules.Dashboard.Configuration;
using HumanResource.Modules.Leaves.Configuration;
using HumanResource.Modules.Attendence.Configuration;
using HumanResource.Modules.Overtime.Configuration;
using HumanResource.Modules.Training.Configuration;
using HumanResource.Modules.Purchases.Configuration;
using HumanResource.Modules.Inventory.Configuration;
using HumanResource.Modules.Transports.Configuration;
using HumanResource.Modules.Assets.Configuration;
using HumanResource.Modules.Users.Configuration;
using HumanResource.Modules.Settings.Configuration;
using HumanResource.Modules.Employees.Configuration;
using HumanResource.Modules.Execuses.Configuration;
using HumanResource.Modules.Shared.Configuration;

namespace HumanResource.Core.Extensions
{
    /// <summary>
    /// Extension methods for IServiceCollection to register HR modules
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Registers all HR module services including navigation providers, badge providers, and core services
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddHRModules(this IServiceCollection services)
        {
            // Register Core Navigation Service
            services.AddScoped<NavigationService>();
            
            // Register Core Task Service
            services.AddScoped<TaskService>();
            
            
            // Register All Module Services
            services.AddAccountServices();
            services.AddDashboardServices();
            services.AddLeavesServices();
            services.AddAttendanceServices();
            services.AddOvertimeServices();
            services.AddTrainingServices();
            services.AddPurchasesServices();
            services.AddInventoryServices();
            services.AddTransportServices();
            services.AddAssetsServices();
            services.AddUsersServices();
            services.AddSettingsServices();
            services.AddEmployeesServices();
            services.AddExcusesServices();
            services.AddSharedServices();
            
            return services;
        }

        /// <summary>
        /// Registers only core navigation services (useful for testing or minimal setups)
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddCoreNavigationServices(this IServiceCollection services)
        {
            services.AddScoped<NavigationService>();
            services.AddScoped<TaskService>();
            return services;
        }

        /// <summary>
        /// Registers HR modules selectively based on configuration
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="enabledModules">List of module names to enable</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddHRModules(this IServiceCollection services, params string[] enabledModules)
        {
            // Register Core Navigation Service
            services.AddScoped<NavigationService>();
            
            // Register Core Task Service
            services.AddScoped<TaskService>();
            
  
            var moduleSet = new HashSet<string>(enabledModules, StringComparer.OrdinalIgnoreCase);
            
            // Register modules based on enabled list
            if (moduleSet.Contains("Account")) services.AddAccountServices();
            if (moduleSet.Contains("Dashboard")) services.AddDashboardServices();
            if (moduleSet.Contains("Leaves")) services.AddLeavesServices();
            if (moduleSet.Contains("Attendance")) services.AddAttendanceServices();
            if (moduleSet.Contains("Overtime")) services.AddOvertimeServices();
            if (moduleSet.Contains("Training")) services.AddTrainingServices();
            if (moduleSet.Contains("Purchases")) services.AddPurchasesServices();
            if (moduleSet.Contains("Inventory")) services.AddInventoryServices();
            if (moduleSet.Contains("Transport")) services.AddTransportServices();
            if (moduleSet.Contains("Assets")) services.AddAssetsServices();
            if (moduleSet.Contains("Users")) services.AddUsersServices();
            if (moduleSet.Contains("Settings")) services.AddSettingsServices();
            if (moduleSet.Contains("Employees")) services.AddEmployeesServices();
            if (moduleSet.Contains("Excuses")) services.AddExcusesServices();
            if (moduleSet.Contains("Shared")) services.AddSharedServices();
            
            return services;
        }
    }
} 