﻿@using HumanResource.Modules.Training.ViewModels
@model TrainingViewModel

<div class="row">
 <div class="col-lg-6">
<div class="card">
   <div class="card-header  d-flex justify-content-between">
      <h3><strong>@Model._l("Study Plan")</strong></h3>
          @if(Model.Can("Training-admin|Training-department"))
          {
           <button class="btn btn-link text-dark" data-toggle="modal" data-target="#edit-StudyPlan-modal"><i class="far fa-edit"></i></button>
          }
       
       </div>
    <div class="card-body">
        <div class="row">
            <table class="table">
               <tr>
                <th>@Model._l("Request date")</th>
                 <td>@Model._d(@Model.TqualReq.TxnDate)</td>
               </tr>
               <tr>
                <th>@Model._l("Required Qualification")</th>
                <td>@Model.TqualReq.TqualCode.QualDespA</td>             
              </tr>
               <tr>
                <th>@Model._l("Field of Study")</th>
               <td>@Model.TqualReq.TSubjectCode.SubjDespA</td>               
                </tr>
                <tr>
                  <th>@Model._l("University")</th>
                 <td>
                   @{if (Model.TqualReq.UnivInstCode != null && Model.TqualReq.UnivInstCode != 0)
                     {
                      @Model.TqualReq.TunivInstCode.UnivInstDespA
                     }
                    }
                  </td>
                  </tr>
                <tr>
                 <th>@Model._l("Academic Specialization")</th>
                   <td>
                      @if (@Model.TqualReq.StudyType != null && @Model.TqualReq.StudyType != 0)
                    {
                       @Model.TqualReq.TstudyType.StudyTypeDespA
                    }
                  </td>
                 </tr>
                 <tr>
                  <th>@Model._l("Country code")</th>
                 <td> @if (@Model.TqualReq.CountryCode != null && @Model.TqualReq.CountryCode != 0)
                    {  @Model.TqualReq.TcountryCode.CountryDespA }
                </td>
                  </tr>
                <tr>
                <th>@Model._l("Reason")</th>
                <td>@Model.TqualReq.Reson</td>
                </tr>
                <tr>
                  <th>@Model._l("Decition Details")</th>
                 <td>
                   @if (Model.TqualReq.DecisionNo != null)
                    {
                      @Model.TqualReq.DecisionNo

                     }
                                else if (Model.TqualReq.InitialAcceptanceAppr == 1 )
                     {
                       @if (Model.Can("Training-admin|Training-department"))
                       {
                         <button class="btn btn-primary rounded-pill" data-toggle="modal" data-target="#edit-Decision-modal"><i class="fas fa-plus"></i>@Model._l("Add")</button>
                         }
                                
                     }
                 
                 </td>
                 </tr>
 
                </table>
        </div>
   
    
    
    </div>
</div>
</div>

<div class="col-lg-6">
        <div class="card">
            <div class="card-header  d-flex justify-content-between">
                <h3><strong>@Model._l("Admission")</strong></h3>
             </div>
            <div class="card-body ">
                @if (Model.TqualReq.InitialAcceptanceFileId == null || Model.TqualReq.InitialAcceptanceFileId == 0 ||Model.TqualReq.InitialAcceptanceAppr == 2)
                        { 
                           <form action="~/Training/PlanFileUpload?InYear=@Model.TqualReq.InYear&InDeptInd=@Model.TqualReq.InDeptInd&InMailNo=@Model.TqualReq.InMailNo&InDocSlNo=@Model.TqualReq.InDocSlNo" enctype="multipart/form-data" method="POST" class="ajax">
                              <div class="d-flex justify-content-between">
                            <div class="col-lg-8 form-inline">
                                    <label >@Model._l("Initial Acceptance")  </label>
                                    <input type="file" name="InitialAcceptanceFile" class="form-control" value="Model.TqualReq.InitialAcceptanceFileId" required />
                                </div>
                                 <div class="col-lg-4">
                                <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                               </div>
                              </div>
                          </form>
                        }
                        else
                        {
                            <div class="d-flex justify-content-between">
                          
                                <div class="text-center">
                                   <a target="_blank" href="@Model._h.GetFile(Model.TqualReq.InitialAcceptanceFileGuid)"> <i class="fa fa-file-alt fa-2x"> </i>  </a>
                                    <p>@Model._l("Initial Acceptance")</p>
                                </div>

                        @if (Model.Can("Training-admin|Training-department"))
                        {
                            @if ((Model.TqualReq.InitialAcceptanceFileGuid != null) && (Model.TqualReq.InitialAcceptanceAppr == 2 || Model.TqualReq.InitialAcceptanceAppr == 0 || Model.TqualReq.InitialAcceptanceAppr == null))
                            {
                                <div class="col-lg-6">

                                    <form action="~/Training/DocumentDecline" class="ajax" method="post">
                                        <div class="modal fade" id="InitialAcceptance-decline-modal" role="dialog" aria-labelledby="InitialAcceptance-modalLabel" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger">
                                                        <h5 class="modal-title" id="InitialAcceptance-modalLabel">@Model._l("Declined by department manager")</h5>
                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <label for="">@Model._l("Decline Note")</label>
                                                        <textarea maxlength="225" name="DeclineNote" class="form-control"></textarea>
                                                    </div>
                                                    <div class="modal-footer d-flex">
                                                        <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Cancel")</button>
                                                        <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Submit")</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <input type="hidden" name="InYear" value="@Model.TqualReq.InYear">
                                        <input type="hidden" name="InDeptInd" value="@Model.TqualReq.InDeptInd">
                                        <input type="hidden" name="InDocSlNo" value="@Model.TqualReq.InDocSlNo">
                                        <input type="hidden" name="InMailNo" value="@Model.TqualReq.InMailNo">
                                        <input type="hidden" name="EmpNo" value="@Model.TqualReq.EmpNo">
                                        <input type="hidden" name="InitialAcceptanceAppr" value="2">
                                    </form>

                                    <div class="d-flex justify-content-center">
                                        <a href="~/Training/DocumentApprove?InYear=@Model.TqualReq.InYear&InDeptInd=@Model.TqualReq.InDeptInd&InDocSlNo=@Model.TqualReq.InDocSlNo&InMailNo=@Model.TqualReq.InMailNo&EmpNo=@Model.TqualReq.EmpNo&&InitialAcceptanceAppr=1"
                                        class="btn btn-larg btn-success mx-1 after-confirm rounded-pill btn-block">@Model._l("Approve")</a>

                                        <a href="#" class="btn btn-larg btn-danger mx-1  rounded-pill btn-block" data-toggle="modal" data-target="#InitialAcceptance-decline-modal">@Model._l("Decline")</a>
                                    </div>

                                </div>
                            }
                        } 
                    </div>
                        }
                   <br />                  
            </div>
        </div>
    </div>
</div>
 <div class="row">
@if (@Model.TqualReq.DecisionNo != null)
{

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3><strong>@Model._l("Employee Attendance")</strong></h3>
                    @if (Model.Can("Training-admin|Training-department"))
                    {
                        <button class="btn btn-primary rounded-pill" data-toggle="modal" data-target="#edit-UpdateEmpAttend-modal"><i class="fas fa-plus"></i>@Model._l("New")</button>
                    }
                </div>


                <div class="card-body">
                    <div class="row">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>@Model._l("ID") </th>
                                    <th>@Model._l("Date From") </th>
                                    <th>@Model._l("Date To") </th>
                                    <th>@Model._l("No of Days")</th>
                                    <th>@Model._l("Day") </th>
                                    <th>@Model._l("From Time") </th>
                                    <th>@Model._l("To Time") </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var request in Model.TqualEmpAttendances)
                                {
                                    <tr>
                                        <td><a href="#" onclick="get_Attend_data(@request.Id)">#@request.Id</a> </td>
                                        <td>@Model._d(@request.FromDate)</td>
                                        <td>@Model._d(@request.ToDate)</td>
                                        <td>

                                            @{
                                                DateTime end = (DateTime)request.ToDate.AddDays(1);
                                                DateTime start = (DateTime)request.FromDate;
                                                TimeSpan diff = end.Subtract(start);

                                                @diff.ToString("dd")
                                                ;
                                            }

                                        </td>
                                        <td>@request.Day</td>
                                        <td>@Model._t(@request.TimeFrom)</td>
                                        <td>@Model._t(@request.TimeTo)</td>
                                    </tr>

                                }
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3><strong>@Model._l("Study Report")</strong></h3>
                    @if (Model.Can("Training-admin|Training-department"))
                    {
                        <button class="btn btn-primary rounded-pill" data-toggle="modal" data-target="#edit-StudyReport-modal"><i class="fas fa-plus"></i>@Model._l("New")</button>
                    }
                </div>
                <div class="card-body">
                    <div class="row">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>@Model._l("Year") </th>
                                    <th>@Model._l("Semester") </th>
                                    <th>@Model._l("Result") </th>
                                    <th>@Model._l("Grade") </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var request in Model.tqualStudyReports)
                                {
                                    <tr>
                                        <td> @request.Year</td>
                                        <td>@request.Semester</td>
                                        <td>@request.Result</td>
                                        <td>@request.Grade</td>
                                    </tr>

                                }
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
}
</div>

<form action="~/Training/UpdateStudyPlan?InYear=@Model.TqualReq.InYear&InDeptInd=@Model.TqualReq.InDeptInd&InMailNo=@Model.TqualReq.InMailNo&InDocSlNo=@Model.TqualReq.InDocSlNo" method="POST" class="ajax">
    <div class="modal fade" id="edit-StudyPlan-modal" role="dialog" aria-labelledby="edit-StudyPlan-modal" aria-hidden="true">

        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="edit-StudyPlan-modalLabel">@Model.TqualReq.InYear / @Model.TqualReq.InDeptInd /@Model.TqualReq.InMailNo/@Model.TqualReq.InDocSlNo</h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                      
                            <input type="hidden" name="InYear" value="@Model.TqualReq.InYear">
                            <input type="hidden" name="InDeptInd" value="@Model.TqualReq.InDeptInd">
                            <input type="hidden" name="InMailNo" value="@Model.TqualReq.InMailNo">
                            <input type="hidden" name="InDocSlNo" value="@Model.TqualReq.InDocSlNo">

                        </div>
                    
                

                </div>
                <div class="modal-footer d-flex">
                   
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                </div>
            </div>
        </div>
    </div>

</form>

<form action="~/Training/UpdateEmpAttend" method="POST" class="ajax">
    <div class="modal fade" id="edit-UpdateEmpAttend-modal" role="dialog" aria-labelledby="edit-UpdateEmpAttend-modal" aria-hidden="true">

        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="edit-UpdateEmpAttend-modalLabel"></h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="">@Model._l("Date From")</label>
                            <input type="date" name="FromDate" min='@DateTime.Now.ToString("yyyy-MM-dd")' v-model="DateFromTextBoxValue" :max="DateToTextBoxValue" class="form-control" required /><br>

                         </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("Date To")</label>
                            <input type="date" :min='DateFromTextBoxValue' name="ToDate" v-model="DateToTextBoxValue" class="form-control" required /><br>
                        </div>
                   
                        <div class="col-lg-6">
                            <label for="">@Model._l("Day")</label>
                            @Html.DropDownListFor(m=> m.TqualEmpAttendances.FirstOrDefault().Day, (SelectList)@ViewBag.Day, "---Select Option---", new {@class="form-control"})
                      
                       </div>
                        <div class="col-lg-6">
                            <label for="">
                                @Model._l("From Time")</label>
                            <input type="time" name="TimeFrom" min="07:00" max="14:30" v-model="FromTextBoxValue"  class="form-control" required /><br>

                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("To Time")</label>
                            <input type="time" min="07:00" max="14:30" name="TimeTo" v-model="ToTextBoxValue" class="form-control" required /><br>
                        </div>
                        </div>

                    <input type="hidden" name="EmpNo" value="@Model.TqualReq.EmpNo" />

                </div>
                <div class="modal-footer d-flex">
                   
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                </div>
            </div>
        </div>
    </div>

</form>

<form action="~/Training/UpdateStudyReport" method="POST" class="ajax">
    <div class="modal fade" id="edit-StudyReport-modal" role="dialog" aria-labelledby="edit-StudyReport-modal" aria-hidden="true">

        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="edit-StudyReport-modalLabel"></h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="">@Model._l("Year")</label>
                            @Html.DropDownListFor(m=> m.tqualStudyReports.FirstOrDefault().Year, (SelectList)@ViewBag.Year, "---Select Option---", new {@class="form-control"})
                         </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("Semester")</label>
                            <select name="Semester" class="form-control">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                             </select>
                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("Grade")</label>
                            <select name="Grade" class="form-control">
                                <option value="A+">A+</option>
                                <option value="A">A</option>
                                <option value="A-">A-</option>
                                <option value="B+">B+</option>
                                <option value="B">B</option>
                                <option value="B-">B-</option>
                                <option value="C+">C+</option>
                                <option value="C">C</option>
                                <option value="C-">C-</option>
                                <option value="D+">D+</option>
                                <option value="D">D</option>
                                <option value="D-">D-</option>
                                <option value="F">F</option>
                            </select>
                        </div>
                    
                        <div class="col-lg-6">
                            <label for="Result">@Model._l("Result")  </label>
                            <input type="number" name="Result" min="0" max="4" step="0.01" class="form-control" required /><br>

                        </div>
                      
                        </div>
                    <input type="hidden" name="EmpNo" value="@Model.TqualReq.EmpNo" />
                </div>
                <div class="modal-footer d-flex">
                   
                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                </div>
            </div>
        </div>
    </div>

</form>

<form action="~/Training/DecitionData" method="POST" class="ajax">
    <div class="modal fade" id="edit-Decision-modal" role="dialog" aria-labelledby="edit-Decision-modal" aria-hidden="true">

        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="edit-Decision-modal"></h3>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <label for="">@Model._l("Decision No")</label>
                            <input type="text" name="DecisionNo" class="form-control" required /><br>
                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("Decision Date")</label>
                            <input type="date" name="DecisionDate" min='@DateTime.Now.ToString("yyyy-MM-dd")' class="form-control" required /><br>

                        </div>
                        <div class="col-lg-6">
                            <label for="">@Model._l("Decision Note")</label>
                            <textarea maxlength="225" name="DecisionNote" class="form-control"></textarea>

                        </div>

                        <input type="hidden" name="InYear" value="@Model.TqualReq.InYear">
                        <input type="hidden" name="InDeptInd" value="@Model.TqualReq.InDeptInd">
                        <input type="hidden" name="InMailNo" value="@Model.TqualReq.InMailNo">
                        <input type="hidden" name="InDocSlNo" value="@Model.TqualReq.InDocSlNo">


                    </div>

                </div>
                <div class="modal-footer d-flex">

                    <button type="button" class="btn btn-secondary btn-block rounded-pill" data-dismiss="modal">@Model._l("Close")</button>
                    <button type="submit" class="btn btn-primary btn-block rounded-pill">@Model._l("Save")</button>
                </div>
            </div>
        </div>
    </div>

</form>



<div class="modal fade" id="edit-Attendance-modal" role="dialog" aria-labelledby="edit-Attendance-modalLabel" aria-hidden="true">

    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="edit-Attendance-modalLabel">@Model._l("Edit Attendance record")</h3>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="edit-modal-html">
            </div>
        </div>
    </div>
</div>

<script>

    function get_Attend_data(id) {
        $.get("/Training/ViewAttendanceRecord/" + id, function (data) {
            $('#edit-modal-html').html(data);
            $('#edit-Attendance-modal').modal().show();

        });
    }


    $(document).ready(function () {
        $("#Country").change(function () {
            var selectItem = $(this).val();

            $.ajax({

                url: '/Training/GetUinversity',
                type: 'GET',
                dataType: 'json',
                data: { value: selectItem },
                success: function (data) {
                    $('#Uinversity').empty();

                    $.each(data, function (index, item) {
                        console.log(item);
                        $('#Uinversity').append($('<option>').text(item.text).attr('value', item.value));
                    });

                },
                error: function () {
                    alert('Error occurred while fetching data.');
                }
            });
        });
    });
</script>