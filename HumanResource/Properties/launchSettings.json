{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Develpment", "COMPlus_gcConcurrent": "1", "COMPlus_gcServer": "1", "COMPlus_GCRetainVM": "1", "COMPlus_GCHeapHardLimit": "2147483648"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:8035", "sqlDebugging": true, "nativeDebugging": true}, "https": {"commandName": "Project", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Develpment", "COMPlus_gcConcurrent": "1", "COMPlus_gcServer": "1", "COMPlus_GCRetainVM": "1", "COMPlus_GCHeapHardLimit": "2147483648"}, "dotnetRunMessages": true, "applicationUrl": "https://127.0.0.1:8065;http://127.0.0.1:8072", "sqlDebugging": true, "nativeDebugging": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Develpment", "COMPlus_gcConcurrent": "1", "COMPlus_gcServer": "1", "COMPlus_GCRetainVM": "1", "COMPlus_GCHeapHardLimit": "2147483648"}, "nativeDebugging": true, "sqlDebugging": true}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:28635", "sslPort": 44302}}}