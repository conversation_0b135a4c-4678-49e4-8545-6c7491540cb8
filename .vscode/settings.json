{
    // C# Language Configuration
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
    "dotnet.inlayHints.enableInlayHintsForParameters": true,
    "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true,
    "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true,
    "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true,
    "dotnet.inlayHints.enableInlayHintsForOtherParameters": true,
    "dotnet.inlayHints.enableInlayHintsForTypeParameters": true,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": true,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": true,
    "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": true,
    
    // Razor and C# specific settings
    "razor.completion.root": true,
    "razor.languageServer.debug": false,
    "razor.devmode": false,
    "razor.format.enable": true,
    "razor.plugin.path": "",
    
    // File associations for better language detection
    "files.associations": {
        "*.cshtml": "aspnetcorerazor",
        "*.cs": "csharp"
    },
    
    // Language-specific settings
    "[cshtml]": {
        "editor.defaultFormatter": "ms-dotnettools.csharp",
        "editor.wordWrap": "on",
        "editor.quickSuggestions": {
            "other": "on",
            "comments": "on",
            "strings": "on"
        },
        "editor.suggest.insertMode": "replace",
        "editor.semanticHighlighting.enabled": true,
        "editor.bracketPairColorization.enabled": true,
        "editor.guides.bracketPairs": true,
        "editor.inlayHints.enabled": "on",
        "emmet.includeLanguages": {
            "razor": "html"
        }
    },
    
    "[csharp]": {
        "editor.defaultFormatter": "ms-dotnettools.csharp",
        "editor.wordWrap": "on",
        "editor.quickSuggestions": {
            "other": "on",
            "comments": "on",
            "strings": "on"
        },
        "editor.suggest.insertMode": "replace",
        "editor.semanticHighlighting.enabled": true,
        "editor.bracketPairColorization.enabled": true,
        "editor.guides.bracketPairs": true,
        "editor.inlayHints.enabled": "on"
    },
    
    // General editor settings for better C# experience
    "editor.fontSize": 14,
    "editor.wordWrap": "on",
    "editor.quickSuggestions": {
        "other": "on",
        "comments": "on",
        "strings": "on"
    },
    "editor.wordBasedSuggestions": "matchingDocuments",
    "editor.suggestSelection": "first",
    "editor.suggest.localityBonus": true,
    "editor.suggest.shareSuggestSelections": true,
    "editor.suggestOnTriggerCharacters": true,
    "editor.wordBasedSuggestionsMode": "allDocuments",
    
    // Problem and error settings
    "problems.decorations.enabled": true,
    "problems.showCurrentInStatus": true,
    "editor.renderWhitespace": "boundary",
    "editor.renderControlCharacters": true,
    "editor.showUnused": true,
    "editor.showDeprecated": true,
    
    // Omnisharp settings for better C# support
    "omnisharp.enableRoslynAnalyzers": true,
    "omnisharp.enableEditorConfigSupport": true,
    "omnisharp.semanticHighlighting.enabled": true,
    "omnisharp.useModernNet": true,
    "omnisharp.enableMsBuildLoadProjectsOnDemand": false,
    "omnisharp.loggingLevel": "information",
    "omnisharp.autoStart": true,
    
    // Search and file exclusions
    "search.exclude": {
        "**/node_modules": true,
        "**/dist": true,
        "**/build": true,
        "**/bin": true,
        "**/obj": true,
        "**/.git": true,
        "**/wwwroot/assets": true
    },
    "files.exclude": {
        "**/node_modules": true,
        "**/dist": true,
        "**/build": true,
        "**/bin": true,
        "**/obj": true,
        "**/.git": true
    }
} 