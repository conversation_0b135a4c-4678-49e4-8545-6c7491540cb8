{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/HumanResource/HumanResource.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}"}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/HumanResource/HumanResource.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}"}}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/HumanResource/HumanResource.csproj"], "problemMatcher": "$msCompile", "options": {"cwd": "${workspaceFolder}"}}]}