{"version": "0.2.0", "configurations": [{"name": "Launch HumanResource", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/HumanResource/bin/Debug/net7.0/HumanResource.dll", "args": [], "cwd": "${workspaceFolder}/HumanResource", "console": "internalConsole", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Attach to HumanResource", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}]}